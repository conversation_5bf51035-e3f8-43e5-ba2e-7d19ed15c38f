services:
  db:
    image: public.ecr.aws/docker/library/postgres:14-alpine
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=deepinsights
    ports:
      - 5432:5432
    volumes:
      - db:/var/lib/postgresql/data
    container_name: deepinsight_db
  redis:
    restart: always
    image: public.ecr.aws/docker/library/redis:7.4.3-alpine
    ports:
      - 6379:6379
volumes:
  db:
    driver: local
