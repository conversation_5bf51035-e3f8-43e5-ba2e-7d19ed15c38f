otel_exporter {
    endpoint dd-agent:4317;
}
otel_service_name nginx;
otel_trace on;
otel_trace_context propagate;

# This is (part of) a workaround for
# https://zeplyn.atlassian.net/browse/ENG-1146: calendar meetings with too many
# attendees would not load in the frontend. We should remove this if/once that
# flow is changed to no longer use the URL to pass attendee details.
#
# Note that there is a corresponding change in the startup command for the
# frontend code, which will also need to be removed.
large_client_header_buffers 4 32k;

# By default, do not respond to requests.
server {
    listen 80 default_server;
    listen [::]:80 default_server;

    server_name "";
    return 444;
}

server {
    listen 80;
    listen [::]:80;
    server_name ${NGINX_HOST};

    # If the host doesn't match what we're expecting, reject the traffic.
    if ( $host != "${NGINX_HOST}" ){
        return 444;
    }

    server_tokens off;
    add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' https://widget.intercom.io/; child-src 'self' https://intercom-sheets.com  https://www.intercom-reporting.com   https://www.youtube.com  https://player.vimeo.com  https://fast.wistia.net; connect-src 'self' https://via.intercom.io  https://api.intercom.io  https://api.au.intercom.io  https://api.eu.intercom.io  https://api-iam.intercom.io  https://api-iam.eu.intercom.io  https://api-iam.au.intercom.io   https://api-ping.intercom.io    https://nexus-websocket-a.intercom.io  wss://nexus-websocket-a.intercom.io  https://nexus-websocket-b.intercom.io  wss://nexus-websocket-b.intercom.io  https://nexus-europe-websocket.intercom.io   wss://nexus-europe-websocket.intercom.io   https://nexus-australia-websocket.intercom.io  wss://nexus-australia-websocket.intercom.io   https://uploads.intercomcdn.com  https://uploads.intercomcdn.eu   https://uploads.au.intercomcdn.com   https://uploads.eu.intercomcdn.com  https://uploads.intercomusercontent.com https://browser-intake-us5-datadoghq.com; font-src 'self' https://js.intercomcdn.com  https://fonts.intercomcdn.com https://fonts.gstatic.com  https://fonts.googleapis.com; form-action 'self' https://intercom.help  https://api-iam.intercom.io  https://api-iam.eu.intercom.io  https://api-iam.au.intercom.io; img-src 'self' data: https://js.intercomcdn.com  https://static.intercomassets.com  https://downloads.intercomcdn.com  https://downloads.intercomcdn.eu  https://downloads.au.intercomcdn.com  https://uploads.intercomusercontent.com  https://gifs.intercomcdn.com   https://video-messages.intercomcdn.com  https://messenger-apps.intercom.io  https://messenger-apps.eu.intercom.io  https://messenger-apps.au.intercom.io  https://*.intercom-attachments-1.com  https://*.intercom-attachments.eu  https://*.au.intercom-attachments.com  https://*.intercom-attachments-2.com  https://*.intercom-attachments-3.com  https://*.intercom-attachments-4.com  https://*.intercom-attachments-5.com  https://*.intercom-attachments-6.com  https://*.intercom-attachments-7.com  https://*.intercom-attachments-8.com  https://*.intercom-attachments-9.com  https://static.intercomassets.eu  https://static.au.intercomassets.com https://purecatamphetamine.github.io/; media-src 'self' https://js.intercomcdn.com; script-src 'self' 'unsafe-inline'  https://app.intercom.io/ https://widget.intercom.io/ https://js.intercomcdn.com/ https://api-iam.intercom.io/; style-src 'self' 'unsafe-inline';" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options nosniff always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubdomains; preload" always;
    add_header Referrer-Policy "strict-origin" always;
    add_header X-Frame-Options "DENY" always;

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    location / {
        return 301 https://${NGINX_HOST}$request_uri;
    }
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name ${NGINX_HOST};

    # If the host doesn't match what we're expecting, reject the traffic.
    if ( $host != "${NGINX_HOST}" ){
        return 444;
    }

    server_tokens off;
    add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' https://widget.intercom.io/; child-src 'self' https://intercom-sheets.com  https://www.intercom-reporting.com   https://www.youtube.com  https://player.vimeo.com  https://fast.wistia.net; connect-src 'self' https://via.intercom.io  https://api.intercom.io  https://api.au.intercom.io  https://api.eu.intercom.io  https://api-iam.intercom.io  https://api-iam.eu.intercom.io  https://api-iam.au.intercom.io   https://api-ping.intercom.io    https://nexus-websocket-a.intercom.io  wss://nexus-websocket-a.intercom.io  https://nexus-websocket-b.intercom.io  wss://nexus-websocket-b.intercom.io  https://nexus-europe-websocket.intercom.io   wss://nexus-europe-websocket.intercom.io   https://nexus-australia-websocket.intercom.io  wss://nexus-australia-websocket.intercom.io   https://uploads.intercomcdn.com  https://uploads.intercomcdn.eu   https://uploads.au.intercomcdn.com   https://uploads.eu.intercomcdn.com  https://uploads.intercomusercontent.com https://browser-intake-us5-datadoghq.com; font-src 'self' https://js.intercomcdn.com  https://fonts.intercomcdn.com https://fonts.gstatic.com  https://fonts.googleapis.com; form-action 'self' https://intercom.help  https://api-iam.intercom.io  https://api-iam.eu.intercom.io  https://api-iam.au.intercom.io; img-src 'self' data: https://js.intercomcdn.com  https://static.intercomassets.com  https://downloads.intercomcdn.com  https://downloads.intercomcdn.eu  https://downloads.au.intercomcdn.com  https://uploads.intercomusercontent.com  https://gifs.intercomcdn.com   https://video-messages.intercomcdn.com  https://messenger-apps.intercom.io  https://messenger-apps.eu.intercom.io  https://messenger-apps.au.intercom.io  https://*.intercom-attachments-1.com  https://*.intercom-attachments.eu  https://*.au.intercom-attachments.com  https://*.intercom-attachments-2.com  https://*.intercom-attachments-3.com  https://*.intercom-attachments-4.com  https://*.intercom-attachments-5.com  https://*.intercom-attachments-6.com  https://*.intercom-attachments-7.com  https://*.intercom-attachments-8.com  https://*.intercom-attachments-9.com  https://static.intercomassets.eu  https://static.au.intercomassets.com https://upload.wikimedia.org https://purecatamphetamine.github.io/; media-src 'self' https://js.intercomcdn.com; script-src 'self' 'unsafe-inline'  https://app.intercom.io/ https://widget.intercom.io/ https://js.intercomcdn.com/ https://api-iam.intercom.io/ https://code.jquery.com/; style-src 'self' 'unsafe-inline';" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options nosniff always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubdomains; preload" always;
    add_header Referrer-Policy "strict-origin" always;
    add_header X-Frame-Options "DENY" always;

    client_max_body_size 500M;

    ssl_certificate /etc/nginx/ssl/live/${NGINX_HOST}/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/live/${NGINX_HOST}/privkey.pem;

    # Ensure that the container IPs are re-resolved every fifteen seconds, in
    # case the container has restarted.
    resolver 127.0.0.11 valid=15s;
    set $backend_upstream deepinsight_django;
    set $frontend_upstream deepinsight_web;
    set $admin_upstream deepinsight_admin;

    location ^~ /api/admin {
        rewrite ^/api/admin$ /api/admin/ permanent;
        proxy_pass http://$admin_upstream:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection "";
        proxy_http_version 1.1;
    }

    location ^~ /api/ {
        proxy_pass http://$backend_upstream:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection "";
        proxy_http_version 1.1;
    }

    location /.well-known/ {
        root /www/;
    }

    location / {
        proxy_pass http://$frontend_upstream:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection "";
        proxy_http_version 1.1;
        location ~ ^/(notes|dashboard|insights) {
            proxy_pass http://$frontend_upstream:3000;
            proxy_buffering off;
        }
    }
}

server {
  listen 81;
  server_name localhost;

  allow 127.0.0.1;        # localhost
  allow *********/8;      # All docker local networks
  deny all;               # Deny all other traffic

  access_log off;

  location /nginx_status/ {
    stub_status;

    # ensures the version information can be retrieved
    server_tokens on;
  }
}
