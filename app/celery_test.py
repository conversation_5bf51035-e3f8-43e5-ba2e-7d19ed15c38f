import logging
from unittest.mock import MagicMock, patch

import pytest
from django.test import TestCase, override_settings
from django_celery_beat.models import PeriodicTask

from app.celery import (
    ColorTraceIDFormatter,
    TaskTraceIDFormatter,
    after_setup_logger_handler,
    after_setup_task_logger_handler,
    setup_periodic_tasks,
    worker_process_init_handler,
)
from deepinsights.meetingsapp.models.organization import Organization


class TestCeleryConfig(TestCase):
    """Test cases for Celery configuration and tasks."""

    app_mock: MagicMock
    sender_mock: MagicMock

    def setUp(self) -> None:
        """Set up test environment."""
        self.app_mock = patch("celery.Celery").start()
        self.sender_mock = MagicMock()

    def tearDown(self) -> None:
        """Clean up after tests."""
        patch.stopall()

    @patch("deepinsights.core.integrations.crm.sequoia_salesforce.SequoiaSalesforce")
    def test_sync_salesforce_cases_with_sequoia(self, mock_salesforce: MagicMock) -> None:
        """Test Salesforce sync when Sequoia org exists and credentials are set."""
        from app.celery import sync_salesforce_cases

        # Create Sequoia organization
        Organization.objects.create(name="Sequoia Financial Group")

        # Mock settings
        with self.settings(
            SEQUOIA_SALESFORCE_OAUTH_URL="url",
            SEQUOIA_SALESFORCE_CLIENT_ID="id",
            SEQUOIA_SALESFORCE_CLIENT_SECRET="secret",
        ):
            # Mock Salesforce instance
            mock_sf_instance = mock_salesforce.return_value
            mock_sf_instance.get_sf_cases.return_value = {"records": []}

            # Run the task
            sync_salesforce_cases()

            # Verify Salesforce methods were called
            mock_salesforce.assert_called_once()
            mock_sf_instance.get_sf_cases.assert_called_once()
            mock_sf_instance.generate_notes_from_cases.assert_called_once_with(cases=[])

    def test_sync_salesforce_cases_without_sequoia(self) -> None:
        """Test Salesforce sync when Sequoia org doesn't exist."""
        from app.celery import sync_salesforce_cases

        with self.assertLogs(level="INFO") as cm:
            sync_salesforce_cases()
            self.assertIn("Not a Sequoia Instance", cm.output[0])

    @patch("deepinsights.core.notifications.task_reminder.send_task_reminders")
    def test_send_task_reminders(self, mock_send_reminders: MagicMock) -> None:
        """Test task reminder sending."""
        from app.celery import send_task_reminders

        send_task_reminders()
        mock_send_reminders.assert_called_once()

    @patch("deepinsights.meetingsapp.tasks.delete_old_transcripts")
    def test_delete_old_transcripts(self, mock_delete: MagicMock) -> None:
        """Test old transcript deletion."""
        from app.celery import delete_old_transcripts

        delete_old_transcripts()
        mock_delete.assert_called_once()

    @patch("deepinsights.meetingsapp.tasks.delete_older_notes")
    def test_delete_older_notes(self, mock_delete: MagicMock) -> None:
        """Test old notes deletion."""
        from app.celery import delete_older_notes

        delete_older_notes()
        mock_delete.assert_called_once()

    @patch("deepinsights.meetingsapp.tasks.export_org_metrics")
    def test_export_org_metrics(self, mock_export: MagicMock) -> None:
        """Test organization metrics export."""
        from app.celery import export_org_metrics

        export_org_metrics()
        mock_export.assert_called_once()

    @patch("deepinsights.meetingsapp.tasks.email_waitlisted_user_report")
    def test_email_waitlisted_user_report(self, mock_email: MagicMock) -> None:
        """Test waitlisted user report email."""
        from app.celery import email_waitlisted_user_report

        email_waitlisted_user_report()
        mock_email.assert_called_once()

    @override_settings(APP_DOMAIN="http://example.com/localhost")
    @patch.object(PeriodicTask, "objects")
    @patch("app.celery.logging.info")
    def test_setup_periodic_tasks_non_localhost(self, mock_log: MagicMock, mock_periodic_objects: MagicMock) -> None:
        sender = MagicMock()

        setup_periodic_tasks(sender=sender)  # type: ignore[no-untyped-call]

        self.assertEqual(sender.add_periodic_task.call_count, 9)
        mock_periodic_objects.get.assert_not_called()
        mock_log.assert_not_called()

    @override_settings(APP_DOMAIN="http://localhost:3000")
    @patch.object(PeriodicTask, "objects")
    @patch("app.celery.logging.info")
    def test_setup_periodic_tasks_localhost(self, mock_log: MagicMock, mock_periodic_objects: MagicMock) -> None:
        sender = MagicMock()
        first_periodic_object = MagicMock()
        second_periodic_object = MagicMock()
        third_periodic_object = MagicMock()
        fourth_periodic_object = MagicMock()
        mock_periodic_objects.get.side_effect = [
            first_periodic_object,
            second_periodic_object,
            third_periodic_object,
            fourth_periodic_object,
        ]

        setup_periodic_tasks(sender=sender)  # type: ignore[no-untyped-call]

        self.assertEqual(sender.add_periodic_task.call_count, 9)
        self.assertEqual(mock_periodic_objects.get.call_count, 4)
        self.assertEqual(mock_log.call_count, 4)
        for object in [first_periodic_object, second_periodic_object, third_periodic_object, fourth_periodic_object]:
            self.assertTrue(isinstance(object.enabled, bool))
            self.assertFalse(object.enabled)
            object.save.assert_called_once()


@pytest.mark.parametrize("formatter_cls", [ColorTraceIDFormatter, TaskTraceIDFormatter])
def test_format_adds_trace_id_for_no_trace(formatter_cls: type[ColorTraceIDFormatter | TaskTraceIDFormatter]) -> None:
    log_record = logging.LogRecord(
        name="test",
        level=logging.INFO,
        pathname="test.py",
        lineno=1,
        msg="Test message",
        args=None,
        exc_info=None,
    )

    formatter = formatter_cls("[%(otelTraceID)s %(otelSpanID)s] %(message)s")

    assert formatter.format(log_record) == "[00000000000000000000000000000000 0000000000000000] Test message"


@patch("app.opentelemetry.trace.get_current_span")
@pytest.mark.parametrize("formatter_cls", [ColorTraceIDFormatter, TaskTraceIDFormatter])
def test_format_adds_trace_id_for_trace(
    mock_get_current_span: MagicMock, formatter_cls: type[ColorTraceIDFormatter | TaskTraceIDFormatter]
) -> None:
    log_record = logging.LogRecord(
        name="test",
        level=logging.INFO,
        pathname="test.py",
        lineno=1,
        msg="Test message",
        args=None,
        exc_info=None,
    )

    mock_get_current_span.return_value.get_span_context.return_value = MagicMock(
        is_valid=True,
        trace_id=0x12345,
        span_id=0x6789,
    )

    formatter = formatter_cls("[%(otelTraceID)s %(otelSpanID)s] %(message)s")

    assert formatter.format(log_record) == "[00000000000000000000000000012345 0000000000006789] Test message"


@patch("app.celery.configure_opentelemetry")
def test_worker_process_init_handler(mock_configure_opentelemetry: MagicMock) -> None:
    worker_process_init_handler()

    mock_configure_opentelemetry.assert_called_once_with("celery")


@pytest.mark.parametrize("colorize", [True, False])
def test_after_setup_logger_handler(colorize: bool) -> None:
    logger = logging.Logger("test_logger")
    handlers = [logging.NullHandler(), logging.NullHandler()]
    for h in handlers:
        logger.addHandler(h)

    after_setup_logger_handler(logger, colorize)

    assert all(
        [(isinstance(h.formatter, ColorTraceIDFormatter) and h.formatter.use_color == colorize) for h in handlers]
    )


@pytest.mark.parametrize("colorize", [True, False])
def test_after_setup_task_logger_handler(colorize: bool) -> None:
    logger = logging.Logger("test_logger")
    handlers = [logging.NullHandler(), logging.NullHandler()]
    for h in handlers:
        logger.addHandler(h)

    after_setup_task_logger_handler(logger, colorize)

    assert all(
        [(isinstance(h.formatter, TaskTraceIDFormatter) and h.formatter.use_color == colorize) for h in handlers]
    )
