import logging

from opentelemetry import metrics, trace
from opentelemetry.exporter.otlp.proto.http.metric_exporter import OTLPMetricExporter
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.aiohttp_client import AioHttpClientInstrumentor
from opentelemetry.instrumentation.botocore import BotocoreInstrumentor
from opentelemetry.instrumentation.celery import CeleryInstrumentor
from opentelemetry.instrumentation.django import DjangoInstrumentor
from opentelemetry.instrumentation.httpx import HTTPXClientInstrumentor
from opentelemetry.instrumentation.openai import OpenAIInstrumentor
from opentelemetry.instrumentation.psycopg2 import Psycopg2Instrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.instrumentation.threading import ThreadingInstrumentor
from opentelemetry.instrumentation.urllib import URLLibInstrumentor
from opentelemetry.instrumentation.urllib3 import URLLib3Instrumentor
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
from opentelemetry.sdk.resources import SERVICE_NAME, Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor


class TraceIDLogFilter(logging.Filter):
    """A log filter that adds OpenTelemetry trace and span IDs to log records."""

    def filter(self, record: logging.LogRecord) -> bool:
        # This is adapted from the code in the OpenTelemetry Logging Instrumentor.
        # That code checks whether the span and span context are valid, and otherwise
        # does not update the record. This code always updates the record, so that
        # the log formatting always has otelSpanID and otelTraceID set.
        ctx = trace.get_current_span().get_span_context()
        record.otelSpanID = format(ctx.span_id, "016x")
        record.otelTraceID = format(ctx.trace_id, "032x")
        return True


def configure_opentelemetry(service_name: str) -> None:
    resource = Resource.create(attributes={SERVICE_NAME: service_name})

    provider = TracerProvider(resource=resource)
    provider.add_span_processor(BatchSpanProcessor(OTLPSpanExporter()))
    trace.set_tracer_provider(provider)

    metric_reader = PeriodicExportingMetricReader(OTLPMetricExporter())
    meter_provider = MeterProvider(resource=resource, metric_readers=[metric_reader])
    metrics.set_meter_provider(meter_provider)

    AioHttpClientInstrumentor().instrument()
    BotocoreInstrumentor().instrument()  # type: ignore[no-untyped-call]
    # Celery doesn't run in the FastAPI/Django or admin processes, but in order to connect up Celery
    # tasks with their callers, we need to do this instrumentation in the FastAPI/Django process.
    CeleryInstrumentor().instrument()
    DjangoInstrumentor().instrument()
    HTTPXClientInstrumentor().instrument()
    OpenAIInstrumentor().instrument()
    Psycopg2Instrumentor().instrument(skip_dep_check=True)
    RedisInstrumentor().instrument()
    RequestsInstrumentor().instrument()
    ThreadingInstrumentor().instrument()
    URLLib3Instrumentor().instrument()
    URLLibInstrumentor().instrument()
