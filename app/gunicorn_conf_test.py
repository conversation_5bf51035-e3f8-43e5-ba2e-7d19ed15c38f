from unittest.mock import MagicMock, patch

from app.gunicorn_conf import post_fork


def test_post_fork_logs_worker_spawned() -> None:
    server_mock = MagicMock()
    worker_mock = MagicMock()
    worker_mock.pid = 1234

    post_fork(server_mock, worker_mock)
    server_mock.log.info.assert_called_once_with("Worker spawned (pid: %s)", worker_mock.pid)


@patch("app.gunicorn_conf.configure_opentelemetry")
def test_post_fork_configures_opentelemetry(mock_configure_opentelemetry: MagicMock) -> None:
    post_fork(MagicMock(), MagicMock())

    mock_configure_opentelemetry.assert_called_once_with("django")
