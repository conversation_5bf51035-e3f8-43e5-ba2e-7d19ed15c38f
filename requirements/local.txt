#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile --no-annotate local.in
#
a2wsgi==1.10.7
aiohappyeyeballs==2.4.3
aiohttp==3.10.11
aiohttp-retry==2.8.3
aioresponses==0.7.7
aiosignal==1.3.1
amqp==5.2.0
annotated-types==0.7.0
anthropic[bedrock]==0.30.0
anyio==4.6.2.post1
appdirs==1.4.4
argon2-cffi==21.3.0
argon2-cffi-bindings==21.2.0
asgiref==3.8.1
astroid==3.3.5
async-timeout==5.0.0
attrs==24.2.0
azure-core==1.32.0
azure-identity==1.19.0
billiard==4.2.1
black==24.10.0
boto3==1.35.52
boto3-stubs==1.35.70
botocore==1.35.52
botocore-stubs==1.35.70
cachetools==5.5.0
celery==5.4.0
celery-types==0.23.0
certifi==2024.8.30
cffi==1.17.1
cfgv==3.4.0
charset-normalizer==3.4.0
click==8.1.7
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
config==0.5.1
coverage[toml]==7.6.4
cron-descriptor==1.4.5
cryptography==44.0.1
dataclasses-json==0.6.4
debugpy==1.8.7
decorator==5.1.1
deepgram-sdk==2.11.0
deprecated==1.2.14
dill==0.3.9
distlib==0.3.9
distro==1.9.0
django==4.2.21
django-anymail[amazon-ses]==8.2
django-celery-beat==2.7.0
django-celery-results==2.5.1
django-cors-headers==3.14.0
django-coverage-plugin==3.0.0
django-environ==0.10.0
django-extensions==3.2.1
django-formtools==2.5.1
django-google-sso==6.3.0
django-json-widget==2.0.1
django-jsonform==2.22.0
django-migration-linter==5.1.0
django-model-utils==5.0.0
django-oauth-toolkit==3.0.1
django-phonenumber-field[phonenumbers]==8.0.0
django-redis==5.2.0
django-simple-history==3.7.0
django-stubs==5.1.1
django-stubs-ext==5.1.1
django-timezone-field==7.0
django-waffle==4.1.0
djangorestframework==3.15.2
djangorestframework-simplejwt==5.4.0
djangorestframework-stubs==3.15.1
dnspython==2.7.0
email-validator==2.2.0
execnet==2.1.1
fastapi[standard]==0.115.6
fastapi-cli[standard]==0.0.7
filelock==3.16.1
flake8==7.1.1
flake8-isort==6.1.1
frozenlist==1.5.0
fsspec==2024.10.0
google-api-core==2.24.2
google-api-python-client==2.166.0
google-api-python-client-stubs==1.29.0
google-auth==2.35.0
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.1
googleapis-common-protos==1.69.2
gunicorn==23.0.0
h11==0.16.0
h2==4.1.0
hpack==4.0.0
httpcore==1.0.9
httplib2==0.22.0
httptools==0.6.4
httpx[http2]==0.27.2
huggingface-hub==0.26.2
humanize==4.9.0
hyperframe==6.0.1
identify==2.6.1
idna==3.10
importlib-metadata==8.4.0
importlib-resources==6.4.5
iniconfig==2.0.0
isodate==0.7.2
isort==5.13.2
jinja2==3.1.6
jiter==0.7.0
jmespath==1.0.1
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jwcrypto==1.5.6
kombu==5.4.2
loguru==0.7.2
lxml==5.3.0
m3u8-generator==1.5
markdown==3.6
markdown-it-py==3.0.0
markupsafe==3.0.2
marshmallow==3.23.0
mccabe==0.7.0
mdurl==0.1.2
microsoft-kiota-abstractions==1.6.0
microsoft-kiota-authentication-azure==1.6.0
microsoft-kiota-http==1.6.0
microsoft-kiota-serialization-form==1.6.0
microsoft-kiota-serialization-json==1.6.0
microsoft-kiota-serialization-multipart==1.6.0
microsoft-kiota-serialization-text==1.6.0
more-itertools==10.5.0
msal==1.32.0
msal-extensions==1.2.0
msgraph-core==1.1.6
msgraph-sdk==1.19.0
multidict==6.1.0
mypy==1.13.0
mypy-extensions==1.0.0
nodeenv==1.9.1
oauthlib==3.2.2
office365-rest-python-client==2.5.14
openai==1.68.2
opentelemetry-api==1.31.1
opentelemetry-exporter-otlp-proto-common==1.31.1
opentelemetry-exporter-otlp-proto-http==1.31.1
opentelemetry-instrumentation==0.52b1
opentelemetry-instrumentation-aiohttp-client==0.52b1
opentelemetry-instrumentation-asgi==0.52b1
opentelemetry-instrumentation-botocore==0.52b1
opentelemetry-instrumentation-celery==0.52b1
opentelemetry-instrumentation-dbapi==0.52b1
opentelemetry-instrumentation-django==0.52b1
opentelemetry-instrumentation-fastapi==0.52b1
opentelemetry-instrumentation-httpx==0.52b1
opentelemetry-instrumentation-openai==0.39.0
opentelemetry-instrumentation-psycopg2==0.52b1
opentelemetry-instrumentation-redis==0.52b1
opentelemetry-instrumentation-requests==0.52b1
opentelemetry-instrumentation-threading==0.52b1
opentelemetry-instrumentation-urllib==0.52b1
opentelemetry-instrumentation-urllib3==0.52b1
opentelemetry-instrumentation-wsgi==0.52b1
opentelemetry-propagator-aws-xray==1.0.2
opentelemetry-proto==1.31.1
opentelemetry-sdk==1.31.1
opentelemetry-semantic-conventions==0.52b1
opentelemetry-semantic-conventions-ai==0.4.3
opentelemetry-util-http==0.52b1
packaging==24.1
pathspec==0.12.1
pendulum==3.0.0
phonenumbers==8.13.51
pillow==10.3.0
platformdirs==4.3.6
pluggy==1.5.0
portalocker==2.10.1
pre-commit==4.0.1
prompt-toolkit==3.0.48
propcache==0.2.0
proto-plus==1.26.1
protobuf==5.29.4
psycopg2-binary==2.9.6
pyasn1==0.6.1
pyasn1-modules==0.4.1
pycodestyle==2.12.1
pycparser==2.22
pydantic==2.9.2
pydantic-core==2.23.4
pydantic-extra-types==2.10.1
pyflakes==3.2.0
pygments==2.19.1
pyjwt[crypto]==2.9.0
pylint==3.3.1
pylint-django==2.6.1
pylint-plugin-utils==0.8.2
pyparsing==2.4.7
pytest==8.3.3
pytest-asyncio==0.24.0
pytest-cov==6.0.0
pytest-django==4.9.0
pytest-dotenv==0.5.2
pytest-httpx==0.33.0
pytest-md==0.2.0
pytest-rerunfailures==15.0
pytest-sugar==1.0.0
pytest-xdist==3.6.1
python-crontab==3.2.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-liquid==1.12.1
python-multipart==0.0.20
python-slugify==8.0.1
python-soql-parser==0.2.0
pytz==2023.3
pyyaml==6.0.2
redis==4.5.4
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-file==2.1.0
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
responses==0.20.0
rich==13.9.4
rich-toolkit==0.13.2
rpds-py==0.24.0
rsa==4.9
ruff==0.7.1
s3transfer==0.10.3
shellingham==1.5.4
simple-mockforce==0.8.1
simple-salesforce==1.12.6
six==1.16.0
sniffio==1.3.1
sqlparse==0.5.1
starlette==0.41.3
std-uritemplate==1.0.6
termcolor==2.5.0
text-unidecode==1.3
tiktoken==0.9.0
time-machine==2.16.0
tokenizers==0.20.1
toml==0.10.2
tomlkit==0.13.2
tqdm==4.66.6
twilio==9.3.8
typer==0.15.1
types-awscrt==0.23.1
types-boto3==1.0.2
types-cachetools==5.5.0.20240820
types-cffi==1.16.0.20240331
types-colorama==0.4.15.20240311
types-deprecated==1.2.15.20241117
types-docutils==0.21.0.20241005
types-httplib2==0.22.0.20241221
types-jmespath==1.0.2.20240106
types-jsonschema==4.23.0.20240813
types-markdown==3.7.0.20240822
types-psutil==********241102
types-psycopg2==2.9.21.20241019
types-pygments==2.18.0.20240506
types-pyopenssl==24.1.0.20240722
types-python-dateutil==2.9.0.20241003
types-pytz==2024.2.0.20241003
types-pyyaml==6.0.12.20240917
types-redis==4.6.0.20241004
types-requests==2.32.0.20241016
types-s3transfer==0.10.4
types-setuptools==75.6.0.20241126
types-ujson==5.10.0.20240515
typing-extensions==4.12.2
typing-inspect==0.9.0
tzdata==2024.2
uritemplate==4.1.1
urllib3==2.2.3
uvicorn[standard]==0.34.0
uvloop==0.21.0
vine==5.1.0
virtualenv==20.27.1
watchfiles==1.0.4
wcwidth==0.2.13
websockets==13.1
whitenoise==6.6.0
wrapt==1.16.0
yarl==1.17.1
zeep==4.3.1
zipp==3.20.2
