-c constraints.txt

Office365-REST-Python-Client
Pillow==10.3.0  # https://github.com/python-pillow/Pillow
a2wsgi==1.10.7
anthropic[bedrock]==0.30.0
argon2-cffi==21.3.0  # https://github.com/hynek/argon2_cffi
botocore
celery
config
dataclasses-json==0.6.4
deepgram-sdk==2.11.0
django-celery-beat
django-celery-results
django-cors-headers==3.14.0  # https://github.com/adamchainz/django-cors-headers
django-environ==0.10.0  # https://github.com/joke2k/django-environ
django-extensions==3.2.1  # https://github.com/django-extensions/django-extensions
django-formtools
django-google-sso==6.3.0
django-json-widget==2.0.1
django-jsonform==2.22.0
django-model-utils==5.0.0
django-oauth-toolkit
django-phonenumber-field[phonenumbers]
django-redis==5.2.0  # https://github.com/jazzband/django-redis
django-simple-history
django-stubs  # https://github.com/typeddjango/django-stubs
django-waffle==4.1.0
django<5.0.0 # https://www.djangoproject.com/
djangorestframework-simplejwt
djangorestframework==3.15.2  # https://github.com/encode/django-rest-framework
django-anymail[amazon_ses]==8.2  # https://github.com/anymail/django-anymail
fastapi[standard]==0.115.6
google-api-python-client-stubs
google-auth
gunicorn==23.0.0  # https://github.com/benoitc/gunicorn
humanize==4.9.0
jsonschema
m3u8-generator
markdown==3.6
microsoft-kiota-serialization-json
msal
msgraph-sdk
oauthlib
openai==1.68.2
opentelemetry-api
opentelemetry-exporter-otlp-proto-http
opentelemetry-instrumentation
opentelemetry-instrumentation-aiohttp_client
opentelemetry-instrumentation-botocore
opentelemetry-instrumentation-celery
opentelemetry-instrumentation-django
opentelemetry-instrumentation-fastapi
opentelemetry-instrumentation-httpx
opentelemetry-instrumentation-openai
opentelemetry-instrumentation-psycopg2
opentelemetry-instrumentation-redis
opentelemetry-instrumentation-requests
opentelemetry-instrumentation-threading
opentelemetry-instrumentation-urllib
opentelemetry-instrumentation-urllib3
opentelemetry-proto
opentelemetry-sdk
opentelemetry-semantic-conventions
opentelemetry-util-http
phonenumbers
pydantic
pydantic-extra-types
python-liquid==1.12.1
python-slugify==8.0.1  # https://github.com/un33k/python-slugify
pytz==2023.3  # https://github.com/stub42/pytz
redis==4.5.4  # https://github.com/redis/redis-py
simple_salesforce
tomlkit
twilio
whitenoise==6.6.0
