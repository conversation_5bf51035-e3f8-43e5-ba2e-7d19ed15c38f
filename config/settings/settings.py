"""Settings for the app. Use environment variables for environment-specific configuration."""

from datetime import <PERSON><PERSON><PERSON>
from pathlib import Path

import django_stubs_ext
import environ

from app.opentelemetry import TraceIDLogFilter

BASE_DIR = Path(__file__).resolve(strict=True).parent.parent.parent
APPS_DIR = BASE_DIR / "deepinsights"
env = environ.Env()

django_stubs_ext.monkeypatch()

# GENERAL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#debug
DEBUG = env.bool("DJANGO_DEBUG", default=False)
# https://docs.djangoproject.com/en/dev/ref/settings/#secret-key
SECRET_KEY = env("DJANGO_SECRET_KEY", default="")
# Local time zone. Choices are
# http://en.wikipedia.org/wiki/List_of_tz_zones_by_name
# though not all of them may be available with every OS.
# In Windows, this must be set to your system time zone.
TIME_ZONE = "UTC"
# https://docs.djangoproject.com/en/dev/ref/settings/#language-code
LANGUAGE_CODE = "en-us"
# https://docs.djangoproject.com/en/dev/ref/settings/#site-id
SITE_ID = 1
# https://docs.djangoproject.com/en/dev/ref/settings/#use-i18n
USE_I18N = True
# https://docs.djangoproject.com/en/dev/ref/settings/#use-tz
USE_TZ = True
# https://docs.djangoproject.com/en/dev/ref/settings/#locale-paths
LOCALE_PATHS = []  # type: ignore[var-annotated]

ALLOWED_HOSTS = env.list("DJANGO_ALLOWED_HOSTS", default=[])

DATA_UPLOAD_MAX_MEMORY_SIZE = 524288000  # 500M

# DATABASES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#databases

DATABASES = {"default": env.db("DATABASE_URL", default="")}
DATABASES["default"]["ATOMIC_REQUESTS"] = True
DATABASES["default"]["CONN_MAX_AGE"] = env.int("CONN_MAX_AGE", default=60)  # noqa: F405
# https://docs.djangoproject.com/en/stable/ref/settings/#std:setting-DEFAULT_AUTO_FIELD
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"


# URLS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#root-urlconf
ROOT_URLCONF = "app.urls"
# https://docs.djangoproject.com/en/dev/ref/settings/#wsgi-application
WSGI_APPLICATION = "app.django_wsgi.application"

# APPS
# ------------------------------------------------------------------------------
DJANGO_APPS = [
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.sites",
    "django.contrib.messages",
    "django.contrib.postgres",
    "django.contrib.staticfiles",
    "django.contrib.admin",
    "django.forms",
    "django_extensions",
    "django_celery_results",
    "django_celery_beat",
]
THIRD_PARTY_APPS = [
    "rest_framework",  # Django REST Framework: REST APIs for Django
    "corsheaders",  # CORS header support
    "rest_framework_simplejwt",  # JWT token support for DRF
    "rest_framework_simplejwt.token_blacklist",  # JWT token blacklisting
    "simple_history",  # Adds historical records to Django models
    "django_jsonform",  # Provides a user-friendly interface for editing JSON in the admin console.
    "waffle",  # Feature flagging
    "django_google_sso",  # Google SSO for the admin console.
    "django_json_widget",  # Improved JSON editing for the admin console.
    "phonenumber_field",  # A model field for validated phone numbers
    "oauth2_provider",  # Support for third-party OAuth2 client apps.
]
ZEPLYN_APPS = [
    "deepinsights.users.apps.UsersConfig",
    "deepinsights.meetingsapp.apps.MeetingsappConfig",
    "deepinsights.core.apps.CoreConfig",
]

ENVIRONMENT_SPECIFIC_APPS = env.list("DJANGO_ENVIRONMENT_SPECIFIC_APPS", default=[])

# https://docs.djangoproject.com/en/dev/ref/settings/#installed-apps
INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + ZEPLYN_APPS + ENVIRONMENT_SPECIFIC_APPS

# MIGRATIONS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#migration-modules
MIGRATION_MODULES = {"sites": "deepinsights.contrib.sites.migrations"}

# AUTHENTICATION
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#authentication-backends
AUTHENTICATION_BACKENDS = [
    "django.contrib.auth.backends.ModelBackend",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#auth-user-model
AUTH_USER_MODEL = "users.User"

# PASSWORDS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#password-hashers
PASSWORD_HASHERS = [
    # https://docs.djangoproject.com/en/dev/topics/auth/passwords/#using-argon2-with-django
    "django.contrib.auth.hashers.Argon2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher",
    "django.contrib.auth.hashers.BCryptSHA256PasswordHasher",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#auth-password-validators
AUTH_PASSWORD_VALIDATORS = [
    {"NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator"},
    {"NAME": "django.contrib.auth.password_validation.MinimumLengthValidator"},
    {"NAME": "django.contrib.auth.password_validation.CommonPasswordValidator"},
    {"NAME": "django.contrib.auth.password_validation.NumericPasswordValidator"},
]

# MIDDLEWARE
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#middleware
MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.locale.LocaleMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "waffle.middleware.WaffleMiddleware",
]

# STATIC
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#static-root
STATIC_ROOT = str(BASE_DIR / "staticfiles")
# https://docs.djangoproject.com/en/dev/ref/settings/#static-url
STATIC_URL = "/api/static/"
# https://docs.djangoproject.com/en/dev/ref/contrib/staticfiles/#std:setting-STATICFILES_DIRS
STATICFILES_DIRS = [str(BASE_DIR / "static")]
# https://docs.djangoproject.com/en/dev/ref/contrib/staticfiles/#staticfiles-finders
STATICFILES_FINDERS = [
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
]
STATICFILES_STORAGE = "deepinsights.utils.storages.MyStaticFilesStorage"
# MEDIA
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#media-root
MEDIA_ROOT = str(APPS_DIR / "media")
# https://docs.djangoproject.com/en/dev/ref/settings/#media-url
MEDIA_URL = "/api/media/"

# TEMPLATES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#templates
TEMPLATES = [
    {
        # https://docs.djangoproject.com/en/dev/ref/settings/#std:setting-TEMPLATES-BACKEND
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        # https://docs.djangoproject.com/en/dev/ref/settings/#dirs
        "DIRS": [str(APPS_DIR / "templates")],
        # https://docs.djangoproject.com/en/dev/ref/settings/#app-dirs
        "APP_DIRS": True,
        "OPTIONS": {
            # https://docs.djangoproject.com/en/dev/ref/settings/#template-context-processors
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.template.context_processors.i18n",
                "django.template.context_processors.media",
                "django.template.context_processors.static",
                "django.template.context_processors.tz",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    }
]

# https://docs.djangoproject.com/en/dev/ref/settings/#form-renderer
FORM_RENDERER = "django.forms.renderers.TemplatesSetting"

# FIXTURES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#fixture-dirs
FIXTURE_DIRS = (str(APPS_DIR / "fixtures"),)

# SECURITY
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#session-cookie-httponly
SESSION_COOKIE_HTTPONLY = True
# https://docs.djangoproject.com/en/dev/ref/settings/#csrf-cookie-httponly
CSRF_COOKIE_HTTPONLY = True
CSRF_TRUSTED_ORIGINS = [
    "http://localhost",
    "http://localhost:3000",
    "http://localhost:8000",
    "http://127.0.0.1:8000",
    "http://127.0.0.1:3000",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#x-frame-options
X_FRAME_OPTIONS = "DENY"

# SECURITY
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#secure-proxy-ssl-header
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")
# https://docs.djangoproject.com/en/dev/ref/settings/#session-cookie-secure
SESSION_COOKIE_SECURE = env.bool("DJANGO_SESSION_COOKIE_SECURE", default=True)
# https://docs.djangoproject.com/en/dev/ref/settings/#csrf-cookie-secure
CSRF_COOKIE_SECURE = env.bool("DJANGO_CSRF_COOKIE_SECURE", default=True)
# https://docs.djangoproject.com/en/dev/topics/security/#ssl-https
# https://docs.djangoproject.com/en/dev/ref/settings/#secure-hsts-seconds
# TODO: set this to 60 seconds first and then to 518400 once you prove the former works
SECURE_HSTS_SECONDS = 60
# https://docs.djangoproject.com/en/dev/ref/settings/#secure-hsts-include-subdomains
SECURE_HSTS_INCLUDE_SUBDOMAINS = env.bool("DJANGO_SECURE_HSTS_INCLUDE_SUBDOMAINS", default=True)
# https://docs.djangoproject.com/en/dev/ref/settings/#secure-hsts-preload
SECURE_HSTS_PRELOAD = env.bool("DJANGO_SECURE_HSTS_PRELOAD", default=True)
# https://docs.djangoproject.com/en/dev/ref/middleware/#x-content-type-options-nosniff
SECURE_CONTENT_TYPE_NOSNIFF = env.bool("DJANGO_SECURE_CONTENT_TYPE_NOSNIFF", default=True)


# EMAIL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#email-backend
EMAIL_BACKEND = env("DJANGO_EMAIL_BACKEND", default="")
# https://docs.djangoproject.com/en/dev/ref/settings/#email-timeout
EMAIL_TIMEOUT = 5

DEFAULT_FROM_EMAIL = env("DJANGO_DEFAULT_FROM_EMAIL", default="Zeplyn <<EMAIL>>")

EMAIL_SUBJECT_PREFIX = env("DJANGO_EMAIL_SUBJECT_PREFIX", default="[Zeplyn]")
# https://docs.djangoproject.com/en/dev/ref/settings/#server-email
SERVER_EMAIL = env("DJANGO_SERVER_EMAIL", default=DEFAULT_FROM_EMAIL)

# ADMIN
# ------------------------------------------------------------------------------

# https://docs.djangoproject.com/en/dev/ref/settings/#admins
ADMINS = []  # type: ignore[var-annotated]
# https://docs.djangoproject.com/en/dev/ref/settings/#managers
MANAGERS = ADMINS

# LOGGING
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#logging
# See https://docs.djangoproject.com/en/dev/topics/logging for
# more details on how to customize your logging configuration.
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "filters": {
        "require_debug_false": {"()": "django.utils.log.RequireDebugFalse"},
        "add_trace_id": {"()": TraceIDLogFilter},
    },
    "formatters": {
        "traced_logs": {
            # The format for the trace ID and span ID is adapted from the OpenTelemetry Logging Instrumentor.
            "format": "%(levelname)s %(asctime)s %(pathname)s:%(lineno)d [trace_id=%(otelTraceID)s span_id=%(otelSpanID)s] %(message)s"
        },
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "filters": ["add_trace_id"],
            "formatter": "traced_logs",
        },
    },
    "loggers": {
        "django": {
            "handlers": ["console"],
            "propagate": True,
        },
    },
    "root": {"level": "DEBUG" if DEBUG else "INFO", "handlers": ["console"]},
}

# Celery
# ------------------------------------------------------------------------------
if USE_TZ:
    # https://docs.celeryq.dev/en/stable/userguide/configuration.html#std:setting-timezone
    CELERY_TIMEZONE = TIME_ZONE
# https://docs.celeryq.dev/en/stable/userguide/configuration.html#std:setting-broker_url
CELERY_BROKER_URL = env("CELERY_BROKER_URL", default="")
# https://docs.celeryq.dev/en/stable/userguide/configuration.html#std:setting-result_backend
CELERY_CACHE_BACKEND = "django-cache"

CELERY_RESULT_BACKEND = "django-db"
# https://docs.celeryq.dev/en/stable/userguide/configuration.html#result-extended
CELERY_RESULT_EXTENDED = True
# https://docs.celeryq.dev/en/stable/userguide/configuration.html#result-backend-always-retry
# https://github.com/celery/celery/pull/6122
CELERY_RESULT_BACKEND_ALWAYS_RETRY = True
# https://docs.celeryq.dev/en/stable/userguide/configuration.html#result-backend-max-retries
CELERY_RESULT_BACKEND_MAX_RETRIES = 10
# https://docs.celeryq.dev/en/stable/userguide/configuration.html#std:setting-accept_content
CELERY_ACCEPT_CONTENT = ["application/json"]
# https://docs.celeryq.dev/en/stable/userguide/configuration.html#std:setting-task_serializer
CELERY_TASK_SERIALIZER = "json"
# https://docs.celeryq.dev/en/stable/userguide/configuration.html#std:setting-result_serializer
CELERY_RESULT_SERIALIZER = "json"
# https://docs.celeryq.dev/en/stable/userguide/configuration.html#task-time-limit
# TODO: set to whatever value is adequate in your circumstances
CELERY_TASK_TIME_LIMIT = 5 * 60
# https://docs.celeryq.dev/en/stable/userguide/configuration.html#task-soft-time-limit
# TODO: set to whatever value is adequate in your circumstances
CELERY_TASK_SOFT_TIME_LIMIT = 60
# https://docs.celeryq.dev/en/stable/userguide/configuration.html#beat-scheduler
CELERY_BEAT_SCHEDULER = "django_celery_beat.schedulers:DatabaseScheduler"
# https://docs.celeryq.dev/en/stable/userguide/configuration.html#worker-send-task-events
CELERY_WORKER_SEND_TASK_EVENTS = True
# https://docs.celeryq.dev/en/stable/userguide/configuration.html#std-setting-task_send_sent_event
CELERY_TASK_SEND_SENT_EVENT = True
CELERY_TASK_ALWAYS_EAGER = env.bool("CELERY_TASK_ALWAYS_EAGER", False)
CELERY_TASK_EAGER_PROPAGATES = env.bool("CELERY_TASK_EAGER_PROPAGATES", False)


# django-rest-framework
# -------------------------------------------------------------------------------
# django-rest-framework - https://www.django-rest-framework.org/api-guide/settings/
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": ("rest_framework_simplejwt.authentication.JWTAuthentication",),
    "DEFAULT_PERMISSION_CLASSES": ("rest_framework.permissions.IsAuthenticated",),
}

# django-cors-headers - https://github.com/adamchainz/django-cors-headers#setup
CORS_URLS_REGEX = r"^/api/.*$"
CORS_ALLOWED_ORIGINS = [
    "http://127.0.0.1:3000",
    "http://127.0.0.1:8000",
    "http://demo.zeplyn.ai",
    "http://django",
    "http://django:3000",
    "http://localhost",
    "http://localhost:3000",
    "http://localhost:8000",
    "http://staging.app.zeplyn.ai",
    "http://test.zeplyn.ai",
    "https://demo.zeplyn.ai",
    "https://sequoia.app.zeplyn.ai",
    "https://sequoia.app.zeplyn.ai",
    "https://staging.app.zeplyn.ai",
    "https://test.zeplyn.ai",
    "https://zeplyn.ai",
]

CORS_ALLOW_HEADERS = ["*"]

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(days=15),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=30),
}

# CACHES
# ------------------------------------------------------------------------------
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": env("REDIS_URL", default=""),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            # Mimicing memcache behavior.
            # https://github.com/jazzband/django-redis#memcached-exceptions-behavior
            "IGNORE_EXCEPTIONS": True,
        },
    }
}


# django-waffle (Feature flags)
# ----------------------------------------

WAFFLE_FLAG_MODEL = "users.Flag"
WAFFLE_SWITCH_MODEL = "users.Switch"
WAFFLE_SAMPLE_MODEL = "users.Sample"
FLAG_ENVIRONMENT = env("FLAG_ENVIRONMENT", default="prod")

# Your stuff...
# ------------------------------------------------------------------------------

DEEPGRAM_API_KEY = env("DEEPGRAM_API_KEY", default="")
AWS_ACCESS_KEY_ID = env("DJANGO_AWS_ACCESS_KEY_ID", default="")
AWS_SECRET_ACCESS_KEY = env("DJANGO_AWS_SECRET_ACCESS_KEY", default="")
AWS_S3_REGION_NAME = env("DJANGO_AWS_S3_REGION_NAME", default="us-east-1")
AWS_S3_FILE_FOLDER = env("S3_FOLDER_NAME", default="dummyfolder")
EMAIL_URL = env("EMAIL_URL", default="http://localhost:3000/auth/reset-password")
AWS_MEETING_BUCKET = env("AWS_MEETING_BUCKET", default="zeplyn-pilot")
OPENAI_API_KEY = env("OPENAI_API_KEY", default="")
OPENAI_ORG_ID = env("OPENAI_ORG_ID", default="")
MEMO_MAX_TOKEN_LENGTH = env("MEMO_MAX_TOKEN_LENGTH", default=1000)
MEMO_MIN_TOKEN_LENGTH = env("MEMO_MIN_TOKEN_LENGTH", default=100)
MEETING_MIN_TOKEN_LENGTH = env("MEETING_MIN_TOKEN_LENGTH", default=50)

SALESFORCE_USER = env("SALESFORCE_USER", default="")
SALESFORCE_PASSWORD = env("SALESFORCE_PASSWORD", default="")
SALESFORCE_INSTANCE = env("SALESFORCE_INSTANCE", default="")
SALESFORCE_CONSUMER_KEY = env("SALESFORCE_CONSUMER_KEY", default="")
SALESFORCE_CONSUMER_SECRET = env("SALESFORCE_CONSUMER_SECRET", default="")
SALESFORCE_ZEPLYN_APP_DOMAIN = env("SALESFORCE_ZEPLYN_APP_DOMAIN", default="zeplyn-dev-ed.develop.my")

APP_DOMAIN = env("APP_DOMAIN", default="")
REDTAIL_API_KEY = env("REDTAIL_API_KEY", default="")
REDTAIL_BASE_URL = env("REDTAIL_BASE_URL", default="")
LLM_MAX_RETRIES = env("LLM_MAX_RETRIES", default=3)
WEALTHBOX_CLIENT_ID = env("WEALTHBOX_CLIENT_ID", default="")
AWS_S3_MAX_MEMORY_SIZE = env.int("DJANGO_AWS_S3_MAX_MEMORY_SIZE", default=100_000_000)
WEALTHBOX_CLIENT_SECRET = env("WEALTHBOX_CLIENT_SECRET", default="")
WEALTHBOX_OAUTH_BASE_URL = env("WEALTHBOX_OAUTH_BASE_URL", default="")
WEALTHBOX_BASE_URL = env("WEALTHBOX_BASE_URL", default="")
SEQUOIA_SALESFORCE_OAUTH_URL = env("SEQUOIA_SALESFORCE_OAUTH_URL", default="")
SEQUOIA_SALESFORCE_CLIENT_ID = env("SEQUOIA_SALESFORCE_CLIENT_ID", default="")
SEQUOIA_SALESFORCE_CLIENT_SECRET = env("SEQUOIA_SALESFORCE_CLIENT_SECRET", default="")
ZOOM_CLIENT_ID = env("ZOOM_CLIENT_ID", default="")
ZOOM_REDIRECT_URL = env("ZOOM_REDIRECT_URL", default="")
ZOOM_RECALL_CLIENT_ID = env("ZOOM_RECALL_CLIENT_ID", default="")

SHAREFILE_CLIENT_ID = env("SHAREFILE_CLIENT_ID", default="")
SHAREFILE_CLIENT_SECRET = env("SHAREFILE_CLIENT_SECRET", default="")
SHAREFILE_HOSTNAME = env("SHAREFILE_HOSTNAME", default="")
SHAREFILE_USERNAME = env("SHAREFILE_USERNAME", default="")
SHAREFILE_PASSWORD = env("SHAREFILE_PASSWORD", default="")
SHAREFILE_PARENTDIR = env("SHAREFILE_PARENTDIR", default="")

# Google SSO for the admin console
# OAuth secrets
GOOGLE_SSO_CLIENT_ID = "************-5qc75bt4d20ml4eij06gngkplu07s823.apps.googleusercontent.com"
GOOGLE_SSO_PROJECT_ID = "************"
GOOGLE_SSO_CLIENT_SECRET = "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"

# Only show a Google login button on the admin page.
SSO_SHOW_FORM_ON_ADMIN_PAGE = False

# Allow zeplyn.ai accounts to log in to the admin console.
GOOGLE_SSO_ALLOWABLE_DOMAINS = ["zeplyn.ai"]

# Do not automatically create users for Zeplyn accounts.
GOOGLE_SSO_AUTO_CREATE_USERS = False

MSAL_CLIENT_ID = env("MSAL_CLIENT_ID", default="")
MSAL_CLIENT_SECRET = env("MSAL_CLIENT_SECRET", default="")

GOOGLE_CLIENT_ID = env("GOOGLE_CLIENT_ID", default="")
GOOGLE_CLIENT_SECRET = env("GOOGLE_CLIENT_SECRET", default="")

MICROSOFT_DYNAMICS_CLIENT_ID = env("MICROSOFT_DYNAMICS_CLIENT_ID", default="")
MICROSOFT_DYNAMICS_CLIENT_SECRET = env("MICROSOFT_DYNAMICS_CLIENT_SECRET", default="")


ANYMAIL = {
    "AMAZON_SES_CLIENT_PARAMS": {
        "aws_access_key_id": AWS_ACCESS_KEY_ID,
        "aws_secret_access_key": AWS_SECRET_ACCESS_KEY,
        "region_name": AWS_S3_REGION_NAME,
    }
}

AWS_QUERYSTRING_AUTH = False
# DO NOT change these unless you know what you're doing.
_AWS_EXPIRY = 60 * 60 * 24 * 7
AWS_S3_OBJECT_PARAMETERS = {
    "CacheControl": f"max-age={_AWS_EXPIRY}, s-maxage={_AWS_EXPIRY}, must-revalidate",
}

RECALL_API_TOKEN = env("RECALL_API_TOKEN", default="")

TWILIO_ACCOUNT_SID = env("TWILIO_ACCOUNT_SID", default="")
TWILIO_AUTH_TOKEN_SID = env("TWILIO_AUTH_TOKEN_SID", default="")
TWILIO_AUTH_TOKEN_SECRET = env("TWILIO_AUTH_TOKEN_SECRET", default="")
TWILIO_PHONE_NUMBER = env("TWILIO_PHONE_NUMBER", default="")
WEBHOOK_DOMAIN = env("WEBHOOK_DOMAIN", default="")
if WEBHOOK_DOMAIN.endswith("/"):
    raise ValueError("WEBHOOK_DOMAIN should not end with a slash")

PHONENUMBER_DEFAULT_REGION = "US"

# Whether or not to enable the admin console.
#
# Turning this off will cause the admin console URLs not to be served.
ENABLE_ADMIN = env.bool("DJANGO_ENABLE_ADMIN", default=False)

# Whether or not to enable admin logging to the console.
#
# Turning this off will disable a FastAPI request middleware that logs admin actions to the console.
ENABLE_ADMIN_LOGGING = env.bool("FASTAPI_ENABLE_ADMIN_LOGGING", default=True)

# Whether or not to load feature flags from the flag definitions file, flagdefs.py, into the
# database.
#
# Setting this to False will skip the flag loading process, so no flag definitions will be
# created/updated.
LOAD_FEATURE_FLAGS = env.bool("DJANGO_LOAD_FEATURE_FLAGS", default=True)

# How long Celery tasks should hold their locks.
#
# If a task takes longer than this timeout, other tasks will be able to acquire the lock and run.
CELERY_TASK_LOCK_TIMEOUT = env.int("CELERY_TASK_LOCK_TIMEOUT", default=600)

# Whether or not to enable locking of Celery tasks.
#
# If this is true, then tasks will acquire locks that can prevent other tasks from running; if
# false, then no locking will occur.
ENABLE_CELERY_TASK_LOCKING = env.bool("ENABLE_CELERY_TASK_LOCKING", default=True)

# The expiration time interval for the cache used to cache calendar events.
CALENDAR_EVENT_CACHE_TTL_SECONDS = env.int("CALENDAR_EVENT_CACHE_TTL_SECONDS", default=60)

# How long we should wait between runs of the calendar reconciliation task for a given user.
CALENDAR_RECONCILIATION_TASK_TTL_SECONDS = env.int("CALENDAR_RECONCILIATION_TASK_TTL", default=60)

# How long to look ahead when syncing calendar events for a given user.
CALENDAR_EVENT_SYNC_DEFAULT_LOOKAHEAD_SECONDS = env.int(
    "CALENDAR_EVENT_SYNC_DEFAULT_LOOKAHEAD_SECONDS", default=31 * 60 * 60 * 24
)


# Django OAuth Toolkit

# Override the default application model with the custom one.
# https://django-oauth-toolkit.readthedocs.io/en/latest/advanced_topics.html#extending-the-application-model
OAUTH2_PROVIDER_APPLICATION_MODEL = "meetingsapp.OAuthApplication"
OAUTH2_PROVIDER_ACCESS_TOKEN_MODEL = "meetingsapp.OAuthAccessToken"
OAUTH2_PROVIDER_REFRESH_TOKEN_MODEL = "meetingsapp.OAuthRefreshToken"
OAUTH2_PROVIDER_ID_TOKEN_MODEL = "meetingsapp.OAuthIDToken"
OAUTH2_PROVIDER_GRANT_MODEL = "meetingsapp.OAuthGrant"
