export APP_DOMAIN="http://localhost:3000"
export AWS_MEETING_BUCKET="zeplyn-cb-dev"
export CELERY_BROKER_URL=redis://localhost:6379/0
export DATABASE_URL=postgres://postgres:postgres@localhost:5432/deepinsights
export DJANGO_ALLOWED_HOSTS="*"
export DJANGO_CSRF_COOKIE_SECURE=false
export DJANGO_DEBUG=True
export DJANGO_EMAIL_BACKEND="anymail.backends.amazon_ses.EmailBackend"
export DJANGO_ENABLE_ADMIN=True
export DJANGO_ENVIRONMENT_SPECIFIC_APPS=django_migration_linter
export DJANGO_LOAD_FEATURE_FLAGS=True
export DJANGO_SECURE_CONTENT_TYPE_NOSNIFF=false
export DJANGO_SECURE_HSTS_INCLUDE_SUBDOMAINS=false
export DJANGO_SECURE_HSTS_PRELOAD=false
export DJANGO_SESSION_COOKIE_SECURE=false
export DJANGO_SETTINGS_MODULE="config.settings.settings"
export FLAG_ENVIRONMENT="dev"
export OPENAPI_INTERNAL_SCHEMA_ENDPOINTS_ENABLED=true
export OPENAPI_PUBLIC_SCHEMA_ENDPOINTS_ENABLED=true
export OTEL_SDK_DISABLED=true
export REDIS_URL=redis://localhost:6379/1

# Use if you want to run Jupyter on staging
# export DJANGO_ALLOW_ASYNC_UNSAFE="true"
# export DATABASE_URL=<get staging url from documentation>
