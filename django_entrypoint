#!/bin/bash -Ee

function echo_ts() {
  echo "${2:-INFO} $(date +"%Y-%m-%d %H:%M:%S,%3N") $1"
}

function log_error() {
  echo_ts "Error starting server: line $1" "ERROR"
}

trap 'log_error ${LINENO}' ERR

postgres_ready() {
python << END
import sys

import psycopg2

try:
    psycopg2.connect(
        dbname="${POSTGRES_DB}",
        user="${POSTGRES_USER}",
        password="${POSTGRES_PASSWORD}",
        host="${POSTGRES_HOST}",
        port="5432",
    )
except psycopg2.OperationalError as e:
    print(f"error message:::::::::::::::{e}")
sys.exit(0)

END
}

function wait_for_postgres() {
  until postgres_ready; do
    >&2 echo_ts 'Waiting for PostgreSQL to become available...'
    sleep 1
  done
  >&2 echo_ts 'PostgreSQL is available'
}

function start_server() {
  local worker_count=${1:-${DJANGO_WORKER_COUNT:-7}}

  flags=(
    --bind 0.0.0.0:8000
    app.fastapi:app
    --config app/gunicorn_conf.py
    --log-level=info
    --timeout 120
    --workers $worker_count
    --keep-alive 60
  )

  if [ -n "${USE_RELOADING_WORKER}" ]; then
    flags+=('--reload' '-k app.reloading_worker.RestartableUvicornWorker')
  else
    flags+=('-k uvicorn.workers.UvicornWorker')
  fi

  if [ -n "${ENABLE_VSCODE_DEBUGGING}" ]; then
    echo_ts "Attach VSCode debugger (0.0.0.0:5678) to proceed..."
    if [[ "$(uname -s)" == "Darwin" ]]; then
      export OBJC_DISABLE_INITIALIZE_FORK_SAFETY=YES
    fi
    python3 -m debugpy --listen 0.0.0.0:5678 --wait-for-client -m gunicorn ${flags[@]}
  else
    gunicorn ${flags[@]}
  fi
}

function collect_static_files() {
  echo_ts "Collecting static files..."
  python3 manage.py collectstatic --noinput
  echo_ts "Static files collected"
}

function run_admin_mode() {
  echo_ts "Starting up Django admin console"

  wait_for_postgres
  collect_static_files

  # Start server with reduced workers for admin mode
  start_server 2
  exit 0
}

function run_normal_mode() {
  echo_ts "Starting up Django backend."

  wait_for_postgres

  echo_ts "Migrating..."
  python manage.py migrate
  echo_ts "Migrations done. Checking status..."
  python manage.py migrate --check
  echo_ts "Migrations confirmed."

  echo_ts "Flushing expired JWTs..."
  python manage.py flushexpiredtokens
  echo_ts "Flushed expired JWTs"

  collect_static_files

  start_server
}

# Check for admin mode
if [ "$1" = "admin" ]; then
  run_admin_mode
else
  run_normal_mode
fi
