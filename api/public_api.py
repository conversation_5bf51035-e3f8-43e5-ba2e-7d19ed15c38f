import logging
import os
from typing import Annotated, Any

from django.conf import settings
from fastapi import Depends, Form, Request, Response
from pydantic import BaseModel, Field

from api.dependencies import user_from_authorization_header
from api.fastapi_with_adjusted_schema import EXTRA_SUPPORTS_OAUTH_AUTH, FastAPIWithRootPathAdjustedSchema
from api.oauth_server import client_credentials_server
from api.routers.calendar import public_router as calendar_router
from api.routers.crm import public_router as crm_router
from api.routers.meeting_artifacts import public_router as meeting_artifacts_router
from api.routers.note import public_router as note_router
from app.base_paths import PUBLIC_API_PATH
from deepinsights.users.models.user import User

_auth_kwargs: dict[str, Any] = {EXTRA_SUPPORTS_OAUTH_AUTH: True}

# The FastAPI ASGI application for the public API (Zeplyn's third-party API).
public_api = FastAPIWithRootPathAdjustedSchema(
    debug=settings.DEBUG,
    # According to the FastAPI docs, the Swagger endpoint is only generated if the openapi_url is
    # not None.
    openapi_url="/openapi.json" if os.environ.get("OPENAPI_PUBLIC_SCHEMA_ENDPOINTS_ENABLED") else None,
    root_path=PUBLIC_API_PATH,
    root_path_in_servers=False,
    title="Zeplyn API",
    summary="An API for the Zeplyn platform",
    description=(
        "## Getting started\n"
        "This API requires a configured OAuth2 application to access, and only supports the "
        "`client_credentials` OAuth2 flow. You can configure a new OAuth2 Application in the "
        "[Zeplyn admin interface](/api/admin/oauth2_provider/application/)."
        "\n\n"
        "## Getting an access token\n"
        "Once you have a client ID and secret, you can use the `/oauth/token` endpoint to get an "
        "access token. The API does not yet support any scopes; tokens have access to all "
        "endpoints.\n"
        "## Authenticating\n"
        "Once you have an access token, you can use it to authenticate requests to the API by "
        "including it in the `Authorization` header as a Bearer token:\n\n"
        "```\n"
        "Authorization: Bearer <token>\n"
        "```\n\n"
        "## Encryption\n"
        "Data is encrypted in transit to the proxy fronting this API server using TLS. There is no "
        "specific encryption applied to the data in transit other than that provided by the transport."
    ),
    version="1.0.0",
    **_auth_kwargs,
)


class OAuthTokenRequest(BaseModel):
    grant_type: str = Field(
        ..., description="The type of grant being requested. Currently only `client_credentials` is supported."
    )
    client_id: str = Field(
        ...,
        description=(
            "The client ID of the OAuth2 Application. Get this from the "
            "[admin console](/api/admin/oauth2_provider/application/)."
        ),
    )
    client_secret: str = Field(
        ...,
        description=(
            "The client secret of the OAuth2 Application. Get this from the "
            "[admin console](/api/admin/oauth2_provider/application/)."
        ),
    )


class OAuthTokenResponse(BaseModel):
    access_token: str = Field(..., description="The access token.")
    token_type: str = Field(..., description="The type of token. Currently only `Bearer` is supported.")
    expires_in: int = Field(..., description="The number of seconds until the token expires.")
    scope: str = Field(..., description="The scope of the token. Currently unused.")


class OAuthTokenErrorResponse(BaseModel):
    error: str = Field(..., description="The error.")


@public_api.post(
    "/oauth/token",
    tags=["auth"],
    summary="Get an OAuth2 access token",
    description="Acquire an access token for an OAuth2 authorization flow.",
    response_model=OAuthTokenResponse,
    responses={
        200: {"description": "Success"},
        400: {"description": "Bad request", "model": OAuthTokenErrorResponse},
        401: {"description": "Unauthorized", "model": OAuthTokenErrorResponse},
        500: {"description": "Internal server error"},
    },
)
def get_oauth_token(request: Request, token_request: Annotated[OAuthTokenRequest, Form()]) -> Response:  # noqa: F821
    url = request.url
    method = request.method

    # There is a type stubs library for oauthlib, but its typing is more restrictive than the actual
    # types allowed by the library, so we don't use it.
    headers, body, status = client_credentials_server.create_token_response(
        str(url), method, token_request.model_dump(), request.headers
    )

    return Response(content=body, status_code=status, headers=headers)


@public_api.get("/health", status_code=204)
def health_check(user: User = Depends(user_from_authorization_header)) -> None:
    logging.info("user: %s", user)


public_api.include_router(crm_router, prefix="/crm")
public_api.include_router(calendar_router, prefix="/events")
public_api.include_router(meeting_artifacts_router, prefix="/meeting_artifacts")
public_api.include_router(note_router, prefix="/notes")
