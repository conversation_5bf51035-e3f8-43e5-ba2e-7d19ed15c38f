import os
from typing import Any

from django.conf import settings

from api.fastapi_with_adjusted_schema import EXTRA_SUPPORTS_INTERNAL_AUTH, FastAPIWithRootPathAdjustedSchema
from api.routers.attendee import router as attendee_router
from api.routers.auth import router as auth_router
from api.routers.bot import router as bot_router
from api.routers.bot_webhooks import router as bot_webhooks_router
from api.routers.calendar import router as calendar_events_router
from api.routers.client import router as client_router
from api.routers.crm import router as crm_router
from api.routers.meeting_artifacts import router as meeting_artifacts_router
from api.routers.note import router as note_router
from api.routers.oauth import router as oauth_router
from api.routers.onboarding import router as onboarding_router
from api.routers.search import router as search_router
from api.routers.settings import router as settings_router
from api.routers.task import router as task_router
from app.base_paths import INTERNAL_API_PATH

_auth_kwargs: dict[str, Any] = {EXTRA_SUPPORTS_INTERNAL_AUTH: True}

# The FastAPI ASGI application for the internal API (Zeplyn's first-party API).
internal_api = FastAPIWithRootPathAdjustedSchema(
    debug=settings.DEBUG,
    # According to the FastAPI docs, the Swagger endpoint is only generated if the openapi_url is
    # not None.
    openapi_url="/openapi.json" if os.environ.get("OPENAPI_INTERNAL_SCHEMA_ENDPOINTS_ENABLED") else None,
    root_path=INTERNAL_API_PATH,
    root_path_in_servers=False,
    title="Zeplyn Internal API",
    summary="Zeplyn first-party API",
    description="Zeplyn first-party API, use by Zeplyn's web and mobile apps.",
    version="1.0.0",
    **_auth_kwargs,
)

internal_api.include_router(attendee_router, prefix="/attendee")
internal_api.include_router(auth_router, prefix="/auth")
internal_api.include_router(bot_router, prefix="/bot")
internal_api.include_router(bot_webhooks_router, prefix="/bot_webhooks")
internal_api.include_router(calendar_events_router, prefix="/calendar")
internal_api.include_router(client_router, prefix="/client")
internal_api.include_router(crm_router, prefix="/crm")
internal_api.include_router(meeting_artifacts_router, prefix="/meeting_artifact")
internal_api.include_router(note_router, prefix="/note")
internal_api.include_router(oauth_router, prefix="/oauth")
internal_api.include_router(onboarding_router, prefix="/onboarding")
internal_api.include_router(search_router, prefix="/search")
internal_api.include_router(settings_router, prefix="/settings")
internal_api.include_router(task_router, prefix="/task")
