from typing import Annotated

import rest_framework_simplejwt
from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request, Security, status
from fastapi.security import API<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials, HTTPBearer
from rest_framework_simplejwt.authentication import JWTAuthentication

from api.fastapi_with_adjusted_schema import EXTRA_SUPPORTS_INTERNAL_AUTH, EXTRA_SUPPORTS_OAUTH_AUTH
from api.oauth_server import client_credentials_server
from deepinsights.users.models.user import User

# HTTP Bearer token security scheme for FastAPI.
_bearer_auth = HTTPBearer()

# Query string access token security scheme for FastAPI.
_access_key_auth = APIKeyQuery(name="access_token")


# A FastAPI dependency that returns the Django User associated with the request, which must contain
# an access token in the query string.
#
# Throws if the credentials do not represent a valid user.
def user_from_access_token(request: Request, access_token: Annotated[str, Security(_access_key_auth)]) -> User:
    return _user_for_token(request=request, token=access_token)


# A FastAPI dependency that returns the Django User associated with the request, which must have a
# correct authorization header.
#
# Throws if the credentials do not represent a valid user.
def user_from_authorization_header(
    request: Request,
    credentials: Annotated[HTTPAuthorizationCredentials, Security(_bearer_auth)],
) -> User:
    return _user_for_token(request=request, scheme=credentials.scheme, token=credentials.credentials)


# Returns the user for a given token scheme and value.
#
# This makes a determination about which authentication scheme(s) to support based on the FastAPI
# app that is associated with the request.
def _user_for_token(*, request: Request, scheme: str = "Bearer", token: str) -> User:
    user = None
    if request.app.extra.get(EXTRA_SUPPORTS_INTERNAL_AUTH):
        user = _user_for_internal_jwt_token(scheme=scheme, token=token)
    if request.app.extra.get(EXTRA_SUPPORTS_OAUTH_AUTH):
        user = _user_for_oauth_token(request=request)
    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
    return user


# Returns the user for a given internal auth token (first-party auth).
def _user_for_internal_jwt_token(scheme: str, token: str) -> User:
    # Re-use the logic from Django Rest Framework Simple JWT, so that the credential handling is
    # ~the same as Django.
    authenticator = JWTAuthentication()
    token_header = bytes(f"{scheme} {token}", "iso-8859-1")
    try:
        raw_token = authenticator.get_raw_token(token_header)
        if not raw_token:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
        validated_token = authenticator.get_validated_token(raw_token)
        user = authenticator.get_user(validated_token)
        if not (isinstance(user, User)):
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return User.objects.select_related("organization").get(pk=user.pk)
    except (
        rest_framework_simplejwt.exceptions.InvalidToken,
        rest_framework_simplejwt.exceptions.AuthenticationFailed,
    ):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)


# Returns the user for a given OAuth token (third-party auth).
def _user_for_oauth_token(request: Request) -> User:
    try:
        # There is a type stubs library for oauthlib, but its typing is more restrictive than the
        # actual types allowed by the library, so we don't use it.
        valid, r = client_credentials_server.verify_request(str(request.url), request.method, None, request.headers)
        if not valid:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
        if not (user := r.client.user):
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
        return user  # type: ignore[no-any-return]
    except Exception:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
