import asyncio
import datetime
from typing import Generator
from unittest.mock import ANY, Async<PERSON>ock, MagicMock, patch
from urllib.parse import quote_plus
from uuid import uuid4

import pydantic
import pytest
from django.core.cache import cache
from django.utils import timezone
from fastapi import status
from fastapi.testclient import TestClient
from httpx import ASGITransport, AsyncClient
from pytest_django.fixtures import SettingsWrapper

from api.dependencies import user_from_authorization_header
from api.internal_api import internal_api as app
from api.routers.calendar import CalendarEvent as APICalendarEvent
from api.routers.calendar import ScheduledEvent, _cache_counter
from api.routers.calendar_models import CalendarLookahead
from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent, EventParticipant
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent as DBScheduledEvent
from deepinsights.users.models.flags import Flag
from deepinsights.users.models.user import User

client = TestClient(app)

pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture(autouse=True)
def setup_teardown(settings: SettingsWrapper) -> Generator[None, None, None]:
    app.dependency_overrides = {}
    settings.CALENDAR_EVENT_CACHE_TTL_SECONDS = 0
    yield
    app.dependency_overrides = {}


@pytest.fixture
def test_user(django_user_model: User) -> User:
    user = django_user_model.objects.create(username="<EMAIL>")
    app.dependency_overrides[user_from_authorization_header] = lambda: user
    return user


def test_unauthenticated() -> None:
    response = client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")
    assert response.status_code == status.HTTP_403_FORBIDDEN


@patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
def test_fetch_exception(mock_fetch_calendar_events: MagicMock, test_user: User) -> None:
    mock_fetch_calendar_events.side_effect = Exception("Test error")

    assert (
        client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC").status_code
        == status.HTTP_500_INTERNAL_SERVER_ERROR
    )


@patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
def test_fetch_partial_error(mock_fetch_calendar_events: MagicMock, test_user: User) -> None:
    mock_fetch_calendar_events.return_value = ([], False)

    assert client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC").status_code == status.HTTP_200_OK


@patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
def test_no_calendar_events(mock_fetch_calendar_events: MagicMock, test_user: User) -> None:
    mock_fetch_calendar_events.return_value = ([], True)

    response = client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")

    assert response.status_code == status.HTTP_200_OK
    assert len(response.json()) == 0


@patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
def test_calendar_events(mock_fetch_calendar_events: MagicMock, test_user: User) -> None:
    date = timezone.now()
    events = [
        CalendarEvent(
            provider="google",
            id="345",
            user_specific_id="u345",
            title="Test meeting",
            body="Event body",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
        ),
        CalendarEvent(
            provider="google",
            id="456",
            user_specific_id="u456",
            title="Test meeting 2",
            body="Event body",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[EventParticipant(id="2", email_address="<EMAIL>", name="Test Example 2")],
            meeting_urls=[pydantic.HttpUrl("https://example.com/2")],
        ),
        CalendarEvent(
            provider="google",
            id="567",
            user_specific_id="u567",
            title="Test meeting 3",
            body="Event body",
            start_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc),
            end_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc)
            + datetime.timedelta(days=1),
            all_day=True,
            participants=[],
            meeting_urls=[],
        ),
        CalendarEvent(
            provider="microsoft",
            id="123",
            user_specific_id="u123",
            title="Test meeting",
            body="Event body",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
        ),
        CalendarEvent(
            provider="microsoft",
            id="234",
            user_specific_id="u234",
            title="Test meeting 2",
            body="Event body",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[EventParticipant(id="2", email_address="<EMAIL>", name="Test Example 2")],
            meeting_urls=[pydantic.HttpUrl("https://example.com/2")],
        ),
        CalendarEvent(
            provider="microsoft",
            id="345",
            user_specific_id="u345",
            title="Test meeting 3",
            body="Event body",
            start_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc),
            end_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc)
            + datetime.timedelta(days=1),
            all_day=True,
            participants=[],
            meeting_urls=[],
        ),
        CalendarEvent(
            provider="redtail",
            id="345",
            user_specific_id="u345",
            title="Test meeting",
            body="Event body",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
        ),
        CalendarEvent(
            provider="redtail",
            id="456",
            user_specific_id="u456",
            title="Test meeting 2",
            body="Event body",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[EventParticipant(id="2", email_address="<EMAIL>", name="Test Example 2")],
            meeting_urls=[pydantic.HttpUrl("https://example.com/2")],
        ),
        CalendarEvent(
            provider="redtail",
            id="567",
            user_specific_id="u567",
            title="Test meeting 3",
            body="Event body",
            start_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc),
            end_time=date.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=datetime.timezone.utc)
            + datetime.timedelta(days=1),
            all_day=True,
            participants=[],
            meeting_urls=[],
        ),
    ]
    mock_fetch_calendar_events.return_value = (events, True)

    response = client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")

    assert response.status_code == status.HTTP_200_OK
    assert [APICalendarEvent.model_validate(event.model_dump()) for event in events] == [
        APICalendarEvent.model_validate(event) for event in response.json()
    ]


@patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
def test_attendee_mapping(mock_fetch_calendar_events: MagicMock, test_user: User) -> None:
    org = Organization.objects.create(name="Test organization")
    test_user.organization = org
    test_user.crm_configuration["crm_system"] = "wealthbox"
    test_user.save()

    # Use a newly-created user, without any foreign keys preloaded, to better match the behavior of
    # the actual code.
    app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.get(pk=test_user.pk)

    client_one = Client.objects.create(
        name="Test client", email="<EMAIL>", organization=org, crm_system="wealthbox"
    )
    client_one.authorized_users.add(test_user)
    client_one.save()
    client_two = Client.objects.create(
        name="Test client two",
        email="<EMAIL>",
        organization=org,
        crm_system="wealthbox",
    )
    client_two.authorized_users.add(test_user)
    client_two.save()
    user_one = User.objects.create(
        username="<EMAIL>", email="<EMAIL>", name="Test user", organization=org
    )
    user_two = User.objects.create(
        username="<EMAIL>", email="<EMAIL>", name="Test user two", organization=org
    )

    date = timezone.now()
    events = [
        CalendarEvent(
            provider="google",
            id="123",
            user_specific_id="u123",
            title="Test meeting",
            body="Event body",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[
                EventParticipant(id="1", email_address="<EMAIL>", name="One"),
                EventParticipant(id="2", email_address="<EMAIL>", name="Two"),
                EventParticipant(id="3", email_address="<EMAIL>", name="Other"),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
        ),
        CalendarEvent(
            provider="google",
            id="234",
            user_specific_id="u234",
            title="Test meeting 2",
            body="Event body",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[
                EventParticipant(id="1", email_address="<EMAIL>", name="One"),
                EventParticipant(id="2", email_address="<EMAIL>", name="Two"),
                EventParticipant(id="3", email_address="<EMAIL>", name="Other Two"),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example.com/2")],
        ),
    ]
    mock_fetch_calendar_events.return_value = (events, True)

    expected_events = [
        CalendarEvent(
            provider="google",
            id="123",
            user_specific_id="u123",
            title="Test meeting",
            body="Event body",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[
                EventParticipant(
                    id="1",
                    zeplyn_uuid=client_one.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                    email_address="<EMAIL>",
                    name="Test client",
                ),
                EventParticipant(
                    id="2",
                    zeplyn_uuid=user_one.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.USER,
                    email_address="<EMAIL>",
                    name="Test user",
                ),
                EventParticipant(id="3", email_address="<EMAIL>", name="Other"),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
        ),
        CalendarEvent(
            provider="google",
            id="234",
            user_specific_id="u234",
            title="Test meeting 2",
            body="Event body",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[
                EventParticipant(
                    id="1",
                    zeplyn_uuid=client_two.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                    email_address="<EMAIL>",
                    name="Test client two",
                ),
                EventParticipant(
                    id="2",
                    zeplyn_uuid=user_two.uuid,
                    zeplyn_kind=EventParticipant.ZeplynKind.USER,
                    email_address="<EMAIL>",
                    name="Test user two",
                ),
                EventParticipant(id="3", email_address="<EMAIL>", name="Other Two"),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example.com/2")],
        ),
    ]

    response = client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")

    assert response.status_code == status.HTTP_200_OK
    assert [APICalendarEvent.model_validate(event.model_dump()) for event in expected_events] == [
        APICalendarEvent.model_validate(event) for event in response.json()
    ]


@patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
def test_attendee_mapping_permissions(
    mock_fetch_calendar_events: MagicMock, test_user: User, django_user_model: User
) -> None:
    org = Organization.objects.create(name="Test organization")
    org_two = Organization.objects.create(name="Test organization two")
    test_user.organization = org
    test_user.crm_configuration["crm_system"] = "wealthbox"
    test_user.save()
    user_two = django_user_model.objects.create(username="<EMAIL>", organization=org_two)
    user_two.crm_configuration["crm_system"] = "wealthbox"
    user_two.save()
    # Use a newly-created user, without any foreign keys preloaded, to better match the behavior of
    # the actual code.
    app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.get(pk=test_user.pk)

    user_one_client = Client.objects.create(
        name="User one client", email="<EMAIL>", organization=org, crm_system="wealthbox"
    )
    user_one_client.authorized_users.add(test_user)
    user_one_client.save()
    user_two_client = Client.objects.create(
        name="User two client", email="<EMAIL>", organization=org_two, crm_system="wealthbox"
    )
    user_two_client.authorized_users.add(user_two)
    user_two_client.save()
    user_two_client_two = Client.objects.create(
        name="User two client two",
        email="<EMAIL>",
        organization=org_two,
        crm_system="wealthbox",
    )
    user_two_client_two.authorized_users.add(user_two)
    user_two_client_two.save()
    user_one_user = User.objects.create(
        username="<EMAIL>", email="<EMAIL>", name="User one user", organization=org
    )
    user_two_user = User.objects.create(
        username="@example.com",
        email="<EMAIL>",
        name="User two user",
        organization=org_two,
    )

    date = timezone.now()
    events = [
        CalendarEvent(
            provider="microsoft",
            id="123",
            user_specific_id="u123",
            title="Test meeting",
            body="Event body",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[
                EventParticipant(id="1", email_address="<EMAIL>", name="One"),
                EventParticipant(id="2", email_address="<EMAIL>", name="Two"),
                EventParticipant(id="3", email_address="<EMAIL>", name="Three"),
                EventParticipant(id="4", email_address="<EMAIL>", name="Four"),
            ],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
        ),
    ]
    mock_fetch_calendar_events.return_value = (events, True)

    expected_participants = [
        EventParticipant(
            id="1",
            zeplyn_uuid=user_one_client.uuid,
            zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
            email_address="<EMAIL>",
            name="User one client",
        ),
        EventParticipant(
            id="2",
            zeplyn_uuid=user_one_user.uuid,
            zeplyn_kind=EventParticipant.ZeplynKind.USER,
            email_address="<EMAIL>",
            name="User one user",
        ),
        EventParticipant(id="3", email_address="<EMAIL>", name="Three"),
        EventParticipant(id="4", email_address="<EMAIL>", name="Four"),
    ]

    response = client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")

    assert response.status_code == status.HTTP_200_OK
    assert expected_participants == [
        EventParticipant.model_validate(participant) for participant in response.json()[0].get("participants")
    ]

    # Repeat with user_two

    app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.get(pk=user_two.pk)

    expected_participants = [
        EventParticipant(
            id="1",
            zeplyn_uuid=user_two_client.uuid,
            zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
            email_address="<EMAIL>",
            name="User two client",
        ),
        EventParticipant(id="2", email_address="<EMAIL>", name="Two"),
        EventParticipant(
            id="3",
            zeplyn_uuid=user_two_client_two.uuid,
            zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
            email_address="<EMAIL>",
            name="User two client two",
        ),
        EventParticipant(
            id="4",
            zeplyn_uuid=user_two_user.uuid,
            zeplyn_kind=EventParticipant.ZeplynKind.USER,
            email_address="<EMAIL>",
            name="User two user",
        ),
    ]
    response = client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")

    assert response.status_code == status.HTTP_200_OK
    assert expected_participants == [
        EventParticipant.model_validate(participant) for participant in response.json()[0].get("participants")
    ]


def test_calendar_lookahead_enum() -> None:
    assert CalendarLookahead("invalid_value") == CalendarLookahead.ONE_DAY
    assert CalendarLookahead("one_hour") == CalendarLookahead.ONE_HOUR
    assert CalendarLookahead("end_of_day") == CalendarLookahead.END_OF_DAY


@patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
def test_invalid_lookahead(mock_fetch_calendar_events: MagicMock, test_user: User) -> None:
    test_user.calendar_lookahead = "invalid_value"
    test_user.save()

    mock_fetch_calendar_events.return_value = ([], True)

    client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")

    mock_fetch_calendar_events.assert_called_once_with(test_user, datetime.timedelta(days=1))


@patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
def test_default_lookahead(mock_fetch_calendar_events: MagicMock, test_user: User) -> None:
    mock_fetch_calendar_events.return_value = ([], True)

    client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")

    mock_fetch_calendar_events.assert_called_once_with(ANY, datetime.timedelta(days=1))


@pytest.mark.parametrize(
    ("base_date", "tz", "lookahead", "expected_interval"),
    [
        # Simple lookahead intervals
        (
            datetime.datetime(2022, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            "Etc/UTC",
            CalendarLookahead.ONE_HOUR,
            datetime.timedelta(hours=1),
        ),
        (
            datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            "Pacific/Guam",
            CalendarLookahead.ONE_HOUR,
            datetime.timedelta(hours=1),
        ),
        (
            datetime.datetime(2021, 2, 2, 10, 10, 10, tzinfo=datetime.timezone.utc),
            "Etc/UTC",
            CalendarLookahead.ONE_DAY,
            datetime.timedelta(days=1),
        ),
        (
            datetime.datetime(2024, 2, 2, 10, 10, 10, tzinfo=datetime.timezone.utc),
            # This is UTC-10: the sign is intentionally inverted (see https://en.wikipedia.org/wiki/Tz_database#Area)
            quote_plus("Etc/GMT+10"),
            CalendarLookahead.ONE_DAY,
            datetime.timedelta(days=1),
        ),
        (
            datetime.datetime(2023, 3, 4, 1, 1, 1, tzinfo=datetime.timezone.utc),
            "Etc/UTC",
            CalendarLookahead.TWO_DAYS,
            datetime.timedelta(days=2),
        ),
        (
            datetime.datetime(2023, 3, 4, 1, 1, 1, tzinfo=datetime.timezone.utc),
            "Asia/Kathmandu",
            CalendarLookahead.TWO_DAYS,
            datetime.timedelta(days=2),
        ),
        (
            datetime.datetime(2023, 3, 4, 1, 1, 1, tzinfo=datetime.timezone.utc),
            "America/Los_Angeles",
            CalendarLookahead.TWO_DAYS,
            datetime.timedelta(days=2),
        ),
        # End of day from UTC
        (
            datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            "Etc/UTC",
            CalendarLookahead.END_OF_DAY,
            datetime.timedelta(days=1),
        ),
        (
            datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            "Etc/UTC",
            CalendarLookahead.END_OF_DAY,
            datetime.timedelta(hours=23),
        ),
        (
            datetime.datetime(2024, 1, 1, 12, 30, 30, tzinfo=datetime.timezone.utc),
            "Etc/UTC",
            CalendarLookahead.END_OF_DAY,
            datetime.timedelta(hours=11, minutes=29, seconds=30),
        ),
        # End of day from UTC+5
        (
            datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            "Antarctica/Mawson",
            CalendarLookahead.END_OF_DAY,
            datetime.timedelta(hours=19),
        ),
        (
            datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            "Antarctica/Mawson",
            CalendarLookahead.END_OF_DAY,
            datetime.timedelta(hours=18),
        ),
        (
            datetime.datetime(2024, 1, 1, 12, 30, 30, tzinfo=datetime.timezone.utc),
            "Antarctica/Mawson",
            CalendarLookahead.END_OF_DAY,
            datetime.timedelta(hours=6, minutes=29, seconds=30),
        ),
        (
            datetime.datetime(2024, 1, 1, 20, 0, 0, tzinfo=datetime.timezone.utc),
            "Antarctica/Mawson",
            CalendarLookahead.END_OF_DAY,
            datetime.timedelta(hours=23),
        ),
        # End of day from UTC-8
        # The sign is intentionally inverted: see https://en.wikipedia.org/wiki/Tz_database#Area
        (
            datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            quote_plus("Etc/GMT+8"),
            CalendarLookahead.END_OF_DAY,
            datetime.timedelta(hours=8),
        ),
        (
            datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            quote_plus("Etc/GMT+8"),
            CalendarLookahead.END_OF_DAY,
            datetime.timedelta(hours=7),
        ),
        (
            datetime.datetime(2024, 1, 1, 12, 30, 30, tzinfo=datetime.timezone.utc),
            quote_plus("Etc/GMT+8"),
            CalendarLookahead.END_OF_DAY,
            datetime.timedelta(hours=19, minutes=29, seconds=30),
        ),
        (
            datetime.datetime(2024, 1, 1, 20, 0, 0, tzinfo=datetime.timezone.utc),
            quote_plus("Etc/GMT+8"),
            CalendarLookahead.END_OF_DAY,
            datetime.timedelta(hours=12),
        ),
        # End of week from UTC
        # Jan 1, 2024 is a Monday; the week starts on Sunday
        (
            datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            "Africa/Sao_Tome",
            CalendarLookahead.END_OF_WEEK,
            datetime.timedelta(days=6),
        ),
        (
            datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            "Africa/Sao_Tome",
            CalendarLookahead.END_OF_WEEK,
            datetime.timedelta(days=5, hours=23),
        ),
        (
            datetime.datetime(2024, 1, 6, 23, 30, 30, tzinfo=datetime.timezone.utc),
            "Africa/Sao_Tome",
            CalendarLookahead.END_OF_WEEK,
            datetime.timedelta(minutes=29, seconds=30),
        ),
        # End of week from UTC+5
        (
            datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            "Indian/Maldives",
            CalendarLookahead.END_OF_WEEK,
            datetime.timedelta(days=5, hours=19),
        ),
        (
            datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            "Indian/Maldives",
            CalendarLookahead.END_OF_WEEK,
            datetime.timedelta(days=5, hours=18),
        ),
        (
            datetime.datetime(2024, 1, 1, 12, 30, 30, tzinfo=datetime.timezone.utc),
            "Indian/Maldives",
            CalendarLookahead.END_OF_WEEK,
            datetime.timedelta(days=5, hours=6, minutes=29, seconds=30),
        ),
        # Saturday night UTC, Sunday morning UTC+5
        (
            datetime.datetime(2023, 12, 30, 20, 0, 0, tzinfo=datetime.timezone.utc),
            "Indian/Maldives",
            CalendarLookahead.END_OF_WEEK,
            datetime.timedelta(days=6, hours=23),
        ),
        # End of week from UTC-8
        (
            datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            "Pacific/Pitcairn",
            CalendarLookahead.END_OF_WEEK,
            datetime.timedelta(days=6, hours=8),
        ),
        (
            datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            "Pacific/Pitcairn",
            CalendarLookahead.END_OF_WEEK,
            datetime.timedelta(days=6, hours=7),
        ),
        (
            datetime.datetime(2024, 1, 1, 12, 30, 30, tzinfo=datetime.timezone.utc),
            "Pacific/Pitcairn",
            CalendarLookahead.END_OF_WEEK,
            datetime.timedelta(days=5, hours=19, minutes=29, seconds=30),
        ),
        # Sunday morning UTC, Saturday night UTC-8
        (
            datetime.datetime(2023, 12, 31, 4, 0, 0, tzinfo=datetime.timezone.utc),
            "Pacific/Pitcairn",
            CalendarLookahead.END_OF_WEEK,
            datetime.timedelta(hours=4),
        ),
        # End of next week from UTC
        # Jan 1, 2024 is a Monday; the week starts on Sunday
        (
            datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            "Africa/Sao_Tome",
            CalendarLookahead.END_OF_NEXT_WEEK,
            datetime.timedelta(days=13),
        ),
        (
            datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            "Africa/Sao_Tome",
            CalendarLookahead.END_OF_NEXT_WEEK,
            datetime.timedelta(days=12, hours=23),
        ),
        (
            datetime.datetime(2024, 1, 6, 23, 30, 30, tzinfo=datetime.timezone.utc),
            "Africa/Sao_Tome",
            CalendarLookahead.END_OF_NEXT_WEEK,
            datetime.timedelta(days=7, minutes=29, seconds=30),
        ),
        # End of next week from UTC+5
        (
            datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            "Indian/Maldives",
            CalendarLookahead.END_OF_NEXT_WEEK,
            datetime.timedelta(days=12, hours=19),
        ),
        (
            datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            "Indian/Maldives",
            CalendarLookahead.END_OF_NEXT_WEEK,
            datetime.timedelta(days=12, hours=18),
        ),
        (
            datetime.datetime(2024, 1, 1, 12, 30, 30, tzinfo=datetime.timezone.utc),
            "Indian/Maldives",
            CalendarLookahead.END_OF_NEXT_WEEK,
            datetime.timedelta(days=12, hours=6, minutes=29, seconds=30),
        ),
        # Saturday night UTC, Sunday morning UTC+5
        (
            datetime.datetime(2023, 12, 30, 20, 0, 0, tzinfo=datetime.timezone.utc),
            "Indian/Maldives",
            CalendarLookahead.END_OF_NEXT_WEEK,
            datetime.timedelta(days=13, hours=23),
        ),
        # End of next week from UTC-8
        (
            datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            "Pacific/Pitcairn",
            CalendarLookahead.END_OF_NEXT_WEEK,
            datetime.timedelta(days=13, hours=8),
        ),
        (
            datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
            "Pacific/Pitcairn",
            CalendarLookahead.END_OF_NEXT_WEEK,
            datetime.timedelta(days=13, hours=7),
        ),
        (
            datetime.datetime(2024, 1, 1, 12, 30, 30, tzinfo=datetime.timezone.utc),
            "Pacific/Pitcairn",
            CalendarLookahead.END_OF_NEXT_WEEK,
            datetime.timedelta(days=12, hours=19, minutes=29, seconds=30),
        ),
        # Sunday morning UTC, Saturday night UTC-8
        (
            datetime.datetime(2023, 12, 31, 4, 0, 0, tzinfo=datetime.timezone.utc),
            "Pacific/Pitcairn",
            CalendarLookahead.END_OF_NEXT_WEEK,
            datetime.timedelta(days=7, hours=4),
        ),
    ],
)
@patch("api.routers.calendar._now")
@patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
def test_lookahead(
    mock_fetch_calendar_events: MagicMock,
    mock_now: MagicMock,
    django_user_model: User,
    base_date: datetime.datetime,
    tz: str,
    lookahead: CalendarLookahead,
    expected_interval: datetime.timedelta,
    test_user: User,
) -> None:
    test_user.calendar_lookahead = lookahead
    test_user.save()

    mock_now.return_value = base_date

    mock_fetch_calendar_events.return_value = ([], True)

    client.get(f"/api/v2/calendar/list_events?time_zone={tz}")

    mock_fetch_calendar_events.assert_called_once_with(ANY, expected_interval)


@patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
def test_list_events_filters_no_meeting_urls(mock_fetch_calendar_events: MagicMock, test_user: User) -> None:
    f = Flag.objects.get(name=Flags.EnableShowMeetingsWithoutURLsSetting.name)
    f.everyone = True
    f.override_enabled_by_environment = None  # To ensure no environment override
    f.save()

    test_user.show_events_without_meeting_urls = False
    test_user.save()

    date = timezone.now()
    events = [
        CalendarEvent(
            provider="microsoft",
            id="123",
            user_specific_id="u123",
            title="Test meeting",
            body="Event body",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[],
            meeting_urls=[pydantic.HttpUrl("https://example.com")],
        ),
        CalendarEvent(
            provider="microsoft",
            id="234",
            user_specific_id="u234",
            title="Test meeting 2",
            body="Event body",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[],
            meeting_urls=[],
        ),
        CalendarEvent(
            provider="microsoft",
            id="345",
            user_specific_id="u345",
            title="Test meeting 3",
            body="Event body",
            start_time=date + datetime.timedelta(hours=2),
            end_time=date + datetime.timedelta(hours=3),
            all_day=False,
            participants=[],
            meeting_urls=[pydantic.HttpUrl("https://example.com/3")],
        ),
    ]
    mock_fetch_calendar_events.return_value = (events, True)

    response = client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")

    assert response.status_code == status.HTTP_200_OK
    assert [APICalendarEvent.model_validate(event.model_dump()) for event in [events[0], events[2]]] == [
        APICalendarEvent.model_validate(event) for event in response.json()
    ]


@patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
def test_list_events_filters_no_meeting_urls_flag_disabled(
    mock_fetch_calendar_events: MagicMock, test_user: User
) -> None:
    f = Flag.objects.get(name=Flags.EnableShowMeetingsWithoutURLsSetting.name)
    f.everyone = False
    f.override_enabled_by_environment = None  # To ensure no environment override
    f.save()
    test_user.show_events_without_meeting_urls = False
    test_user.save()

    date = timezone.now()
    events = [
        CalendarEvent(
            provider="google",
            id="123",
            user_specific_id="u123",
            title="Test meeting",
            body="Event body",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[],
            meeting_urls=[],
        ),
    ]
    mock_fetch_calendar_events.return_value = (events, True)

    response = client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")

    assert response.status_code == status.HTTP_200_OK
    assert [APICalendarEvent.model_validate(event.model_dump()) for event in events] == [
        APICalendarEvent.model_validate(event) for event in response.json()
    ]


@patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
def test_crm_entity_parsing(mock_fetch_calendar_events: MagicMock, test_user: User) -> None:
    date = timezone.now()
    events = [
        CalendarEvent(
            provider="google",
            id="123",
            user_specific_id="u123",
            title="Test meeting",
            body="Test[ZDATA]<salesforce_case_id>TEST</salesforce_case_id>[/ZDATA]",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[],
            meeting_urls=[],
        ),
        CalendarEvent(
            provider="microsoft",
            id="234",
            user_specific_id="u234",
            title="Test meeting two",
            body="[ZDATA]<salesforce_case_id>TEST_TWO</salesforce_case_id>[/ZDATA]Moretextafter",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[],
            meeting_urls=[],
        ),
        CalendarEvent(
            provider="microsoft",
            id="345",
            user_specific_id="u345",
            title="Test meeting three",
            body="<p>&lsqb;ZDATA&rsqb;&lt;salesforce&lowbar;case&lowbar;id&gt;TEST&lt;&sol;salesforce&lowbar;case&lowbar;id&gt;&lsqb;&sol;ZDATA&rsqb;</p>",
            start_time=date + datetime.timedelta(hours=1),
            end_time=date + datetime.timedelta(hours=2),
            all_day=False,
            participants=[],
            meeting_urls=[],
        ),
    ]
    mock_fetch_calendar_events.return_value = (events, True)

    org = Organization.objects.create(name="Test organization")
    test_user.organization = org
    test_user.save()
    user_client = Client.objects.create(
        organization=org,
        crm_id="TEST_TWO",
        name="Test client",
    )
    user_client.authorized_users.add(test_user)
    user_client.save()

    # Create another client with a matching CRM ID but for whom the user is not authorized.
    Client.objects.create(
        organization=org,
        crm_id="TEST",
        name="Other test client",
    )

    response = client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")

    assert response.status_code == status.HTTP_200_OK
    assert [
        APICalendarEvent.model_validate(
            {**events[0].model_dump(), "linked_crm_entity": {"name": "Test meeting", "id": "TEST"}}
        ),
        APICalendarEvent.model_validate(
            {**events[1].model_dump(), "linked_crm_entity": {"name": "Test client", "id": "TEST_TWO"}}
        ),
        APICalendarEvent.model_validate(
            {**events[2].model_dump(), "linked_crm_entity": {"name": "Test meeting three", "id": "TEST"}}
        ),
    ] == [APICalendarEvent.model_validate(event) for event in response.json()]


@pytest.mark.asyncio
class TestCalendarWithCaching:
    @pytest.fixture(autouse=True)
    def setup(self, settings: SettingsWrapper, django_user_model: User) -> Generator[None, None, None]:
        cache.clear()
        settings.CALENDAR_EVENT_CACHE_TTL_SECONDS = 0.1
        self.user = django_user_model.objects.create(
            username="<EMAIL>", email="<EMAIL>", name="Test user"
        )
        self.org = Organization.objects.create(name="Test organization")
        self.user.organization = self.org
        self.user.save()
        app.dependency_overrides[user_from_authorization_header] = lambda: self.user
        yield
        app.dependency_overrides = {}

    @patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
    async def test_no_caching(self, mock_fetch_calendar_events: MagicMock, settings: SettingsWrapper) -> None:
        settings.CALENDAR_EVENT_CACHE_TTL_SECONDS = 0
        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as ac:
            events = [
                CalendarEvent(
                    provider="google",
                    id="123",
                    user_specific_id="u123",
                    title="Test meeting",
                    body="Event body",
                    start_time=timezone.now(),
                    end_time=timezone.now() + datetime.timedelta(hours=1),
                    all_day=False,
                    participants=[],
                    meeting_urls=[],
                )
            ]
            mock_fetch_calendar_events.side_effect = [(events, True), ([], True)]

            response_one_coro = ac.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")
            response_two_coro = ac.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")
            response_one = await response_one_coro
            response_two = await response_two_coro
            assert response_one.status_code == 200
            assert [APICalendarEvent.model_validate(event.model_dump()) for event in events] == [
                APICalendarEvent.model_validate(event) for event in response_one.json()
            ]
            assert response_two.status_code == 200
            assert not response_two.json()

    @patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
    async def test_cache_hit_then_miss(self, mock_fetch_calendar_events: MagicMock) -> None:
        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as ac:
            events = [
                CalendarEvent(
                    provider="google",
                    id="123",
                    user_specific_id="u123",
                    title="Test meeting",
                    body="Event body",
                    start_time=timezone.now(),
                    end_time=timezone.now() + datetime.timedelta(hours=1),
                    all_day=False,
                    participants=[],
                    meeting_urls=[],
                )
            ]

            mock_fetch_calendar_events.return_value = (events, True)
            response = await ac.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")
            assert response.status_code == 200
            assert [APICalendarEvent.model_validate(event.model_dump()) for event in events] == [
                APICalendarEvent.model_validate(event) for event in response.json()
            ]

            mock_fetch_calendar_events.return_value = ([], True)
            response = await ac.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")
            assert response.status_code == 200
            assert [APICalendarEvent.model_validate(event.model_dump()) for event in events] == [
                APICalendarEvent.model_validate(event) for event in response.json()
            ]

            await asyncio.sleep(0.3)

            response = await ac.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")
            assert response.status_code == 200
            assert not response.json()

    @patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
    async def test_cache_not_updated_on_fetch_failure(self, mock_fetch_calendar_events: MagicMock) -> None:
        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as ac:
            events = [
                CalendarEvent(
                    provider="google",
                    id="123",
                    user_specific_id="u123",
                    title="Test meeting",
                    body="Event body",
                    start_time=timezone.now(),
                    end_time=timezone.now() + datetime.timedelta(hours=1),
                    all_day=False,
                    participants=[],
                    meeting_urls=[],
                )
            ]
            mock_fetch_calendar_events.side_effect = [Exception("Error"), (events, True)]

            response = await ac.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")

            assert response.status_code == 500

            response = await ac.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")

            assert response.status_code == 200
            assert [APICalendarEvent.model_validate(event.model_dump()) for event in events] == [
                APICalendarEvent.model_validate(event) for event in response.json()
            ]

    @patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
    async def test_cache_not_updated_on_partial_fetch_failure(self, mock_fetch_calendar_events: MagicMock) -> None:
        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as ac:
            events = [
                CalendarEvent(
                    provider="google",
                    id="123",
                    user_specific_id="u123",
                    title="Test meeting",
                    body="Event body",
                    start_time=timezone.now(),
                    end_time=timezone.now() + datetime.timedelta(hours=1),
                    all_day=False,
                    participants=[],
                    meeting_urls=[],
                )
            ]
            updated_events = [
                CalendarEvent(
                    provider="microsoft",
                    id="234",
                    user_specific_id="u234",
                    title="Test meeting 2",
                    body="Event body",
                    start_time=timezone.now(),
                    end_time=timezone.now() + datetime.timedelta(hours=1),
                    all_day=False,
                    participants=[],
                    meeting_urls=[],
                )
            ]
            mock_fetch_calendar_events.side_effect = [(events, False), (updated_events, True)]

            response = await ac.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")

            assert response.status_code == 200
            assert [APICalendarEvent.model_validate(event.model_dump()) for event in events] == [
                APICalendarEvent.model_validate(event) for event in response.json()
            ]

            response = await ac.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")

            assert response.status_code == 200
            assert [APICalendarEvent.model_validate(event.model_dump()) for event in updated_events] == [
                APICalendarEvent.model_validate(event) for event in response.json()
            ]

    @patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
    async def test_cache_hit_attendee_matching(
        self, mock_fetch_calendar_events: MagicMock, settings: SettingsWrapper
    ) -> None:
        settings.CALENDAR_EVENT_CACHE_TTL_SECONDS = 10
        self.user.crm_configuration["crm_system"] = "wealthbox"
        await self.user.asave()
        client_one = await Client.objects.acreate(
            name="Test client", email="<EMAIL>", organization=self.org, crm_system="wealthbox"
        )
        await client_one.authorized_users.aadd(self.user)
        await client_one.asave()

        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as ac:
            now = timezone.now()
            events = [
                CalendarEvent(
                    provider="google",
                    id="123",
                    user_specific_id="u234",
                    title="Test meeting",
                    body="Event body",
                    start_time=now,
                    end_time=now + datetime.timedelta(hours=1),
                    all_day=False,
                    participants=[
                        EventParticipant(id="1", email_address="<EMAIL>", name="Client One"),
                        EventParticipant(id="2", email_address=self.user.email, name="User One"),
                        EventParticipant(id="3", email_address="<EMAIL>", name="Other"),
                    ],
                    meeting_urls=[pydantic.HttpUrl("https://example.com")],
                )
            ]

            expected_events = [
                CalendarEvent(
                    provider="google",
                    id="123",
                    user_specific_id="u234",
                    title="Test meeting",
                    body="Event body",
                    start_time=now,
                    end_time=now + datetime.timedelta(hours=1),
                    all_day=False,
                    participants=[
                        EventParticipant(
                            id="1",
                            zeplyn_uuid=client_one.uuid,
                            zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                            email_address="<EMAIL>",
                            name="Test client",
                        ),
                        EventParticipant(
                            id="2",
                            zeplyn_uuid=self.user.uuid,
                            zeplyn_kind=EventParticipant.ZeplynKind.USER,
                            email_address=self.user.email,
                            name="Test user",
                        ),
                        EventParticipant(id="3", email_address="<EMAIL>", name="Other"),
                    ],
                    meeting_urls=[pydantic.HttpUrl("https://example.com")],
                )
            ]

            mock_fetch_calendar_events.return_value = (events, True)
            response = await ac.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")
            assert response.status_code == 200
            assert [APICalendarEvent.model_validate(event.model_dump()) for event in expected_events] == [
                APICalendarEvent.model_validate(event) for event in response.json()
            ]

            mock_fetch_calendar_events.return_value = ([], True)

            await asyncio.sleep(0.1)

            response = await ac.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")
            assert response.status_code == 200
            assert [APICalendarEvent.model_validate(event.model_dump()) for event in expected_events] == [
                APICalendarEvent.model_validate(event) for event in response.json()
            ]


class TestScheduledEvents:
    def _db_scheduled_event_with_calendar_event(
        self, user: User, scheduled_event: CalendarEvent
    ) -> tuple[CalendarEvent, DBScheduledEvent]:
        return scheduled_event, DBScheduledEvent.objects.create(
            user=user,
            start_time=scheduled_event.start_time,
            end_time=scheduled_event.end_time,
            shared_source_id=scheduled_event.id,
            user_specific_source_id=scheduled_event.user_specific_id,
            source_data=scheduled_event.model_dump(mode="json"),
        )

    @patch("api.routers.calendar.reconcile_calendar_events")
    def test_no_calendar_events(self, mock_reconcile_calendar_events: MagicMock, test_user: User) -> None:
        response = client.get("/api/v2/calendar/events?time_zone=Etc/UTC")
        test_user.calendar_lookahead = CalendarLookahead.ONE_DAY
        test_user.save()

        assert response.status_code == status.HTTP_200_OK
        assert len(response.json()) == 0
        mock_reconcile_calendar_events.delay_on_commit.assert_called_once_with(test_user.uuid)

    @patch("api.routers.calendar.reconcile_calendar_events")
    def test_calendar_events_without_autojoin(
        self, mock_reconcile_calendar_events: MagicMock, test_user: User, django_user_model: User
    ) -> None:
        test_user.calendar_lookahead = CalendarLookahead.ONE_DAY
        test_user.save()

        # Create the events in a different order than they should be returned, to check ordering.
        date = timezone.now() + datetime.timedelta(minutes=1)
        note = Note.objects.create()
        first_event_source_data, first_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="google",
                id="234",
                user_specific_id="u234",
                title="Test meeting",
                body="Event body",
                start_time=date + datetime.timedelta(hours=1),
                end_time=date + datetime.timedelta(hours=2),
                all_day=False,
                participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
                meeting_urls=[pydantic.HttpUrl("https://example.com")],
            ),
        )
        first_event.note = note
        first_event.save()
        second_event_source_data, second_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="microsoft",
                id="123",
                user_specific_id="u123",
                title="Test meeting",
                body="Event body",
                start_time=date,
                end_time=date + datetime.timedelta(minutes=30),
                all_day=False,
                participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
                meeting_urls=[pydantic.HttpUrl("https://example.com")],
            ),
        )
        third_event_source_data, third_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="redtail",
                id="345",
                user_specific_id="u345",
                title="Test meeting",
                body="Event body",
                start_time=date,
                end_time=date + datetime.timedelta(hours=1),
                all_day=False,
                participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
                meeting_urls=[pydantic.HttpUrl("https://example.com")],
            ),
        )

        second_user = django_user_model.objects.create()
        other_user_event = DBScheduledEvent.objects.create(
            user=second_user,
            start_time=second_event_source_data.start_time,
            end_time=second_event_source_data.end_time,
            shared_source_id=second_event_source_data.id,
            user_specific_source_id=second_event_source_data.user_specific_id,
            source_data=second_event_source_data.model_dump(mode="json"),
        )

        _, future_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="google",
                id="456",
                user_specific_id="u456",
                title="Test meeting",
                body="Event body",
                start_time=date + datetime.timedelta(days=1, minutes=1),
                end_time=date + datetime.timedelta(days=1, hours=1),
                all_day=False,
                participants=[],
                meeting_urls=[],
            ),
        )

        zeplyn_created_event = DBScheduledEvent.objects.create(
            user=test_user,
            start_time=timezone.now(),
            end_time=timezone.now() + datetime.timedelta(hours=1),
        )

        response = client.get("/api/v2/calendar/events?time_zone=Etc/UTC")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == [
            {
                **second_event_source_data.model_dump(mode="json", exclude={"body"}),
                "linked_crm_entity": None,
                "autojoin_enabled": False,
                "autojoin_available": False,
                "autojoin_editable": True,
                "scheduled_event_uuid": str(second_event.uuid),
            },
            {
                **third_event_source_data.model_dump(mode="json", exclude={"body"}),
                "linked_crm_entity": None,
                "autojoin_enabled": False,
                "autojoin_available": False,
                "autojoin_editable": True,
                "scheduled_event_uuid": str(third_event.uuid),
            },
            {
                **first_event_source_data.model_dump(mode="json", exclude={"body"}),
                "linked_crm_entity": None,
                "autojoin_enabled": False,
                "autojoin_available": False,
                "autojoin_editable": True,
                "scheduled_event_uuid": str(first_event.uuid),
            },
        ]
        mock_reconcile_calendar_events.delay_on_commit.assert_called_once_with(test_user.uuid)

    def test_event_time_handling(self, test_user: User) -> None:
        test_user.calendar_lookahead = CalendarLookahead.ONE_DAY
        test_user.save()

        f = Flag.objects.get(name=Flags.EnableShowMeetingsWithoutURLsSetting.name)
        f.everyone = True
        f.override_enabled_by_environment = None  # To ensure no environment override
        f.save()

        date = timezone.now()
        _, past_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="microsoft",
                id="123",
                user_specific_id="u123",
                title="Test meeting",
                body="Event body",
                start_time=date - datetime.timedelta(hours=1),
                end_time=date - datetime.timedelta(minutes=30),
                all_day=False,
                participants=[],
                meeting_urls=[pydantic.HttpUrl("https://example.com")],
            ),
        )

        _, ongoing_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="microsoft",
                id="234",
                user_specific_id="u234",
                title="Test meeting 2",
                body="Event body",
                start_time=date - datetime.timedelta(minutes=30),
                end_time=date + datetime.timedelta(minutes=1),
                all_day=False,
                participants=[],
                meeting_urls=[],
            ),
        )
        _, future_included_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="microsoft",
                id="345",
                user_specific_id="u345",
                title="Test meeting 3",
                body="Event body",
                start_time=date + datetime.timedelta(days=1, minutes=-1),
                end_time=date + datetime.timedelta(days=1, minutes=29),
                all_day=False,
                participants=[],
                meeting_urls=[],
            ),
        )
        _, future_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="microsoft",
                id="456",
                user_specific_id="u456",
                title="Test meeting 3",
                body="Event body",
                start_time=date + datetime.timedelta(days=1, minutes=1),
                end_time=date + datetime.timedelta(days=1, minutes=31),
                all_day=False,
                participants=[],
                meeting_urls=[],
            ),
        )

        response = client.get("/api/v2/calendar/events?time_zone=Etc/UTC")

        assert response.status_code == status.HTTP_200_OK
        assert [ongoing_event.uuid, future_included_event.uuid] == [
            ScheduledEvent.model_validate(event).scheduled_event_uuid for event in response.json()
        ]

    @patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
    def test_attendee_mapping(self, mock_fetch_calendar_events: MagicMock, test_user: User) -> None:
        org = Organization.objects.create(name="Test organization")
        test_user.organization = org
        test_user.crm_configuration["crm_system"] = "wealthbox"
        test_user.save()

        # Use a newly-created user, without any foreign keys preloaded, to better match the behavior of
        # the actual code.
        app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.get(pk=test_user.pk)

        client_one = Client.objects.create(
            name="Test client", email="<EMAIL>", organization=org, crm_system="wealthbox"
        )
        client_one.authorized_users.add(test_user)
        client_one.save()
        client_two = Client.objects.create(
            name="Test client two",
            email="<EMAIL>",
            organization=org,
            crm_system="wealthbox",
        )
        client_two.authorized_users.add(test_user)
        client_two.save()
        user_one = User.objects.create(
            username="<EMAIL>", email="<EMAIL>", name="Test user", organization=org
        )
        user_two = User.objects.create(
            username="<EMAIL>", email="<EMAIL>", name="Test user two", organization=org
        )

        date = timezone.now() + datetime.timedelta(minutes=1)
        _, first_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="google",
                id="123",
                user_specific_id="u123",
                title="Test meeting",
                body="Event body",
                start_time=date,
                end_time=date + datetime.timedelta(hours=1),
                all_day=False,
                participants=[
                    EventParticipant(id="1", email_address="<EMAIL>", name="One"),
                    EventParticipant(id="2", email_address="<EMAIL>", name="Two"),
                    EventParticipant(id="3", email_address="<EMAIL>", name="Other"),
                ],
                meeting_urls=[pydantic.HttpUrl("https://example.com")],
            ),
        )
        _, second_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="google",
                id="234",
                user_specific_id="u234",
                title="Test meeting 2",
                body="Event body",
                start_time=date + datetime.timedelta(hours=1),
                end_time=date + datetime.timedelta(hours=2),
                all_day=False,
                participants=[
                    EventParticipant(id="1", email_address="<EMAIL>", name="One"),
                    EventParticipant(id="2", email_address="<EMAIL>", name="Two"),
                    EventParticipant(id="3", email_address="<EMAIL>", name="Other Two"),
                ],
                meeting_urls=[pydantic.HttpUrl("https://example.com/2")],
            ),
        )

        expected_events = [
            ScheduledEvent(
                provider="google",
                id="123",
                user_specific_id="u123",
                title="Test meeting",
                start_time=date,
                end_time=date + datetime.timedelta(hours=1),
                all_day=False,
                participants=[
                    EventParticipant(
                        id="1",
                        zeplyn_uuid=client_one.uuid,
                        zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                        email_address="<EMAIL>",
                        name="Test client",
                    ),
                    EventParticipant(
                        id="2",
                        zeplyn_uuid=user_one.uuid,
                        zeplyn_kind=EventParticipant.ZeplynKind.USER,
                        email_address="<EMAIL>",
                        name="Test user",
                    ),
                    EventParticipant(id="3", email_address="<EMAIL>", name="Other"),
                ],
                meeting_urls=[pydantic.HttpUrl("https://example.com")],
                scheduled_event_uuid=str(first_event.uuid),
                autojoin_enabled=False,
                autojoin_available=False,
                autojoin_editable=True,
            ),
            ScheduledEvent(
                provider="google",
                id="234",
                user_specific_id="u234",
                title="Test meeting 2",
                start_time=date + datetime.timedelta(hours=1),
                end_time=date + datetime.timedelta(hours=2),
                all_day=False,
                participants=[
                    EventParticipant(
                        id="1",
                        zeplyn_uuid=client_two.uuid,
                        zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                        email_address="<EMAIL>",
                        name="Test client two",
                    ),
                    EventParticipant(
                        id="2",
                        zeplyn_uuid=user_two.uuid,
                        zeplyn_kind=EventParticipant.ZeplynKind.USER,
                        email_address="<EMAIL>",
                        name="Test user two",
                    ),
                    EventParticipant(id="3", email_address="<EMAIL>", name="Other Two"),
                ],
                meeting_urls=[pydantic.HttpUrl("https://example.com/2")],
                scheduled_event_uuid=str(second_event.uuid),
                autojoin_enabled=False,
                autojoin_available=False,
                autojoin_editable=True,
            ),
        ]

        response = client.get("/api/v2/calendar/events?time_zone=Etc/UTC")

        assert response.status_code == status.HTTP_200_OK
        assert [ScheduledEvent.model_validate(event.model_dump()) for event in expected_events] == [
            ScheduledEvent.model_validate(event) for event in response.json()
        ]

    def test_attendee_mapping_permissions(self, test_user: User, django_user_model: User) -> None:
        org = Organization.objects.create(name="Test organization")
        org_two = Organization.objects.create(name="Test organization two")
        test_user.organization = org
        test_user.crm_configuration["crm_system"] = "wealthbox"
        test_user.save()
        user_two = django_user_model.objects.create(username="<EMAIL>", organization=org_two)
        user_two.crm_configuration["crm_system"] = "wealthbox"
        user_two.save()
        # Use a newly-created user, without any foreign keys preloaded, to better match the behavior of
        # the actual code.
        app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.get(pk=test_user.pk)

        user_one_client = Client.objects.create(
            name="User one client", email="<EMAIL>", organization=org, crm_system="wealthbox"
        )
        user_one_client.authorized_users.add(test_user)
        user_one_client.save()
        user_two_client = Client.objects.create(
            name="User two client", email="<EMAIL>", organization=org_two, crm_system="wealthbox"
        )
        user_two_client.authorized_users.add(user_two)
        user_two_client.save()
        user_two_client_two = Client.objects.create(
            name="User two client two",
            email="<EMAIL>",
            organization=org_two,
            crm_system="wealthbox",
        )
        user_two_client_two.authorized_users.add(user_two)
        user_two_client_two.save()
        user_one_user = User.objects.create(
            username="<EMAIL>", email="<EMAIL>", name="User one user", organization=org
        )
        user_two_user = User.objects.create(
            username="@example.com",
            email="<EMAIL>",
            name="User two user",
            organization=org_two,
        )

        date = timezone.now()
        _, event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="microsoft",
                id="123",
                user_specific_id="u123",
                title="Test meeting",
                body="Event body",
                start_time=date,
                end_time=date + datetime.timedelta(hours=1),
                all_day=False,
                participants=[
                    EventParticipant(id="1", email_address="<EMAIL>", name="One"),
                    EventParticipant(id="2", email_address="<EMAIL>", name="Two"),
                    EventParticipant(id="3", email_address="<EMAIL>", name="Three"),
                    EventParticipant(id="4", email_address="<EMAIL>", name="Four"),
                ],
                meeting_urls=[pydantic.HttpUrl("https://example.com")],
            ),
        )

        expected_participants = [
            EventParticipant(
                id="1",
                zeplyn_uuid=user_one_client.uuid,
                zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                email_address="<EMAIL>",
                name="User one client",
            ),
            EventParticipant(
                id="2",
                zeplyn_uuid=user_one_user.uuid,
                zeplyn_kind=EventParticipant.ZeplynKind.USER,
                email_address="<EMAIL>",
                name="User one user",
            ),
            EventParticipant(id="3", email_address="<EMAIL>", name="Three"),
            EventParticipant(id="4", email_address="<EMAIL>", name="Four"),
        ]

        response = client.get("/api/v2/calendar/events?time_zone=Etc/UTC")

        assert response.status_code == status.HTTP_200_OK
        assert expected_participants == [
            EventParticipant.model_validate(participant) for participant in response.json()[0].get("participants")
        ]

        # Repeat with user_two

        app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.get(pk=user_two.pk)
        event.user = user_two
        event.save()

        expected_participants = [
            EventParticipant(
                id="1",
                zeplyn_uuid=user_two_client.uuid,
                zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                email_address="<EMAIL>",
                name="User two client",
            ),
            EventParticipant(id="2", email_address="<EMAIL>", name="Two"),
            EventParticipant(
                id="3",
                zeplyn_uuid=user_two_client_two.uuid,
                zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                email_address="<EMAIL>",
                name="User two client two",
            ),
            EventParticipant(
                id="4",
                zeplyn_uuid=user_two_user.uuid,
                zeplyn_kind=EventParticipant.ZeplynKind.USER,
                email_address="<EMAIL>",
                name="User two user",
            ),
        ]
        response = client.get("/api/v2/calendar/events?time_zone=Etc/UTC")

        assert response.status_code == status.HTTP_200_OK
        assert expected_participants == [
            EventParticipant.model_validate(participant) for participant in response.json()[0].get("participants")
        ]

    @pytest.mark.parametrize(
        ("base_date", "tz", "lookahead", "expected_interval"),
        [
            # Simple lookahead intervals
            (
                datetime.datetime(2022, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                "Etc/UTC",
                CalendarLookahead.ONE_HOUR,
                datetime.timedelta(hours=1),
            ),
            (
                datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                "Pacific/Guam",
                CalendarLookahead.ONE_HOUR,
                datetime.timedelta(hours=1),
            ),
            (
                datetime.datetime(2021, 2, 2, 10, 10, 10, tzinfo=datetime.timezone.utc),
                "Etc/UTC",
                CalendarLookahead.ONE_DAY,
                datetime.timedelta(days=1),
            ),
            (
                datetime.datetime(2024, 2, 2, 10, 10, 10, tzinfo=datetime.timezone.utc),
                # This is UTC-10: the sign is intentionally inverted (see https://en.wikipedia.org/wiki/Tz_database#Area)
                quote_plus("Etc/GMT+10"),
                CalendarLookahead.ONE_DAY,
                datetime.timedelta(days=1),
            ),
            (
                datetime.datetime(2023, 3, 4, 1, 1, 1, tzinfo=datetime.timezone.utc),
                "Etc/UTC",
                CalendarLookahead.TWO_DAYS,
                datetime.timedelta(days=2),
            ),
            (
                datetime.datetime(2023, 3, 4, 1, 1, 1, tzinfo=datetime.timezone.utc),
                "Asia/Kathmandu",
                CalendarLookahead.TWO_DAYS,
                datetime.timedelta(days=2),
            ),
            (
                datetime.datetime(2023, 3, 4, 1, 1, 1, tzinfo=datetime.timezone.utc),
                "America/Los_Angeles",
                CalendarLookahead.TWO_DAYS,
                datetime.timedelta(days=2),
            ),
            # End of day from UTC
            (
                datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                "Etc/UTC",
                CalendarLookahead.END_OF_DAY,
                datetime.timedelta(days=1),
            ),
            (
                datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
                "Etc/UTC",
                CalendarLookahead.END_OF_DAY,
                datetime.timedelta(hours=23),
            ),
            (
                datetime.datetime(2024, 1, 1, 12, 30, 30, tzinfo=datetime.timezone.utc),
                "Etc/UTC",
                CalendarLookahead.END_OF_DAY,
                datetime.timedelta(hours=11, minutes=29, seconds=30),
            ),
            # End of day from UTC+5
            (
                datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                "Antarctica/Mawson",
                CalendarLookahead.END_OF_DAY,
                datetime.timedelta(hours=19),
            ),
            (
                datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
                "Antarctica/Mawson",
                CalendarLookahead.END_OF_DAY,
                datetime.timedelta(hours=18),
            ),
            (
                datetime.datetime(2024, 1, 1, 12, 30, 30, tzinfo=datetime.timezone.utc),
                "Antarctica/Mawson",
                CalendarLookahead.END_OF_DAY,
                datetime.timedelta(hours=6, minutes=29, seconds=30),
            ),
            (
                datetime.datetime(2024, 1, 1, 20, 0, 0, tzinfo=datetime.timezone.utc),
                "Antarctica/Mawson",
                CalendarLookahead.END_OF_DAY,
                datetime.timedelta(hours=23),
            ),
            # End of day from UTC-8
            # The sign is intentionally inverted: see https://en.wikipedia.org/wiki/Tz_database#Area
            (
                datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                quote_plus("Etc/GMT+8"),
                CalendarLookahead.END_OF_DAY,
                datetime.timedelta(hours=8),
            ),
            (
                datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
                quote_plus("Etc/GMT+8"),
                CalendarLookahead.END_OF_DAY,
                datetime.timedelta(hours=7),
            ),
            (
                datetime.datetime(2024, 1, 1, 12, 30, 30, tzinfo=datetime.timezone.utc),
                quote_plus("Etc/GMT+8"),
                CalendarLookahead.END_OF_DAY,
                datetime.timedelta(hours=19, minutes=29, seconds=30),
            ),
            (
                datetime.datetime(2024, 1, 1, 20, 0, 0, tzinfo=datetime.timezone.utc),
                quote_plus("Etc/GMT+8"),
                CalendarLookahead.END_OF_DAY,
                datetime.timedelta(hours=12),
            ),
            # End of week from UTC
            # Jan 1, 2024 is a Monday; the week starts on Sunday
            (
                datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                "Africa/Sao_Tome",
                CalendarLookahead.END_OF_WEEK,
                datetime.timedelta(days=6),
            ),
            (
                datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
                "Africa/Sao_Tome",
                CalendarLookahead.END_OF_WEEK,
                datetime.timedelta(days=5, hours=23),
            ),
            (
                datetime.datetime(2024, 1, 6, 23, 30, 30, tzinfo=datetime.timezone.utc),
                "Africa/Sao_Tome",
                CalendarLookahead.END_OF_WEEK,
                datetime.timedelta(minutes=29, seconds=30),
            ),
            # End of week from UTC+5
            (
                datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                "Indian/Maldives",
                CalendarLookahead.END_OF_WEEK,
                datetime.timedelta(days=5, hours=19),
            ),
            (
                datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
                "Indian/Maldives",
                CalendarLookahead.END_OF_WEEK,
                datetime.timedelta(days=5, hours=18),
            ),
            (
                datetime.datetime(2024, 1, 1, 12, 30, 30, tzinfo=datetime.timezone.utc),
                "Indian/Maldives",
                CalendarLookahead.END_OF_WEEK,
                datetime.timedelta(days=5, hours=6, minutes=29, seconds=30),
            ),
            # Saturday night UTC, Sunday morning UTC+5
            (
                datetime.datetime(2023, 12, 30, 20, 0, 0, tzinfo=datetime.timezone.utc),
                "Indian/Maldives",
                CalendarLookahead.END_OF_WEEK,
                datetime.timedelta(days=6, hours=23),
            ),
            # End of week from UTC-8
            (
                datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                "Pacific/Pitcairn",
                CalendarLookahead.END_OF_WEEK,
                datetime.timedelta(days=6, hours=8),
            ),
            (
                datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
                "Pacific/Pitcairn",
                CalendarLookahead.END_OF_WEEK,
                datetime.timedelta(days=6, hours=7),
            ),
            (
                datetime.datetime(2024, 1, 1, 12, 30, 30, tzinfo=datetime.timezone.utc),
                "Pacific/Pitcairn",
                CalendarLookahead.END_OF_WEEK,
                datetime.timedelta(days=5, hours=19, minutes=29, seconds=30),
            ),
            # Sunday morning UTC, Saturday night UTC-8
            (
                datetime.datetime(2023, 12, 31, 4, 0, 0, tzinfo=datetime.timezone.utc),
                "Pacific/Pitcairn",
                CalendarLookahead.END_OF_WEEK,
                datetime.timedelta(hours=4),
            ),
            # End of next week from UTC
            # Jan 1, 2024 is a Monday; the week starts on Sunday
            (
                datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                "Africa/Sao_Tome",
                CalendarLookahead.END_OF_NEXT_WEEK,
                datetime.timedelta(days=13),
            ),
            (
                datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
                "Africa/Sao_Tome",
                CalendarLookahead.END_OF_NEXT_WEEK,
                datetime.timedelta(days=12, hours=23),
            ),
            (
                datetime.datetime(2024, 1, 6, 23, 30, 30, tzinfo=datetime.timezone.utc),
                "Africa/Sao_Tome",
                CalendarLookahead.END_OF_NEXT_WEEK,
                datetime.timedelta(days=7, minutes=29, seconds=30),
            ),
            # End of next week from UTC+5
            (
                datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                "Indian/Maldives",
                CalendarLookahead.END_OF_NEXT_WEEK,
                datetime.timedelta(days=12, hours=19),
            ),
            (
                datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
                "Indian/Maldives",
                CalendarLookahead.END_OF_NEXT_WEEK,
                datetime.timedelta(days=12, hours=18),
            ),
            (
                datetime.datetime(2024, 1, 1, 12, 30, 30, tzinfo=datetime.timezone.utc),
                "Indian/Maldives",
                CalendarLookahead.END_OF_NEXT_WEEK,
                datetime.timedelta(days=12, hours=6, minutes=29, seconds=30),
            ),
            # Saturday night UTC, Sunday morning UTC+5
            (
                datetime.datetime(2023, 12, 30, 20, 0, 0, tzinfo=datetime.timezone.utc),
                "Indian/Maldives",
                CalendarLookahead.END_OF_NEXT_WEEK,
                datetime.timedelta(days=13, hours=23),
            ),
            # End of next week from UTC-8
            (
                datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
                "Pacific/Pitcairn",
                CalendarLookahead.END_OF_NEXT_WEEK,
                datetime.timedelta(days=13, hours=8),
            ),
            (
                datetime.datetime(2024, 1, 1, 1, 0, 0, tzinfo=datetime.timezone.utc),
                "Pacific/Pitcairn",
                CalendarLookahead.END_OF_NEXT_WEEK,
                datetime.timedelta(days=13, hours=7),
            ),
            (
                datetime.datetime(2024, 1, 1, 12, 30, 30, tzinfo=datetime.timezone.utc),
                "Pacific/Pitcairn",
                CalendarLookahead.END_OF_NEXT_WEEK,
                datetime.timedelta(days=12, hours=19, minutes=29, seconds=30),
            ),
            # Sunday morning UTC, Saturday night UTC-8
            (
                datetime.datetime(2023, 12, 31, 4, 0, 0, tzinfo=datetime.timezone.utc),
                "Pacific/Pitcairn",
                CalendarLookahead.END_OF_NEXT_WEEK,
                datetime.timedelta(days=7, hours=4),
            ),
        ],
    )
    @patch("api.routers.calendar._now")
    @patch("api.routers.calendar.DBScheduledEvent")
    def test_lookahead(
        self,
        mock_scheduled_event: MagicMock,
        mock_now: MagicMock,
        base_date: datetime.datetime,
        tz: str,
        lookahead: CalendarLookahead,
        expected_interval: datetime.timedelta,
        test_user: User,
    ) -> None:
        test_user.calendar_lookahead = lookahead
        test_user.save()

        mock_now.return_value = base_date

        mock_scheduled_event.objects.filter.return_value.order_by.return_value = DBScheduledEvent.objects.none()

        client.get(f"/api/v2/calendar/events?time_zone={tz}")

        mock_scheduled_event.objects.filter.assert_called_once_with(
            user=ANY,
            user_specific_source_id__isnull=False,
            end_time__gte=base_date,
            start_time__lte=base_date + expected_interval,
        )

    def test_no_meeting_urls(self, test_user: User) -> None:
        f = Flag.objects.get(name=Flags.EnableShowMeetingsWithoutURLsSetting.name)
        f.everyone = True
        f.override_enabled_by_environment = None  # To ensure no environment override
        f.save()

        test_user.show_events_without_meeting_urls = False
        test_user.save()

        date = timezone.now()
        _, first_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="microsoft",
                id="123",
                user_specific_id="u123",
                title="Test meeting",
                body="Event body",
                start_time=date,
                end_time=date + datetime.timedelta(hours=1),
                all_day=False,
                participants=[],
                meeting_urls=[pydantic.HttpUrl("https://example.com")],
            ),
        )

        _, second_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="microsoft",
                id="234",
                user_specific_id="u234",
                title="Test meeting 2",
                body="Event body",
                start_time=date + datetime.timedelta(hours=1),
                end_time=date + datetime.timedelta(hours=2),
                all_day=False,
                participants=[],
                meeting_urls=[],
            ),
        )
        _, third_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="microsoft",
                id="345",
                user_specific_id="u345",
                title="Test meeting 3",
                body="Event body",
                start_time=date + datetime.timedelta(hours=2),
                end_time=date + datetime.timedelta(hours=3),
                all_day=False,
                participants=[],
                meeting_urls=[pydantic.HttpUrl("https://example.com/3")],
            ),
        )

        response = client.get("/api/v2/calendar/events?time_zone=Etc/UTC")

        assert response.status_code == status.HTTP_200_OK
        assert [first_event.uuid, third_event.uuid] == [
            ScheduledEvent.model_validate(event).scheduled_event_uuid for event in response.json()
        ]

    def test_no_meeting_urls_flag_disabled(self, test_user: User) -> None:
        f = Flag.objects.get(name=Flags.EnableShowMeetingsWithoutURLsSetting.name)
        f.everyone = False
        f.override_enabled_by_environment = None  # To ensure no environment override
        f.save()
        test_user.show_events_without_meeting_urls = False
        test_user.save()

        date = timezone.now() + datetime.timedelta(minutes=1)
        event_source_data, event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="microsoft",
                id="123",
                user_specific_id="u123",
                title="Test meeting",
                body="Event body",
                start_time=date,
                end_time=date + datetime.timedelta(hours=1),
                all_day=False,
                participants=[],
                meeting_urls=[],
            ),
        )

        response = client.get("/api/v2/calendar/events?time_zone=Etc/UTC")

        assert response.status_code == status.HTTP_200_OK
        assert [
            ScheduledEvent.model_validate(
                {
                    **event_source_data.model_dump(mode="json", exclude={"body"}),
                    "linked_crm_entity": None,
                    "scheduled_event_uuid": event.uuid,
                    "autojoin_enabled": False,
                    "autojoin_available": False,
                    "autojoin_editable": True,
                }
            )
        ] == [ScheduledEvent.model_validate(event) for event in response.json()]

    @pytest.fixture
    def test_user_with_autojoin_calendar(self, django_user_model: User) -> User:
        user = django_user_model.objects.create()
        user.recall_calendar_id = "test-calendar-id"
        user.save()
        app.dependency_overrides[user_from_authorization_header] = lambda: user
        return user

    @pytest.mark.parametrize(
        "recall_calendar_platform, ms_autojoin_enabled, google_autojoin_enabled, crm_autojoin_enabled",
        [
            (None, False, False, False),
            ("other", False, False, False),
            ("microsoft", True, False, False),
            ("google", False, True, False),
        ],
    )
    @patch("api.routers.calendar.RecallBotController")
    def test_events_with_autojoin(
        self,
        mock_recall_bot_controller: MagicMock,
        test_user_with_autojoin_calendar: User,
        recall_calendar_platform: str | None,
        ms_autojoin_enabled: bool,
        google_autojoin_enabled: bool,
        crm_autojoin_enabled: bool,
    ) -> None:
        mock_recall_bot_controller.connected_calendar_platform.return_value = recall_calendar_platform

        date = timezone.now() + datetime.timedelta(minutes=1)
        _, ms_event = self._db_scheduled_event_with_calendar_event(
            test_user_with_autojoin_calendar,
            CalendarEvent(
                provider="microsoft",
                id="123",
                user_specific_id="u123",
                title="Test meeting",
                body="Event body",
                start_time=date,
                end_time=date + datetime.timedelta(hours=1),
                all_day=False,
                participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
                meeting_urls=[pydantic.HttpUrl("https://example.com")],
            ),
        )
        _, google_event = self._db_scheduled_event_with_calendar_event(
            test_user_with_autojoin_calendar,
            CalendarEvent(
                provider="google",
                id="234",
                user_specific_id="u234",
                title="Test meeting 2",
                body="Event body",
                start_time=date + datetime.timedelta(hours=1),
                end_time=date + datetime.timedelta(hours=2),
                all_day=False,
                participants=[EventParticipant(id="2", email_address="<EMAIL>", name="Test Example 2")],
                meeting_urls=[pydantic.HttpUrl("https://example.com/2")],
            ),
        )
        _, crm_event = self._db_scheduled_event_with_calendar_event(
            test_user_with_autojoin_calendar,
            CalendarEvent(
                provider="redtail",
                id="345",
                user_specific_id="u345",
                title="Test meeting 3",
                body="Event body",
                start_time=date + datetime.timedelta(hours=2),
                end_time=date + datetime.timedelta(hours=3),
                all_day=False,
                participants=[],
                meeting_urls=[pydantic.HttpUrl("https://example.com/3")],
            ),
        )

        response = client.get("/api/v2/calendar/events?time_zone=Etc/UTC")

        assert response.status_code == status.HTTP_200_OK
        assert [
            {
                "scheduled_event_uuid": ms_event.uuid,
                "autojoin_enabled": ms_autojoin_enabled,
                "autojoin_editable": True,
            },
            {
                "scheduled_event_uuid": google_event.uuid,
                "autojoin_enabled": google_autojoin_enabled,
                "autojoin_editable": True,
            },
            {
                "scheduled_event_uuid": crm_event.uuid,
                "autojoin_enabled": crm_autojoin_enabled,
                "autojoin_editable": True,
            },
        ] == [
            ScheduledEvent.model_validate(event).model_dump(
                include={"scheduled_event_uuid", "autojoin_enabled", "autojoin_editable"}
            )
            for event in response.json()
        ]
        mock_recall_bot_controller.connected_calendar_platform.assert_called_once_with(
            test_user_with_autojoin_calendar.recall_calendar_id
        )

        # Test with autojoin enabled
        ms_event.autojoin_behavior = DBScheduledEvent.AutoJoinOverride.ENABLED
        ms_event.save()
        google_event.autojoin_behavior = DBScheduledEvent.AutoJoinOverride.ENABLED
        google_event.save()
        crm_event.autojoin_behavior = DBScheduledEvent.AutoJoinOverride.ENABLED
        crm_event.save()

        response = client.get("/api/v2/calendar/events?time_zone=Etc/UTC")

        assert response.status_code == status.HTTP_200_OK
        assert [
            {
                "scheduled_event_uuid": ms_event.uuid,
                "autojoin_enabled": ms_autojoin_enabled,
            },
            {
                "scheduled_event_uuid": google_event.uuid,
                "autojoin_enabled": google_autojoin_enabled,
            },
            {
                "scheduled_event_uuid": crm_event.uuid,
                "autojoin_enabled": crm_autojoin_enabled,
            },
        ] == [
            ScheduledEvent.model_validate(event).model_dump(include={"scheduled_event_uuid", "autojoin_enabled"})
            for event in response.json()
        ]

        # Test with autojoin disabled
        ms_event.autojoin_behavior = DBScheduledEvent.AutoJoinOverride.DISABLED
        ms_event.save()
        google_event.autojoin_behavior = DBScheduledEvent.AutoJoinOverride.DISABLED
        google_event.save()
        crm_event.autojoin_behavior = DBScheduledEvent.AutoJoinOverride.DISABLED
        crm_event.save()

        response = client.get("/api/v2/calendar/events?time_zone=Etc/UTC")

        assert response.status_code == status.HTTP_200_OK
        assert [
            {
                "scheduled_event_uuid": ms_event.uuid,
                "autojoin_enabled": False,
            },
            {
                "scheduled_event_uuid": google_event.uuid,
                "autojoin_enabled": False,
            },
            {
                "scheduled_event_uuid": crm_event.uuid,
                "autojoin_enabled": False,
            },
        ] == [
            ScheduledEvent.model_validate(event).model_dump(include={"scheduled_event_uuid", "autojoin_enabled"})
            for event in response.json()
        ]

        test_user_with_autojoin_calendar.refresh_from_db()
        assert test_user_with_autojoin_calendar.recall_calendar_platform == recall_calendar_platform

    @pytest.mark.parametrize(
        "recall_calendar_platform, ms_autojoin_available, google_autojoin_available, crm_autojoin_available",
        [
            (None, False, False, False),
            ("other", False, False, False),
            ("microsoft", True, False, False),
            ("google", False, True, False),
        ],
    )
    @pytest.mark.parametrize(
        "autojoin_behavior",
        [
            DBScheduledEvent.AutoJoinOverride.ENABLED,
            DBScheduledEvent.AutoJoinOverride.DISABLED,
            DBScheduledEvent.AutoJoinOverride.DEFAULT,
        ],
    )
    @patch("api.routers.calendar.RecallBotController")
    def test_events_with_autojoin_availability(
        self,
        mock_recall_bot_controller: MagicMock,
        test_user_with_autojoin_calendar: User,
        recall_calendar_platform: str | None,
        autojoin_behavior: DBScheduledEvent.AutoJoinOverride,
        ms_autojoin_available: bool,
        google_autojoin_available: bool,
        crm_autojoin_available: bool,
    ) -> None:
        mock_recall_bot_controller.connected_calendar_platform.return_value = recall_calendar_platform

        date = timezone.now()
        _, ms_event = self._db_scheduled_event_with_calendar_event(
            test_user_with_autojoin_calendar,
            CalendarEvent(
                provider="microsoft",
                id="123",
                user_specific_id="u123",
                title="Test meeting",
                body="Event body",
                start_time=date,
                end_time=date + datetime.timedelta(hours=1),
                all_day=False,
                participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
                meeting_urls=[pydantic.HttpUrl("https://example.com")],
            ),
        )
        ms_event.autojoin_behavior = autojoin_behavior
        ms_event.save()
        _, google_event = self._db_scheduled_event_with_calendar_event(
            test_user_with_autojoin_calendar,
            CalendarEvent(
                provider="google",
                id="234",
                user_specific_id="u234",
                title="Test meeting 2",
                body="Event body",
                start_time=date + datetime.timedelta(hours=1),
                end_time=date + datetime.timedelta(hours=2),
                all_day=False,
                participants=[EventParticipant(id="2", email_address="<EMAIL>", name="Test Example 2")],
                meeting_urls=[pydantic.HttpUrl("https://example.com/2")],
            ),
        )
        google_event.autojoin_behavior = autojoin_behavior
        google_event.save()
        _, crm_event = self._db_scheduled_event_with_calendar_event(
            test_user_with_autojoin_calendar,
            CalendarEvent(
                provider="redtail",
                id="345",
                user_specific_id="u345",
                title="Test meeting 3",
                body="Event body",
                start_time=date + datetime.timedelta(hours=2),
                end_time=date + datetime.timedelta(hours=3),
                all_day=False,
                participants=[],
                meeting_urls=[],
            ),
        )
        crm_event.autojoin_behavior = autojoin_behavior
        crm_event.save()

        response = client.get("/api/v2/calendar/events?time_zone=Etc/UTC")

        assert response.status_code == status.HTTP_200_OK
        assert [
            {
                "scheduled_event_uuid": ms_event.uuid,
                "autojoin_available": ms_autojoin_available,
            },
            {
                "scheduled_event_uuid": google_event.uuid,
                "autojoin_available": google_autojoin_available,
            },
            {
                "scheduled_event_uuid": crm_event.uuid,
                "autojoin_available": crm_autojoin_available,
            },
        ] == [
            ScheduledEvent.model_validate(event).model_dump(include={"scheduled_event_uuid", "autojoin_available"})
            for event in response.json()
        ]

    @patch("api.routers.calendar.reconcile_calendar_events")
    def test_events_with_autojoin_editability(
        self, mock_reconcile_calendar_events: MagicMock, test_user: User, django_user_model: User
    ) -> None:
        test_user.calendar_lookahead = CalendarLookahead.ONE_DAY
        test_user.save()

        # Create the events in a different order than they should be returned, to check ordering.
        date = timezone.now()
        _, editable_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="google",
                id="234",
                user_specific_id="u234",
                title="Test meeting",
                body="Event body",
                start_time=date + datetime.timedelta(hours=1),
                end_time=date + datetime.timedelta(hours=2),
                all_day=False,
                participants=[],
                meeting_urls=[pydantic.HttpUrl("https://example.com")],
            ),
        )
        _, non_editable_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="microsoft",
                id="123",
                user_specific_id="u123",
                title="Test meeting",
                body="Event body",
                start_time=date - datetime.timedelta(minutes=30),
                end_time=date + datetime.timedelta(hours=1),
                all_day=False,
                participants=[],
                meeting_urls=[pydantic.HttpUrl("https://example.com")],
            ),
        )
        _, finished_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="microsoft",
                id="345",
                user_specific_id="u345",
                title="Finished meeting",
                body="Event body",
                start_time=date - datetime.timedelta(hours=2),
                end_time=date - datetime.timedelta(hours=1),
                all_day=False,
                participants=[],
                meeting_urls=[pydantic.HttpUrl("https://example.com/3")],
            ),
        )

        response = client.get("/api/v2/calendar/events?time_zone=Etc/UTC")

        assert response.status_code == status.HTTP_200_OK
        assert [
            ScheduledEvent.model_validate(event).model_dump(include={"scheduled_event_uuid", "autojoin_editable"})
            for event in response.json()
        ] == [
            {
                "scheduled_event_uuid": non_editable_event.uuid,
                "autojoin_editable": False,
            },
            {
                "scheduled_event_uuid": editable_event.uuid,
                "autojoin_editable": True,
            },
        ]
        mock_reconcile_calendar_events.delay_on_commit.assert_called_once_with(test_user.uuid)

    @patch("api.routers.calendar.RecallBotController")
    def test_events_with_autojoin_recall_error(
        self,
        mock_recall_bot_controller: MagicMock,
        test_user_with_autojoin_calendar: User,
    ) -> None:
        mock_recall_bot_controller.connected_calendar_platform.side_effect = Exception("Error fetching calendar info")
        date = timezone.now()
        _, ms_event = self._db_scheduled_event_with_calendar_event(
            test_user_with_autojoin_calendar,
            CalendarEvent(
                provider="microsoft",
                id="123",
                user_specific_id="u123",
                title="Test meeting",
                body="Event body",
                start_time=date,
                end_time=date + datetime.timedelta(hours=1),
                all_day=False,
                participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
                meeting_urls=[pydantic.HttpUrl("https://example.com")],
            ),
        )

        response = client.get("/api/v2/calendar/events?time_zone=Etc/UTC")

        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert not test_user_with_autojoin_calendar.recall_calendar_platform

    @patch("api.routers.calendar.RecallBotController")
    def test_events_with_autojoin_recall_platform_already_set(
        self,
        mock_recall_bot_controller: MagicMock,
        test_user_with_autojoin_calendar: User,
    ) -> None:
        test_user_with_autojoin_calendar.recall_calendar_platform = "microsoft"
        test_user_with_autojoin_calendar.save()
        mock_recall_bot_controller.connected_calendar_platform.return_value = "microsoft"

        response = client.get("/api/v2/calendar/events?time_zone=Etc/UTC")

        assert response.status_code == status.HTTP_200_OK
        mock_recall_bot_controller.connected_calendar_platform.assert_not_called()

    @patch("api.routers.calendar.reconcile_calendar_events")
    def test_crm_entity_parsing(self, mock_reconcile_calendar_events: MagicMock, test_user: User) -> None:
        date = timezone.now() + datetime.timedelta(minutes=1)
        first_event_source_data, first_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="google",
                id="234",
                user_specific_id="u234",
                title="Test meeting",
                body="Test[ZDATA]<salesforce_case_id>TEST</salesforce_case_id>[/ZDATA]",
                start_time=date,
                end_time=date + datetime.timedelta(hours=1),
                all_day=False,
                participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
                meeting_urls=[pydantic.HttpUrl("https://example.com")],
            ),
        )
        second_event_source_data, second_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="microsoft",
                id="123",
                user_specific_id="u123",
                title="Test meeting",
                body="[ZDATA]<salesforce_case_id>TEST_TWO</salesforce_case_id>[/ZDATA]Test",
                start_time=date + datetime.timedelta(minutes=30),
                end_time=date + datetime.timedelta(hours=1),
                all_day=False,
                participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
                meeting_urls=[pydantic.HttpUrl("https://example.com")],
            ),
        )
        third_event_source_data, third_event = self._db_scheduled_event_with_calendar_event(
            test_user,
            CalendarEvent(
                provider="redtail",
                id="345",
                user_specific_id="u345",
                title="Test meeting",
                body="<a>&lsqb;ZDATA&rsqb;&lt;salesforce&lowbar;case&lowbar;id&gt;TEST&lt;&sol;salesforce&lowbar;case&lowbar;id&gt;&lsqb;&sol;ZDATA&rsqb;</a>",
                start_time=date + datetime.timedelta(hours=1),
                end_time=date + datetime.timedelta(hours=2),
                all_day=False,
                participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
                meeting_urls=[pydantic.HttpUrl("https://example.com")],
            ),
        )

        org = Organization.objects.create(name="Test organization")
        test_user.organization = org
        test_user.save()
        user_client = Client.objects.create(
            organization=org,
            crm_id="TEST_TWO",
            name="Test client",
        )
        user_client.authorized_users.add(test_user)
        user_client.save()

        # Create another client with a matching CRM ID but for whom the user is not authorized.
        Client.objects.create(
            organization=org,
            crm_id="TEST",
            name="Other test client",
        )

        response = client.get("/api/v2/calendar/events?time_zone=Etc/UTC")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == [
            {
                **first_event_source_data.model_dump(mode="json", exclude={"body"}),
                "linked_crm_entity": {
                    "name": "Test meeting",
                    "id": "TEST",
                },
                "autojoin_enabled": False,
                "autojoin_available": False,
                "autojoin_editable": True,
                "scheduled_event_uuid": str(first_event.uuid),
            },
            {
                **second_event_source_data.model_dump(mode="json", exclude={"body"}),
                "linked_crm_entity": {
                    "name": "Test client",
                    "id": "TEST_TWO",
                },
                "autojoin_enabled": False,
                "autojoin_available": False,
                "autojoin_editable": True,
                "scheduled_event_uuid": str(second_event.uuid),
            },
            {
                **third_event_source_data.model_dump(mode="json", exclude={"body"}),
                "linked_crm_entity": {
                    "name": "Test meeting",
                    "id": "TEST",
                },
                "autojoin_enabled": False,
                "autojoin_available": False,
                "autojoin_editable": True,
                "scheduled_event_uuid": str(third_event.uuid),
            },
        ]
        mock_reconcile_calendar_events.delay_on_commit.assert_called_once_with(test_user.uuid)


@patch("api.routers.calendar.update_autojoin_bots")
class TestUpdateAutoJoin:
    def test_update_autojoin(self, mock_update_autojoin_bots: MagicMock, test_user: User) -> None:
        date = timezone.now()
        event = DBScheduledEvent.objects.create(
            user=test_user,
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            shared_source_id="test-event-id",
            user_specific_source_id="test-user-event-id",
            source_data={},
        )

        response = client.post(
            "/api/v2/calendar/update_autojoin",
            json={"auto_join": True, "scheduled_event_uuid": str(event.uuid)},
        )

        assert response.status_code == status.HTTP_200_OK
        event.refresh_from_db()
        assert event.autojoin_behavior == DBScheduledEvent.AutoJoinOverride.ENABLED
        mock_update_autojoin_bots.delay_on_commit.assert_called_once_with(
            test_user.uuid, [event.uuid], update_recall=True
        )

    def test_update_autojoin_event_not_found(self, mock_update_autojoin_bots: MagicMock, test_user: User) -> None:
        response = client.post(
            "/api/v2/calendar/update_autojoin",
            json={"auto_join": True, "scheduled_event_uuid": str(uuid4())},
        )

        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["detail"] == "Scheduled event not found"
        mock_update_autojoin_bots.delay_on_commit.assert_not_called()

    def test_update_autojoin_event_different_user(
        self, mock_update_autojoin_bots: MagicMock, test_user: User, django_user_model: User
    ) -> None:
        other_user = django_user_model.objects.create(username="otheruser")
        date = timezone.now()
        event = DBScheduledEvent.objects.create(
            user=other_user,
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            shared_source_id="test-event-id",
            user_specific_source_id="test-user-event-id",
            source_data={},
        )

        response = client.post(
            "/api/v2/calendar/update_autojoin",
            json={"auto_join": True, "scheduled_event_uuid": str(event.uuid)},
        )

        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["detail"] == "Scheduled event not found"
        mock_update_autojoin_bots.delay_on_commit.assert_not_called()


@patch("api.routers.calendar._cache_counter")
@patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
def test_cache_counter_incremented_on_cache_hit(
    mock_fetch_calendar_events: MagicMock, mock_cache_counter: MagicMock, test_user: User, settings: SettingsWrapper
) -> None:
    settings.CALENDAR_EVENT_CACHE_TTL_SECONDS = 10

    date = timezone.now()
    events = [
        CalendarEvent(
            provider="google",
            id="test123",
            user_specific_id="u123",
            title="Test meeting",
            body="Event body",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
            meeting_urls=["https://example.com"],
        )
    ]
    mock_fetch_calendar_events.return_value = (events, True)

    response = client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")
    assert response.status_code == 200

    mock_cache_counter.add.assert_not_called()

    response = client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")
    assert response.status_code == 200

    mock_cache_counter.add.assert_called_once_with(1)


@patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock)
def test_different_lookaheads_use_different_cache_keys(
    mock_fetch_calendar_events: MagicMock, test_user: User, settings: SettingsWrapper
) -> None:
    """Test that different lookahead values use different cache keys."""

    settings.CALENDAR_EVENT_CACHE_TTL_SECONDS = 10
    cache.clear()

    date = timezone.now()
    events = [
        CalendarEvent(
            provider="google",
            id="test123",
            user_specific_id="u123",
            title="Test meeting",
            body="Event body",
            start_time=date,
            end_time=date + datetime.timedelta(hours=1),
            all_day=False,
            participants=[EventParticipant(id="1", email_address="<EMAIL>", name="Test Example")],
            meeting_urls=["https://example.com"],
        )
    ]
    mock_fetch_calendar_events.return_value = (events, True)

    test_user.calendar_lookahead = CalendarLookahead.ONE_DAY
    test_user.save()

    response = client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")
    assert response.status_code == 200
    assert mock_fetch_calendar_events.call_count == 1

    test_user.calendar_lookahead = CalendarLookahead.TWO_DAYS
    test_user.save()

    response = client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")

    assert response.status_code == 200
    assert mock_fetch_calendar_events.call_count == 2  # Called again, not using cache


def test_metrics_creation() -> None:
    """Test that the metrics counter is created correctly."""
    assert hasattr(_cache_counter, "add")


@pytest.mark.asyncio
async def test_async_cache_counter_incremented(settings: SettingsWrapper, django_user_model: User) -> None:
    """Test cache counter in an async context."""
    cache.clear()
    settings.CALENDAR_EVENT_CACHE_TTL_SECONDS = 10

    user = await django_user_model.objects.acreate(username="<EMAIL>", email="<EMAIL>")

    app.dependency_overrides[user_from_authorization_header] = lambda: user

    try:
        with patch("api.routers.calendar._cache_counter") as mock_cache_counter:
            with patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock) as mock_fetch_events:
                date = timezone.now()
                events = [
                    CalendarEvent(
                        provider="google",
                        id="async123",
                        user_specific_id="async123",
                        title="Async Test",
                        body="Event body",
                        start_time=date,
                        end_time=date + datetime.timedelta(hours=1),
                        all_day=False,
                        participants=[],
                        meeting_urls=["https://example.com"],
                    )
                ]
                mock_fetch_events.return_value = (events, True)

                async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as client:
                    response = await client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")
                    assert response.status_code == 200

                    mock_cache_counter.add.assert_not_called()

                    mock_cache_counter.reset_mock()

                    response = await client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")
                    assert response.status_code == 200

                    mock_cache_counter.add.assert_called_once_with(1)
    finally:
        app.dependency_overrides = {}


@pytest.mark.asyncio
async def test_async_cache_hit_after_successful_fetch(settings: SettingsWrapper, django_user_model: User) -> None:
    """Test that the cache is properly used after a successful fetch and metrics are incremented."""
    cache.clear()
    settings.CALENDAR_EVENT_CACHE_TTL_SECONDS = 0.1

    user = await django_user_model.objects.acreate(
        username="<EMAIL>", email="<EMAIL>"
    )

    app.dependency_overrides[user_from_authorization_header] = lambda: user

    try:
        with patch("api.routers.calendar._cache_counter") as mock_cache_counter:
            with patch("api.routers.calendar.fetch_calendar_events", new_callable=AsyncMock) as mock_fetch_events:
                events = [
                    CalendarEvent(
                        provider="google",
                        id="123",
                        user_specific_id="u123",
                        title="Test meeting",
                        body="Event body",
                        start_time=timezone.now(),
                        end_time=timezone.now() + datetime.timedelta(hours=1),
                        all_day=False,
                        participants=[],
                        meeting_urls=[],
                    )
                ]

                mock_fetch_events.return_value = (events, True)

                async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as client:
                    response = await client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")
                    assert response.status_code == 200

                    mock_cache_counter.add.assert_not_called()

                    mock_fetch_events.return_value = ([], True)

                    response = await client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")
                    assert response.status_code == 200

                    assert len(response.json()) > 0

                    mock_cache_counter.add.assert_called_once_with(1)

                    await asyncio.sleep(0.2)

                    mock_cache_counter.reset_mock()
                    response = await client.get("/api/v2/calendar/list_events?time_zone=Etc/UTC")
                    assert response.status_code == 200

                    assert len(response.json()) == 0

                    mock_cache_counter.add.assert_not_called()
    finally:
        app.dependency_overrides = {}
