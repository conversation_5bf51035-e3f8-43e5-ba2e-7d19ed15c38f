import datetime
import uuid
from threading import Timer
from typing import Any, Generator
from unittest.mock import AsyncMock, MagicMock, patch

import phonenumbers
import pytest
from fastapi import status
from fastapi.testclient import TestClient
from pytest_django.fixtures import SettingsWrapper

from api.dependencies import user_from_access_token, user_from_authorization_header
from api.internal_api import internal_api as app
from api.routers.bot import handler
from deepinsights.core.integrations.meetingbot.bot_controller import Bo<PERSON>R<PERSON>po<PERSON>, BotStatus
from deepinsights.meetingsapp.models.meeting_bot import MeetingBot
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.phone_number import PhoneNumber
from deepinsights.users.models.user import User

client = TestClient(app)

pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture(autouse=True)
def teardown() -> Generator[Any, None, None]:
    app.dependency_overrides = {}
    yield
    app.dependency_overrides = {}


@pytest.fixture
def test_organization() -> Organization:
    return Organization.objects.create(name="Test Org")


@pytest.fixture
def test_user(django_user_model: User, test_organization: Organization) -> User:
    test_user = django_user_model.objects.create(
        username="<EMAIL>",
        first_name="Test",
        last_name="User",
        organization=test_organization,
    )
    app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.prefetch_related(
        "organization"
    ).get(username="<EMAIL>")
    app.dependency_overrides[user_from_access_token] = lambda: User.objects.prefetch_related("organization").get(
        username="<EMAIL>"
    )
    return test_user


@patch("deepinsights.meetingsapp.models.meeting_bot.RecallBotController")
def test_stream_bot_events_success(mock_bot_controller: MagicMock, test_user: User) -> None:
    bot = MeetingBot.objects.create(
        recall_bot_id="recall_bot_id",
        bot_owner=test_user,
    )

    def close_connection() -> None:
        handler.waiting_to_close = True

    Timer(3, close_connection).start()

    mock_bot_controller.return_value.aget_bot_status = AsyncMock(
        side_effect=[BotStatus.SCHEDULED, BotStatus.IN_WAITING_ROOM, BotStatus.IN_CALL_RECORDING]
    )

    response = client.get(f"/api/v2/bot/{bot.uuid}/events")
    assert response.status_code == status.HTTP_200_OK
    assert response.headers["content-type"] == "text/event-stream; charset=utf-8"
    assert response.text == "data: scheduled\n\ndata: in_waiting_room\n\ndata: in_call_recording\n\n"


def test_stream_bot_events_bot_not_found(test_user: User) -> None:
    response = client.get(f"/api/v2/bot/{uuid.uuid4()}/events")
    assert response.status_code == status.HTTP_404_NOT_FOUND


def test_stream_bot_events_forbidden(test_user: User, django_user_model: User) -> None:
    other_user = django_user_model.objects.create(username="<EMAIL>")
    note = Note.objects.create(note_owner=other_user)
    bot = MeetingBot.objects.create(recall_bot_id="recall_bot_id", note=note)

    response = client.get(f"/api/v2/bot/{bot.uuid}/events")
    assert response.status_code == status.HTTP_403_FORBIDDEN


def test_stream_bot_events_no_recall_bot_id(test_user: User) -> None:
    bot = MeetingBot.objects.create()
    response = client.get(f"/api/v2/bot/{bot.uuid}/events")
    assert response.status_code == status.HTTP_400_BAD_REQUEST


def test_get_bot_success(test_user: User) -> None:
    bot = MeetingBot.objects.create(
        recall_bot_id="recall_bot_id", bot_owner=test_user, meeting_link="http://example.com"
    )
    with patch.object(MeetingBot, "aget_status") as mock_get_status:
        mock_get_status.return_value = BotStatus.IN_CALL_RECORDING
        response = client.get(f"/api/v2/bot/{bot.uuid}")
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {
        "uuid": str(bot.uuid),
        "platform_id": "recall_bot_id",
        "meeting_link": "http://example.com",
        "status": "in_call_recording",
        "supports_pause_resume": True,
        "meeting_type": "video_call",
    }


def test_get_bot_not_found(test_user: User) -> None:
    response = client.get(f"/api/v2/bot/{uuid.uuid4()}")
    assert response.status_code == status.HTTP_404_NOT_FOUND


def test_get_bot_allowed_if_no_note(test_user: User) -> None:
    bot = MeetingBot.objects.create()

    response = client.get(f"/api/v2/bot/{bot.uuid}")
    assert response.status_code == status.HTTP_200_OK


def test_get_bot_not_allowed_if_bot_owner(test_user: User, django_user_model: User) -> None:
    other_user = django_user_model.objects.create(username="<EMAIL>")
    note = Note.objects.create(note_owner=other_user)

    bot = MeetingBot.objects.create(bot_owner=test_user, note=note)

    response = client.get(f"/api/v2/bot/{bot.uuid}")
    assert response.status_code == status.HTTP_403_FORBIDDEN


def test_get_bot_allowed_if_note_authorized_user(test_user: User, django_user_model: User) -> None:
    other_user = django_user_model.objects.create(username="<EMAIL>")
    note = Note.objects.create(note_owner=other_user)
    note.authorized_users.add(test_user)
    note.save()

    bot = MeetingBot.objects.create(note=note)

    response = client.get(f"/api/v2/bot/{bot.uuid}")
    assert response.status_code == status.HTTP_200_OK


def test_get_bot_allowed_if_superuser(test_user: User, django_user_model: User) -> None:
    other_user = django_user_model.objects.create(username="<EMAIL>")
    note = Note.objects.create(note_owner=other_user)
    test_user.is_superuser = True
    test_user.save()

    bot = MeetingBot.objects.create(note=note)

    response = client.get(f"/api/v2/bot/{bot.uuid}")
    assert response.status_code == status.HTTP_200_OK


@pytest.mark.parametrize(
    "license_type, allowed",
    [
        (User.LicenseType.staff, True),
        (User.LicenseType.csa, True),
        (User.LicenseType.advisor, False),
    ],
)
def test_get_bot_allowed_license_type(
    test_user: User, django_user_model: User, license_type: User.LicenseType, allowed: bool
) -> None:
    other_user = django_user_model.objects.create(username="<EMAIL>", organization=test_user.organization)
    note = Note.objects.create(note_owner=other_user)
    test_user.license_type = license_type
    test_user.save()

    bot = MeetingBot.objects.create(note=note)

    response = client.get(f"/api/v2/bot/{bot.uuid}")
    assert response.status_code == (status.HTTP_200_OK if allowed else status.HTTP_403_FORBIDDEN)


def test_start_meeting_success(test_user: User) -> None:
    bot = MeetingBot.objects.create(bot_owner=test_user)
    with patch.object(MeetingBot, "create_bot_and_start_recording") as mock_create_bot, patch.object(
        MeetingBot, "aget_status"
    ) as mock_aget_status:
        mock_aget_status.return_value = BotStatus.UNKNOWN
        response = client.post(f"/api/v2/bot/{bot.uuid}/start")
        assert response.status_code == status.HTTP_200_OK
        mock_create_bot.assert_called_once()
        mock_aget_status.assert_called_once()


def test_start_meeting_bot_not_found(test_user: User) -> None:
    with patch.object(MeetingBot, "create_bot_and_start_recording") as mock_create_bot:
        response = client.post(f"/api/v2/bot/{uuid.uuid4()}/start")
        assert response.status_code == status.HTTP_404_NOT_FOUND
        mock_create_bot.assert_not_called()


def test_start_meeting_forbidden(test_user: User, django_user_model: User) -> None:
    other_user = django_user_model.objects.create(username="<EMAIL>")
    note = Note.objects.create(note_owner=other_user)
    bot = MeetingBot.objects.create(recall_bot_id="recall_bot_id", note=note)

    with patch.object(MeetingBot, "create_bot_and_start_recording") as mock_create_bot:
        response = client.post(f"/api/v2/bot/{bot.uuid}/start")
        assert response.status_code == status.HTTP_403_FORBIDDEN
        mock_create_bot.assert_not_called()


def test_start_meeting_action_failed_recall_bot_exists(test_user: User) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="recall_bot_id", bot_owner=test_user)
    with patch.object(MeetingBot, "create_bot_and_start_recording") as mock_create_bot, patch.object(
        MeetingBot, "aget_status"
    ) as mock_aget_status:
        response = client.post(f"/api/v2/bot/{bot.uuid}/start")
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        mock_create_bot.assert_not_called()
        mock_aget_status.assert_not_called()


def test_start_meeting_action_failed_bot_not_created(test_user: User) -> None:
    bot = MeetingBot.objects.create(bot_owner=test_user)
    with patch.object(MeetingBot, "create_bot_and_start_recording") as mock_create_bot, patch.object(
        MeetingBot, "aget_status"
    ) as mock_aget_status:

        def set_bot_id() -> None:
            bot.recall_bot_id = "recall_bot_id"
            bot.save()

        mock_create_bot.side_effect = set_bot_id
        mock_aget_status.return_value = BotStatus.NOT_CREATED
        response = client.post(f"/api/v2/bot/{bot.uuid}/start")
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


def test_pause_recording_success(test_user: User) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="recall_bot_id", bot_owner=test_user)
    with patch.object(MeetingBot, "pause_recording") as mock_pause_recording:
        mock_pause_recording.return_value = MagicMock(status=status.HTTP_200_OK)
        response = client.post(f"/api/v2/bot/{bot.uuid}/pause")
        assert response.status_code == status.HTTP_200_OK
        mock_pause_recording.assert_called_once()


def test_pause_recording_bot_not_found(test_user: User) -> None:
    with patch.object(MeetingBot, "pause_recording") as mock_pause_recording:
        response = client.post(f"/api/v2/bot/{uuid.uuid4()}/pause")
        assert response.status_code == status.HTTP_404_NOT_FOUND
        mock_pause_recording.assert_not_called()


def test_pause_recording_forbidden(test_user: User, django_user_model: User) -> None:
    other_user = django_user_model.objects.create(username="<EMAIL>")
    note = Note.objects.create(note_owner=other_user)
    bot = MeetingBot.objects.create(recall_bot_id="recall_bot_id", note=note)

    with patch.object(MeetingBot, "pause_recording") as mock_pause_recording:
        response = client.post(f"/api/v2/bot/{bot.uuid}/pause")
        assert response.status_code == status.HTTP_403_FORBIDDEN
        mock_pause_recording.assert_not_called()


def test_pause_recording_action_failed(test_user: User) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="recall_bot_id", bot_owner=test_user)
    with patch.object(MeetingBot, "pause_recording") as mock_pause_recording:
        mock_pause_recording.return_value = MagicMock(status=status.HTTP_500_INTERNAL_SERVER_ERROR, details="Error")
        response = client.post(f"/api/v2/bot/{bot.uuid}/pause")
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


def test_resume_recording_success(test_user: User) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="recall_bot_id", bot_owner=test_user)
    with patch.object(MeetingBot, "resume_recording") as mock_resume_recording:
        mock_resume_recording.return_value = MagicMock(status=status.HTTP_200_OK)
        response = client.post(f"/api/v2/bot/{bot.uuid}/resume")
        assert response.status_code == status.HTTP_200_OK
        mock_resume_recording.assert_called_once()


def test_resume_recording_bot_not_found(test_user: User) -> None:
    with patch.object(MeetingBot, "resume_recording") as mock_resume_recording:
        response = client.post(f"/api/v2/bot/{uuid.uuid4()}/resume")
        assert response.status_code == status.HTTP_404_NOT_FOUND
        mock_resume_recording.assert_not_called()


def test_resume_recording_forbidden(test_user: User, django_user_model: User) -> None:
    other_user = django_user_model.objects.create(username="<EMAIL>")
    note = Note.objects.create(note_owner=other_user)
    bot = MeetingBot.objects.create(recall_bot_id="recall_bot_id", note=note)

    with patch.object(MeetingBot, "resume_recording") as mock_resume_recording:
        response = client.post(f"/api/v2/bot/{bot.uuid}/resume")
        assert response.status_code == status.HTTP_403_FORBIDDEN
        mock_resume_recording.assert_not_called()


def test_resume_recording_action_failed(test_user: User) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="recall_bot_id", bot_owner=test_user)
    with patch.object(MeetingBot, "resume_recording") as mock_resume_recording:
        mock_resume_recording.return_value = MagicMock(status=status.HTTP_500_INTERNAL_SERVER_ERROR, details="Error")
        response = client.post(f"/api/v2/bot/{bot.uuid}/resume")
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


def test_leave_meeting_success(test_user: User) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="recall_bot_id", bot_owner=test_user)
    with patch.object(MeetingBot, "leave_meeting") as mock_leave_meeting:
        mock_leave_meeting.return_value = MagicMock(status=status.HTTP_200_OK)
        response = client.post(f"/api/v2/bot/{bot.uuid}/leave")
        assert response.status_code == status.HTTP_200_OK
        mock_leave_meeting.assert_called_once()


def test_leave_meeting_bot_not_found(test_user: User) -> None:
    with patch.object(MeetingBot, "leave_meeting") as mock_leave_meeting:
        response = client.post(f"/api/v2/bot/{uuid.uuid4()}/leave")
        assert response.status_code == status.HTTP_404_NOT_FOUND
        mock_leave_meeting.assert_not_called()


def test_leave_meeting_forbidden(test_user: User, django_user_model: User) -> None:
    other_user = django_user_model.objects.create(username="<EMAIL>")
    note = Note.objects.create(note_owner=other_user)
    bot = MeetingBot.objects.create(recall_bot_id="recall_bot_id", note=note)

    with patch.object(MeetingBot, "leave_meeting") as mock_leave_meeting:
        response = client.post(f"/api/v2/bot/{bot.uuid}/leave")
        assert response.status_code == status.HTTP_403_FORBIDDEN
        mock_leave_meeting.assert_not_called()


def test_leave_meeting_action_failed(test_user: User) -> None:
    bot = MeetingBot.objects.create(recall_bot_id="recall_bot_id", bot_owner=test_user)
    with patch.object(MeetingBot, "leave_meeting") as mock_leave_meeting:
        mock_leave_meeting.return_value = MagicMock(status=status.HTTP_500_INTERNAL_SERVER_ERROR, details="Error")
        response = client.post(f"/api/v2/bot/{bot.uuid}/leave")
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


@pytest.mark.parametrize(
    "meeting_link",
    ["+12125551212", "", "https://zoom.us/meeting"],
)
@patch("deepinsights.meetingsapp.models.meeting_bot.TwilioConferenceBotController")
@patch("deepinsights.meetingsapp.models.meeting_bot.RecallBotController")
@patch("deepinsights.meetingsapp.models.meeting_bot.TwilioCallBotController")
def test_bot_action_async_handling(
    mock_call_controller: MagicMock,
    mock_recall_bot_controller: MagicMock,
    mock_conference_controller: MagicMock,
    meeting_link: str,
    test_user: User,
) -> None:
    for mock_controller in [mock_call_controller, mock_recall_bot_controller, mock_conference_controller]:
        mock_controller.return_value.leave_call.return_value = BotResponse(status=status.HTTP_200_OK, details={})
    bot = MeetingBot.objects.create(recall_bot_id="recall_bot_id", bot_owner=test_user, meeting_link=meeting_link)
    response = client.post(f"/api/v2/bot/{bot.uuid}/leave")
    assert response.status_code == status.HTTP_200_OK


@pytest.mark.parametrize(
    "form_data",
    [
        {"RecordingSid": "recording_sid", "RecordingStatus": "completed"},
        {"RecordingSid": "recording_sid", "ConferenceSid": "recall_bot_id"},
        {"RecordingSid": "recording_sid", "CallSid": "recall_bot_id"},
        {"ConferenceSid": "recall_bot_id", "RecordingStatus": "completed"},
        {"CallSid": "recall_bot_id", "RecordingStatus": "completed"},
    ],
)
def test_handle_twilio_recording_event_invalid_payload(form_data: dict[str, str]) -> None:
    assert client.post("/api/v2/bot/twilio/recording", data=form_data).status_code == status.HTTP_400_BAD_REQUEST


def test_handle_twilio_recording_event_status_not_completed() -> None:
    form_data = {"RecordingSid": "recording_sid", "ConferenceSid": "recall_bot_id", "RecordingStatus": "in_progress"}

    response = client.post("/api/v2/bot/twilio/recording", data=form_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not response.text


def test_handle_twilio_recording_event_bot_not_found(test_user: User) -> None:
    form_data = {"RecordingSid": "recording_sid", "ConferenceSid": "recall_bot_id", "RecordingStatus": "completed"}

    response = client.post("/api/v2/bot/twilio/recording", data=form_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not response.text


def test_handle_twilio_recording_event_bot_has_no_note(test_user: User) -> None:
    MeetingBot.objects.create(recall_bot_id="recall_bot_id", bot_owner=test_user)

    form_data = {"RecordingSid": "recording_sid", "ConferenceSid": "recall_bot_id", "RecordingStatus": "completed"}

    response = client.post("/api/v2/bot/twilio/recording", data=form_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not response.text


@patch("api.routers.bot.process_phone_call_recording")
def test_handle_twilio_recording_event_success(mock_process_phone_call_recording: MagicMock, test_user: User) -> None:
    note = Note.objects.create(note_owner=test_user)
    bot = MeetingBot.objects.create(recall_bot_id="recall_bot_id", bot_owner=test_user, note=note)

    form_data = {"RecordingSid": "recording_sid", "ConferenceSid": "recall_bot_id", "RecordingStatus": "completed"}

    response = client.post("/api/v2/bot/twilio/recording", data=form_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not response.text
    bot.refresh_from_db()
    assert bot.note
    assert bot.note.status == Note.PROCESSING_STATUS.uploaded
    mock_process_phone_call_recording.delay.assert_called_once_with(bot.uuid, False)


@pytest.mark.parametrize(
    "form_data",
    [
        {"StatusCallbackEvent": "conference-end"},
        {"ConferenceSid": "recall_bot_id"},
    ],
)
def test_handle_twilio_conference_event_no_conference_sid(form_data: dict[str, str]) -> None:
    assert client.post("/api/v2/bot/twilio/conference", data=form_data).status_code == status.HTTP_400_BAD_REQUEST


def test_handle_twilio_conference_event_bot_not_found() -> None:
    form_data = {"ConferenceSid": "non_existent_sid", "StatusCallbackEvent": "conference-end"}

    response = client.post("/api/v2/bot/twilio/conference", data=form_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not response.text


@pytest.mark.parametrize(
    "form_data",
    [
        {"ConferenceSid": "non_existent_sid", "StatusCallbackEvent": "other"},
        {"ConferenceSid": "non_existent_sid", "StatusCallbackEvent": "invalid"},
    ],
)
def test_handle_twilio_conference_event_unhandled_event(form_data: dict[str, str]) -> None:
    response = client.post("/api/v2/bot/twilio/conference", data=form_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not response.text


@patch("api.routers.bot.TwilioConferenceBotController")
def test_handle_twilio_conference_event_empty_meeting_link(mock_bot_controller: MagicMock, test_user: User) -> None:
    note = Note.objects.create(note_owner=test_user)
    MeetingBot.objects.create(recall_bot_id="recall_bot_id", bot_owner=test_user, note=note)

    form_data = {"ConferenceSid": "recall_bot_id", "StatusCallbackEvent": "participant-join"}

    response = client.post("/api/v2/bot/twilio/conference", data=form_data)
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    mock_bot_controller.return_value.add_participant_if_not_in_call.assert_not_called()


@patch("api.routers.bot.TwilioConferenceBotController")
def test_handle_twilio_conference_event_invalid_phone_number(mock_bot_controller: MagicMock, test_user: User) -> None:
    note = Note.objects.create(note_owner=test_user)
    MeetingBot.objects.create(
        recall_bot_id="recall_bot_id", bot_owner=test_user, note=note, meeting_link="invalid_phone"
    )

    form_data = {"ConferenceSid": "recall_bot_id", "StatusCallbackEvent": "participant-join"}

    response = client.post("/api/v2/bot/twilio/conference", data=form_data)
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    mock_bot_controller.return_value.add_participant_if_not_in_call.assert_not_called()


def test_handle_twilio_conference_event_success_conference_end(test_user: User) -> None:
    note = Note.objects.create(note_owner=test_user)
    bot = MeetingBot.objects.create(recall_bot_id="recall_bot_id", bot_owner=test_user, note=note)

    form_data = {"ConferenceSid": "recall_bot_id", "StatusCallbackEvent": "conference-end"}

    response = client.post("/api/v2/bot/twilio/conference", data=form_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not response.text
    bot.refresh_from_db()
    assert bot.note
    assert bot.note.status == Note.PROCESSING_STATUS.uploaded


@patch("api.routers.bot.TwilioConferenceBotController")
def test_handle_twilio_conference_event_bot_has_no_note_conference_end(
    mock_bot_controller: MagicMock, test_user: User
) -> None:
    MeetingBot.objects.create(recall_bot_id="recall_bot_id", bot_owner=test_user)

    form_data = {"ConferenceSid": "recall_bot_id", "StatusCallbackEvent": "conference-end"}

    response = client.post("/api/v2/bot/twilio/conference", data=form_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not response.text
    mock_bot_controller.return_value.add_participant_if_not_in_call.assert_not_called()


@patch("api.routers.bot.TwilioConferenceBotController")
def test_handle_twilio_conference_event_failure_participant_join(
    mock_bot_controller: MagicMock, test_user: User
) -> None:
    note = Note.objects.create(note_owner=test_user)
    MeetingBot.objects.create(
        recall_bot_id="recall_bot_id", bot_owner=test_user, note=note, meeting_link="+12125551212"
    )

    form_data = {"ConferenceSid": "recall_bot_id", "StatusCallbackEvent": "participant-join"}

    mock_bot_controller.return_value.add_participant_if_not_in_call = AsyncMock(side_effect=Exception("Test exception"))
    response = client.post("/api/v2/bot/twilio/conference", data=form_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not response.text
    mock_bot_controller.return_value.add_participant_if_not_in_call.assert_called_once_with(
        phonenumbers.parse("2125551212", "US"),
        None,
    )
    mock_bot_controller.return_value.make_recording_announcement.assert_not_called()


@patch("api.routers.bot.TwilioConferenceBotController")
def test_handle_twilio_conference_event_success_participant_join(
    mock_bot_controller: MagicMock, test_user: User
) -> None:
    note = Note.objects.create(note_owner=test_user)
    MeetingBot.objects.create(
        recall_bot_id="recall_bot_id", bot_owner=test_user, note=note, meeting_link="+12125551212"
    )

    form_data = {"ConferenceSid": "recall_bot_id", "StatusCallbackEvent": "participant-join"}

    mock_bot_controller.return_value.add_participant_if_not_in_call = AsyncMock(return_value=True)
    response = client.post("/api/v2/bot/twilio/conference", data=form_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not response.text
    mock_bot_controller.return_value.add_participant_if_not_in_call.assert_called_once_with(
        phonenumbers.parse("2125551212", "US"),
        None,
    )
    mock_bot_controller.return_value.make_recording_announcement.assert_not_called()


@patch("api.routers.bot.TwilioConferenceBotController")
def test_handle_twilio_conference_event_success_participant_join_organization_phone_number(
    mock_bot_controller: MagicMock, test_user: User, test_organization: Organization
) -> None:
    test_organization.phone_number = "+12125551213"
    test_organization.save()
    note = Note.objects.create(note_owner=test_user)
    MeetingBot.objects.create(
        recall_bot_id="recall_bot_id", bot_owner=test_user, note=note, meeting_link="+12125551212"
    )

    form_data = {"ConferenceSid": "recall_bot_id", "StatusCallbackEvent": "participant-join"}

    mock_bot_controller.return_value.add_participant_if_not_in_call = AsyncMock(return_value=True)
    response = client.post("/api/v2/bot/twilio/conference", data=form_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not response.text
    mock_bot_controller.return_value.add_participant_if_not_in_call.assert_called_once_with(
        phonenumbers.parse("2125551212", "US"),
        phonenumbers.parse("2125551213", "US"),
    )


@patch("api.routers.bot.TwilioConferenceBotController")
def test_handle_twilio_conference_event_success_participant_already_in_call(
    mock_bot_controller: MagicMock, test_user: User
) -> None:
    note = Note.objects.create(note_owner=test_user)
    MeetingBot.objects.create(
        recall_bot_id="recall_bot_id", bot_owner=test_user, note=note, meeting_link="+12125551212"
    )

    form_data = {"ConferenceSid": "recall_bot_id", "StatusCallbackEvent": "participant-join"}

    mock_bot_controller.return_value.add_participant_if_not_in_call = AsyncMock(return_value=False)
    mock_bot_controller.return_value.make_recording_announcement = AsyncMock(return_value=None)
    response = client.post("/api/v2/bot/twilio/conference", data=form_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not response.text
    mock_bot_controller.return_value.add_participant_if_not_in_call.assert_called_once_with(
        phonenumbers.parse("2125551212", "US"),
        None,
    )
    mock_bot_controller.return_value.make_recording_announcement.assert_called_once_with(
        phonenumbers.parse("2125551212", "US")
    )


@pytest.mark.parametrize(
    "form_data",
    [
        {"CallStatus": "completed"},
        {"CallSid": "call_sid"},
        {"CallSid": "call_sid", "CallStatus": "ringing", "Direction": "inbound", "From": ""},
    ],
)
def test_handle_twilio_call_event_invalid_payload(form_data: dict[str, str]) -> None:
    response = client.post("/api/v2/bot/twilio/call", data=form_data)
    assert response.status_code == status.HTTP_400_BAD_REQUEST


def test_handle_twilio_call_event_no_matching_user_or_org(test_user: User) -> None:
    form_data = {"CallSid": "call_sid", "CallStatus": "ringing", "Direction": "inbound", "From": "+12125551212"}
    response = client.post("/api/v2/bot/twilio/call", data=form_data)
    assert response.status_code == status.HTTP_200_OK
    assert response.text == '<?xml version="1.0" encoding="UTF-8"?><Response><Reject /></Response>'


def test_handle_twilio_call_event_multiple_matching_orgs(test_user: User, test_organization: Organization) -> None:
    test_organization.phone_number = "+12125551212"
    test_organization.save()

    org_two = Organization.objects.create(name="Test Org 2")
    org_two.phone_number = "+12125551212"
    org_two.save()

    form_data = {"CallSid": "call_sid", "CallStatus": "ringing", "Direction": "inbound", "From": "+12125551212"}
    response = client.post("/api/v2/bot/twilio/call", data=form_data)
    assert response.status_code == status.HTTP_200_OK
    assert response.text == '<?xml version="1.0" encoding="UTF-8"?><Response><Reject /></Response>'


def test_handle_twilio_call_event_matching_orgs(
    test_user: User, test_organization: Organization, settings: SettingsWrapper
) -> None:
    settings.WEBHOOK_DOMAIN = "http://example.com"
    test_organization.phone_number = "+12125551212"
    test_organization.save()

    form_data = {"CallSid": "call_sid", "CallStatus": "ringing", "Direction": "inbound", "From": "+12125551212"}
    response = client.post("/api/v2/bot/twilio/call", data=form_data)
    assert response.status_code == status.HTTP_200_OK
    assert response.text == (
        '<?xml version="1.0" encoding="UTF-8"?>'
        "<Response>"
        '<Gather action="http://example.com/api/v2/bot/twilio/pin" finishOnKey="#" method="POST" timeout="20">'
        "<Say>Please enter your PIN, followed by the pound sign.</Say>"
        "</Gather>"
        "<Hangup />"
        "</Response>"
    )


def test_handle_twilio_call_event_create_note_and_bot(test_user: User, settings: SettingsWrapper) -> None:
    settings.WEBHOOK_DOMAIN = "http://example.com"
    test_user.name = "Test User"
    test_user.phone_numbers.add(PhoneNumber.objects.create(number="+12125551212"))
    test_user.save()

    form_data = {"CallSid": "call_sid", "CallStatus": "ringing", "Direction": "inbound", "From": "+12125551212"}
    response = client.post("/api/v2/bot/twilio/call", data=form_data)

    assert response.status_code == status.HTTP_200_OK
    assert response.text == (
        '<?xml version="1.0" encoding="UTF-8"?>'
        "<Response>"
        "<Say />"
        '<Pause length="8" />'
        "<Say>This meeting is being transcribed.</Say>"
        "<Record "
        'recordingStatusCallback="http://example.com/api/v2/bot/twilio/recording" '
        'recordingStatusCallbackEvent="completed" '
        'timeout="7200" />'
        "</Response>"
    )

    note = Note.objects.first()
    assert note
    assert list(note.authorized_users.values("id")) == [{"id": test_user.id}]
    assert list(note.attendees.values("user", "attendee_name")) == [
        {"user": test_user.id, "attendee_name": "Test User"}
    ]
    assert Note.objects.values("note_owner", "note_type", "status", "metadata", "meeting_type").first() == {
        "note_owner": test_user.id,
        "note_type": Note.NOTE_TYPE.meeting_recording,
        "status": Note.PROCESSING_STATUS.scheduled,
        "metadata": {
            "meeting_name": "Recorded phone call",
        },
        "meeting_type": MeetingType.objects.get(key="client").id,
    }
    assert note.created < datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(seconds=1)


def test_handle_twilio_call_event_create_note_and_bot_multiple_users(
    test_user: User, django_user_model: User, settings: SettingsWrapper
) -> None:
    settings.WEBHOOK_DOMAIN = "http://example.com"

    test_user.name = "Test User"
    test_user.phone_numbers.add(PhoneNumber.objects.create(number="+12125551212"))
    test_user.phone_numbers.add(PhoneNumber.objects.create(number="+12125551213"))
    test_user.save()

    user_two = django_user_model.objects.create(
        username="test2", organization=test_user.organization, name="Test User 2"
    )
    user_two.phone_numbers.add(PhoneNumber.objects.create(number="+12125551212"))
    user_two.save()

    user_three = django_user_model.objects.create(
        username="test3", organization=test_user.organization, name="Test User 3"
    )
    user_three.phone_numbers.add(PhoneNumber.objects.create(number="+12125551212", primary=True))
    unmatched_user = django_user_model.objects.create(
        username="test4", organization=test_user.organization, name="Test User 4"
    )
    unmatched_user.phone_numbers.add(PhoneNumber.objects.create(number="+12125551214"))
    unmatched_user.save()

    form_data = {"CallSid": "call_sid", "CallStatus": "ringing", "Direction": "inbound", "From": "+12125551212"}
    response = client.post("/api/v2/bot/twilio/call", data=form_data)
    assert response.status_code == status.HTTP_200_OK

    assert (note := Note.objects.first())
    assert note.note_owner == test_user
    assert set([u["id"] for u in note.authorized_users.values("id")]) == set([test_user.id, user_two.id, user_three.id])
    assert list(note.attendees.values("user", "attendee_name")) == [
        {"user": test_user.id, "attendee_name": "Test User"},
        {"user": user_two.id, "attendee_name": "Test User 2"},
        {"user": user_three.id, "attendee_name": "Test User 3"},
    ]


def test_handle_twilio_call_event_call_not_completed() -> None:
    form_data = {"CallSid": "call_sid", "CallStatus": "in_progress"}
    response = client.post("/api/v2/bot/twilio/call", data=form_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not response.text


def test_handle_twilio_call_outbound_completed_event_bot_not_found() -> None:
    form_data = {"CallSid": "call_sid", "CallStatus": "completed"}
    response = client.post("/api/v2/bot/twilio/call", data=form_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not response.text


def test_handle_twilio_call_outbound_completed_event_bot_has_no_note(test_user: User) -> None:
    MeetingBot.objects.create(recall_bot_id="call_sid", bot_owner=test_user)

    form_data = {"CallSid": "call_sid", "CallStatus": "completed"}
    response = client.post("/api/v2/bot/twilio/call", data=form_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not response.text


def test_handle_twilio_call_outbound_completed_event_success(test_user: User) -> None:
    note = Note.objects.create(note_owner=test_user)
    bot = MeetingBot.objects.create(recall_bot_id="call_sid", bot_owner=test_user, note=note)

    form_data = {"CallSid": "call_sid", "CallStatus": "completed"}
    response = client.post("/api/v2/bot/twilio/call", data=form_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    assert not response.text
    bot.refresh_from_db()
    assert bot.note
    assert bot.note.status == Note.PROCESSING_STATUS.uploaded


def test_handle_twilio_participant_join_event_success() -> None:
    response = client.post("/api/v2/bot/twilio/on_participant_join")
    assert response.status_code == status.HTTP_200_OK
    assert response.headers["content-type"] == "text/xml; charset=utf-8"
    assert response.text == (
        '<?xml version="1.0" encoding="UTF-8"?>'
        "<Response>"
        '<Pause length="8" />'
        "<Say>This meeting is being transcribed.</Say>"
        "</Response>"
    )


@pytest.mark.parametrize(
    "form_data, expected_status",
    [
        ({}, status.HTTP_400_BAD_REQUEST),
        ({"CallSid": "call_sid"}, status.HTTP_400_BAD_REQUEST),
        ({"CallSid": "call_sid", "CallStatus": "ringing"}, status.HTTP_400_BAD_REQUEST),
        ({"CallSid": "call_sid", "CallStatus": "ringing", "From": "+12125551212"}, status.HTTP_204_NO_CONTENT),
        (
            {"CallSid": "call_sid", "CallStatus": "ringing", "From": "+12125551212", "Direction": "outbound"},
            status.HTTP_204_NO_CONTENT,
        ),
        (
            {
                "CallSid": "call_sid",
                "CallStatus": "completed",
                "From": "+12125551212",
                "Direction": "inbound",
            },
            status.HTTP_204_NO_CONTENT,
        ),
        (
            {
                "CallSid": "call_sid",
                "CallStatus": "ringing",
                "From": "+12125551212",
                "Direction": "inbound",
            },
            status.HTTP_204_NO_CONTENT,
        ),
    ],
)
def test_handle_pin_validation(form_data: dict[str, str], expected_status: int) -> None:
    response = client.post("/api/v2/bot/twilio/pin", data=form_data)
    assert response.status_code == expected_status
    assert not response.text


def test_handle_pin_organization_not_found() -> None:
    form_data = {
        "CallSid": "call_sid",
        "CallStatus": "ringing",
        "Digits": "1234",
        "Direction": "inbound",
        "From": "+12125551212",
    }

    response = client.post("/api/v2/bot/twilio/pin", data=form_data)
    assert response.status_code == status.HTTP_200_OK
    assert (
        response.text
        == '<?xml version="1.0" encoding="UTF-8"?><Response><Say>We\'re sorry, but we could not recognize that PIN. Please contact support.</Say><Hangup /></Response>'
    )


@patch("api.routers.bot._start_recording_call_for_user")
def test_handle_pin_user_not_found(mock_start_recording: AsyncMock, test_organization: Organization) -> None:
    test_organization.phone_number = "+12125551212"
    test_organization.save()

    form_data = {
        "CallSid": "call_sid",
        "CallStatus": "ringing",
        "Digits": "1234",
        "Direction": "inbound",
        "From": "+12125551212",
    }

    response = client.post("/api/v2/bot/twilio/pin", data=form_data)
    assert response.status_code == status.HTTP_200_OK
    assert (
        response.text
        == '<?xml version="1.0" encoding="UTF-8"?><Response><Say>We\'re sorry, but we could not recognize that PIN. Please redial and try again.</Say><Hangup /></Response>'
    )
    mock_start_recording.assert_not_called()


def test_handle_pin_multiple_users(test_organization: Organization, test_user: User, django_user_model: User) -> None:
    test_organization.phone_number = "+12125551212"
    test_organization.save()

    test_user.pin = "1234"
    test_user.save()

    django_user_model.objects.create(
        username="<EMAIL>",
        organization=test_organization,
        pin=1234,
    )

    form_data = {
        "CallSid": "call_sid",
        "CallStatus": "ringing",
        "Digits": "1234",
        "Direction": "inbound",
        "From": "+12125551212",
    }

    response = client.post("/api/v2/bot/twilio/pin", data=form_data)

    assert response.status_code == status.HTTP_200_OK
    assert (
        response.text
        == '<?xml version="1.0" encoding="UTF-8"?><Response><Say>We\'re sorry, but we could not recognize that PIN. Please contact support.</Say><Hangup /></Response>'
    )


def test_handle_pin_invalid_pin(test_organization: Organization) -> None:
    test_organization.phone_number = "+12125551212"
    test_organization.save()

    form_data = {
        "CallSid": "call_sid",
        "CallStatus": "ringing",
        "Digits": "invalid",
        "Direction": "inbound",
        "From": "+12125551212",
    }

    response = client.post("/api/v2/bot/twilio/pin", data=form_data)

    assert response.status_code == status.HTTP_200_OK
    assert (
        response.text
        == '<?xml version="1.0" encoding="UTF-8"?><Response><Say>We\'re sorry, but we could not recognize that PIN. Please contact support.</Say><Hangup /></Response>'
    )


def test_handle_pin_success(test_organization: Organization, test_user: User, settings: SettingsWrapper) -> None:
    settings.WEBHOOK_DOMAIN = "http://example.com"
    test_organization.phone_number = "+12125551212"
    test_organization.save()

    test_user.pin = "1234"
    test_user.name = "Test User"
    test_user.save()

    form_data = {
        "CallSid": "call_sid",
        "CallStatus": "ringing",
        "Digits": "1234",
        "Direction": "inbound",
        "From": "+12125551212",
    }

    response = client.post("/api/v2/bot/twilio/pin", data=form_data)

    assert response.status_code == status.HTTP_200_OK
    assert response.text == (
        '<?xml version="1.0" encoding="UTF-8"?>'
        "<Response>"
        "<Say />"
        '<Pause length="8" />'
        "<Say>This meeting is being transcribed.</Say>"
        "<Record "
        'recordingStatusCallback="http://example.com/api/v2/bot/twilio/recording" '
        'recordingStatusCallbackEvent="completed" '
        'timeout="7200" />'
        "</Response>"
    )

    note = Note.objects.first()
    assert note
    assert list(note.authorized_users.values("id")) == [{"id": test_user.id}]
    assert list(note.attendees.values("user", "attendee_name")) == [
        {"user": test_user.id, "attendee_name": "Test User"}
    ]
    assert Note.objects.values("note_owner", "note_type", "status", "metadata", "meeting_type").first() == {
        "note_owner": test_user.id,
        "note_type": Note.NOTE_TYPE.meeting_recording,
        "status": Note.PROCESSING_STATUS.scheduled,
        "metadata": {
            "meeting_name": "Recorded phone call",
        },
        "meeting_type": MeetingType.objects.get(key="client").id,
    }
    assert note.created < datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(seconds=1)
