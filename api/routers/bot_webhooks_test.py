import logging
import uuid
from datetime import datetime, timedelta
from datetime import timezone as datetime_timezone
from typing import Any, Callable
from unittest.mock import AsyncMock, MagicMock, patch

import pydantic
import pytest
from django.utils import timezone
from fastapi.testclient import TestClient
from httpx import Response

from api.internal_api import internal_api as app
from api.routers.bot_webhooks import __parse_info_from_bot_meeting
from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent, EventParticipant
from deepinsights.core.integrations.calendar.microsoft import MicrosoftCredentials
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.meeting_bot import MeetingBot
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.users.models.user import User

client = TestClient(app)
pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture
def test_organization() -> Organization:
    """Create a test organization"""
    return Organization.objects.create(name="Test Organization")


@pytest.fixture
def test_user(django_user_model: User, test_organization: Organization) -> User:
    """Create a test user"""
    return django_user_model.objects.create(
        username="<EMAIL>",
        first_name="Test",
        last_name="User",
        organization=test_organization,
        license_type=User.LicenseType.advisor,
    )


def test_parse_info_from_bot_meeting_no_raw_event(test_user: User) -> None:
    calendar_event = {"meeting_url": "http://example.com", "platform": "google_calendar", "raw": None}
    attendees, meeting_title, meeting_url, event_id, user_specific_event_id = __parse_info_from_bot_meeting(
        calendar_event, test_user
    )
    assert attendees == []
    assert meeting_title == ""
    assert meeting_url == "http://example.com"
    assert event_id == ""
    assert user_specific_event_id == ""


@patch("api.routers.bot_webhooks.google.calendar_entries_from_events")
def test_parse_info_from_bot_meeting_empty_events(mock_google_calendar_entries: MagicMock, test_user: User) -> None:
    calendar_event = {"meeting_url": "http://example.com", "platform": "google_calendar", "raw": {"id": "event-id"}}
    mock_google_calendar_entries.return_value = []
    attendees, meeting_title, meeting_url, event_id, user_specific_event_id = __parse_info_from_bot_meeting(
        calendar_event, test_user
    )
    assert attendees == []
    assert meeting_title == ""
    assert meeting_url == "http://example.com"
    assert event_id == ""
    assert user_specific_event_id == ""


@patch("api.routers.bot_webhooks.google.calendar_entries_from_events")
@patch("api.routers.bot_webhooks.events_with_zeplyn_attendees")
def test_parse_info_from_bot_meeting_google_calendar(
    mock_events_with_zeplyn_attendees: MagicMock, mock_google_calendar_entries: MagicMock, test_user: User
) -> None:
    calendar_event = {"meeting_url": "http://example.com", "platform": "google_calendar", "raw": {"id": "event-id"}}
    attendee_uuid = uuid.uuid4()
    mock_google_calendar_entries.return_value = [
        CalendarEvent(
            provider="google",
            id="event-id",
            user_specific_id="user-event-id",
            title="Test Meeting",
            body="Test Body",
            start_time=timezone.now(),
            end_time=timezone.now(),
            all_day=False,
            participants=[EventParticipant(email_address="<EMAIL>", name="One", zeplyn_uuid=attendee_uuid)],
            meeting_urls=[pydantic.HttpUrl("http://example.com")],
        )
    ]
    mock_events_with_zeplyn_attendees.return_value = mock_google_calendar_entries.return_value

    attendees, meeting_title, meeting_url, event_id, user_specific_event_id = __parse_info_from_bot_meeting(
        calendar_event, test_user
    )
    assert meeting_title == "Test Meeting"
    assert meeting_url == "http://example.com"
    assert attendees == [{"name": "One", "uuid": str(attendee_uuid), "type": "unknown"}]
    assert event_id == "event-id"
    assert user_specific_event_id == "user-event-id"

    mock_events_with_zeplyn_attendees.assert_called_once_with(mock_google_calendar_entries.return_value, test_user)


@patch("api.routers.bot_webhooks.microsoft.fetch_calendar_event", new_callable=AsyncMock)
@patch("api.routers.bot_webhooks.MicrosoftOAuth")
def test_parse_info_from_bot_meeting_microsoft_calendar(
    ms_oauth: MagicMock, ms_fetch_calendar_event: MagicMock, test_user: User
) -> None:
    # This is a mock of a real Recall API response for an Outlook calendar event.
    calendar_event = {
        "id": "event-id",
        "start_time": "2024-11-26T00:50:00Z",
        "end_time": "2024-11-26T01:15:00Z",
        "calendar_id": "calendar-id",
        "raw": {
            "id": "ms-event-id",
            "end": {"dateTime": "2024-11-26T01:15:00.0000000", "timeZone": "UTC"},
            "uid": "uid",
            "body": {
                "content": "<html></html>",
                "contentType": "html",
            },
            "type": "singleInstance",
            "start": {"dateTime": "2024-11-26T00:50:00.0000000", "timeZone": "UTC"},
            "showAs": "busy",
            "iCalUId": "ical-id",
            "isDraft": False,
            "subject": "Test Meeting",
            "webLink": "http://example.com",
            "isAllDay": False,
            "location": {
                "uniqueId": "Microsoft Teams Meeting",
                "displayName": "Microsoft Teams Meeting",
                "locationType": "default",
                "uniqueIdType": "private",
            },
            "attendees": [
                {
                    "type": "required",
                    "status": {"time": "0001-01-01T00:00:00Z", "response": "none"},
                    "emailAddress": {"name": "<EMAIL>", "address": "<EMAIL>"},
                },
                {
                    "type": "required",
                    "status": {"time": "0001-01-01T00:00:00Z", "response": "none"},
                    "emailAddress": {"name": "<EMAIL>", "address": "<EMAIL>"},
                },
            ],
            "changeKey": "change-key",
            "locations": [
                {
                    "uniqueId": "Microsoft Teams Meeting",
                    "displayName": "Microsoft Teams Meeting",
                    "locationType": "default",
                    "uniqueIdType": "private",
                }
            ],
            "organizer": {"emailAddress": {"name": "User", "address": "<EMAIL>"}},
            "categories": [],
            "importance": "normal",
            "@odata.etag": 'W/"etag"',
            "@odata.type": "#microsoft.graph.event",
            "bodyPreview": "",
            "isCancelled": False,
            "isOrganizer": True,
            "sensitivity": "normal",
            "isReminderOn": False,
            "occurrenceId": None,
            "hideAttendees": False,
            "onlineMeeting": {"joinUrl": "https://example.com/meeting_url"},
            "transactionId": "transaction_id",
            "hasAttachments": False,
            "responseStatus": {"time": "0001-01-01T00:00:00Z", "response": "organizer"},
            "seriesMasterId": None,
            "createdDateTime": "2024-11-26T00:48:30.4979876Z",
            "isOnlineMeeting": True,
            "onlineMeetingUrl": None,
            "responseRequested": True,
            "originalEndTimeZone": "Pacific Standard Time",
            "lastModifiedDateTime": "2024-11-26T00:48:36.2129328Z",
            "allowNewTimeProposals": True,
            "onlineMeetingProvider": "teamsForBusiness",
            "originalStartTimeZone": "Pacific Standard Time",
            "reminderMinutesBeforeStart": 15,
        },
        "platform": "microsoft_outlook",
        "platform_id": "ms-event-id",
        "ical_uid": "ical-id",
        "meeting_platform": "microsoft_teams",
        "meeting_url": "http://example.com/meeting_url",
        "created_at": "2024-11-26T00:48:33.893143Z",
        "updated_at": "2024-11-26T00:48:36.461466Z",
        "is_deleted": False,
        "bots": [
            {
                "bot_id": "bot-id",
                "start_time": "2024-11-26T00:50:00Z",
                "deduplication_key": "deduplication_key",
                "meeting_url": "http://example.com/meeting_url",
            }
        ],
    }

    parsed_event = CalendarEvent(
        provider="microsoft",
        id="ms-event-ical-id",
        user_specific_id="ms-event-id",
        title="Test Meeting",
        body="Test Body",
        start_time=datetime(2024, 11, 26, 0, 50, tzinfo=datetime_timezone.utc),
        end_time=datetime(2024, 11, 26, 1, 15, tzinfo=datetime_timezone.utc),
        all_day=False,
        participants=[
            EventParticipant(email_address="<EMAIL>"),
            EventParticipant(email_address="<EMAIL>"),
        ],
        meeting_urls=[pydantic.HttpUrl("http://example.com/meeting_url")],
    )

    ms_oauth.return_value.get_access_token = AsyncMock(return_value="access_token")
    expiry_time = timezone.now() + timedelta(minutes=1)
    ms_oauth.return_value.get_expiry = AsyncMock(return_value=expiry_time)
    ms_fetch_calendar_event.return_value = parsed_event

    attendees, meeting_title, meeting_url, event_id, user_specific_event_id = __parse_info_from_bot_meeting(
        calendar_event, test_user
    )
    assert meeting_title == "Test Meeting"
    assert meeting_url == "http://example.com/meeting_url"
    # UUIDs are generated and are unpredictable, but they should exist.
    assert all([attendees.pop("uuid", None) for attendees in attendees])
    assert attendees == [
        {"name": "<EMAIL>", "type": "unknown"},
        {"name": "<EMAIL>", "type": "unknown"},
    ]
    assert event_id == "ms-event-ical-id"
    assert user_specific_event_id == "ms-event-id"

    ms_fetch_calendar_event.assert_called_once_with(
        MicrosoftCredentials.from_access_token("access_token", int(expiry_time.timestamp())),
        "ms-event-id",
    )


@patch("api.routers.bot_webhooks.microsoft.fetch_calendar_event")
@patch("api.routers.bot_webhooks.MicrosoftOAuth")
def test_parse_info_from_bot_meeting_microsoft_calendar_no_access_token(
    ms_oauth: MagicMock, ms_calendar: MagicMock, test_user: User
) -> None:
    calendar_event = {"id": "event-id", "platform": "microsoft_outlook", "raw": {"id": "event-id"}}

    ms_oauth.return_value.get_access_token = AsyncMock(return_value=None)

    attendees, meeting_title, meeting_url, event_id, user_specific_event_id = __parse_info_from_bot_meeting(
        calendar_event, test_user
    )
    assert not meeting_title
    assert not meeting_url
    assert not attendees
    assert not event_id
    assert not user_specific_event_id

    ms_calendar.fetch_calendar_event.assert_not_called()


@patch("api.routers.bot_webhooks.microsoft")
@patch("api.routers.bot_webhooks.MicrosoftOAuth")
def test_parse_info_from_bot_meeting_microsoft_calendar_access_token_error(
    ms_oauth: MagicMock, ms_calendar: MagicMock, test_user: User
) -> None:
    calendar_event = {"id": "event-id", "platform": "microsoft_outlook", "raw": {"id": "event-id"}}

    ms_oauth.return_value.get_access_token = AsyncMock(side_effect=Exception)

    attendees, meeting_title, meeting_url, event_id, user_specific_event_id = __parse_info_from_bot_meeting(
        calendar_event, test_user
    )
    assert not meeting_title
    assert not meeting_url
    assert not attendees
    assert not event_id
    assert not user_specific_event_id

    ms_calendar.fetch_calendar_event.assert_not_called()


@patch("api.routers.bot_webhooks.microsoft.fetch_calendar_event", new_callable=AsyncMock)
@patch("api.routers.bot_webhooks.MicrosoftOAuth")
@patch("api.routers.bot_webhooks.timezone")
def test_parse_info_from_bot_meeting_microsoft_calendar_no_expiry(
    mock_timezone: MagicMock, ms_oauth: MagicMock, ms_fetch_calendar_event: MagicMock, test_user: User
) -> None:
    calendar_event = {"id": "event-id", "platform": "microsoft_outlook", "raw": {"id": "event-id"}}

    ms_oauth.return_value.get_access_token = AsyncMock(return_value="access_token")
    ms_oauth.return_value.get_expiry = AsyncMock(return_value=None)
    ms_fetch_calendar_event.return_value = None
    now = datetime(2024, 11, 26, 0, 50)
    mock_timezone.now.return_value = now

    attendees, meeting_title, meeting_url, event_id, user_specific_event_id = __parse_info_from_bot_meeting(
        calendar_event, test_user
    )
    assert not meeting_title
    assert not meeting_url
    assert not attendees
    assert not event_id
    assert not user_specific_event_id

    ms_fetch_calendar_event.assert_called_once_with(
        MicrosoftCredentials.from_access_token("access_token", int(datetime.timestamp(now + timedelta(minutes=5)))),
        "event-id",
    )


@patch("api.routers.bot_webhooks.microsoft")
@patch("api.routers.bot_webhooks.MicrosoftOAuth")
def test_parse_info_from_bot_meeting_microsoft_calendar_expiry_error(
    ms_oauth: MagicMock, ms_calendar: MagicMock, test_user: User
) -> None:
    calendar_event = {"id": "event-id", "platform": "microsoft_outlook", "raw": {"id": "event-id"}}

    ms_oauth.return_value.get_access_token = AsyncMock(return_value="access_token")
    ms_oauth.return_value.get_expiry = AsyncMock(side_effect=Exception)

    attendees, meeting_title, meeting_url, event_id, user_specific_event_id = __parse_info_from_bot_meeting(
        calendar_event, test_user
    )
    assert not meeting_title
    assert not meeting_url
    assert not attendees
    assert not event_id
    assert not user_specific_event_id

    ms_calendar.fetch_calendar_event.assert_not_called()


@patch("api.routers.bot_webhooks.microsoft")
@patch("api.routers.bot_webhooks.MicrosoftOAuth")
def test_parse_info_from_bot_meeting_microsoft_calendar_no_event_id(
    ms_oauth: MagicMock, ms_calendar: MagicMock, test_user: User
) -> None:
    calendar_event = {"id": "event-id", "platform": "microsoft_outlook", "raw": {"other": "value"}}

    ms_oauth.return_value.get_access_token = AsyncMock(return_value="access_token")
    ms_oauth.return_value.get_expiry = AsyncMock(return_value=datetime(2024, 11, 26, 0, 50))

    attendees, meeting_title, meeting_url, event_id, user_specific_event_id = __parse_info_from_bot_meeting(
        calendar_event, test_user
    )
    assert not meeting_title
    assert not meeting_url
    assert not attendees
    assert not event_id
    assert not user_specific_event_id

    ms_calendar.fetch_calendar_event.assert_not_called()


@patch("api.routers.bot_webhooks.microsoft")
@patch("api.routers.bot_webhooks.MicrosoftOAuth")
def test_parse_info_from_bot_meeting_microsoft_calendar_empty_response(
    ms_oauth: MagicMock, ms_calendar: MagicMock, test_user: User
) -> None:
    calendar_event = {"id": "event-id", "platform": "microsoft_outlook", "raw": {"id": "event-id"}}

    ms_oauth.return_value.get_access_token = AsyncMock(return_value="access_token")
    ms_oauth.return_value.get_expiry = AsyncMock(return_value=datetime(2024, 11, 26, 0, 50))
    ms_calendar.fetch_calendar_event = AsyncMock(return_value=None)

    attendees, meeting_title, meeting_url, event_id, user_specific_event_id = __parse_info_from_bot_meeting(
        calendar_event, test_user
    )
    assert not meeting_title
    assert not meeting_url
    assert not attendees
    assert not event_id
    assert not user_specific_event_id

    ms_calendar.fetch_calendar_event.assert_called_once()


@patch("api.routers.bot_webhooks.google.calendar_entries_from_events")
@patch("api.routers.bot_webhooks.events_with_zeplyn_attendees")
def test_parse_info_from_bot_meeting_empty_title(
    mock_events_with_zeplyn_attendees: MagicMock, mock_google_calendar_entries: MagicMock, test_user: User
) -> None:
    calendar_event = {"meeting_url": "http://example.com", "platform": "google_calendar", "raw": {"id": "event-id"}}
    mock_google_calendar_entries.return_value = [
        CalendarEvent(
            provider="google",
            id="event-id",
            user_specific_id="event-id",
            title="",
            body="",
            start_time=timezone.now(),
            end_time=timezone.now(),
            all_day=False,
            participants=[],
            meeting_urls=[pydantic.HttpUrl("http://example.com")],
        )
    ]
    mock_events_with_zeplyn_attendees.return_value = mock_google_calendar_entries.return_value

    _, meeting_title, _, _, _ = __parse_info_from_bot_meeting(calendar_event, test_user)
    assert meeting_title == ""


@pytest.mark.parametrize(
    "participants, expected_title",
    [
        (
            [],
            "",
        ),
        (
            [EventParticipant(id="participantOne", email_address="<EMAIL>")],
            "<NAME_EMAIL>",
        ),
        (
            [
                EventParticipant(id="participantOne", email_address="<EMAIL>"),
                EventParticipant(id="participantTwo", email_address="<EMAIL>", name="Client Two"),
            ],
            "<NAME_EMAIL>, Client Two",
        ),
        (
            [
                EventParticipant(id="participantOne", email_address="<EMAIL>", name="Client One"),
                EventParticipant(id="participantTwo", email_address="<EMAIL>"),
                EventParticipant(id="participantThree", email_address="<EMAIL>", name="Client Three"),
            ],
            "Meeting with Client One, <EMAIL>, Client Three",
        ),
        (
            [
                EventParticipant(id="participantOne", email_address="<EMAIL>", name="Client One"),
                EventParticipant(id="participantTwo", email_address="<EMAIL>"),
                EventParticipant(id="participantThree", email_address="<EMAIL>", name="Client Three"),
                EventParticipant(id="participantFour", email_address="<EMAIL>"),
                EventParticipant(id="participantFive", email_address="<EMAIL>"),
            ],
            "Meeting with Client One, <EMAIL>, Client Three, others",
        ),
    ],
)
@patch("api.routers.bot_webhooks.google.calendar_entries_from_events")
@patch("api.routers.bot_webhooks.events_with_zeplyn_attendees")
def test_parse_info_from_bot_meeting_title_from_participants(
    mock_events_with_zeplyn_attendees: MagicMock,
    mock_google_calendar_entries: MagicMock,
    test_user: User,
    participants: list[EventParticipant],
    expected_title: str,
) -> None:
    calendar_event = {"meeting_url": "http://example.com", "platform": "google_calendar", "raw": {"id": "event-id"}}
    mock_google_calendar_entries.return_value = [
        CalendarEvent(
            provider="google",
            id="event-id",
            user_specific_id="event-id",
            title="",
            body="Test Body",
            start_time=timezone.now(),
            end_time=timezone.now(),
            all_day=False,
            participants=participants,
            meeting_urls=[pydantic.HttpUrl("http://example.com")],
        )
    ]
    mock_events_with_zeplyn_attendees.return_value = mock_google_calendar_entries.return_value

    _, meeting_title, _, _, _ = __parse_info_from_bot_meeting(calendar_event, test_user)
    assert meeting_title == expected_title


def test_parse_info_from_bot_meeting_microsoft_calendar_invalid_data(test_user: User) -> None:
    # This is a mock of a real Recall API response for an Outlook calendar event.
    calendar_event = {
        "id": "event-id",
        "start_time": "2024-11-26T00:50:00Z",
        "end_time": "2024-11-26T01:15:00Z",
        "calendar_id": "calendar-id",
        "raw": {"invalid": "data"},
        "platform": "microsoft_outlook",
        "platform_id": "ms-event-id",
        "ical_uid": "ical-id",
        "meeting_platform": "microsoft_teams",
        "meeting_url": "http://example.com/meeting_url",
        "created_at": "2024-11-26T00:48:33.893143Z",
        "updated_at": "2024-11-26T00:48:36.461466Z",
        "is_deleted": False,
        "bots": [
            {
                "bot_id": "bot-id",
                "start_time": "2024-11-26T00:50:00Z",
                "deduplication_key": "deduplication_key",
                "meeting_url": "http://example.com/meeting_url",
            }
        ],
    }

    attendees, meeting_title, meeting_url, event_id, user_specific_event_id = __parse_info_from_bot_meeting(
        calendar_event, test_user
    )
    assert not meeting_title
    assert meeting_url == "http://example.com/meeting_url"
    assert not attendees
    assert not event_id
    assert not user_specific_event_id


class TestCreateNoteForAutojoinMeeting:
    @pytest.fixture()
    def setup(self, django_user_model: User) -> Callable[[], Any]:
        org = Organization.objects.create(name="Test Org")
        self.user = django_user_model.objects.create(username="test", organization=org)
        self.user_two = django_user_model.objects.create(
            username="test2", email="<EMAIL>", organization=org, name="Second User"
        )

        self.test_client = Client.objects.create(name="Test Client", organization=org)
        self.test_client.authorized_users.add(self.user)
        self.test_client.save()

        self.recall_bot_id = "test-bot-id"

        payload = {
            "event": "bot.status_change",
            "data": {
                "bot_id": self.recall_bot_id,
                "status": {
                    "code": "joining_call",
                    "created_at": "2023-01-01T00:00:00Z",
                    "sub_code": None,
                    "message": None,
                },
            },
        }

        def call() -> Response:
            with patch("api.routers.bot_webhooks.RecallBotController") as mock_bot_controller:
                with patch(
                    "api.routers.bot_webhooks.google.calendar_entries_from_events"
                ) as mock_calendar_entries_from_events:
                    mock_bot_controller.return_value.get_internal_bot_uuid.return_value = None
                    mock_bot_controller.return_value.calendar_event.return_value = {
                        "platform": "google_calendar",
                        "meeting_url": "http://example.com",
                        "raw": {"id": "event-id"},
                    }
                    mock_bot_controller.return_value.zeplyn_user_uuid.return_value = str(self.user.uuid)
                    mock_calendar_entries_from_events.return_value = [
                        CalendarEvent(
                            provider="google",
                            id="event-id",
                            user_specific_id="user-specific-event-id",
                            title="Test Meeting",
                            body="Test Body",
                            start_time=timezone.now(),
                            end_time=timezone.now(),
                            all_day=False,
                            participants=[
                                EventParticipant(
                                    email_address="<EMAIL>",
                                    zeplyn_uuid=self.user_two.uuid,
                                    zeplyn_kind=EventParticipant.ZeplynKind.USER,
                                ),
                                EventParticipant(
                                    email_address="<EMAIL>",
                                    zeplyn_uuid=self.test_client.uuid,
                                    zeplyn_kind=EventParticipant.ZeplynKind.CLIENT,
                                ),
                            ],
                            meeting_urls=[pydantic.HttpUrl("http://example.com")],
                        )
                    ]
                    return client.post("/api/v2/bot_webhooks/recall", json=payload)

        return call

    @pytest.mark.parametrize("enable_attendees_as_authorized_users", [True, False])
    @patch("api.routers.bot_webhooks.Flags")
    def test_create_note_and_bot(
        self,
        mock_flags: MagicMock,
        setup: Callable[[], Response],
        enable_attendees_as_authorized_users: bool,
    ) -> None:
        mock_flags.EnableSetAttendeesAsAuthorizedUsers.is_active_for_user.return_value = (
            enable_attendees_as_authorized_users
        )

        response = setup()

        assert response.status_code == 200

        assert (note := Note.objects.all().first())
        assert set(note.authorized_users.all()) == set(
            [self.user, self.user_two] if enable_attendees_as_authorized_users else [self.user]
        )
        assert list(
            Attendee.objects.all().order_by("attendee_name").values("note", "attendee_name", "client", "user")
        ) == [
            {"note": note.pk, "attendee_name": "Second User", "client": None, "user": self.user_two.pk},
            {"note": note.pk, "attendee_name": "<EMAIL>", "client": self.test_client.pk, "user": None},
        ]
        assert note.meeting_type
        assert note.status == Note.PROCESSING_STATUS.scheduled
        assert note.note_owner == self.user
        assert note.note_type == Note.NOTE_TYPE.meeting_recording
        assert (metadata := note.metadata)
        assert metadata.get("meeting_name") == "Test Meeting"
        assert metadata.get("meeting_source_id") == "event-id"
        assert not note.scheduled_event

        assert (bot := MeetingBot.objects.all().first())
        assert note.bot_uuid == bot.uuid
        assert bot.bot_owner == self.user
        assert bot.meeting_link == "http://example.com"
        assert bot.recall_bot_id == self.recall_bot_id

    def test_create_note_scheduled_event_matching_by_user_specific_id(self, setup: Callable[[], Response]) -> None:
        matching_event = ScheduledEvent.objects.create(
            user_specific_source_id="user-specific-event-id",
            shared_source_id="event-id",
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=1),
            user=self.user,
        )
        ScheduledEvent.objects.create(
            user_specific_source_id="other-user-specific-event-id",
            shared_source_id="event-id",
            start_time=matching_event.start_time,
            end_time=matching_event.end_time,
            user=self.user_two,
        )

        response = setup()

        assert response.status_code == 200

        assert (note := Note.objects.all().first())
        assert note.scheduled_event == matching_event

    def test_create_note_scheduled_event_matching_by_shared_source_id(self, setup: Callable[[], Response]) -> None:
        older_event = ScheduledEvent.objects.create(
            user_specific_source_id="other-user-specific-event-id",
            shared_source_id="event-id",
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=1),
            user=self.user_two,
        )
        matching_event = ScheduledEvent.objects.create(
            user_specific_source_id="even-another-user-specific-event-id",
            shared_source_id="event-id",
            start_time=older_event.start_time,
            end_time=older_event.end_time,
            user=self.user_two,
        )

        response = setup()

        assert response.status_code == 200

        assert (note := Note.objects.all().first())
        assert note.scheduled_event == matching_event

    def test_existing_non_matching_notes(
        self,
        setup: Callable[[], Response],
    ) -> None:
        # User not in authorized users.
        not_authorized_note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_source_id": "event-id"},
        )
        # Not in scheduled state.
        not_scheduled_note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.uploaded,
            metadata={"meeting_source_id": "event-id"},
        )
        not_scheduled_note.authorized_users.add(self.user)
        not_scheduled_note.save()
        # Meeting source ID does not match.
        different_event_note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_source_id": "other-event-id"},
        )
        different_event_note.authorized_users.add(self.user)
        different_event_note.save()
        # Scheduled event does not match
        scheduled_event_note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_source_id": "other-event-id"},
        )
        scheduled_event_note.authorized_users.add(self.user)
        scheduled_event_note.save()
        ScheduledEvent.objects.create(
            user_specific_source_id="other-event-id",
            shared_source_id="other-event-id",
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=1),
            user=self.user,
            note=scheduled_event_note,
        )

        response = setup()

        assert response.status_code == 200

        assert Note.objects.count() == 5
        assert (note := Note.objects.order_by("-created").first())
        assert (metadata := note.metadata)
        assert metadata.get("meeting_name") == "Test Meeting"
        assert metadata.get("meeting_source_id") == "event-id"

        assert (bot := MeetingBot.objects.all().first())
        assert note.bot_uuid == bot.uuid

    def test_existing_matching_single_note(
        self,
        setup: Callable[[], Response],
    ) -> None:
        note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_source_id": "event-id", "meeting_name": "Client Meeting"},
            meeting_type=MeetingType.objects.get(key="client"),
        )
        note.authorized_users.add(self.user)
        note.save()

        response = setup()

        assert response.status_code == 200

        assert Note.objects.count() == 1
        assert (bot := MeetingBot.objects.all().first())
        assert bot.note
        assert bot.note.uuid == note.uuid

    def test_existing_matching_notes(
        self,
        setup: Callable[[], Response],
    ) -> None:
        # Client meeting type note
        client_meeting_note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_source_id": "event-id", "meeting_name": "Client Meeting"},
            meeting_type=MeetingType.objects.get(key="client"),
        )
        client_meeting_note.authorized_users.add(self.user)
        client_meeting_note.save()

        other_meeting_type = MeetingType.objects.create(key="other", name="Other Meeting Type")

        # Other meeting type note
        other_meeting_type_note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_source_id": "event-id", "meeting_name": "Other Meeting"},
            meeting_type=other_meeting_type,
        )
        other_meeting_type_note.authorized_users.add(self.user)
        other_meeting_type_note.save()

        # Newer other meeting type note
        newer_other_meeting_type_note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_source_id": "event-id", "meeting_name": "Newer Other Meeting"},
            meeting_type=other_meeting_type,
        )
        newer_other_meeting_type_note.authorized_users.add(self.user)
        newer_other_meeting_type_note.save()

        response = setup()

        assert response.status_code == 200

        assert Note.objects.count() == 3
        assert (bot := MeetingBot.objects.all().first())
        assert bot.note
        assert bot.note.uuid == other_meeting_type_note.uuid

    def test_existing_matching_notes_with_scheduled_events(
        self,
        setup: Callable[[], Response],
    ) -> None:
        other_meeting_type = MeetingType.objects.create(key="other", name="Other Meeting Type")

        # Client meeting type note with scheduled event with matching shared source ID
        scheduled_event_shared_id_client_note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_name": "Scheduled event shared ID client meeting"},
            meeting_type=MeetingType.objects.get(key="client"),
        )
        scheduled_event_shared_id_client_note.authorized_users.add(self.user)
        scheduled_event_shared_id_client_note.authorized_users.add(self.user_two)
        scheduled_event_shared_id_client_note.save()

        ScheduledEvent.objects.create(
            user_specific_source_id="other-user-event-id-clientmeetingtype",
            shared_source_id="event-id",
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=1),
            user=self.user_two,
            note=scheduled_event_shared_id_client_note,
        )

        # Client meeting type note with scheduled event with matching user source ID
        #
        # There is a uniquess limit on the ScheduledEvent model, so we cannot create notes with scheduled events
        # that have different meeting types and both match the user-specific source ID.
        scheduled_event_user_specific_id_client_note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_name": "Scheduled event user specific ID client meeting"},
            meeting_type=MeetingType.objects.get(key="client"),
        )
        scheduled_event_user_specific_id_client_note.authorized_users.add(self.user)
        scheduled_event_user_specific_id_client_note.save()

        ScheduledEvent.objects.create(
            user_specific_source_id="user-specific-event-id",
            shared_source_id="event-id",
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=1),
            user=self.user_two,
            note=scheduled_event_user_specific_id_client_note,
        )

        # Other meeting type note with scheduled event with matching shared source ID
        scheduled_event_shared_id_other_meeting_type_note = Note.objects.create(
            note_owner=self.user_two,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_name": "Scheduled event shared ID other meeting"},
            meeting_type=other_meeting_type,
        )
        scheduled_event_shared_id_other_meeting_type_note.authorized_users.add(self.user)
        scheduled_event_shared_id_other_meeting_type_note.save()

        ScheduledEvent.objects.create(
            user_specific_source_id="other-user-event-id-othermeetingtype",
            shared_source_id="event-id",
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=1),
            user=self.user_two,
            note=scheduled_event_shared_id_other_meeting_type_note,
        )

        # As noted above, there is a uniqueness limit on the ScheduledEvent model, so we cannot
        # create a note with a scheduled event that has a different meeting type and matches the
        # user-specific source ID.

        # Client meeting type note
        client_meeting_note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_source_id": "event-id", "meeting_name": "Client Meeting"},
            meeting_type=MeetingType.objects.get(key="client"),
        )
        client_meeting_note.authorized_users.add(self.user)
        client_meeting_note.save()

        # Other meeting type note
        other_meeting_type_note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_source_id": "event-id", "meeting_name": "Other Meeting"},
            meeting_type=other_meeting_type,
        )
        other_meeting_type_note.authorized_users.add(self.user)
        other_meeting_type_note.save()

        # Newer other meeting type note
        newer_other_meeting_type_note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_source_id": "event-id", "meeting_name": "Newer Other Meeting"},
            meeting_type=other_meeting_type,
        )
        newer_other_meeting_type_note.authorized_users.add(self.user)
        newer_other_meeting_type_note.save()

        # Go through the notes in their expected order, calling the webhook and then deleting the
        # note so the next expected note will be linked on the next call.
        for note in [
            scheduled_event_user_specific_id_client_note,
            scheduled_event_shared_id_other_meeting_type_note,
            scheduled_event_shared_id_client_note,
            other_meeting_type_note,
            newer_other_meeting_type_note,
            client_meeting_note,
        ]:
            response = setup()

            assert response.status_code == 200

            assert (bot := MeetingBot.objects.all().first())
            assert bot.note == note

            note.delete()
            bot.delete()

    def test_existing_bot_different_link_no_recall_id(
        self,
        setup: Callable[[], Response],
        caplog: pytest.LogCaptureFixture,
    ) -> None:
        note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_source_id": "event-id"},
        )
        note.authorized_users.add(self.user)
        note.save()

        bot = MeetingBot.objects.create(bot_owner=self.user, note=note, meeting_link="http://example2.com")

        with caplog.at_level(logging.WARNING):
            response = setup()
            assert caplog.messages == [f"Disconecting bot {bot.uuid} from note {note.uuid}"]

        assert response.status_code == 200

        assert Note.objects.count() == 1
        assert MeetingBot.objects.count() == 2
        note.refresh_from_db()
        assert note.meetingbot_set.count() == 1
        assert note.meetingbot_set.first() != bot

    def test_existing_bots_with_recall_ids(
        self,
        setup: Callable[[], Response],
        caplog: pytest.LogCaptureFixture,
    ) -> None:
        note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_source_id": "event-id"},
        )
        note.authorized_users.add(self.user)
        note.save()

        bot = MeetingBot.objects.create(
            bot_owner=self.user, note=note, meeting_link="http://example2.com", recall_bot_id="other-bot-id"
        )
        bot_two = MeetingBot.objects.create(
            bot_owner=self.user, note=note, meeting_link="http://example.com", recall_bot_id="same-url-bot-id"
        )

        with caplog.at_level(logging.WARNING):
            response = setup()
            assert caplog.messages == [
                f"Note {note.uuid} already has at least one bot with a valid Recall bot ID. All bot UUIDs: {str([str(bot.uuid), str(bot_two.uuid)])}",
                f"Disconecting bot {bot.uuid} from note {note.uuid}",
                f"Disconecting bot {bot_two.uuid} from note {note.uuid}",
            ]

        assert response.status_code == 200

        assert Note.objects.count() == 1
        assert MeetingBot.objects.count() == 3
        note.refresh_from_db()
        assert note.meetingbot_set.count() == 1
        assert (new_bot := note.meetingbot_set.first())
        assert new_bot != bot
        assert new_bot != bot_two
        assert new_bot.recall_bot_id == self.recall_bot_id
        assert new_bot.meeting_link == "http://example.com"

    def test_existing_bots_no_recall_ids(
        self,
        setup: Callable[[], Response],
        caplog: pytest.LogCaptureFixture,
    ) -> None:
        note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_source_id": "event-id"},
        )
        note.authorized_users.add(self.user)
        note.save()

        bot = MeetingBot.objects.create(bot_owner=self.user, note=note, meeting_link="http://example2.com")
        bot_two = MeetingBot.objects.create(bot_owner=self.user, note=note, meeting_link="http://example.com")
        bot_three = MeetingBot.objects.create(bot_owner=self.user, note=note, meeting_link="http://example.com")

        with caplog.at_level(logging.WARNING):
            response = setup()
            assert caplog.messages == [
                f"Disconecting bot {bot.uuid} from note {note.uuid}",
                f"Disconecting bot {bot_three.uuid} from note {note.uuid}",
            ]

        assert response.status_code == 200

        assert Note.objects.count() == 1
        assert MeetingBot.objects.count() == 3
        note.refresh_from_db()
        assert note.meetingbot_set.count() == 1
        assert (new_bot := note.meetingbot_set.first())
        assert new_bot == bot_two
        assert new_bot.recall_bot_id == self.recall_bot_id

    def test_existing_bots_matching_recall_id(
        self,
        setup: Callable[[], Response],
        caplog: pytest.LogCaptureFixture,
    ) -> None:
        note = Note.objects.create(
            note_owner=self.user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.scheduled,
            metadata={"meeting_source_id": "event-id"},
        )
        note.authorized_users.add(self.user)
        note.save()

        MeetingBot.objects.create(bot_owner=self.user, note=note, meeting_link="http://example2.com")
        MeetingBot.objects.create(bot_owner=self.user, note=note, meeting_link="http://example.com")
        MeetingBot.objects.create(
            bot_owner=self.user, note=note, meeting_link="http://example.com", recall_bot_id=self.recall_bot_id
        )

        with caplog.at_level(logging.WARNING):
            response = setup()
            assert not caplog.messages

        assert response.status_code == 200

        # In this case, the bot with the matching Recall ID will be matched when the bot status
        # change is handled, and it will be updated. The auto-join linking code will not be called.
        note.refresh_from_db()
        assert note.meetingbot_set.count() == 3


def test_handle_bot_status_change_existing_bot_call_ended(test_user: User) -> None:
    bot_id = "test-bot-id"
    note = Note.objects.create(
        note_owner=test_user,
        note_type=Note.NOTE_TYPE.meeting_recording,
        status=Note.PROCESSING_STATUS.scheduled,
    )
    MeetingBot.objects.create(recall_bot_id=bot_id, note=note)

    payload = {
        "event": "bot.status_change",
        "data": {
            "bot_id": bot_id,
            "status": {
                "code": "call_ended",
                "created_at": "2023-01-01T00:00:00Z",
                "sub_code": None,
                "message": None,
            },
        },
    }

    response = client.post("/api/v2/bot_webhooks/recall", json=payload)

    assert response.status_code == 200
    note.refresh_from_db()
    assert note.status == Note.PROCESSING_STATUS.uploaded


def test_handle_bot_status_change_no_existing_bot(test_user: User) -> None:
    bot_id = "test-bot-id"
    payload = {
        "event": "bot.status_change",
        "data": {
            "bot_id": bot_id,
            "status": {
                "code": "joining_call",
                "created_at": "2023-01-01T00:00:00Z",
                "sub_code": None,
                "message": None,
            },
        },
    }

    response = client.post("/api/v2/bot_webhooks/recall", json=payload)

    assert response.status_code == 204


@patch.object(MeetingBot, "processing_task")
def test_handle_bot_status_change_existing_bot_done(mock_processing_task: MagicMock, test_user: User) -> None:
    bot_id = "test-bot-id"
    note = Note.objects.create(
        note_owner=test_user,
        note_type=Note.NOTE_TYPE.meeting_recording,
        status=Note.PROCESSING_STATUS.scheduled,
    )
    bot = MeetingBot.objects.create(recall_bot_id=bot_id, note=note)

    payload = {
        "event": "bot.status_change",
        "data": {
            "bot_id": bot_id,
            "status": {
                "code": "done",
                "created_at": "2023-01-01T00:00:00Z",
                "sub_code": None,
                "message": None,
            },
        },
    }

    response = client.post("/api/v2/bot_webhooks/recall", json=payload)

    assert response.status_code == 200
    note.refresh_from_db()
    assert note.status == Note.PROCESSING_STATUS.uploaded
    mock_processing_task.delay_on_commit.assert_called_once_with(bot.uuid, False)


@patch("api.routers.bot_webhooks.__handle_bot_status_change")
def test_post_bot_status_change(mock_handle_bot_status_change: MagicMock) -> None:
    mock_handle_bot_status_change.return_value = Response(status_code=200)
    payload = {"event": "bot.status_change", "data": {"bot_id": "test-bot-id", "status": {"code": "joining_call"}}}

    response = client.post("/api/v2/bot_webhooks/recall", json=payload)

    assert response.status_code == 200
    mock_handle_bot_status_change.assert_called_once_with(payload["data"])


@patch("api.routers.bot_webhooks.__handle_calendar_event")
def test_post_calendar_event(mock_handle_calendar_event: MagicMock) -> None:
    mock_handle_calendar_event.return_value = Response(status_code=200)
    payload: dict[str, Any] = {"event": "calendar.event_created", "data": {"calendar_id": "test-calendar-id"}}

    response = client.post("/api/v2/bot_webhooks/recall", json=payload)

    assert response.status_code == 200
    mock_handle_calendar_event.assert_called_once_with(payload["event"], payload["data"])


def test_post_unhandled_event(caplog: pytest.LogCaptureFixture) -> None:
    payload: dict[str, Any] = {"event": "unknown.event", "data": {}}

    with caplog.at_level(logging.WARNING):
        response = client.post("/api/v2/bot_webhooks/recall", json=payload)
        assert response.status_code == 200
        assert len(caplog.records) == 1
        assert "Received a Recall webhook call with an unhandled event type" in caplog.text


def test_post_exception_handling(caplog: pytest.LogCaptureFixture) -> None:
    with patch("api.routers.bot_webhooks.__handle_bot_status_change", side_effect=Exception("Test Exception")):
        payload = {"event": "bot.status_change", "data": {"bot_id": "test-bot-id"}}

        with caplog.at_level(logging.ERROR) as log:
            response = client.post("/api/v2/bot_webhooks/recall", json=payload)
            assert response.status_code == 200
            assert len(caplog.records) == 1
            assert "Error handling Recall webhook call" in caplog.text


@patch("api.routers.bot_webhooks.update_bots_for_calendar_events")
def test_handle_calendar_event_invalid_calendar_id(mock_update_bots: MagicMock) -> None:
    payload = {"event": "calendar.sync_events", "data": {"last_updated_ts": "2023-01-01T00:00:00Z"}}

    response = client.post("/api/v2/bot_webhooks/recall", json=payload)

    assert response.status_code == 200
    mock_update_bots.delay_on_commit.assert_not_called()


@patch("api.routers.bot_webhooks.update_bots_for_calendar_events")
def test_handle_calendar_event_calendar_update(mock_update_bots: MagicMock) -> None:
    mock_update_bots.delay_on_commit.assert_not_called()
    payload = {"event": "calendar.update", "data": {"calendar_id": "test-calendar-id"}}

    response = client.post("/api/v2/bot_webhooks/recall", json=payload)

    assert response.status_code == 200
    mock_update_bots.delay_on_commit.assert_not_called()


@patch("api.routers.bot_webhooks.update_bots_for_calendar_events")
def test_handle_calendar_event_sync_events_invalid_last_updated_ts(mock_update_bots: MagicMock) -> None:
    payload = {"event": "calendar.sync_events", "data": {"calendar_id": "test-calendar-id"}}

    response = client.post("/api/v2/bot_webhooks/recall", json=payload)

    assert response.status_code == 200
    mock_update_bots.delay_on_commit.assert_not_called()


@patch("api.routers.bot_webhooks.update_bots_for_calendar_events")
def test_handle_calendar_event_sync_events_unknown_user(mock_update_bots: MagicMock) -> None:
    payload = {
        "event": "calendar.sync_events",
        "data": {"calendar_id": "test-calendar-id", "last_updated_ts": "2023-01-01T00:00:00Z"},
    }

    response = client.post("/api/v2/bot_webhooks/recall", json=payload)

    assert response.status_code == 200
    mock_update_bots.delay_on_commit.assert_not_called()


@patch("api.routers.bot_webhooks.update_bots_for_calendar_events")
def test_handle_calendar_event_sync_events_valid(mock_update_bots: MagicMock, test_user: User) -> None:
    event = "calendar.sync_events"
    test_user.recall_calendar_id = "test-calendar-id"
    test_user.save()
    payload = {
        "event": "calendar.sync_events",
        "data": {"calendar_id": "test-calendar-id", "last_updated_ts": "2023-01-01T00:00:00Z"},
    }

    response = client.post("/api/v2/bot_webhooks/recall", json=payload)

    assert response.status_code == 200
    mock_update_bots.delay_on_commit.assert_called_once_with("test-calendar-id", "2023-01-01T00:00:00Z", test_user.uuid)
