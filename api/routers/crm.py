import base64
import logging
from typing import Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Response, status
from pydantic import BaseModel, Field

from api.dependencies import user_from_authorization_header
from deepinsights.core.integrations.crm.crm_manager import get_crm_interface
from deepinsights.core.integrations.crm.redtail import Redtail
from deepinsights.core.integrations.crm.sharefile import ShareFile
from deepinsights.core.integrations.crm.sharepoint import SharePoint
from deepinsights.core.integrations.crm.wealthbox import Wealthbox
from deepinsights.core.ml.emails import email_meeting_notes
from deepinsights.core.preferences.preferences import CrmConfiguration, DefaultClientFilter
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note, is_authorized_to_view_note
from deepinsights.meetingsapp.tasks import sync_crm_clients
from deepinsights.users.models.user import User

logger = logging.getLogger(__name__)

router = APIRouter(tags=["crm"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")
public_router = APIRouter(
    tags=["crm"],
    generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}",
)


class RedtailCredentials(BaseModel):
    username: str
    password: str


class ClientResponse(BaseModel):
    uuid: str
    name: str
    type: str


class CursorData(BaseModel):
    uuid: str
    name: str


class ClientListResponse(BaseModel):
    client_selection_enabled: bool
    clients: list[ClientResponse]
    crm_system: str | None
    next_page_token: str | None = Field(
        None, description="Token to fetch the next page. Null if there are no more results."
    )


class PreviewData(BaseModel):
    preview_data: dict[str, Any]


def _validate_note_and_crm(note_id: UUID, user: User) -> tuple[Note, bool]:
    try:
        note = Note.objects.get(uuid=note_id)
    except Note.DoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Could not find the note")

    if not is_authorized_to_view_note(user, note):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to access this note")

    if not note.note_owner:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Note does not have an owner")

    crm_interface = get_crm_interface(note.note_owner)
    requires_client = not isinstance(crm_interface, (Wealthbox, ShareFile, SharePoint))

    if requires_client and not note.client:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Note does not have a CRM client associated with it"
        )

    return note, requires_client


@router.get(
    "/crm_sync_preview/{note_id}",
    response_model=PreviewData,
    responses={
        403: {"description": "Not authorized to view this note"},
        404: {"description": "Note not found"},
        400: {"description": "Bad request"},
        500: {"description": "Internal server error"},
    },
)
def fetch_crm_sync_preview(note_id: UUID, user: User = Depends(user_from_authorization_header)) -> PreviewData:
    note, _ = _validate_note_and_crm(note_id, user)
    try:
        preview_data = note.note_owner.crm_handler.preview_before_syncing_with_crm(note)  # type: ignore[union-attr]
        return PreviewData(preview_data=preview_data)
    except Exception as e:
        logger.error("Error generating preview data for note: %s", note_id, exc_info=e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post(
    "/upload_note/{note_id}",
    responses={
        204: {"description": "Note uploaded to CRM successfully"},
        400: {"description": "Bad request"},
        403: {"description": "Not authorized"},
        404: {"description": "Note not found"},
        500: {"description": "Internal server error"},
    },
)
@public_router.post(
    "/upload_note/{note_id}",
    responses={
        204: {"description": "Note uploaded to CRM successfully"},
        400: {"description": "Bad request"},
        403: {"description": "Not authorized"},
        404: {"description": "Note not found"},
        500: {"description": "Internal server error"},
    },
    summary="Uploads information from a note to the CRM",
    description=(
        "Uploads information from a note to the CRM. The note must be associated with a client. "
        "This is stateful: it copies note data from Zeplyn into the CRM."
    ),
)
def upload_note_to_crm(note_id: UUID, user: User = Depends(user_from_authorization_header)) -> Response:
    note, _ = _validate_note_and_crm(note_id, user)
    try:
        note.note_owner.crm_handler.add_interaction_with_client(note)  # type: ignore[union-attr]
        notification_preferences = user.get_preferences().notification_preferences
        if notification_preferences.email_meeting_notes_post_sync:
            email_meeting_notes(str(note_id), user)
        note.status = Note.PROCESSING_STATUS.finalized
        note.save()
        return Response(status_code=status.HTTP_204_NO_CONTENT)
    except Exception as e:
        logger.error("Error uploading note to CRM: %s", note_id, exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error uploading note to CRM: {str(e)}"
        )


@router.get(
    "/clients",
    response_model=ClientListResponse,
    responses={
        500: {"description": "Internal server error"},
    },
)
def get_client_list(
    q: str | None = None,
    cursor: str | None = None,
    page_size: int | None = None,
    user: User = Depends(user_from_authorization_header),
) -> ClientListResponse:
    use_pagination = page_size and page_size > 0

    if not (crm_system := user.get_crm_configuration().crm_system) or crm_system == "none":
        return ClientListResponse(client_selection_enabled=False, clients=[], crm_system=None, pagination_metadata=None)

    try:
        query_filters = {
            "authorized_users__isnull": False,
            "authorized_users": user,
            "crm_system": crm_system,
        }

        if q:
            query_filters["name__contains"] = q

        match client_filter := user.get_crm_configuration().default_client_filter:
            case DefaultClientFilter.ALL:
                pass
            case DefaultClientFilter.OWNED:
                query_filters["owner"] = user
            case _:
                logging.warning("Unknown default client filter: %s", client_filter)

        base_query = Client.objects.filter(**query_filters).only("uuid", "name", "client_type")

        # If cursor is provided, add filter to get records after the cursor
        if cursor:
            try:
                json_str = base64.b64decode(cursor).decode("utf-8")
                cursor_data = CursorData.model_validate_json(json_str)
                greater_name_filter = base_query.filter(name__gt=cursor_data.name)
                same_name_filter = base_query.filter(name=cursor_data.name, uuid__gt=cursor_data.uuid)
                base_query = greater_name_filter | same_name_filter
            except Exception as e:
                logger.warning(f"Invalid cursor format: {cursor} - {e}")
        base_query = base_query.order_by("name", "uuid")

        if use_pagination:
            assert page_size is not None
            clients = base_query[: page_size + 1]
            has_next_page = len(clients) > page_size

            next_page_token = None
            if has_next_page:
                last_client = clients[page_size - 1]
                clients = clients[:page_size]

                cursor_data = CursorData(name=last_client.name, uuid=str(last_client.uuid))
                next_page_token = base64.b64encode(cursor_data.model_dump_json().encode("utf-8")).decode("utf-8")

        else:
            # No pagination, fetch all clients
            clients = base_query.all()
            next_page_token = None
        client_list = [
            ClientResponse(uuid=str(client.uuid), name=client.name, type=client.client_type) for client in clients
        ]
        return ClientListResponse(
            client_selection_enabled=True,
            clients=client_list,
            crm_system=crm_system,
            next_page_token=next_page_token,
        )
    except Exception as e:
        logger.error("Error getting client list for user %s", user.uuid, exc_info=e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error")


@router.post(
    "/redtail/generate-key",
    responses={
        204: {"description": "User key generated successfully"},
        400: {"description": "Bad request"},
        500: {"description": "Internal server error"},
    },
)
def generate_redtail_user_key(
    credentials: RedtailCredentials, user: User = Depends(user_from_authorization_header)
) -> Response:
    try:
        _set_user_crm_system(user, "redtail")
        user.refresh_from_db()

        crm_handler = user.crm_handler
        if not crm_handler or not isinstance(crm_handler, Redtail):
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="User is not in a redtail enabled org")

        user_key = crm_handler.generate_user_key_code(credentials.username, credentials.password)
        if not user_key:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to generate user key")

        updated_crm_config = CrmConfiguration.from_dict(user.crm_configuration)
        updated_crm_config.redtail.user_key = user_key
        user.crm_configuration = updated_crm_config.to_dict()
        user.save()

        sync_crm_clients.delay_on_commit(user.uuid)
        return Response(status_code=status.HTTP_204_NO_CONTENT)

    except Exception as e:
        logger.error("Failed to generate user key for user %s", user.uuid, exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to generate redtail user key"
        )


class RedtailStatusResponse(BaseModel):
    active: bool


@router.get(
    "/redtail/status",
    responses={
        200: {"description": "Success", "content": {"application/json": {"example": {"integration_status": "Active"}}}},
        400: {"description": "Bad request"},
        500: {"description": "Internal server error"},
    },
)
def get_redtail_integration_status(user: User = Depends(user_from_authorization_header)) -> RedtailStatusResponse:
    crm_handler = user.crm_handler
    if not crm_handler or not isinstance(crm_handler, Redtail):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="User and user's organization do not use Redtail as a CRM"
        )

    try:
        return RedtailStatusResponse(active=bool(user.get_crm_configuration().redtail.user_key))
    except Exception as e:
        logger.error("Failed to get Redtail integration status for user %s", user.uuid, exc_info=e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to get Redtail status")


def _set_user_crm_system(user: User, system: str) -> None:
    crm_config = user.get_crm_configuration()
    crm_config.crm_system = system
    user.crm_configuration = crm_config.to_dict()
    user.save()
