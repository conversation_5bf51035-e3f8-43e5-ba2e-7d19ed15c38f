import uuid
from datetime import datetime, timedelta, timezone
from typing import Any, Generator
from unittest.mock import patch

import pytest
from django.utils import timezone as django_timezone
from fastapi import status
from fastapi.testclient import TestClient

from api.dependencies import user_from_authorization_header
from api.internal_api import internal_api as app
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User

client = TestClient(app)

pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture(autouse=True)
def setUpTearDown() -> Generator[Any, None, None]:
    app.dependency_overrides = {}
    yield
    app.dependency_overrides = {}


@pytest.fixture
def test_organization() -> Organization:
    return Organization.objects.create(name="Test Org")


def _create_note(
    note_owner: User,
    authorized_users: list[User] | None = None,
    title: str = "Test meeting",
) -> Note:
    n = Note.objects.create(
        note_owner=note_owner,
        uuid=uuid.uuid4(),
        note_type="meeting_recording",
        status="uploaded",
        metadata={"meeting_name": title},
        raw_transcript="[{'speaker': 'speaker0', 'start': datetime.timedelta(microseconds=640000), 'end': datetime.timedelta(seconds=10, microseconds=20000), 'utt': \"Welcome to TRS.\"}, {'speaker': 'speaker1', 'start': datetime.timedelta(seconds=10, microseconds=320000), 'end': datetime.timedelta(seconds=12, microseconds=99999), 'utt': 'Thank you. Yeah. Genuinely'}]",
    )
    n.authorized_users.add(*authorized_users if authorized_users is not None else [note_owner])
    n.save()
    return n


@pytest.fixture
def test_user(django_user_model: User, test_organization: Organization) -> User:
    test_user = django_user_model.objects.create(
        username="<EMAIL>",
        first_name="Test",
        last_name="User",
        organization=test_organization,
        license_type=User.LicenseType.advisor,
    )
    app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.get(username="<EMAIL>")
    return test_user


def _create_task(task_owner: User, assignee: User | None = None, note: Note | None = None) -> Task:
    task: Task = Task.objects.create(
        task_owner=task_owner,
        assignee=assignee,
        task_title="Test Task",
        task_desc="Test Description",
        completed=False,
        note=note,
    )
    return task


def test_edit_task_success(test_user: User) -> None:
    note = Note.objects.create(note_owner=test_user)
    note.authorized_users.add(test_user)
    task = _create_task(test_user)
    update_data = {
        "title": "Updated Title",
        "description": "Updated Description",
        "completed": True,
        "due_date": datetime.now(timezone.utc).isoformat(),
        "assignee": None,
        "parent_note_uuid": str(note.uuid),
    }

    response = client.patch(f"/api/v2/task/{task.uuid}", json=update_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT

    task.refresh_from_db()
    assert task.task_title == "Updated Title"
    assert task.task_desc == "Updated Description"
    assert task.completed is True
    assert task.note
    assert task.note.uuid == note.uuid


def test_edit_task_note_link(test_user: User) -> None:
    task = _create_task(test_user)
    note = Note.objects.create(note_owner=test_user)
    note.authorized_users.add(test_user)

    response = client.patch(f"/api/v2/task/{task.uuid}", json={"parent_note_uuid": str(note.uuid)})
    assert response.status_code == status.HTTP_204_NO_CONTENT
    task.refresh_from_db()
    assert task.note == note


def test_edit_task_note_unlink(test_user: User) -> None:
    note = Note.objects.create(note_owner=test_user)
    note.authorized_users.add(test_user)
    task = _create_task(test_user, note=note)

    response = client.patch(f"/api/v2/task/{task.uuid}", json={"parent_note_uuid": None})
    assert response.status_code == status.HTTP_204_NO_CONTENT
    task.refresh_from_db()
    assert not task.note


def test_edit_task_assignee(test_user: User, django_user_model: User, test_organization: Organization) -> None:
    assignee = django_user_model.objects.create(
        username="<EMAIL>", organization=test_organization, license_type=User.LicenseType.advisor
    )
    note = Note.objects.create(note_owner=test_user)
    note.authorized_users.add(test_user)

    task = _create_task(test_user)

    response = client.patch(
        f"/api/v2/task/{task.uuid}",
        json={"assignee": str(assignee.uuid)},
    )
    assert response.status_code == status.HTTP_204_NO_CONTENT
    task.refresh_from_db()
    assert task.assignee == assignee


def test_edit_task_unauthorized(test_user: User, django_user_model: User) -> None:
    other_user = django_user_model.objects.create(
        username="<EMAIL>",
        organization=Organization.objects.create(name="Other Org"),
        license_type=User.LicenseType.advisor,
    )
    task = _create_task(other_user)

    response = client.patch(f"/api/v2/task/{task.uuid}", json={"title": "New title"})
    assert response.status_code == status.HTTP_403_FORBIDDEN

    task.refresh_from_db()
    assert task.task_title == "Test Task"


def test_edit_task_not_found(test_user: User) -> None:
    response = client.patch(f"/api/v2/task/{uuid.uuid4()}", json={"title": "New title"})
    assert response.status_code == status.HTTP_404_NOT_FOUND


def test_edit_task_invalid_title(test_user: User) -> None:
    task = _create_task(test_user)
    response = client.patch(f"/api/v2/task/{task.uuid}", json={"title": "   "})
    assert response.status_code == status.HTTP_204_NO_CONTENT
    task.refresh_from_db()
    assert task.task_title == "Test Task"  # Title should not change


def test_edit_task_error(test_user: User) -> None:
    task = _create_task(test_user)
    with patch.object(Task, "save") as mock_save:
        mock_save.side_effect = Exception("Database error")
        response = client.patch(f"/api/v2/task/{task.uuid}", json={"title": "New title"})

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    task.refresh_from_db()
    assert task.task_title == "Test Task"  # Title should not change


def test_edit_task_partial_update(test_user: User) -> None:
    task = _create_task(test_user)
    original_title = task.task_title

    update_data = {
        "title": "   ",  # Should not update
        "description": None,  # No change
        "due_date": None,  # No change
        "completed": None,  # No change
        "assignee": None,  # No change
        "parent_note_uuid": None,  # Clear note
    }

    response = client.patch(f"/api/v2/task/{task.uuid}", json=update_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT

    task.refresh_from_db()
    assert task.task_title == original_title
    assert task.task_desc == "Test Description"
    assert task.note is None


def test_edit_task_due_date_updates(test_user: User) -> None:
    task = _create_task(test_user)
    original_date = django_timezone.now()
    task.due_date = original_date
    task.save()

    # Test setting new due date
    new_date = django_timezone.now() + timedelta(days=7)
    update_data = {"due_date": new_date.isoformat()}
    response = client.patch(f"/api/v2/task/{task.uuid}", json=update_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    task.refresh_from_db()
    assert task.due_date.date() == new_date.date()

    # Test invalid due date format
    update_data["due_date"] = "invalid-date"
    response = client.patch(f"/api/v2/task/{task.uuid}", json=update_data)
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


def test_edit_task_completed_updates(test_user: User) -> None:
    task = _create_task(test_user)
    task.completed = False
    task.save()

    # Test marking as completed
    update_data = {"completed": True}
    response = client.patch(f"/api/v2/task/{task.uuid}", json=update_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    task.refresh_from_db()
    assert task.completed is True

    # Test marking as incomplete
    update_data["completed"] = False
    response = client.patch(f"/api/v2/task/{task.uuid}", json=update_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    task.refresh_from_db()
    assert task.completed is False

    # Test completed field unchanged when not included
    update_data = {"task_title": "New title"}
    task.completed = True
    task.save()
    response = client.patch(f"/api/v2/task/{task.uuid}", json=update_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    task.refresh_from_db()
    assert task.completed is True


def test_edit_task_assignee_updates(test_user: User, django_user_model: User, test_organization: Organization) -> None:
    other_user = django_user_model.objects.create(
        username="<EMAIL>",
        organization=test_organization,
        license_type=User.LicenseType.advisor,
    )
    third_user = django_user_model.objects.create(
        username="<EMAIL>",
        organization=test_organization,
        license_type=User.LicenseType.advisor,
    )

    task = _create_task(test_user, assignee=test_user)

    # Test changing assignee to other user
    update_data = {"assignee": str(other_user.uuid)}
    response = client.patch(f"/api/v2/task/{task.uuid}", json=update_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    task.refresh_from_db()
    assert task.assignee == other_user

    # Test changing to another assignee
    update_data["assignee"] = str(third_user.uuid)
    response = client.patch(f"/api/v2/task/{task.uuid}", json=update_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    task.refresh_from_db()
    assert task.assignee == third_user

    # Test assignee unchanged when not included
    task.assignee = other_user
    task.save()
    update_data = {"task_title": "New title"}
    response = client.patch(f"/api/v2/task/{task.uuid}", json=update_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT
    task.refresh_from_db()
    assert task.assignee == other_user


def test_edit_task_clear_description(test_user: User) -> None:
    task = _create_task(test_user)

    update_data = {
        "description": "",  # Clear description
    }

    response = client.patch(f"/api/v2/task/{task.uuid}", json=update_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT

    task.refresh_from_db()
    assert task.task_desc == ""


def test_edit_task_keep_fields_unchanged(test_user: User) -> None:
    note = _create_note(test_user)
    task = _create_task(test_user, note=note)
    original_desc = task.task_desc
    original_completed = task.completed
    original_assignee = task.assignee

    update_data = {
        "title": "New Title",
        "parent_note_uuid": str(note.uuid),
    }

    response = client.patch(f"/api/v2/task/{task.uuid}", json=update_data)
    assert response.status_code == status.HTTP_204_NO_CONTENT

    task.refresh_from_db()
    assert task.task_title == "New Title"
    assert task.task_desc == original_desc
    assert task.completed == original_completed
    assert task.assignee == original_assignee
    assert task.note == note


def test_create_task_success(test_user: User) -> None:
    task_data = {
        "title": "Test Task",
        "owner_uuid": str(test_user.uuid),
        "description": "Test Description",
        "due_date": datetime.now(timezone.utc).isoformat(),
    }

    response = client.post("/api/v2/task/create", json=task_data)

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert "task_uuid" in data

    task = Task.objects.get(uuid=data["task_uuid"])
    assert task.task_title == "Test Task"
    assert task.task_desc == "Test Description"
    assert task.task_owner == test_user
    assert task.assignee == test_user


def test_create_task_with_note(test_user: User) -> None:
    note = Note.objects.create(note_owner=test_user)
    note.authorized_users.add(test_user)

    task_data = {
        "owner_uuid": str(test_user.uuid),
        "title": "Test Task",
        "description": "Test Description",
        "due_date": datetime.now(timezone.utc).isoformat(),
        "parent_note_uuid": str(note.uuid),
    }

    response = client.post("/api/v2/task/create", json=task_data)

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    task = Task.objects.get(uuid=data["task_uuid"])
    assert task.note == note


def test_create_task_unauthorized_user(
    test_user: User, django_user_model: User, test_organization: Organization
) -> None:
    other_user = django_user_model.objects.create(
        username="<EMAIL>",
        organization=test_organization,
        license_type=User.LicenseType.advisor,
    )

    note = _create_note(note_owner=other_user)
    note.authorized_users.add(test_user)

    task_data = {
        "owner_uuid": str(other_user.uuid),
        "title": "Test Task",
        "description": "Test Description",
        "due_date": datetime.now(timezone.utc).isoformat(),
        "parent_note_uuid": str(note.uuid),
    }

    response = client.post("/api/v2/task/create", json=task_data)

    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Not authorized to create task for another user"}


def test_create_task_superuser_for_other(
    test_user: User, django_user_model: User, test_organization: Organization
) -> None:
    other_user = django_user_model.objects.create(
        username="<EMAIL>",
        organization=test_organization,
        license_type=User.LicenseType.advisor,
    )

    # Make test_user a superuser
    test_user.is_superuser = True
    test_user.save()

    note = _create_note(note_owner=other_user)

    task_data = {
        "owner_uuid": str(other_user.uuid),
        "title": "Test Task",
        "description": "Test Description",
        "due_date": datetime.now(timezone.utc).isoformat(),
        "parent_note_uuid": str(note.uuid),
    }

    response = client.post("/api/v2/task/create", json=task_data)

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    task = Task.objects.get(uuid=data["task_uuid"])
    assert task.task_owner == other_user
    assert task.assignee == other_user
    assert task.note == note


def test_create_task_unauthorized_note(
    test_user: User, django_user_model: User, test_organization: Organization
) -> None:
    """Test creating a task for a note without authorization."""
    other_user = django_user_model.objects.create(
        username="<EMAIL>",
        organization=test_organization,
        license_type=User.LicenseType.advisor,
    )
    note = Note.objects.create(note_owner=other_user)

    task_data = {
        "owner_uuid": str(test_user.uuid),
        "title": "Test Task",
        "description": "Test Description",
        "due_date": datetime.now(timezone.utc).isoformat(),
        "parent_note_uuid": str(note.uuid),
    }

    response = client.post("/api/v2/task/create", json=task_data)

    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Not authorized to create task for this note"}


def test_create_task_user_not_found(test_user: User) -> None:
    # Make test_user a superuser to bypass the authorization check
    test_user.is_superuser = True
    test_user.save()

    note = _create_note(note_owner=test_user)

    task_data = {
        "owner_uuid": str(uuid.uuid4()),
        "title": "Test Task",
        "description": "Test Description",
        "due_date": datetime.now(timezone.utc).isoformat(),
        "parent_note_uuid": str(note.uuid),
    }

    response = client.post("/api/v2/task/create", json=task_data)

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Task owner not found"}


def test_create_task_minimal_fields(test_user: User) -> None:
    task_data = {
        "owner_uuid": str(test_user.uuid),
        "title": "Test Task",
    }

    response = client.post("/api/v2/task/create", json=task_data)

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    task = Task.objects.get(uuid=data["task_uuid"])
    assert task.task_title == "Test Task"
    assert task.task_desc is None
    assert task.due_date is None


def test_unauthenticated() -> None:
    dummy_uuid = uuid.uuid4()

    view_response = client.get(f"/api/v2/task/{dummy_uuid}")
    delete_response = client.delete(f"/api/v2/task/{dummy_uuid}")

    assert view_response.status_code == status.HTTP_403_FORBIDDEN
    assert delete_response.status_code == status.HTTP_403_FORBIDDEN


def test_view_task_success(test_user: User) -> None:
    task = _create_task(test_user)

    response = client.get(f"/api/v2/task/{task.uuid}")

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["uuid"] == str(task.uuid)
    assert data["title"] == "Test Task"
    assert data["description"] == "Test Description"
    assert data["completed"] is False
    assignee = {
        "uuid": str(test_user.uuid),
        "name": "Test User",
    }
    assert data["owner"] == assignee
    assert data["assignee"] == assignee
    assert data["assignees"] == [assignee]


def test_view_task_with_assignee(test_user: User, django_user_model: User, test_organization: Organization) -> None:
    assignee = django_user_model.objects.create(
        username="<EMAIL>",
        first_name="Assigned",
        last_name="User",
        organization=test_organization,
    )
    task = _create_task(test_user, assignee=assignee)

    response = client.get(f"/api/v2/task/{task.uuid}")

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["assignee"]["uuid"] == str(assignee.uuid)
    assert data["assignee"]["name"] == "Assigned User"


def test_view_task_with_note(test_user: User) -> None:
    note = Note.objects.create(note_owner=test_user)
    note.authorized_users.add(test_user)
    task = _create_task(test_user, note=note)

    response = client.get(f"/api/v2/task/{task.uuid}")

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["parent_note_uuid"] == str(note.uuid)


def test_view_task_not_found(test_user: User) -> None:
    non_existent_uuid = uuid.uuid4()

    response = client.get(f"/api/v2/task/{non_existent_uuid}")

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Task not found"}


@pytest.mark.parametrize(
    "license_type,is_superuser",
    [
        (User.LicenseType.csa, False),
        (User.LicenseType.staff, False),
        (User.LicenseType.advisor, True),
    ],
)
def test_view_task_authorized_users(
    test_user: User,
    django_user_model: User,
    test_organization: Organization,
    license_type: User.LicenseType,
    is_superuser: bool,
) -> None:
    task_owner = django_user_model.objects.create(
        username="<EMAIL>",
        organization=test_organization,
        license_type=User.LicenseType.advisor,
    )
    task = _create_task(task_owner)

    test_user.license_type = license_type
    test_user.is_superuser = is_superuser
    test_user.save()

    response = client.get(f"/api/v2/task/{task.uuid}")
    assert response.status_code == status.HTTP_200_OK


@pytest.mark.parametrize(
    "license_type,is_superuser",
    [
        (User.LicenseType.advisor, False),
    ],
)
def test_view_task_unauthorized_users(
    test_user: User,
    django_user_model: User,
    test_organization: Organization,
    license_type: User.LicenseType,
    is_superuser: bool,
) -> None:
    task_owner = django_user_model.objects.create(
        username="<EMAIL>",
        organization=test_organization,
        license_type=User.LicenseType.advisor,
    )
    task = _create_task(task_owner)

    test_user.license_type = license_type
    test_user.is_superuser = is_superuser
    test_user.save()

    response = client.get(f"/api/v2/task/{task.uuid}")
    assert response.status_code == status.HTTP_403_FORBIDDEN


def test_delete_task_success(test_user: User) -> None:
    task = _create_task(test_user)

    response = client.delete(f"/api/v2/task/{task.uuid}")

    assert response.status_code == status.HTTP_204_NO_CONTENT
    task.refresh_from_db()
    assert task.is_deleted is True


def test_delete_task_not_found(test_user: User) -> None:
    non_existent_uuid = uuid.uuid4()

    response = client.delete(f"/api/v2/task/{non_existent_uuid}")

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Task not found"}


def test_delete_task_unauthorized(test_user: User, django_user_model: User) -> None:
    other_org = Organization.objects.create(name="Other Org")  # Different organization
    other_user = django_user_model.objects.create(
        username="<EMAIL>",
        organization=other_org,
    )
    task = _create_task(other_user)

    response = client.delete(f"/api/v2/task/{task.uuid}")

    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Not authorized to view this task"}


def test_delete_task_error(test_user: User) -> None:
    task = _create_task(test_user)

    with patch.object(Task, "save") as mock_save:
        mock_save.side_effect = Exception("Database error")
        response = client.delete(f"/api/v2/task/{task.uuid}")

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {"detail": "An error occurred while deleting the task"}
    task.refresh_from_db()
    assert task.is_deleted is False


def test_view_task_authorized_users_from_note(
    test_user: User, django_user_model: User, test_organization: Organization
) -> None:
    other_user = django_user_model.objects.create(
        username="<EMAIL>",
        organization=test_organization,
    )
    note = Note.objects.create(note_owner=other_user)
    note.authorized_users.add(test_user)
    task = _create_task(other_user, note=note)

    response = client.get(f"/api/v2/task/{task.uuid}")

    assert response.status_code == status.HTTP_200_OK


def test_view_task_assignee_can_view(test_user: User, django_user_model: User, test_organization: Organization) -> None:
    owner = django_user_model.objects.create(
        username="<EMAIL>",
        organization=test_organization,
    )
    task = _create_task(owner, assignee=test_user)

    response = client.get(f"/api/v2/task/{task.uuid}")

    assert response.status_code == status.HTTP_200_OK


def test_list_tasks_success(test_user: User, django_user_model: User) -> None:
    other_user = django_user_model.objects.create_user("<EMAIL>", first_name="Other", last_name="User")
    note = Note.objects.create(note_owner=test_user)
    tasks = [
        Task.objects.create(
            task_owner=test_user,
            task_title="Empty task",
        ),
        Task.objects.create(
            task_owner=test_user,
            task_title="Task with description",
            task_desc="This is a test task",
            completed=True,
            assignee=other_user,
            note=note,
        ),
        Task.objects.create(
            task_owner=test_user,
            task_title="Task with due date",
            task_desc="This is a test task",
            due_date=django_timezone.now() + timedelta(days=7),
            completed=False,
        ),
    ]

    response = client.get(f"/api/v2/task/list_tasks/{test_user.uuid}")

    tasks.reverse()
    assert response.status_code == status.HTTP_200_OK
    assert response.json()["data"] == [
        {
            "uuid": str(task.uuid),
            "title": task.task_title,
            "description": task.task_desc,
            "due_date": task.due_date.isoformat().replace("+00:00", "Z") if task.due_date else None,
            "created": task.created.isoformat().replace("+00:00", "Z"),
            "modified": task.modified.isoformat().replace("+00:00", "Z"),
            "completed": task.completed,
            "parent_note_uuid": None,  # Parent notes are never set for the task list API
            "owner": {
                "uuid": str(task.task_owner.uuid),
                "name": f"{task.task_owner.first_name} {task.task_owner.last_name}",
            }
            if task.task_owner
            else None,
            "assignee": {
                "uuid": str(task.assignee.uuid),
                "name": f"{task.assignee.first_name} {task.assignee.last_name}",
            }
            if task.assignee
            else None,
            "assignees": [
                {
                    "uuid": str(task.assignee.uuid),
                    "name": f"{task.assignee.first_name} {task.assignee.last_name}",
                }
            ]
            if task.assignee
            else None,
        }
        for task in tasks
    ]


def test_list_tasks_csa_can_view_org_tasks(
    test_user: User, django_user_model: User, test_organization: Organization
) -> None:
    other_user = django_user_model.objects.create(
        username="<EMAIL>",
        organization=test_organization,
        license_type=User.LicenseType.advisor,
    )
    test_user.license_type = User.LicenseType.csa
    test_user.save()

    [_create_task(other_user) for _ in range(2)]

    response = client.get(f"/api/v2/task/list_tasks/{other_user.uuid}")

    assert response.status_code == status.HTTP_200_OK
    data = response.json()["data"]
    assert len(data) == 2


def test_list_tasks_advisor_cannot_view_others(
    test_user: User, django_user_model: User, test_organization: Organization
) -> None:
    other_user = django_user_model.objects.create(
        username="<EMAIL>",
        organization=test_organization,
        license_type=User.LicenseType.advisor,
    )
    [_create_task(other_user) for _ in range(2)]

    response = client.get(f"/api/v2/task/list_tasks/{other_user.uuid}")

    assert response.status_code == status.HTTP_403_FORBIDDEN


def test_list_tasks_different_org(test_user: User, django_user_model: User) -> None:
    other_org = Organization.objects.create(name="Other Org")
    other_user = django_user_model.objects.create(
        username="<EMAIL>",
        organization=other_org,
        license_type=User.LicenseType.advisor,
    )
    test_user.license_type = User.LicenseType.csa
    test_user.save()

    response = client.get(f"/api/v2/task/list_tasks/{other_user.uuid}")

    assert response.status_code == status.HTTP_403_FORBIDDEN


def test_list_tasks_user_not_found(test_user: User) -> None:
    response = client.get(f"/api/v2/task/list_tasks/{uuid.uuid4()}")
    assert response.status_code == status.HTTP_404_NOT_FOUND


def test_list_tasks_deleted_tasks_excluded(test_user: User) -> None:
    task = _create_task(test_user)
    task.is_deleted = True
    task.save()

    response = client.get(f"/api/v2/task/list_tasks/{test_user.uuid}")

    assert response.status_code == status.HTTP_200_OK
    data = response.json()["data"]
    assert len(data) == 0


def test_list_tasks_with_search(test_user: User) -> None:
    task1 = _create_task(test_user)
    task1.task_title = "Project Alpha Review"
    task1.save()

    task2 = _create_task(test_user)
    task2.task_title = "Project Beta Planning"
    task2.save()

    task3 = _create_task(test_user)
    task3.task_title = "Meeting with Alpha Team"
    task3.save()

    response = client.get(f"/api/v2/task/list_tasks/{test_user.uuid}?search_query=Alpha")

    assert response.status_code == status.HTTP_200_OK
    data = response.json()["data"]
    assert len(data) == 2
    task_titles = {task["title"] for task in data}
    assert task_titles == {"Project Alpha Review", "Meeting with Alpha Team"}


def test_list_tasks_search_case_insensitive(test_user: User) -> None:
    task1 = _create_task(test_user)
    task1.task_title = "URGENT Meeting"
    task1.save()

    task2 = _create_task(test_user)
    task2.task_title = "urgent task"
    task2.save()

    response = client.get(f"/api/v2/task/list_tasks/{test_user.uuid}?search_query=urgent")

    assert response.status_code == status.HTTP_200_OK
    data = response.json()["data"]
    assert len(data) == 2
    task_titles = {task["title"] for task in data}
    assert task_titles == {"URGENT Meeting", "urgent task"}


def test_list_tasks_empty_search_returns_all(test_user: User) -> None:
    tasks = [_create_task(test_user) for _ in range(3)]

    response = client.get(f"/api/v2/task/list_tasks/{test_user.uuid}?search_query=")

    assert response.status_code == status.HTTP_200_OK
    data = response.json()["data"]
    assert len(data) == 3
    task_uuids = {task["uuid"] for task in data}
    assert task_uuids == {str(task.uuid) for task in tasks}


def test_list_tasks_no_match_search(test_user: User) -> None:
    task = _create_task(test_user)
    task.task_title = "Project Review"
    task.save()

    response = client.get(f"/api/v2/task/list_tasks/{test_user.uuid}?search_query=nonexistent")

    assert response.status_code == status.HTTP_200_OK
    data = response.json()["data"]
    assert len(data) == 0


def test_list_tasks_search_special_characters(test_user: User) -> None:
    task = _create_task(test_user)
    task.task_title = "Project #123 & Review"
    task.save()

    response = client.get(f"/api/v2/task/list_tasks/{test_user.uuid}?search_query=#123")

    assert response.status_code == status.HTTP_200_OK
    data = response.json()["data"]
    assert len(data) == 1
    assert data[0]["title"] == "Project #123 & Review"


def test_list_tasks_search_multiple_words(test_user: User) -> None:
    task1 = _create_task(test_user)
    task1.task_title = "Project Review Meeting"
    task1.save()

    task2 = _create_task(test_user)
    task2.task_title = "Review Project Status"
    task2.save()

    task3 = _create_task(test_user)
    task3.task_title = "Team Meeting"
    task3.save()

    response = client.get(f"/api/v2/task/list_tasks/{test_user.uuid}?search_query=Project Review")

    assert response.status_code == status.HTTP_200_OK
    data = response.json()["data"]
    assert len(data) == 2
    task_titles = {task["title"] for task in data}
    assert task_titles == {"Project Review Meeting", "Review Project Status"}
