import datetime
import logging
from typing import Any
from uuid import uuid4

from asgiref.sync import async_to_sync
from django.db.models import Q
from django.utils import timezone
from fastapi import APIRouter, Response, status
from pydantic import BaseModel

from deepinsights.core.integrations.calendar import google, microsoft
from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.calendar.participant_utils import events_with_zeplyn_attendees
from deepinsights.core.integrations.meetingbot.recall_ai import RecallBotController
from deepinsights.core.integrations.oauth.microsoft import MicrosoftOAuth
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.meeting_bot import MeetingBot
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.meetingsapp.tasks import update_bots_for_calendar_events
from deepinsights.users.models.user import User

logger = logging.getLogger(__name__)

router = APIRouter(tags=["bot_webhook"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")


class WebhookRequest(BaseModel):
    event: str
    data: dict[str, Any]


@router.post("/recall", include_in_schema=False)
def post(webhook: WebhookRequest) -> Response:
    """Handles incoming webhook requests from Recall.ai."""
    try:
        event = webhook.event
        data = webhook.data

        if event == "bot.status_change":
            return __handle_bot_status_change(data)
        elif event.startswith("calendar."):
            return __handle_calendar_event(event, data)
        else:
            logging.warning("Received a Recall webhook call with an unhandled event type: %s. Ignoring.", event)
            return Response(status_code=status.HTTP_200_OK)

    except Exception as e:
        logging.error("Error handling Recall webhook call", exc_info=e)
        return Response(status_code=status.HTTP_200_OK)


# Creates a Zeplyn note and bot for a meeting that was autojoined by a Recall bot (or links the
# autojoin bot to an existing note.)
def __link_or_create_note_for_autojoined_bot_if_needed(bot_id: str, bot_status: str) -> MeetingBot | None:
    bot_controller = RecallBotController(bot_id)

    if not (calendar_event := bot_controller.calendar_event()):
        logging.info("Meeting was not an auto-joined meeting: no calendar event. Not creating/linking note and bot.")
        return None
    if not (zeplyn_user_uuid := bot_controller.zeplyn_user_uuid()):
        logging.info("Meeting was not an auto-joined meeting: no Zeplyn user UUID. Not creating/linking note and bot.")
        return None
    if not (user := User.objects.filter(uuid=zeplyn_user_uuid).first()):
        logging.info(
            "Meeting was not an auto-joined meeting: Zeplyn user not found. Not creating/linking note and bot."
        )
        return None

    attendees, meeting_title, meeting_url, event_id, user_specific_event_id = __parse_info_from_bot_meeting(
        calendar_event, user
    )

    # Find the most-relevant existing note for this meeting, or create a note if none exists.
    notes = (
        Note.objects.filter(status=Note.PROCESSING_STATUS.scheduled)
        .filter(authorized_users__in=[user])
        .filter(
            Q(metadata__meeting_source_id=event_id)
            | Q(_scheduled_event__user_specific_source_id=user_specific_event_id)
            | Q(_scheduled_event__shared_source_id=event_id)
        )
    )
    note = notes.first()
    if notes.count() > 1:
        logging.warning("Multiple notes match this calendar event. Looking for the most-relevant one.")
        # Find the most-relevant note by looking for the oldest note that does not have a client
        # meeting type, falling back to the oldest note with client meeting type, in both cases
        # preferring notes that matched because the matching scheduled event has the same
        # user-specific event ID.
        note = (
            notes.annotate(
                is_default_meeting_type=Q(meeting_type=MeetingType.objects.get(key="client")),
                user_level_match=Q(_scheduled_event__isnull=False)
                & Q(_scheduled_event__user_specific_source_id=user_specific_event_id),
                shared_level_match=Q(_scheduled_event__isnull=False) & Q(_scheduled_event__shared_source_id=event_id),
            )
            .order_by("-user_level_match", "-shared_level_match", "is_default_meeting_type", "created")
            .first()
        ) or note

    if not note:
        logging.info("Creating note for auto-joined meeting")
        metadata = {"meeting_name": meeting_title or "Auto-joined meeting"}
        if event_id:
            metadata["meeting_source_id"] = event_id

        note = Note.objects.create(
            note_owner=user,
            note_type=Note.NOTE_TYPE.meeting_recording,
            status=Note.PROCESSING_STATUS.uploaded
            if bot_status == "call_ended" or bot_status == "done"
            else Note.PROCESSING_STATUS.scheduled,
            metadata=metadata,
            meeting_type=MeetingType.objects.get(key="client"),
        )

        scheduled_event: ScheduledEvent | None = None
        try:
            scheduled_event = ScheduledEvent.objects.get(user_specific_source_id=user_specific_event_id, user=user)
        except ScheduledEvent.DoesNotExist:
            scheduled_event = ScheduledEvent.objects.filter(shared_source_id=event_id).order_by("-created").first()
        if scheduled_event:
            scheduled_event.note = note
            scheduled_event.save()

        note.authorized_users.add(user)
        if attendees:
            Attendee.reconcile_attendees(note, attendees)
            if Flags.EnableSetAttendeesAsAuthorizedUsers.is_active_for_user(user):
                for attendee in note.attendees.filter(user__isnull=False):
                    if attendee.user:
                        note.authorized_users.add(attendee.user)
        note.save()

    # Create or update the meeting bots for this note.
    #
    # At this point, we assume that there is not a Zeplyn MeetingBot associated with the Recall
    # bot ID; if there were, this method would not have been called, because the bot would have
    # been fetched from the database and its status updated appropriately.

    bots = note.meetingbot_set.all().order_by("created")

    # Warn if there are already bots with a valid Recall bot ID. It's possible that these bots
    # contain more information than the autojoin bot, and we may want to reprocess the note later
    # with one of these bots.
    #
    # See the comment above for why we don't filter for the auto join bot's Recall bot ID.
    if bots.filter(recall_bot_id__isnull=False).exists():
        logging.warning(
            "Note %s already has at least one bot with a valid Recall bot ID. All bot UUIDs: %s",
            str(note.uuid),
            str([str(b.uuid) for b in bots]),
        )

    # Attach the Recall bot ID for the autojoin bot to the first bot that does not have a Recall
    # bot ID, and which has the same meeting link, if any such bot exists. Otherwise, create a new
    # bot with the Recall bot ID.
    if null_bot_id_bot := bots.filter(recall_bot_id__isnull=True).filter(meeting_link=meeting_url).first():
        logging.info(
            "Linking Recall bot ID to existing bot %s without bot ID, but with same meeting link",
            str(null_bot_id_bot.uuid),
        )
        null_bot_id_bot.recall_bot_id = bot_id
        null_bot_id_bot.save()
        autojoin_bot = null_bot_id_bot
    else:
        logging.info("Creating new bot for auto-joined meeting")
        autojoin_bot = MeetingBot.objects.create(
            note=note,
            bot_owner=user,
            recall_bot_id=bot_id,
            meeting_link=meeting_url,
        )

    # Detach the rest of the bots from the note, so that they don't interfere with the autojoin bot.
    for bot in bots.exclude(uuid=autojoin_bot.uuid):
        logging.warning("Disconecting bot %s from note %s", str(bot.uuid), str(note.uuid))
        bot.note = None
        bot.save()

    return autojoin_bot


# Parses out meeting information from a Recall calendar event.
#
# Returns a tuple: attendees, meeting title, meeting URL, and event ID any of which may be an
# empty value (but not None).
def __parse_info_from_bot_meeting(
    calendar_event: dict[str, Any], user: User
) -> tuple[list[dict[str, Any]], str, str, str, str]:
    attendees: list[dict[str, Any]] = []
    meeting_url = calendar_event.get("meeting_url", "")
    meeting_title = ""
    event_id = ""
    user_specific_event_id = ""
    calendar_provider = calendar_event.get("platform", "")
    raw_event = calendar_event.get("raw")

    if not raw_event:
        logging.error("No raw event found in Recall calendar event %s", calendar_event)
        return attendees, meeting_title, meeting_url, event_id, user_specific_event_id

    entries: list[CalendarEvent] = []
    if calendar_provider == "google_calendar":
        entries = google.calendar_entries_from_events([raw_event])
    elif calendar_provider == "microsoft_outlook":
        try:
            # Refetch the event, so that we can ensure we have correct, consistent identifiers.
            ms_oauth = MicrosoftOAuth()
            ms_access_token = async_to_sync(ms_oauth.get_access_token)(user)
            ms_expires_on = int(
                datetime.datetime.timestamp(
                    async_to_sync(ms_oauth.get_expiry)(user) or timezone.now() + datetime.timedelta(minutes=5)
                )
            )
            if not ms_access_token or not ms_expires_on:
                logging.error(
                    "Could not access MS calendar for %s. Returning empty calendar event details.",
                    user.uuid,
                )
                return attendees, meeting_title, meeting_url, event_id, user_specific_event_id
            if not (ms_event_id := raw_event.get("id")):
                logging.error("No MS event ID found in Recall calendar event %s", raw_event)
                return attendees, meeting_title, meeting_url, event_id, user_specific_event_id
            if not (
                ms_event := async_to_sync(microsoft.fetch_calendar_event)(
                    microsoft.MicrosoftCredentials.from_access_token(
                        access_token=ms_access_token, expires_on=ms_expires_on
                    ),
                    ms_event_id,
                )
            ):
                logging.error("Could not fetch MS calendar event %s", ms_event_id)
                return attendees, meeting_title, meeting_url, event_id, user_specific_event_id
            entries = [ms_event]
        except Exception as e:
            logging.error("Could not get Microsoft calendar event from Recall calendar event", exc_info=e)

    entries = events_with_zeplyn_attendees(entries, user)
    if not entries:
        logging.error("Could not parse out entries from calendar event: %s", raw_event)
        return attendees, meeting_title, meeting_url, event_id, user_specific_event_id

    event = entries[0]
    meeting_title = event.title
    event_id = event.id
    user_specific_event_id = event.user_specific_id

    for participant in event.participants:
        attendees.append(
            {
                "name": participant.name or participant.email_address,
                "type": participant.zeplyn_kind.value if participant.zeplyn_kind else "unknown",
                "uuid": str(participant.zeplyn_uuid) if participant.zeplyn_uuid else uuid4(),
            }
        )

    if not meeting_title and (participant_titles := [p.name or p.email_address for p in event.participants]):
        if len(participant_titles) > 4:
            participant_titles = participant_titles[:3] + ["others"]
        meeting_title = f"Meeting with {', '.join(participant_titles)}"

    return attendees, meeting_title, meeting_url, event_id, user_specific_event_id


# Handles a Recall.ai bot status change webhook call.
#
# This will try to find a local bot that matches the Recall bot, or create one if the Recall bot
# was created via the calendar integration. If it finds a bot, it will handle end-of-recording
# events and attempt to process the bot recording.
def __handle_bot_status_change(data: dict[str, Any]) -> Response:
    bot_id: str = data.get("bot_id", "")
    status_codes = data.get("status", {})
    bot_status = status_codes.get("code")
    sub_status = status_codes.get("sub_code")

    log_status = logging.error if bot_status == "fatal" else logging.info
    log_status("Bot %s status change to %s:%s", bot_id, bot_status, sub_status)

    # Try to find the bot in our local datastore by its Recall bot ID.
    bot = None
    try:
        bot = MeetingBot.objects.get(recall_bot_id=bot_id)
    except MeetingBot.DoesNotExist:
        try:
            # If there is no bot with a matching Recall bot ID, get the Zeplyn ID attached to the
            # Recall bot (if any), and update the bot based on that.
            recall_bot = RecallBotController(bot_id)
            bot_uuid = recall_bot.get_internal_bot_uuid()
            if not bot_uuid:
                raise MeetingBot.DoesNotExist

            bot = MeetingBot.objects.get(uuid=bot_uuid)
            bot.recall_bot_id = recall_bot.bot_id
            bot.save()
            logging.warning("Bot %s did not have recall bot id. Updated recall_bot_id.", bot_uuid)

        except MeetingBot.DoesNotExist:
            # If there is no bot with a matching Zeplyn bot ID, see if the bot was autocreated for a
            # calendar event and create a bot if so (but only if the bot status is
            # "joining_call"; this is a somewhat-arbitrary selection, but it's the first event
            # in Recall's bot state machine: https://docs.recall.ai/docs/bot-status-change-events).
            if bot_status == "joining_call":
                bot = __link_or_create_note_for_autojoined_bot_if_needed(bot_id, bot_status)

    if not bot:
        return Response(status_code=status.HTTP_204_NO_CONTENT)

    if not (note := bot.note):
        logging.info(
            "Bot %s status is %s with Recall bot ID %s, no note found. "
            "This may be because the previously-associated note was saved with mic audio",
            bot.uuid,
            bot_status,
            bot.recall_bot_id,
        )
        return Response(status_code=status.HTTP_204_NO_CONTENT)

    # Marks the provided Note as being in the `uploaded` state.
    def mark_note_as_uploaded(note: Note) -> None:
        note.status = Note.PROCESSING_STATUS.uploaded
        note.save()
        logging.info(
            "Bot %s status is %s with Recall bot ID %s, note %s marked as uploaded",
            bot.uuid,
            bot_status,
            bot.recall_bot_id,
            note.uuid,
        )

    if bot_status == "call_ended":
        mark_note_as_uploaded(note)
    if bot_status == "done" or bot_status == "fatal":
        mark_note_as_uploaded(note)
        bot.processing_task.delay_on_commit(bot.uuid, False)

    logging.info("Bot %s status updated", bot.uuid)
    return Response(status_code=status.HTTP_200_OK)


# Handles a Recall.ai calendar event webhook call.
def __handle_calendar_event(event: str, data: dict[str, Any]) -> Response:
    if not (calendar_id := data.get("calendar_id")):
        logging.warning("Received an invalid Recall calendar.sync_events webhook call: %s. Ignoring", data)
        return Response(status_code=status.HTTP_200_OK)

    if event == "calendar.update":
        logging.debug("Ignoring calendar update event: %s", data)
        return Response(status_code=status.HTTP_200_OK)

    if event == "calendar.sync_events":
        logging.debug("Handling calendar sync event: %s", data)
        if not (last_updated_ts := data.get("last_updated_ts")):
            logging.warning("Received an invalid Recall calendar.sync_events webhook call: %s. Ignoring", data)
            return Response(status_code=status.HTTP_200_OK)

        if not (user := User.objects.filter(metadata__recall_calendar_id=calendar_id).first()):
            logging.info(
                "Received a Recall calendar.sync_events webhook call for an unknown user's calendar: %s. Ignoring",
                calendar_id,
            )
            return Response(status_code=status.HTTP_200_OK)

        update_bots_for_calendar_events.delay_on_commit(calendar_id, last_updated_ts, user.uuid)
        return Response(status_code=status.HTTP_200_OK)

    logging.warning("Received an unexpected Recall calendar webhook call: %s. Ignoring", event)
    return Response(status_code=status.HTTP_200_OK)
