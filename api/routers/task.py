import logging
from uuid import UUID

from django.contrib.postgres.search import SearchQuery, SearchVector
from django.db.models import Q
from fastapi import APIRouter, Depends, HTTPException, Path, Query, Response, status

from api.dependencies import user_from_authorization_header
from api.routers.task_models import (
    Client,
    CreateTaskRequest,
    CreateTaskResponse,
    ListTasksResponse,
    TaskResponse,
    TaskUpdate,
    get_lite_task_response,
)
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User

router = APIRouter(tags=["task"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")
logger = logging.getLogger(__name__)


def is_authorized_to_view_task(user: User, task: Task) -> bool:
    return (
        user == task.task_owner
        or user == task.assignee
        or (task.note and user in task.note.authorized_users.all())
        or user.is_superuser
        or (
            (user.license_type == User.LicenseType.csa or user.license_type == User.LicenseType.staff)
            and (user.organization == task.task_owner.organization if task.task_owner else False)
        )
    )


@router.post(
    "/create",
    responses={
        200: {"description": "Task created successfully"},
        400: {"description": "Bad Request"},
        403: {"description": "Forbidden"},
        404: {"description": "Not Found"},
        500: {"description": "Internal Server Error"},
    },
    summary="Create a task",
    description="Create a task for a specific user. The user must be authorized to create a task.",
)
def create_task(
    task_data: CreateTaskRequest,
    user: User = Depends(user_from_authorization_header),
) -> CreateTaskResponse:
    if task_data.owner_uuid != user.uuid and not user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to create task for another user"
        )

    try:
        task_owner = User.objects.get(uuid=task_data.owner_uuid)
    except User.DoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Task owner not found")

    note = None
    if task_data.parent_note_uuid:
        try:
            note = Note.objects.get(uuid=task_data.parent_note_uuid)
            note.meetingbot_set.all()
        except Note.DoesNotExist:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid UUID for parent note")
        if task_owner not in note.authorized_users.all():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to create task for this note"
            )
    try:
        task = Task.objects.create(
            task_title=task_data.title,
            task_desc=task_data.description,
            task_owner=task_owner,
            due_date=task_data.due_date,
            note=note,
            assignee=task_owner,
        )

        return CreateTaskResponse(task_uuid=task.uuid)

    except Exception as e:
        logger.error("Failed to create task", exc_info=e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get(
    "/{task_uuid}",
    response_model=TaskResponse,
    responses={
        403: {"description": "Not authorized to view this task"},
        404: {"description": "Task not found"},
        500: {"description": "An error occurred while retrieving the Task"},
    },
    summary="Retrieve data of a specific Task",
    description="Retrieve details of a specific task by its ID. The user must be authorized to view the task.",
)
def view_task(
    task_uuid: UUID = Path(
        ..., examples=["123e4567-e89b-12d3-a456-************"], description="The UUID of the task to retrieve"
    ),
    user: User = Depends(user_from_authorization_header),
) -> TaskResponse:
    task = task = (
        Task.objects.select_related("task_owner", "assignee", "note", "task_owner__organization")
        .prefetch_related("note__authorized_users", "task_owner__organization__users")
        .filter(uuid=task_uuid)
        .first()
    )
    if not task:
        logging.error("Task with uuid %s not found", task_uuid)
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Task not found")

    if not is_authorized_to_view_task(user, task):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view this task")

    try:
        assignee = task.assignee or task.task_owner

        return TaskResponse(
            uuid=task.uuid,
            title=task.task_title,
            description=task.task_desc,
            due_date=task.due_date,
            created=task.created,
            modified=task.modified,
            completed=task.completed,
            parent_note_uuid=task.get_parent_note_uuid(),
            owner=Client(uuid=task.task_owner.uuid, name=task.task_owner.get_full_name()) if task.task_owner else None,
            assignee=Client(uuid=assignee.uuid, name=assignee.get_full_name()) if assignee else None,
            assignees=[Client(uuid=a.uuid, name=a.get_full_name()) for a in task.possible_assignees],
        )

    except Exception as e:
        logging.error("Error fetching task", exc_info=e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Error fetching task") from e


@router.delete(
    "/{task_uuid}",
    responses={
        204: {"description": "Successful response"},
        403: {"description": "Not authorized to delete this task"},
        404: {"description": "Task not found"},
        500: {"description": "An error occurred while deleting the task"},
    },
    summary="Delete a specific task",
    description="Delete a specific task by its ID. The user must be authorized to view the note.",
)
def delete_task(
    task_uuid: UUID = Path(
        ..., examples=["123e4567-e89b-12d3-a456-************"], description="The UUID of the task to delete"
    ),
    user: User = Depends(user_from_authorization_header),
) -> Response:
    try:
        task = Task.objects.get(uuid=task_uuid)
    except Task.DoesNotExist as dne:
        logging.error("Task with uuid %s not found", task_uuid, exc_info=dne)
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Task not found") from dne
    if not is_authorized_to_view_task(user, task):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view this task")

    try:
        task.is_deleted = True
        task.save()

        return Response(status_code=status.HTTP_204_NO_CONTENT)

    except Exception as e:
        logging.error("Error deleting task", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An error occurred while deleting the task"
        ) from e


@router.get(
    "/list_tasks/{user_uuid}",
    response_model=ListTasksResponse,
    responses={
        200: {"description": "Successful response"},
        403: {"description": "Not authorized to see tasks for this user"},
        404: {"description": "User not found"},
        500: {"description": "An error occurred while listing the tasks"},
    },
)
def list_tasks(
    user_uuid: UUID,
    search_query: str = Query(None, description="Search query to filter tasks by title"),
    user: User = Depends(user_from_authorization_header),
) -> ListTasksResponse:
    try:
        target_user = User.objects.select_related("organization").get(uuid=user_uuid)
    except User.DoesNotExist as ude:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found") from ude

    if target_user != user:
        if not (target_user.organization == user.organization and user.license_type == User.LicenseType.csa):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to see tasks for this user"
            )
    try:
        tasks = Task.objects.select_related("task_owner", "task_owner__organization", "assignee").only(
            "created",
            "modified",
            "completed",
            "task_desc",
            "task_title",
            "uuid",
            "assignee",
            "due_date",
            "task_owner",
        )

        if user.license_type == User.LicenseType.csa:
            tasks = tasks.filter(task_owner__organization=target_user.organization)
        else:
            tasks = tasks.filter(Q(task_owner=target_user) | Q(assignee=target_user))

        if search_query:
            tasks = tasks.annotate(search=SearchVector("task_title")).filter(search=SearchQuery(search_query))

        tasks = tasks.order_by("-created")

        return ListTasksResponse(
            data=[get_lite_task_response(task) for task in tasks],
        )

    except Exception as e:
        logger.error("Failed to list tasks", exc_info=e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)) from e


@router.patch("/{task_uuid}")
def edit_task(
    task_uuid: UUID,
    task_data: TaskUpdate,
    user: User = Depends(user_from_authorization_header),
) -> Response:
    task = Task.objects.filter(uuid=task_uuid).first()
    if not task:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Task not found")

    if not is_authorized_to_view_task(user, task):
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to edit this task")

    try:
        if task_data.title and task_data.title.strip() != "":
            task.task_title = task_data.title
        if task_data.description is not None:
            task.task_desc = task_data.description
        if task_data.due_date is not None:
            task.due_date = task_data.due_date
        if task_data.completed is not None:
            task.completed = task_data.completed
        if task_data.assignee is not None:
            task.assignee = User.objects.filter(uuid=task_data.assignee).first()

        if task_data.parent_note_uuid is not None:
            # Link to specific note
            task.note = (
                Note.objects.defer("raw_asr_response", "diarized_trans_with_names", "summary", "summary_by_topics")
                .filter(uuid=task_data.parent_note_uuid)
                .first()
            )
        else:
            # Explicitly set to None
            task.note = None

        task.save()

        logging.info("Task %s updated successfully", str(task.uuid))
        return Response(status_code=status.HTTP_204_NO_CONTENT)

    except Exception as e:
        logger.error("Failed to update task", exc_info=e)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)) from e
