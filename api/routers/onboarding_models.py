import csv
import io
import logging
from typing import Any

from asgiref.sync import sync_to_async
from django.utils import timezone
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from pydantic import BaseModel, EmailStr, Field

from deepinsights.core.preferences.preferences import (
    BotPreferences,
    EmailConfiguration,
    Preferences,
    get_default_preferences,
)
from deepinsights.meetingsapp.admin_utils import process_users_from_csv
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.admin_utils import handle_bulk_user_onboarding
from deepinsights.users.models.user import User

logger = logging.getLogger(__name__)

default_transcript_ttl = 30


# Pydantic models for request validation
class BotPreferencesRequest(BaseModel):
    not_recording_image_b64: str | None = None
    recording_image_b64: str | None = None
    recording_message_b64: str | None = None
    notetaker_name: str | None = None
    enable_video: bool | None = None
    enable_audio_output: bool | None = None

    def update_dataclass(self, bot_prefs: BotPreferences) -> BotPreferences:
        """Update the dataclass with non-None values from this model"""
        for field_name, value in self.__dict__.items():
            if value is not None:
                setattr(bot_prefs, field_name, value)
        return bot_prefs


class EmailConfigurationRequest(BaseModel):
    ccs: list[str] | None = None
    bccs: list[str] | None = None
    followup_email_format_prompt: str | None = None
    meeting_notes_email_template: str | None = None

    def update_dataclass(self, email_config: EmailConfiguration) -> EmailConfiguration:
        """Update the dataclass with non-None values from this model"""
        for field_name, value in self.__dict__.items():
            if value is not None:
                setattr(email_config, field_name, value)
        return email_config


class PreferencesRequest(BaseModel):
    attach_transcript_to_follow_up_emails: bool | None = None
    show_transcript_in_frontend: bool | None = None
    send_followup_email: bool | None = None
    send_task_reminder_email: bool | None = None
    delete_buffer: bool | None = None
    due_date_offset_seconds: int | None = None
    email_settings: EmailConfigurationRequest | None = None
    bot_preferences: BotPreferencesRequest | None = None
    asr_language_code: str | None = None

    def to_dataclass(self) -> Preferences:
        """Convert this request model to a Preferences dataclass"""
        # Get default preferences as starting point
        default_prefs = get_default_preferences()

        # Create a new Preferences instance from these defaults
        p = Preferences.from_dict(default_prefs)

        # Override with provided values
        for field_name, value in self.__dict__.items():
            if value is not None:
                if field_name == "bot_preferences" and value is not None:
                    p.bot_preferences = value.update_dataclass(p.bot_preferences)
                elif field_name == "email_settings" and value is not None:
                    p.email_settings = value.update_dataclass(p.email_settings)
                else:
                    setattr(p, field_name, value)

        return p


class OrganizationData(BaseModel):
    name: str
    description: str | None = None
    transcript_ttl: int | None = None
    preferences: PreferencesRequest | None = None
    existing: bool | None = False  # Flag to indicate if this is an existing org
    id: str | None = None  # Organization ID for updates (required if existing=True)


class UserData(BaseModel):
    email: EmailStr
    first_name: str
    last_name: str
    type: str = "advisor"  # Default to advisor
    send_welcome_email: bool | None = False
    preferences: dict[str, Any] | None = None
    existing: bool | None = False  # Flag to indicate if this is an existing user


class OnboardingRequest(BaseModel):
    organization: OrganizationData
    users: list[UserData]
    api_key: str | None = Field(None, description="Optional API key for authentication")


class OnboardingResponse(BaseModel):
    success: bool
    organization_id: str | None = None
    users_processed: int = 0  # Total users processed (created + updated)
    welcome_emails_sent: int = 0
    errors: list[str] = []


# Organization processing functions
def _prepare_preferences(preferences_data: PreferencesRequest | None) -> dict[str, Any]:
    """Prepare preferences dictionary from request data or defaults."""
    if preferences_data:
        # Convert request to dataclass and then to dict
        return preferences_data.to_dataclass().to_dict()
    else:
        # Use default preferences
        return get_default_preferences()


def _get_or_create_organization(data: OrganizationData, preferences: dict[str, Any]) -> Organization:
    """Get an existing organization or create a new one."""
    org = None

    # If we have an explicit ID to update
    if data.existing and data.id:
        try:
            org = Organization.objects.get(id=data.id)
            logger.info("Found organization with ID %s for update", data.id)
        except Organization.DoesNotExist:
            raise ValueError("Organization with id %s not found", data.id)

        if data.description:
            org.description = data.description

        if data.transcript_ttl:
            org.transcript_ttl = data.transcript_ttl

        if not org.preferences:
            org.preferences = preferences

        for key, value in preferences.items():
            org.preferences[key] = value

        org.save()

    # Otherwise, create it
    else:
        current_time = timezone.now()
        org = Organization.objects.create(
            name=data.name,
            description=data.description or f"Created via API on {current_time.strftime('%Y-%m-%d %H:%M:%S')}",
            transcript_ttl=data.transcript_ttl or default_transcript_ttl,
            preferences=preferences,
        )
        logger.info("Created new organization: %s (ID: %s)", org.name, org.id)

    logger.info("Updated organization: %s ID: %s", org.name, str(org.id))

    return org


async def process_organization_creation(data: OrganizationData) -> Organization:
    """Create a new organization or update an existing one from the provided data."""
    try:
        # Get preferences asynchronously
        preferences = await sync_to_async(_prepare_preferences)(data.preferences)

        # Get or create organization using sync_to_async
        organization = await sync_to_async(_get_or_create_organization)(data, preferences)

        return organization
    except Exception as e:
        logger.error("Error processing organization", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error processing organization: {str(e)}"
        )


# User processing functions
def _update_existing_users(org: Organization, users_to_update: list[UserData]) -> tuple[int, list[str]]:
    """Update existing users in the organization."""
    updated = 0
    update_errors = []

    for user_data in users_to_update:
        try:
            user = User.objects.get(email=user_data.email)

            if user.organization and user.organization.id != org.id:
                update_errors.append(f"User {user_data.email} belongs to a different organization")
                continue

            if user_data.first_name:
                user.first_name = user_data.first_name

            if user_data.last_name:
                user.last_name = user_data.last_name

            if user_data.first_name or user_data.last_name:
                user.name = f"{user.first_name} {user.last_name}".strip()

            if user_data.type:
                user.role = user_data.type
                try:
                    user.license_type = User.LicenseType(user_data.type.lower())
                except (ValueError, AttributeError):
                    pass

            if user_data.preferences:
                if not user.preferences:
                    user.preferences = {}

                for key, value in user_data.preferences.items():
                    user.preferences[key] = value

            user.save()
            updated += 1

        except User.DoesNotExist:
            update_errors.append(f"User {user_data.email} not found for update")
        except Exception as e:
            update_errors.append(f"Error updating user {user_data.email}: {str(e)}")

    return updated, update_errors


def _prepare_csv_data(new_users: list[UserData]) -> str:
    """Prepare CSV data for creating new users."""
    # Create CSV-like structure in memory
    csv_buffer = io.StringIO()
    csv_writer = csv.writer(csv_buffer)

    csv_writer.writerow(["Email", "Name", "First Name", "Last Name", "License Type"])

    for user in new_users:
        name = f"{user.first_name} {user.last_name}".strip()

        csv_writer.writerow([user.email, name, user.first_name, user.last_name, user.type])

    csv_buffer.seek(0)
    return csv_buffer.getvalue()


def _process_csv(
    org: Organization, csv_content: str, extra_context: dict[str, str]
) -> tuple[int, int, int, int, list[str]]:
    """Process CSV content to create users."""
    return process_users_from_csv(organization=org, csv_file=csv_content, extra_context=extra_context)


def _get_users_for_emails(org: Organization, email_list: list[str]) -> dict[str, User | None | str]:
    """Get users for the specified email addresses."""
    user_dict: dict[str, User | None | str] = {}
    for email in email_list:
        try:
            user = User.objects.get(email=email, organization=org)
            user_dict[email] = user
        except User.DoesNotExist:
            user_dict[email] = None
        except Exception as e:
            user_dict[email] = str(e)
    return user_dict


async def process_users_creation(
    organization: Organization, users_data: list[UserData]
) -> tuple[int, list[User], list[str]]:
    """Process user creation or updates from the provided data."""
    created_users_count = 0
    updated_users_count = 0
    users_to_email = []
    errors = []

    # Process existing users first
    existing_users = [user for user in users_data if user.existing]
    new_users = [user for user in users_data if not user.existing]

    # Update existing users
    if existing_users:
        # Process existing users
        updated, update_errors = await sync_to_async(_update_existing_users)(organization, existing_users)
        updated_users_count = updated

        if update_errors:
            errors.extend(update_errors)

    # Process new users via CSV if there are any
    if new_users:
        # Prepare CSV data
        csv_content = _prepare_csv_data(new_users)

        try:
            result = await sync_to_async(_process_csv)(organization, csv_content, {"source": "api_onboarding"})

            users_created, users_skipped, _, _, process_errors = result
            created_users_count = users_created

            if process_errors:
                errors.extend(process_errors)
        except Exception as e:
            errors.append(f"Error processing users: {str(e)}")

    emails_to_check = []

    for user_data in new_users:
        if user_data.send_welcome_email:
            emails_to_check.append(user_data.email)

    for user_data in existing_users:
        if user_data.send_welcome_email:
            emails_to_check.append(user_data.email)

    if emails_to_check:
        try:
            user_dict: dict[str, User | None | str] = await sync_to_async(_get_users_for_emails)(
                organization, emails_to_check
            )

            for email, result in user_dict.items():  # type: ignore[assignment]
                if result is None:
                    errors.append(f"User {email} was not found for welcome email")
                elif isinstance(result, str):
                    errors.append(f"Error retrieving user {email}: {result}")
                else:
                    users_to_email.append(result)
        except Exception as e:
            errors.append(f"Error fetching users: {str(e)}")

    return created_users_count + updated_users_count, users_to_email, errors  # type: ignore[return-value]


# Email sending functions
def _send_batch_emails(org_data: dict[str, Any]) -> list[tuple[Any, bool, str | None]]:
    """Send welcome emails in batch."""
    batch_results: list[tuple[Any, bool, str | None]] = []
    org_name = org_data["name"]
    batch_users = org_data["users"]

    try:
        success = handle_bulk_user_onboarding(
            users=batch_users,
            request=None,
            log_prefix=f"API Onboarding ({org_name}):",
        )

        for user in batch_users:
            if success:
                batch_results.append((user.email, True, None))
            else:
                batch_results.append((user.email, False, "Bulk sending failed"))

    except Exception as e:
        # If bulk sending fails, mark all users as failed
        for user in batch_users:
            batch_results.append((user.email, False, str(e)))

    return batch_results


async def _group_users_by_org(users: list[User]) -> dict[int, dict[str, Any]]:
    """Group users by organization for batch processing."""
    org_users: dict[int, Any] = {}

    for user in users:
        user_with_org = await User.objects.select_related("organization").aget(pk=user.pk)
        org_id = user_with_org.organization.id  # type: ignore[union-attr]
        org_name = user_with_org.organization.name  # type: ignore[union-attr]

        if org_id not in org_users:
            org_users[org_id] = {"name": org_name, "users": []}

        org_users[org_id]["users"].append(user)

    return org_users


async def send_welcome_emails(users: list[User]) -> tuple[int, list[str]]:
    """Send welcome emails to the specified users."""
    batch_emails_sent = 0
    errors = []

    # Group users by organization
    org_users = await _group_users_by_org(users)

    for org_id, org_data in org_users.items():
        try:
            batch_results = await sync_to_async(_send_batch_emails)(org_data)

            for email, success, error in batch_results:
                if success:
                    batch_emails_sent += 1
                else:
                    errors.append(f"Failed to send welcome email to {email}: {error}")

        except Exception as e:
            # Fallback error if the entire batch fails
            for user in org_data["users"]:
                errors.append(f"Error in batch email processing for {user.email}: {str(e)}")

    return batch_emails_sent, errors
