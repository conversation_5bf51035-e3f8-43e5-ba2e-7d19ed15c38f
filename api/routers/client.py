import datetime
import logging
import traceback
from typing import Any
from uuid import UUID

from django.db.models import Prefetch
from fastapi import APIRouter, Depends, HTTPException, Path, status
from pydantic import BaseModel, HttpUrl, ValidationError

from api.dependencies import user_from_authorization_header
from api.routers.note_models import ListNotesResponse, list_notes_response
from api.routers.task_models import TaskResponse, get_lite_task_response
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.client_recap import ClientRecap as DBClientRecap
from deepinsights.meetingsapp.models.client_recap import ClientRecapStatus
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.tasks import process_client_recap
from deepinsights.users.models.user import User

router = APIRouter(tags=["client"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")

logger = logging.getLogger(__name__)


# A reference to a source of information in the recap.
class ClientRecapReference(BaseModel):
    # The ID of the reference.
    #
    # This could be a CRM ID, a Zeplyn note ID, or an arbitrary identifier related to the source.
    id: str

    # The source of this reference.
    #
    # For Zeplyn notes, this will be "Zeplyn"; for CRM notes, this will be the CRM system name; for
    # other data, this will have an arbitrary value.
    source: str

    # A web link that can be used to access the source material for this reference.
    link: HttpUrl | None

    # Text that can be shown to the user when hovering over the reference link.
    hover_text: str | None = None


# A bullet point in the recap.
class ClientRecapBullet(BaseModel):
    # The text of the bullet point.
    text: str

    # Indices into the array of references, indicating which references are related to this bullet.
    references: list[int]


# A category of bullet points in the recap.
class ClientRecapCategory(BaseModel):
    # The topic of the category
    topic: str

    # The bullet points in this category
    bullets: list[ClientRecapBullet]


# A client recap, providing LLM-generated information about a client.
class ClientRecap(BaseModel):
    # The recap itself.
    recap: list[ClientRecapCategory]

    # References to the sources of information in the recap.
    references: list[ClientRecapReference]

    # When the recap was created.
    created_at: datetime.datetime


# Information about a client.
class ClientResponse(BaseModel):
    # The full name of the client.
    name: str

    # The status of the latest client recap for this client (whether valid or not).
    recap_status: ClientRecapStatus | None

    # The latest valid client recap for this client (which may not be the recap or which
    # recap_status was returned, if there is a newer but invalid recap).
    recap: ClientRecap | None

    # A list of notes that are related to this client.
    notes: list[ListNotesResponse]

    # Basic information (e.g., net worth, income) about the client.
    basic_info: dict[str, Any]

    # A list of tasks related to this client.
    action_items: list[TaskResponse] | None


# Info about a client recap.
class ClientRecapResponse(BaseModel):
    # The status of the client recap.
    status: ClientRecapStatus

    # The UUID of the client recap (if it exists).
    uuid: UUID | None = None


def __is_authorized_to_view_client(user: User, client: Client) -> bool:
    return (
        user in client.authorized_users.all()
        or user.is_superuser
        or (
            (user.license_type == User.LicenseType.csa or user.license_type == User.LicenseType.staff)
            and user.organization == client.organization
        )
    )


@router.get(
    "/{client_id}",
    response_model=ClientResponse,
    responses={
        200: {"description": "Successfully retrieved client details"},
        403: {"description": "Not authorized to view this client"},
        404: {"description": "Client not found"},
        500: {"description": "Internal server error"},
    },
    summary="Get client details",
    description="Get detailed information about a specific client including the client recap, notes, and action items",
)
def get_client(
    client_id: UUID = Path(..., description="The UUID of the client to retrieve"),
    user: User = Depends(user_from_authorization_header),
) -> ClientResponse:
    try:
        client = Client.objects.get(uuid=client_id)
    except Client.DoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Client not found")

    if not __is_authorized_to_view_client(user, client):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized to view another user's client"
        )

    try:
        latest_processed_recap = (
            DBClientRecap.objects.filter(client=client, status=ClientRecapStatus.PROCESSED).order_by("-created").first()
        )
        client_recap_summary = latest_processed_recap.get_client_recap_summary() if latest_processed_recap else None
        client_recap_creation_date = latest_processed_recap.created if latest_processed_recap else None
        latest_recap = DBClientRecap.objects.filter(client=client).order_by("-created").first()

        notes = (
            Note.objects.filter(client__uuid=str(client.uuid))
            .defer(
                "raw_asr_response",
                "raw_transcript",
                "diarized_trans_with_names",
                "advisor_notes",
                "key_takeaways",
                "summary_by_topics",
                "summary",
                "raw_asr_response",
                "follow_up_email_contents",
            )
            .order_by("-created")
            .prefetch_related(
                Prefetch("attendees", queryset=Attendee.objects.all().only("note", "uuid", "attendee_name")),
            )
        )

        notes_list = []
        action_items = []

        for note in notes:
            try:
                notes_list.append(list_notes_response(note))
            except ValidationError as v:
                logger.warning("Error serializing note %s: ", note.uuid, exc_info=v)
                continue
            action_items_for_note = [get_lite_task_response(task) for task in note.task_set.all()]
            action_items.extend(action_items_for_note)

        # Translate the stored recap into the format required by the API.
        recap = (
            ClientRecap.model_validate(
                {
                    "recap": client_recap_summary.get("client_recap_summary", []),
                    "references": [
                        {**r, "id": r.get("meeting_uuid")} for r in client_recap_summary.get("references", [])
                    ],
                    "created_at": client_recap_creation_date,
                }
            )
            if client_recap_summary
            else None
        )

        return ClientResponse(
            name=client.name,
            recap_status=(
                ClientRecapStatus(latest_recap.status)
                if latest_recap and latest_recap.status
                else ClientRecapStatus.UNKNOWN
            ),
            recap=recap,
            notes=notes_list,
            basic_info=user.crm_handler.get_client_basic_info(client, user) or {},
            action_items=action_items,
        )

    except Exception as e:
        logging.error("Error getting client: %s", client_id, exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while retrieving client details",
        )


@router.post(
    "/{client_id}/generate-recap",
    response_model=ClientRecapResponse,
    responses={
        200: {"description": "Client recap generation initiated successfully"},
        403: {"description": "Not authorized to generate a client recap for this client"},
        404: {"description": "Client not found"},
        500: {"description": "Internal server error"},
    },
    summary="Generate a client recap",
    description="Initiate the generation of a client recap for a specific client",
)
def generate_client_recap(
    client_id: UUID = Path(..., description="The Zeplyn UUID of the client"),
    user: User = Depends(user_from_authorization_header),
) -> ClientRecapResponse:
    try:
        client = Client.objects.get(uuid=client_id)
    except Client.DoesNotExist:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Client not found")

    if not __is_authorized_to_view_client(user, client):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to generate a recap for another user's client",
        )

    try:
        recap = DBClientRecap.objects.create(user=user, client=client)
        process_client_recap.delay_on_commit(recap.uuid, user.uuid)
        return ClientRecapResponse(status=ClientRecapStatus.PROCESSING, uuid=recap.uuid)
    except Exception as e:
        logger.error(traceback.format_exception(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while generating the client recap",
        )
