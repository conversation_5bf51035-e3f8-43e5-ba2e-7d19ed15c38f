import time
import uuid
from datetime import datetime
from typing import Any, Generator
from unittest.mock import MagicMock, patch

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from api.dependencies import user_from_authorization_header
from api.internal_api import internal_api as app
from deepinsights.core.integrations.crm.wealthbox import Wealthbox
from deepinsights.core.preferences.preferences import DefaultClientFilter
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User

client = TestClient(app)

pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture(autouse=True)
def setUpTearDown() -> Generator[Any, None, None]:
    app.dependency_overrides = {}
    yield
    app.dependency_overrides = {}


@pytest.fixture
def test_organization() -> Organization:
    return Organization.objects.create(name="Test Org")


@pytest.fixture
def test_user(django_user_model: User, test_organization: Organization) -> User:
    test_user = django_user_model.objects.create(
        username="<EMAIL>",
        first_name="Test",
        last_name="User",
        organization=test_organization,
        license_type=User.LicenseType.advisor,
    )
    app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.get(username="<EMAIL>")
    return test_user


@pytest.fixture
def test_client(test_user: User, test_organization: Organization) -> Client:
    crm_client = Client.objects.create(name="Test Client", crm_system="test_crm", organization=test_organization)
    crm_client.authorized_users.add(test_user)
    crm_client.save()
    return crm_client


@pytest.fixture
def test_note(test_user: User, test_client: Client) -> Note:
    note = Note.objects.create(
        note_owner=test_user,
        client={"client_id": str(test_client.uuid), "client_name": test_client.name},
        status="uploaded",
    )
    note.authorized_users.add(test_user)
    note.save()
    return note


# Note Preview Tests


@patch("deepinsights.users.models.user.User.crm_handler")
def test_crm_sync_preview_success(mock_crm_handler: MagicMock, test_user: User, test_note: Note) -> None:
    mock_crm_handler.preview_before_syncing_with_crm.return_value = {"preview": "data"}

    response = client.get(f"/api/v2/crm/crm_sync_preview/{test_note.uuid}")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {"preview_data": {"preview": "data"}}

    mock_crm_handler.preview_before_syncing_with_crm.assert_called_once_with(test_note)


def test_crm_sync_preview_note_not_found(test_user: User) -> None:
    random_uuid = uuid.uuid4()
    response = client.get(f"/api/v2/crm/crm_sync_preview/{random_uuid}")

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Could not find the note"}


def test_crm_sync_preview_note_has_no_owner(test_user: User, test_note: Note) -> None:
    test_note.note_owner = None
    test_note.save()

    response = client.get(f"/api/v2/crm/crm_sync_preview/{test_note.uuid}")

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "Note does not have an owner"}


def test_crm_sync_preview_unauthorized(test_user: User, test_note: Note) -> None:
    # Create another user who shouldn't have access
    other_user = User.objects.create(username="<EMAIL>", organization=test_user.organization)
    app.dependency_overrides[user_from_authorization_header] = lambda: other_user

    response = client.get(f"/api/v2/crm/crm_sync_preview/{test_note.uuid}")

    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Not authorized to access this note"}


@patch("deepinsights.core.integrations.crm.wealthbox.Wealthbox.preview_before_syncing_with_crm")
@patch("api.routers.crm.get_crm_interface")
def test_crm_sync_preview_no_client_wealthbox(
    mock_get_crm: MagicMock, mock_preview: MagicMock, test_user: User, test_note: Note
) -> None:
    test_user.crm_configuration = {
        "crm_system": "wealthbox",
        "wealthbox": {"access_token": "test_token"},
    }
    test_user.save()

    mock_crm = MagicMock(spec=Wealthbox)
    mock_get_crm.return_value = mock_crm
    mock_preview.return_value = {"preview": "data"}

    test_note.client = None
    test_note.save()

    response = client.get(f"/api/v2/crm/crm_sync_preview/{test_note.uuid}")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {"preview_data": {"preview": "data"}}
    mock_preview.assert_called_once_with(test_note)


@patch("api.routers.crm.get_crm_interface")
def test_crm_sync_preview_no_client_redtail(mock_get_crm: MagicMock, test_user: User, test_note: Note) -> None:
    test_user.crm_configuration = {"crm_system": "redtail"}
    test_user.save()
    test_note.client = None
    test_note.save()

    mock_crm = MagicMock()
    mock_get_crm.return_value = mock_crm
    mock_crm.__class__.__name__ = "Redtail"

    test_note.client = None
    test_note.save()

    response = client.get(f"/api/v2/crm/crm_sync_preview/{test_note.uuid}")

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "Note does not have a CRM client associated with it"}


@patch("deepinsights.users.models.user.User.crm_handler")
def test_crm_sync_preview_internal_error(mock_crm_handler: MagicMock, test_user: User, test_note: Note) -> None:
    test_user.crm_configuration = {"crm_system": "redtail"}
    test_user.save()

    mock_crm_handler.preview_before_syncing_with_crm.side_effect = Exception("Test error")

    response = client.get(f"/api/v2/crm/crm_sync_preview/{test_note.uuid}")

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {"detail": "Test error"}

    mock_crm_handler.preview_before_syncing_with_crm.assert_called_once_with(test_note)


# Note Upload Tests


@patch("deepinsights.users.models.user.User.crm_handler")
def test_upload_note_success(mock_crm_handler: MagicMock, test_user: User, test_note: Note) -> None:
    test_user.crm_configuration = {"crm_system": "redtail"}
    test_user.save()

    mock_crm_handler.add_interaction_with_client.return_value = None
    response = client.post(f"/api/v2/crm/upload_note/{test_note.uuid}")

    assert response.status_code == status.HTTP_204_NO_CONTENT
    test_note.refresh_from_db()
    assert test_note.status == "finalized"


def test_upload_note_not_found(test_user: User) -> None:
    random_uuid = uuid.uuid4()
    response = client.post(f"/api/v2/crm/upload_note/{random_uuid}")

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Could not find the note"}


def test_upload_note_unauthorized(test_user: User, test_note: Note) -> None:
    other_user = User.objects.create(username="<EMAIL>", organization=test_user.organization)
    app.dependency_overrides[user_from_authorization_header] = lambda: other_user

    response = client.post(f"/api/v2/crm/upload_note/{test_note.uuid}")

    assert response.status_code == status.HTTP_403_FORBIDDEN
    assert response.json() == {"detail": "Not authorized to access this note"}


@patch("deepinsights.core.integrations.crm.wealthbox.Wealthbox.add_interaction_with_client")
@patch("api.routers.crm.get_crm_interface")
def test_upload_note_no_client_wealthbox(
    mock_get_crm: MagicMock, mock_add_interaction: MagicMock, test_user: User, test_note: Note
) -> None:
    test_user.crm_configuration = {
        "crm_system": "wealthbox",
        "wealthbox": {"access_token": "test_token"},
    }
    test_user.save()

    mock_crm = MagicMock(spec=Wealthbox)
    mock_get_crm.return_value = mock_crm
    mock_add_interaction.return_value = None

    test_note.client = None
    test_note.save()

    response = client.post(f"/api/v2/crm/upload_note/{test_note.uuid}")

    assert response.status_code == status.HTTP_204_NO_CONTENT
    mock_add_interaction.assert_called_once_with(test_note)


@patch("api.routers.crm.get_crm_interface")
def test_upload_note_no_client_redtail(mock_get_crm: MagicMock, test_user: User, test_note: Note) -> None:
    test_user.crm_configuration = {"crm_system": "redtail"}
    test_user.save()
    test_note.client = None
    test_note.save()

    mock_crm = MagicMock()
    mock_get_crm.return_value = mock_crm
    mock_crm.__class__.__name__ = "Redtail"

    test_note.client = None
    test_note.save()

    response = client.post(f"/api/v2/crm/upload_note/{test_note.uuid}")

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "Note does not have a CRM client associated with it"}


@patch("deepinsights.users.models.user.User.crm_handler")
def test_upload_note_internal_error(mock_crm_handler: MagicMock, test_user: User, test_note: Note) -> None:
    test_user.crm_configuration = {"crm_system": "redtail"}
    test_user.save()

    mock_crm_handler.add_interaction_with_client.side_effect = Exception("Test error")

    response = client.post(f"/api/v2/crm/upload_note/{test_note.uuid}")

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {"detail": "Error uploading note to CRM: Test error"}
    mock_crm_handler.add_interaction_with_client.assert_called_once_with(test_note)


@pytest.mark.parametrize(
    "email_enabled,expected_email_called",
    [
        (True, True),  # Email preference enabled
        (False, False),  # Email preference disabled
    ],
)
@patch("deepinsights.users.models.user.User.crm_handler")
@patch("api.routers.crm.email_meeting_notes")
def test_upload_note_email_preference(
    mock_email_meeting_notes: MagicMock,
    mock_crm_handler: MagicMock,
    email_enabled: bool,
    expected_email_called: bool,
    test_user: User,
    test_note: Note,
) -> None:
    preferences = test_user.get_preferences()
    preferences.notification_preferences.email_meeting_notes_post_sync = email_enabled

    test_user.preferences = preferences.to_dict()
    test_user.crm_configuration = {"crm_system": "redtail"}
    test_user.save()

    mock_crm_handler.add_interaction_with_client.return_value = None

    response = client.post(f"/api/v2/crm/upload_note/{test_note.uuid}")

    assert response.status_code == status.HTTP_204_NO_CONTENT

    if expected_email_called:
        mock_email_meeting_notes.assert_called_once_with(str(test_note.uuid), test_user)
    else:
        mock_email_meeting_notes.assert_not_called()

    test_note.refresh_from_db()
    assert test_note.status == "finalized"


#
# Get Client List Tests
#


def test_get_client_list_missing_user(test_user: User) -> None:
    response = client.get(f"/api/v2/crm/clients/{uuid.uuid4()}")
    assert response.status_code == status.HTTP_404_NOT_FOUND


@patch("api.routers.crm.Client.objects.filter", side_effect=Exception("Test exception"))
def test_get_client_list_error(_: MagicMock, test_user: User) -> None:
    test_user.crm_configuration = {"crm_system": "test_crm"}
    test_user.save()

    response = client.get("/api/v2/crm/clients")

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR


def test_get_client_list_default_none_crm_system(test_user: User, test_organization: Organization) -> None:
    response = client.get("/api/v2/crm/clients")

    client_one = Client.objects.create(name="Client One", crm_system="test_crm", organization=test_organization)
    client_one.authorized_users.add(test_user)
    client_one.save()
    client_two = Client.objects.create(name="Client Two", crm_system="test_crm", organization=test_organization)
    client_two.authorized_users.add(test_user)
    client_two.save()

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {
        "client_selection_enabled": False,
        "clients": [],
        "crm_system": None,
        "next_page_token": None,
    }


def test_get_client_list_empty_crm_system(test_user: User, test_organization: Organization) -> None:
    test_user.crm_configuration = {"crm_system": None}
    test_user.save()

    response = client.get("/api/v2/crm/clients")

    client_one = Client.objects.create(name="Client One", crm_system="test_crm", organization=test_organization)
    client_one.authorized_users.add(test_user)
    client_one.save()
    client_two = Client.objects.create(name="Client Two", crm_system="test_crm", organization=test_organization)
    client_two.authorized_users.add(test_user)
    client_two.save()

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {
        "client_selection_enabled": False,
        "clients": [],
        "crm_system": None,
        "next_page_token": None,
    }


def test_get_client_list_with_clients(test_user: User, test_organization: Organization) -> None:
    test_user.crm_configuration = {"crm_system": "test_crm"}
    test_user.save()

    client_one = Client.objects.create(name="Client One", crm_system="test_crm", organization=test_organization)
    client_one.authorized_users.add(test_user)
    client_one.save()
    client_two = Client.objects.create(name="Client Two", crm_system="test_crm", organization=test_organization)
    client_two.authorized_users.add(test_user)
    client_two.save()
    other_crm_client = Client.objects.create(
        name="Other CRM client", crm_system="other_crm", organization=test_organization
    )
    unauthorized_client = Client.objects.create(
        name="Unauthorized client", crm_system="test_crm", organization=test_organization
    )

    response = client.get("/api/v2/crm/clients")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {
        "client_selection_enabled": True,
        "clients": [
            {"uuid": str(client_one.uuid), "name": client_one.name, "type": client_one.client_type},
            {"uuid": str(client_two.uuid), "name": client_two.name, "type": client_two.client_type},
        ],
        "crm_system": "test_crm",
        "next_page_token": None,
    }


def test_get_client_list_filtering(test_user: User, test_organization: Organization) -> None:
    test_user.crm_configuration = {"crm_system": "test_crm"}
    test_user.save()

    client_one = Client.objects.create(name="Client One", crm_system="test_crm", organization=test_organization)
    client_one.authorized_users.add(test_user)
    client_one.save()
    client_two = Client.objects.create(name="Client Two", crm_system="test_crm", organization=test_organization)
    client_two.authorized_users.add(test_user)
    client_two.save()

    response = client.get("/api/v2/crm/clients?q=One")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {
        "client_selection_enabled": True,
        "clients": [{"uuid": str(client_one.uuid), "name": client_one.name, "type": client_one.client_type}],
        "crm_system": "test_crm",
        "next_page_token": None,
    }


class TestGetClientListDefaultFiltering:
    @pytest.fixture(autouse=True)
    def setUp(self, test_user: User, test_organization: Organization, django_user_model: User) -> None:
        self.user = test_user
        self.user.crm_configuration["crm_system"] = "test_crm"
        self.user.save()

        self.user_two = django_user_model.objects.create(
            username="<EMAIL>", email="<EMAIL>", organization=test_organization
        )
        self.user_two.crm_configuration["crm_system"] = "test_crm"
        self.user_two.save()

        self.client_one = Client.objects.create(
            name="Client 1", crm_system="test_crm", owner=self.user, organization=test_organization
        )
        self.client_one.authorized_users.add(self.user)
        self.client_one.save()

        self.client_two = Client.objects.create(
            name="Client 2", crm_system="test_crm", owner=self.user, organization=test_organization
        )
        self.client_two.authorized_users.add(self.user, self.user_two)
        self.client_two.save()

        self.client_three = Client.objects.create(
            name="Client 3", crm_system="test_crm", owner=self.user_two, organization=test_organization
        )
        self.client_three.authorized_users.add(self.user_two)
        self.client_three.save()

        self.client_four = Client.objects.create(
            name="Client 4", crm_system="test_crm", owner=self.user_two, organization=test_organization
        )
        self.client_four.authorized_users.add(self.user, self.user_two)
        self.client_four.save()

        self.client_five = Client.objects.create(name="Client 5", crm_system="test_crm", organization=test_organization)
        self.client_five.authorized_users.add(self.user)
        self.client_five.save()

        self.client_six = Client.objects.create(name="Client 6", crm_system="test_crm", organization=test_organization)
        self.client_six.authorized_users.add(self.user_two)
        self.client_six.save()

        self.other_crm_client = Client.objects.create(
            name="Other CRM client", crm_system="other_crm", organization=test_organization
        )
        self.unauthorized_client = Client.objects.create(
            name="Unauthorized client", crm_system="test_crm", organization=test_organization
        )

    def _client_response_for_client(self, client: Client) -> dict[str, str]:
        return {"uuid": str(client.uuid), "name": client.name, "type": client.client_type}

    def test_no_filter(self) -> None:
        response = client.get("/api/v2/crm/clients")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "client_selection_enabled": True,
            "clients": [
                self._client_response_for_client(self.client_one),
                self._client_response_for_client(self.client_two),
                self._client_response_for_client(self.client_four),
                self._client_response_for_client(self.client_five),
            ],
            "crm_system": "test_crm",
            "next_page_token": None,
        }

    def test_invalid_filter(self) -> None:
        self.user.crm_configuration["default_client_filter"] = "invalid"
        self.user.save()

        response = client.get("/api/v2/crm/clients")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "client_selection_enabled": True,
            "clients": [
                self._client_response_for_client(self.client_one),
                self._client_response_for_client(self.client_two),
                self._client_response_for_client(self.client_four),
                self._client_response_for_client(self.client_five),
            ],
            "crm_system": "test_crm",
            "next_page_token": None,
        }

    def test_owned_filter(self) -> None:
        self.user.crm_configuration["default_client_filter"] = DefaultClientFilter.OWNED
        self.user.save()

        response = client.get("/api/v2/crm/clients")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "client_selection_enabled": True,
            "clients": [
                self._client_response_for_client(self.client_one),
                self._client_response_for_client(self.client_two),
            ],
            "crm_system": "test_crm",
            "next_page_token": None,
        }

    def test_all_filter(self) -> None:
        self.user.crm_configuration["default_client_filter"] = DefaultClientFilter.ALL
        self.user.save()

        response = client.get("/api/v2/crm/clients")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "client_selection_enabled": True,
            "clients": [
                self._client_response_for_client(self.client_one),
                self._client_response_for_client(self.client_two),
                self._client_response_for_client(self.client_four),
                self._client_response_for_client(self.client_five),
            ],
            "crm_system": "test_crm",
            "next_page_token": None,
        }

    def test_no_filter_user_two(self) -> None:
        app.dependency_overrides[user_from_authorization_header] = lambda: self.user_two

        response = client.get("/api/v2/crm/clients")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "client_selection_enabled": True,
            "clients": [
                self._client_response_for_client(self.client_two),
                self._client_response_for_client(self.client_three),
                self._client_response_for_client(self.client_four),
                self._client_response_for_client(self.client_six),
            ],
            "crm_system": "test_crm",
            "next_page_token": None,
        }

    def test_invalid_filter_user_two(self) -> None:
        app.dependency_overrides[user_from_authorization_header] = lambda: self.user_two
        self.user_two.crm_configuration["default_client_filter"] = "invalid"
        self.user_two.save()

        response = client.get("/api/v2/crm/clients")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "client_selection_enabled": True,
            "clients": [
                self._client_response_for_client(self.client_two),
                self._client_response_for_client(self.client_three),
                self._client_response_for_client(self.client_four),
                self._client_response_for_client(self.client_six),
            ],
            "crm_system": "test_crm",
            "next_page_token": None,
        }

    def test_owned_filter_user_two(self) -> None:
        app.dependency_overrides[user_from_authorization_header] = lambda: self.user_two
        self.user_two.crm_configuration["default_client_filter"] = DefaultClientFilter.OWNED
        self.user_two.save()

        response = client.get("/api/v2/crm/clients")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "client_selection_enabled": True,
            "clients": [
                self._client_response_for_client(self.client_three),
                self._client_response_for_client(self.client_four),
            ],
            "crm_system": "test_crm",
            "next_page_token": None,
        }

    def test_all_filter_user_two(self) -> None:
        app.dependency_overrides[user_from_authorization_header] = lambda: self.user_two
        self.user_two.crm_configuration["default_client_filter"] = DefaultClientFilter.ALL
        self.user_two.save()

        response = client.get("/api/v2/crm/clients")

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == {
            "client_selection_enabled": True,
            "clients": [
                self._client_response_for_client(self.client_two),
                self._client_response_for_client(self.client_three),
                self._client_response_for_client(self.client_four),
                self._client_response_for_client(self.client_six),
            ],
            "crm_system": "test_crm",
            "next_page_token": None,
        }


def test_client_list_pagination(test_user: User, test_organization: Organization) -> None:
    test_user.crm_configuration = {"crm_system": "test_crm"}
    test_user.save()

    clients = []
    for i in range(30):
        client_obj = Client.objects.create(
            name=f"Paginated Client {i:02d}", crm_system="test_crm", organization=test_organization
        )
        client_obj.authorized_users.add(test_user)
        client_obj.save()
        clients.append(client_obj)

    first_response = client.get("/api/v2/crm/clients?page_size=25")
    assert first_response.status_code == status.HTTP_200_OK
    first_data = first_response.json()

    assert len(first_data["clients"]) == 25
    assert first_data["next_page_token"] is not None

    next_cursor = first_data["next_page_token"]
    second_response = client.get(f"/api/v2/crm/clients?cursor={next_cursor}")
    assert second_response.status_code == status.HTTP_200_OK
    second_data = second_response.json()

    assert len(second_data["clients"]) == 5
    assert second_data["next_page_token"] is None

    first_page_uuids = {client["uuid"] for client in first_data["clients"]}
    second_page_uuids = {client["uuid"] for client in second_data["clients"]}
    assert len(first_page_uuids.intersection(second_page_uuids)) == 0

    assert len(first_page_uuids) + len(second_page_uuids) == 30


def test_client_list_pagination_with_search(test_user: User, test_organization: Organization) -> None:
    test_user.crm_configuration = {"crm_system": "test_crm"}
    test_user.save()

    alpha_clients = []
    for i in range(30):
        client_obj = Client.objects.create(
            name=f"Alpha Client {i:02d}", crm_system="test_crm", organization=test_organization
        )
        client_obj.authorized_users.add(test_user)
        client_obj.save()
        alpha_clients.append(client_obj)

    beta_clients = []
    for i in range(10):
        client_obj = Client.objects.create(
            name=f"Beta Client {i:02d}", crm_system="test_crm", organization=test_organization
        )
        client_obj.authorized_users.add(test_user)
        client_obj.save()
        beta_clients.append(client_obj)

    first_response = client.get("/api/v2/crm/clients?q=Alpha&page_size=25")
    assert first_response.status_code == status.HTTP_200_OK
    first_data = first_response.json()

    assert len(first_data["clients"]) == 25
    assert first_data["next_page_token"] is not None
    assert all("Alpha" in client["name"] for client in first_data["clients"])

    next_cursor = first_data["next_page_token"]
    second_response = client.get(f"/api/v2/crm/clients?q=Alpha&page_size=25&cursor={next_cursor}")
    assert second_response.status_code == status.HTTP_200_OK
    second_data = second_response.json()

    assert len(second_data["clients"]) == 5
    assert second_data["next_page_token"] is None
    assert all("Alpha" in client["name"] for client in second_data["clients"])

    beta_response = client.get("/api/v2/crm/clients?q=Beta")
    assert beta_response.status_code == status.HTTP_200_OK
    beta_data = beta_response.json()

    assert len(beta_data["clients"]) == 10  # All 10 Beta clients
    assert beta_data["next_page_token"] is None
    assert all("Beta" in client["name"] for client in beta_data["clients"])


def test_client_list_custom_page_size(test_user: User, test_organization: Organization) -> None:
    test_user.crm_configuration = {"crm_system": "test_crm"}
    test_user.save()

    clients = []
    for i in range(15):
        client_obj = Client.objects.create(
            name=f"Custom PageSize Client {i:02d}", crm_system="test_crm", organization=test_organization
        )
        client_obj.authorized_users.add(test_user)
        client_obj.save()
        clients.append(client_obj)

    response = client.get("/api/v2/crm/clients?page_size=10")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()

    assert len(data["clients"]) == 10
    assert data["next_page_token"] is not None

    next_cursor = data["next_page_token"]
    second_response = client.get(f"/api/v2/crm/clients?page_size=10&cursor={next_cursor}")
    assert second_response.status_code == status.HTTP_200_OK
    second_data = second_response.json()

    assert len(second_data["clients"]) == 5
    assert second_data["next_page_token"] is None


def test_client_list_invalid_cursor(test_user: User, test_organization: Organization) -> None:
    test_user.crm_configuration = {"crm_system": "test_crm"}
    test_user.save()

    for i in range(5):
        client_obj = Client.objects.create(name=f"Client {i}", crm_system="test_crm", organization=test_organization)
        client_obj.authorized_users.add(test_user)
        client_obj.save()

    response = client.get("/api/v2/crm/clients?cursor=invalid_cursor")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()

    assert len(data["clients"]) > 0

    response = client.get("/api/v2/crm/clients?cursor=INVALIDBASE64DATA!")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()

    assert len(data["clients"]) > 0


def test_ordering_consistency(test_user: User, test_organization: Organization) -> None:
    test_user.crm_configuration = {"crm_system": "test_crm"}
    test_user.save()

    clients = []
    base_time = datetime.now()

    for i in range(30):
        client_obj = Client.objects.create(
            name=f"Order Client {i:02d}", crm_system="test_crm", organization=test_organization
        )
        client_obj.authorized_users.add(test_user)
        client_obj.modified = base_time
        client_obj.save(update_fields=["modified"])
        clients.append(client_obj)
        time.sleep(0.01)

    first_response = client.get("/api/v2/crm/clients?page_size=10")
    assert first_response.status_code == status.HTTP_200_OK
    first_data = first_response.json()

    next_cursor = first_data["next_page_token"]
    second_response = client.get(f"/api/v2/crm/clients?page_size=10&cursor={next_cursor}")
    assert second_response.status_code == status.HTTP_200_OK
    second_data = second_response.json()

    next_cursor = second_data["next_page_token"]
    third_response = client.get(f"/api/v2/crm/clients?page_size=10&cursor={next_cursor}")
    assert third_response.status_code == status.HTTP_200_OK
    third_data = third_response.json()

    all_uuids: set[str] = set()
    for page_data in [first_data, second_data, third_data]:
        page_uuids = {client["uuid"] for client in page_data["clients"]}
        assert len(page_uuids.intersection(all_uuids)) == 0  # No duplicates
        all_uuids.update(page_uuids)

    assert len(all_uuids) == 30


def test_client_list_no_pagination(test_user: User, test_organization: Organization) -> None:
    test_user.crm_configuration = {"crm_system": "test_crm"}
    test_user.save()

    clients = []
    for i in range(30):
        client_obj = Client.objects.create(
            name=f"No Pagination Client {i:02d}", crm_system="test_crm", organization=test_organization
        )
        client_obj.authorized_users.add(test_user)
        client_obj.save()
        clients.append(client_obj)

    response = client.get("/api/v2/crm/clients?page_size=0")
    assert response.status_code == status.HTTP_200_OK
    data = response.json()

    assert len(data["clients"]) == 30
    assert data["next_page_token"] is None


@patch("api.routers.crm.Redtail.generate_user_key_code")
@patch("api.routers.crm.sync_crm_clients")
def test_generate_redtail_user_key_success_with_redtail_set(
    sync_crm_clients: MagicMock, mock_generate_user_key_code: MagicMock, test_user: User
) -> None:
    test_user.crm_configuration = {"crm_system": "redtail"}
    test_user.save()

    credentials = {"user_id": str(test_user.uuid), "username": "testuser", "password": "testpassword"}
    mock_generate_user_key_code.return_value = "test_user_key"

    response = client.post("/api/v2/crm/redtail/generate-key", json=credentials)

    assert response.status_code == status.HTTP_204_NO_CONTENT
    test_user.refresh_from_db()
    assert test_user.crm_configuration["crm_system"] == "redtail"

    sync_crm_clients.delay_on_commit.assert_called_once_with(test_user.uuid)


@patch("api.routers.crm.Redtail.generate_user_key_code")
@patch("api.routers.crm.sync_crm_clients")
def test_generate_redtail_user_key_success_with_redtail_not_set(
    sync_crm_clients: MagicMock, mock_generate_user_key_code: MagicMock, test_user: User
) -> None:
    credentials = {"user_id": str(test_user.uuid), "username": "testuser", "password": "testpassword"}
    mock_generate_user_key_code.return_value = "test_user_key"

    response = client.post("/api/v2/crm/redtail/generate-key", json=credentials)

    assert response.status_code == status.HTTP_204_NO_CONTENT
    test_user.refresh_from_db()
    assert test_user.crm_configuration["crm_system"] == "redtail"

    sync_crm_clients.delay_on_commit.assert_called_once_with(test_user.uuid)


@patch("api.routers.crm.Redtail.generate_user_key_code")
def test_generate_redtail_user_key_failure(mock_generate_user_key_code: MagicMock, test_user: User) -> None:
    test_user.crm_configuration = {"crm_system": "redtail"}
    test_user.save()

    credentials = {"user_id": str(test_user.uuid), "username": "testuser", "password": "testpassword"}
    mock_generate_user_key_code.side_effect = Exception("Test exception")

    response = client.post("/api/v2/crm/redtail/generate-key", json=credentials)

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json() == {"detail": "Failed to generate redtail user key"}


def test_get_redtail_integration_status_success(test_user: User) -> None:
    test_user.crm_configuration = {"crm_system": "redtail", "redtail": {"user_key": "test_key"}}
    test_user.save()

    response = client.get("/api/v2/crm/redtail/status")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {"active": True}


def test_get_redtail_integration_status_inactive(test_user: User) -> None:
    test_user.crm_configuration = {"crm_system": "redtail", "redtail": {"user_key": None}}
    test_user.save()

    response = client.get("/api/v2/crm/redtail/status")

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {"active": False}


def test_get_redtail_integration_status_not_redtail(test_user: User) -> None:
    test_user.crm_configuration = {"crm_system": "other_crm"}
    test_user.save()

    response = client.get("/api/v2/crm/redtail/status")

    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"detail": "User and user's organization do not use Redtail as a CRM"}
