from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field

from deepinsights.meetingsapp.models.task import Task


class CreateTaskResponse(BaseModel):
    task_uuid: UUID


class CreateTaskRequest(BaseModel):
    title: str
    owner_uuid: UUID
    description: str | None = None
    due_date: datetime | None = None
    parent_note_uuid: UUID | None = None


class Client(BaseModel):
    uuid: UUID
    name: str


class TaskUpdate(BaseModel):
    title: str | None = Field(None, description="The title of the task. If null, the task title will not be updated.")
    description: str | None = Field(
        None, description="The description of the task. If null, the task description will not be updated."
    )
    due_date: datetime | None = Field(
        None, description="The due date of the task. If null, the task due date will not be updated."
    )
    completed: bool | None = Field(
        None, description="The completion status of the task. If null, the task completion status will not be updated."
    )
    assignee: UUID | None = Field(
        None,
        description="The UUID of the user to whom the task is assigned. If null, the task assignee will not be updated.",
    )
    parent_note_uuid: UUID | None = Field(
        None,
        description=(
            "The UUID of the note to which the task is associated. If null, the task parent note will be unset; "
            "in other words, be sure to pass this field in all requests."
        ),
    )


class TaskResponse(BaseModel):
    uuid: UUID
    title: str
    description: str | None = None
    due_date: datetime | None = None
    created: datetime
    modified: datetime
    completed: bool
    parent_note_uuid: UUID | None
    owner: Client | None
    assignee: Client | None
    assignees: list[Client] | None = None


class ListTasksResponse(BaseModel):
    data: list[TaskResponse]


def get_lite_task_response(task: Task) -> TaskResponse:
    assignee = task.assignee or task.task_owner
    return TaskResponse(
        uuid=task.uuid,
        title=task.task_title,
        description=task.task_desc,
        due_date=task.due_date,
        created=task.created,
        modified=task.modified,
        completed=task.completed,
        parent_note_uuid=None,
        owner=Client(uuid=task.task_owner.uuid, name=task.task_owner.get_full_name()) if task.task_owner else None,
        assignee=Client(uuid=assignee.uuid, name=assignee.get_full_name()) if assignee else None,
        assignees=[Client(uuid=assignee.uuid, name=assignee.get_full_name())] if assignee else None,
    )
