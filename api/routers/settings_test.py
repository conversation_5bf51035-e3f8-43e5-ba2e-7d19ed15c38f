import datetime
from typing import Any, Generator
from unittest.mock import ANY, MagicMock, patch

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from api.dependencies import user_from_authorization_header
from api.internal_api import internal_api as app
from api.routers.calendar_models import CalendarLookahead
from api.routers.settings_models import SectionDetails
from deepinsights.core.integrations.meetingbot.recall_ai import <PERSON><PERSON>l<PERSON>otControll<PERSON>
from deepinsights.core.integrations.oauth.google import GoogleOAuth
from deepinsights.core.integrations.oauth.microsoft import MicrosoftOAuth
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.users.models.flags import Flag
from deepinsights.users.models.user import User

client = TestClient(app)

pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture(autouse=True)
def set_up_tear_down() -> Generator[None, None, None]:
    yield
    app.dependency_overrides = {}


@pytest.fixture
def test_user(django_user_model: User) -> User:
    test_user = django_user_model.objects.create(username="<EMAIL>", password="123")
    app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.select_related("organization").get(
        username="<EMAIL>"
    )
    return test_user


def _set_flag_active(flag: Flags, active: bool) -> None:
    f = Flag.objects.get(name=flag.name)
    f.everyone = active
    f.override_enabled_by_environment = None  # To ensure no environment override
    f.save()


@pytest.mark.parametrize(
    "route, method",
    [
        ("/api/v2/settings/menu", "get"),
        ("/api/v2/settings/details", "get"),
        ("/api/v2/settings/save", "post"),
    ],
)
def test_all_unauthenticated_routes(route: str, method: str) -> None:
    match method:
        case "get":
            response = client.get(route)
        case "post":
            response = client.post(route)
    assert response.status_code == status.HTTP_403_FORBIDDEN


@pytest.fixture
def expected_user_menu() -> list[dict[str, Any]]:
    return [
        {
            "id": "my-account",
            "label": "My Account",
            "items": [
                {"id": "integrations", "label": "Integrations", "items": None},
                {"id": "settings", "label": "Settings", "items": None},
                {"id": "profile-details", "label": "Profile Details", "items": None},
            ],
        }
    ]


@pytest.fixture
def expected_admin_menu(expected_user_menu: list[dict[str, Any]]) -> list[dict[str, Any]]:
    return expected_user_menu + [
        {
            "id": "admin",
            "label": "Admin",
            "items": [
                {"id": "user-impersonation", "label": "User Impersonation", "items": None},
            ],
        }
    ]


def test_menu_non_admin(test_user: User, expected_user_menu: list[dict[str, Any]]) -> None:
    response = client.get("/api/v2/settings/menu")
    assert response.status_code == status.HTTP_200_OK, "Expected 200 OK for regular user"
    assert response.json() == expected_user_menu, "Unexpected menu structure for regular user"


def test_menu_admin(test_user: User, expected_admin_menu: list[dict[str, Any]]) -> None:
    _set_flag_active(Flags.EnableUserImpersonation, True)
    test_user.is_superuser = True
    test_user.save()

    response = client.get("/api/v2/settings/menu")
    assert response.status_code == status.HTTP_200_OK, "Expected 200 OK for admin user"
    assert response.json() == expected_admin_menu, "Unexpected menu structure for admin user"


@pytest.mark.parametrize(
    "value",
    ["integrations", "settings", "profile-details"],
)
def test_details_response_pattern(value: str, test_user: User) -> None:
    response = client.get("/api/v2/settings/details", params={"identifier": value})
    assert response.status_code == status.HTTP_200_OK

    response_data = response.json()
    section_details = SectionDetails.model_validate(response_data)

    # Verify that it's a valid SectionDetails object with proper types
    assert isinstance(section_details.label, str)
    assert isinstance(section_details.data, list)
    assert isinstance(section_details.showSaveButton, bool)

    # Additional validations based on the identifier
    if value == "integrations":
        assert section_details.label == "Integrations"
        assert not section_details.showSaveButton
        assert len(section_details.data) >= 2  # At least "My Integrations" and "Available Integrations"

        # Validate that integration cards have the correct structure
        for item in section_details.data:
            assert hasattr(item, "kind")
            assert hasattr(item, "id")
            assert hasattr(item, "label")
            if hasattr(item, "cards"):
                assert isinstance(item.cards, list)
            if hasattr(item, "filters"):
                assert isinstance(item.filters, list)

    elif value == "settings":
        assert section_details.label == "Settings"
        assert section_details.showSaveButton
        # Settings data can be empty if no feature flags are enabled, which is valid behavior
        assert isinstance(section_details.data, list)

        # If settings exist, validate their structure
        for item in section_details.data:
            assert hasattr(item, "kind")
            assert hasattr(item, "id")
            assert hasattr(item, "label")
            assert hasattr(item, "value")

    elif value == "profile-details":
        assert section_details.label == "Profile Details"
        assert not section_details.showSaveButton
        # Should have profile-related fields
        assert len(section_details.data) >= 3  # At least name, email, and contact support

        # Validate profile fields structure
        for item in section_details.data:
            assert hasattr(item, "kind")
            assert hasattr(item, "id")
            assert hasattr(item, "label")
            if hasattr(item, "value"):
                assert isinstance(item.value, (str, type(None)))
            if hasattr(item, "disabled"):
                assert isinstance(item.disabled, bool)


def test_details_invalid_identifier(test_user: User) -> None:
    response = client.get("/api/v2/settings/details", params={"identifier": "invalid-path"})
    assert response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.parametrize(
    "field, is_active, kind, label",
    [
        ("msCalendarAutojoin", True, "boolean-field", "Enable auto-join for calendar"),
        ("msCalendarAutojoin", False, "boolean-field", "Enable auto-join for calendar"),
        ("googleCalendarAutojoin", True, "boolean-field", "Enable auto-join for calendar"),
        ("googleCalendarAutojoin", False, "boolean-field", "Enable auto-join for calendar"),
        ("calendarShowMeetingsWithoutUrls", True, "boolean-field", "Show calendar meetings without meeting URLs"),
        ("calendarShowMeetingsWithoutUrls", False, "boolean-field", "Show calendar meetings without meeting URLs"),
        ("calendarLookahead", True, "single-choice-field", "Calendar lookahead"),
        ("calendarLookahead", False, "single-choice-field", "Calendar lookahead"),
    ],
)
def test_details_settings(field: str, is_active: bool, kind: str, label: str, test_user: User) -> None:
    if field == "msCalendarAutojoin" or field == "googleCalendarAutojoin":
        _set_flag_active(Flags.EnableAutoJoinToggleSetting, True)
        _set_flag_active(Flags.EnableAutoJoinBotsToCalendarEvents, True)

        # mock "microsoft" / "google" in oauth_integration_providers if `is_active` = true
        if is_active:
            OAuthCredentials.objects.create(
                user=test_user,
                integration=field == "msCalendarAutojoin" and "microsoft" or "google",
                access_token="access_token",
                refresh_token="refresh_token",
                scope="",
                expires_in=datetime.datetime.now(datetime.timezone.utc),
                refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=1),
            )

    if field == "calendarShowMeetingsWithoutUrls":
        _set_flag_active(Flags.EnableShowMeetingsWithoutURLsSetting, is_active)

    elif field == "calendarLookahead":
        _set_flag_active(Flags.EnableCalendarLookaheadSetting, is_active)

    response = client.get("/api/v2/settings/details", params={"identifier": "settings"})
    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()

    # check label
    assert response_data["label"] == "Settings"

    # check whether one of the fields has an id of `field` - depending upon `is_active`
    assert any(item["id"] == field for item in response_data["data"]) == is_active

    if is_active:
        # verify label & kind of item whose id is `field`
        item = next(item for item in response_data["data"] if item["id"] == field)
        assert item["label"] == label
        assert item["kind"] == kind

    # check save button
    assert response_data["showSaveButton"]


def test_details_profile(test_user: User) -> None:
    test_user.name = "Harry Potter"
    test_user.email = "<EMAIL>"
    test_user.save()

    print(test_user.name)
    print(test_user)

    response = client.get("/api/v2/settings/details", params={"identifier": "profile-details"})
    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()

    # check data
    assert response_data == {
        "label": "Profile Details",
        "data": [
            {"id": "name", "label": "Full Name", "kind": "text-field", "value": test_user.name, "disabled": True},
            {"id": "email", "label": "Email ID", "kind": "text-field", "value": test_user.email, "disabled": True},
            {
                "id": "contactSupport",
                "label": "Note",
                "kind": "link",
                "description": "Please connect with us if you want to edit your profile information",
                "text": "Contact Support",
                "appTag": "contact-support",
            },
        ],
        "showSaveButton": False,
    }


@pytest.mark.parametrize(
    "value",
    [
        CalendarLookahead.ONE_HOUR,
        CalendarLookahead.END_OF_DAY,
        CalendarLookahead.ONE_DAY,
        CalendarLookahead.TWO_DAYS,
        CalendarLookahead.END_OF_WEEK,
        CalendarLookahead.END_OF_NEXT_WEEK,
    ],
)
def test_save_calendar_lookahead(value: str, test_user: User) -> None:
    payload = {"calendarLookahead": value}
    response = client.post("/api/v2/settings/save", json=payload)
    assert response.status_code == status.HTTP_200_OK
    test_user.refresh_from_db()
    assert test_user.calendar_lookahead == value


@pytest.mark.parametrize("value", [True, False])
def test_save_calendar_show_meetings_without_urls(value: bool, test_user: User) -> None:
    payload = {"calendarShowMeetingsWithoutUrls": value}
    response = client.post("/api/v2/settings/save", json=payload)
    assert response.status_code == status.HTTP_200_OK
    test_user.refresh_from_db()
    assert test_user.show_events_without_meeting_urls == value


@pytest.mark.parametrize("value", [True, False])
@patch("api.routers.settings.update_recall_auto_join_integration")
def test_save_ms_calendar_autojoin(
    mock_update_recall_auto_join_integration: MagicMock, value: bool, test_user: User
) -> None:
    payload = {"msCalendarAutojoin": value}
    response = client.post("/api/v2/settings/save", json=payload)
    assert response.status_code == status.HTTP_200_OK
    mock_update_recall_auto_join_integration.assert_called_once_with(
        test_user,
        value,
        ANY,
        RecallBotController.link_microsoft_calendar,
    )
    assert isinstance(mock_update_recall_auto_join_integration.call_args[0][2], MicrosoftOAuth)


@pytest.mark.parametrize("value", [True, False])
@patch("api.routers.settings.update_recall_auto_join_integration")
def test_save_google_calendar_autojoin(
    mock_update_recall_auto_join_integration: MagicMock, value: bool, test_user: User
) -> None:
    payload = {"googleCalendarAutojoin": value}
    response = client.post("/api/v2/settings/save", json=payload)
    assert response.status_code == status.HTTP_200_OK
    mock_update_recall_auto_join_integration.assert_called_once_with(
        test_user, value, ANY, RecallBotController.link_google_calendar
    )
    assert isinstance(mock_update_recall_auto_join_integration.call_args[0][2], GoogleOAuth)
