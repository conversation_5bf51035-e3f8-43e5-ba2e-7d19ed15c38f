from enum import StrEnum, unique


@unique
class CalendarLookahead(StrEnum):
    ONE_HOUR = "one_hour"
    END_OF_DAY = "end_of_day"
    ONE_DAY = "one_day"
    TWO_DAYS = "two_days"
    END_OF_WEEK = "end_of_week"
    END_OF_NEXT_WEEK = "end_of_next_week"

    @classmethod
    def _missing_(cls, _: object) -> "CalendarLookahead":
        return cls.ONE_DAY

    @classmethod
    def values(cls) -> set[str]:
        return set([value.value for value in cls])
