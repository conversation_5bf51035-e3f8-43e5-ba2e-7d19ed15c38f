from enum import Enum
from typing import List, Optional, Union

from pydantic import BaseModel


# The types of integrations that can be displayed.
class IntegrationType(str, Enum):
    CRM = "CRM"

    MEETING_SOFTWARE = "MeetingSoftware"

    CALENDAR = "Calendar"


class MenuItemId(Enum):
    MY_ACCOUNT = "my-account"
    INTEGRATIONS = "integrations"
    SETTINGS = "settings"
    PROFILE_DETAILS = "profile-details"

    ADMIN = "admin"
    USER_IMPERSONATION = "user-impersonation"


class MenuItem(BaseModel):
    # unique identifier for the menu item
    id: MenuItemId

    # name of the menu item
    label: str

    # sub-menu items
    items: list["MenuItem"] | None = None


# NOTE: the union results in "flattening of the children types" as well
# i.e., all fields such as `cards` & `filters` are now assumed to be mandatory on every item in `SectionDetails.data` - unless explicitly marked as optional
SectionItemType = Union[
    "SectionItemTextField",
    "SectionItemSingleChoiceField",
    "SectionItemMultiChoiceField",
    "SectionItemBooleanField",
    "SectionItemAcknowledgementField",
    "SectionItemLink",
    "SectionItemIntegrationCards",
]


class SectionItemFieldType(Enum):
    TEXT = "text-field"
    SINGLE_CHOICE = "single-choice-field"
    MULTI_CHOICE = "multi-choice-field"
    BOOLEAN = "boolean-field"
    ACKNOWLEDGEMENT = "acknowledgement-field"
    INTEGRATION_CARDS = "integration-cards"
    INTEGRATION_CARD = "integration-card"
    LINK = "link"


class SectionDetails(BaseModel):
    # name of the section
    label: str

    # list of data items in the section
    data: List[SectionItemType]

    showSaveButton: bool = False


class SectionItemBase(BaseModel):
    # unique identifier
    id: str

    # name of the data item
    label: str


class SectionItemTextField(SectionItemBase):
    # type of the data item
    kind: SectionItemFieldType = SectionItemFieldType.TEXT
    placeholder: str | None = None
    value: str | None = None  # value of the field
    disabled: bool | None = False  # if true, disable the field


class LabeledEntity(BaseModel):
    id: str
    label: str


SettingsFilter = [
    LabeledEntity(id=IntegrationType.CRM, label="CRM"),
    LabeledEntity(id=IntegrationType.CALENDAR, label="Calendar"),
    LabeledEntity(id=IntegrationType.MEETING_SOFTWARE, label="Meeting Software"),
]


class SectionItemSingleChoiceField(SectionItemBase):
    kind: SectionItemFieldType = SectionItemFieldType.SINGLE_CHOICE
    options: Optional[list[LabeledEntity]] = None
    value: Optional[str] = None  # value of the field


class SectionItemMultiChoiceField(SectionItemBase):
    kind: SectionItemFieldType = SectionItemFieldType.MULTI_CHOICE
    options: Optional[list[LabeledEntity]] = None
    value: Optional[List[str]] = None  # value of the field


class SectionItemBooleanField(SectionItemBase):
    kind: SectionItemFieldType = SectionItemFieldType.BOOLEAN
    value: Optional[bool] = False  # value of the field


class SectionItemAcknowledgementField(SectionItemBase):
    kind: SectionItemFieldType = SectionItemFieldType.ACKNOWLEDGEMENT
    value: Optional[bool] = False  # value of the field


class SectionItemLink(SectionItemBase):
    kind: SectionItemFieldType = SectionItemFieldType.LINK
    description: str | None = None
    text: str | None = None
    appTag: str | None = None


class SectionItemIntegrationCards(SectionItemBase):
    kind: SectionItemFieldType = SectionItemFieldType.INTEGRATION_CARDS
    filters: Optional[list[LabeledEntity]] = None

    # list of integration cards
    cards: Optional[List["SectionItemIntegrationCard"]] = None


class SectionItemIntegrationCard(SectionItemBase):
    # type of the data item
    kind: SectionItemFieldType = SectionItemFieldType.INTEGRATION_CARD

    # tag name by which to filter
    tag: str

    # indicates whether user has completed this integration
    isActive: bool

    redirectPath: Optional[str] = None

    value: Optional[str] = None

    isInteractible: Optional[bool] = False


# This is used to save settings that are editable by the user.
class SaveRequest(BaseModel):
    name: str | None = None
    companyName: str | None = None
    msCalendarAutojoin: bool | None = None
    googleCalendarAutojoin: bool | None = None
    calendarLookahead: str | None = None
    calendarShowMeetingsWithoutUrls: bool | None = None
    bentoTagsEnabled: bool | None = None
