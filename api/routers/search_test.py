import json
import uuid
from typing import Any, Dict
from unittest.mock import AsyncMock, patch

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from api.dependencies import user_from_authorization_header
from api.internal_api import internal_api as app
from api.routers.note_models import SummarySection
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User

client = TestClient(app)

pytestmark = [pytest.mark.django_db(transaction=True)]


@pytest.fixture(autouse=True)
def setup_teardown():  # type: ignore[no-untyped-def]
    app.dependency_overrides = {}
    yield
    app.dependency_overrides = {}


def _create_note(
    note_owner: User,
    authorized_users: list[User] | None = None,
    title: str = "Test meeting",
    diarized_transcript: str
    | None = """[{'speaker': '<PERSON>', 'start': datetime.timedelta(microseconds=640000), 'end': datetime.timedelta(seconds=10, microseconds=20000), 'utt': \"Welcome to TRS.\"}, {'speaker': '<PERSON>', 'start': datetime.timedelta(seconds=10, microseconds=320000), 'end': datetime.timedelta(seconds=12, microseconds=99999), 'utt': 'Thank you. Yeah. Genuinely'}]""",
    summary: Dict[Any, Any] | None = {
        "sections": [
            {
                "topic": "MORTGAGE REFINANCING OPTIONS",
                "bullets": [
                    "Aaron Price informed me that their construction loan at 3.85% is no longer viable as the construction company requires the final payment at closing. The bank has proposed a cash-out refinance with higher rates.",
                    "The new proposed rates are 5.875% or 5.625% if they reduce the loan amount to $620,000 by paying $35,000 out of pocket.",
                ],
            },
        ]
    },
) -> Note:
    n: Note = Note.objects.create(
        note_owner=note_owner,
        uuid=uuid.uuid4(),
        note_type="meeting_recording",
        status="uploaded",
        metadata={"meeting_name": title},
        raw_transcript="[{'speaker': 'speaker0', 'start': datetime.timedelta(microseconds=640000), 'end': datetime.timedelta(seconds=10, microseconds=20000), 'utt': \"Welcome to TRS.\"}, {'speaker': 'speaker1', 'start': datetime.timedelta(seconds=10, microseconds=320000), 'end': datetime.timedelta(seconds=12, microseconds=99999), 'utt': 'Thank you. Yeah. Genuinely'}]",
        diarized_trans_with_names=diarized_transcript,
        summary=summary,
    )
    n.authorized_users.add(*authorized_users if authorized_users is not None else [note_owner])
    n.save()
    return n


@pytest.fixture
def test_user(django_user_model: User) -> User:
    test_user = django_user_model.objects.create(
        username="<EMAIL>",
    )
    app.dependency_overrides[user_from_authorization_header] = lambda: User.objects.get(username="<EMAIL>")
    return test_user


def test_unauthenticated():  # type: ignore[no-untyped-def]
    search_params = {"query": "test"}
    response = client.get("/api/v2/search/123", params=search_params)

    assert response.status_code == status.HTTP_403_FORBIDDEN


@patch("api.routers.search.search_note", new_callable=AsyncMock)
def test_search_note_found(mock_search_note, test_user: User):  # type: ignore[no-untyped-def]
    """Test successful search on an authorized note."""
    note = _create_note(note_owner=test_user)

    summary_section = SummarySection(topic="Test", bullets=["test1", "test2", "test3", "test4"])
    mock_search_note.return_value = summary_section

    # Make the request
    search_params = {"query": "test"}
    response = client.get(f"/api/v2/search/{note.uuid}", params=search_params)

    # Verify response
    assert response.status_code == status.HTTP_200_OK
    assert "answer" in response.json()
    assert "search_query_id" in response.json()
    assert response.json()["answer"] == summary_section.model_dump()
    assert response.json()["search_query_id"] is not None
    mock_search_note.assert_called_once()


def test_search_note_not_found(test_user: User):  # type: ignore[no-untyped-def]
    app.dependency_overrides[user_from_authorization_header] = lambda: test_user
    response = client.get(f"/api/v2/search/{uuid.uuid4()}", params={"query": "test"})

    assert response.status_code == status.HTTP_404_NOT_FOUND


def test_search_not_authorized(test_user: User):  # type: ignore[no-untyped-def]
    other_user = User.objects.create(username="<EMAIL>")
    note = _create_note(note_owner=other_user)

    response = client.get(f"/api/v2/search/{note.uuid}", params={"query": "test"})

    assert response.status_code == status.HTTP_403_FORBIDDEN


@patch("deepinsights.core.ml.search.call_gpt4o_async")
def test_search_internal_error(mock_openai_call, test_user: User):  # type: ignore[no-untyped-def]
    """Test that a search error returns 500."""
    note = _create_note(note_owner=test_user)

    mock_openai_call.side_effect = Exception("Failed to perform search on this note")
    search_params = {"query": "test"}
    response = client.get(f"/api/v2/search/{note.uuid}", params=search_params)

    assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
    assert response.json()["detail"] == "An error occurred while doing search on the note"
    mock_openai_call.assert_called_once()


@patch("deepinsights.core.ml.search.call_gpt4o_async")
def test_search_transcript_and_summary_present(mock_openai_call, test_user: User) -> None:  # type: ignore[no-untyped-def]
    """Test search when both transcript and summary are present."""
    note = _create_note(note_owner=test_user)
    mock_response = {"topic": "Test", "bullets": ["test1", "test2", "test3", "test4"]}

    summary_section = SummarySection(topic="Test", bullets=["test1", "test2", "test3", "test4"])
    mock_openai_call.return_value = json.dumps(mock_response)
    search_params = {"query": "test"}
    response = client.get(f"/api/v2/search/{note.uuid}", params=search_params)

    assert response.status_code == status.HTTP_200_OK
    assert "answer" in response.json()
    assert "search_query_id" in response.json()
    assert response.json()["answer"] == summary_section.model_dump()
    assert response.json()["search_query_id"] is not None
    mock_openai_call.assert_called_once()


@patch("api.routers.search.search_note", new_callable=AsyncMock)
def test_search_transcript_present_summary_absent(mock_search_note, test_user: User) -> None:  # type: ignore[no-untyped-def]
    """Test search when transcript is present and summary is absent."""
    note = _create_note(note_owner=test_user, summary=None)
    summary_section = SummarySection(topic="Test", bullets=["test1", "test2", "test3", "test4"])

    mock_search_note.return_value = summary_section
    search_params = {"query": "test"}
    response = client.get(f"/api/v2/search/{note.uuid}", params=search_params)

    assert response.status_code == status.HTTP_200_OK
    assert "answer" in response.json()
    assert "search_query_id" in response.json()
    assert response.json()["answer"] == summary_section.model_dump()
    assert response.json()["search_query_id"] is not None
    mock_search_note.assert_called_once()


@patch("api.routers.search.search_note", new_callable=AsyncMock)
def test_search_summary_present_transcript_absent(mock_search_note, test_user: User) -> None:  # type: ignore[no-untyped-def]
    """Test search when summary is present and transcript is absent."""
    note = _create_note(note_owner=test_user, diarized_transcript=None)
    summary_section = SummarySection(topic="Test", bullets=["test1", "test2", "test3", "test4"])

    mock_search_note.return_value = summary_section
    search_params = {"query": "test"}
    response = client.get(f"/api/v2/search/{note.uuid}", params=search_params)

    assert response.status_code == status.HTTP_200_OK
    assert "answer" in response.json()
    assert "search_query_id" in response.json()
    assert response.json()["answer"] == summary_section.model_dump()
    assert response.json()["search_query_id"] is not None
    mock_search_note.assert_called_once()


@patch("api.routers.search.search_note", new_callable=AsyncMock)
def test_search_transcript_and_summary_absent(mock_search_note, test_user: User) -> None:  # type: ignore[no-untyped-def]
    """Test search when both transcript and summary are absent."""
    note = _create_note(
        note_owner=test_user,
        diarized_transcript=None,
        summary=None,
    )

    # Expect a 404 error since there's no content to search
    search_params = {"query": "test"}
    response = client.get(f"/api/v2/search/{note.uuid}", params=search_params)

    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json()["detail"] == "No transcript or summary found on note"
