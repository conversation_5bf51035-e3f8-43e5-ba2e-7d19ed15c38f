from asgiref.sync import sync_to_async
from django.conf import settings
from fastapi import APIRouter, Depends, HTTPException, Query, Request, status

from api.dependencies import user_from_authorization_header
from api.routers.calendar import DEFAULT_LOOKAHEAD
from api.routers.calendar_models import Calendar<PERSON>ookahead
from api.routers.settings_models import (
    IntegrationType,
    LabeledEntity,
    MenuItem,
    MenuItemId,
    SaveRequest,
    SectionDetails,
    SectionItemBooleanField,
    SectionItemIntegrationCard,
    SectionItemIntegrationCards,
    SectionItemLink,
    SectionItemSingleChoiceField,
    SectionItemTextField,
    SectionItemType,
    SettingsFilter,
)
from deepinsights.core.integrations.calendar.auto_join import update_recall_auto_join_integration
from deepinsights.core.integrations.meetingbot.recall_ai import RecallBotController
from deepinsights.core.integrations.oauth.google import GoogleOAuth
from deepinsights.core.integrations.oauth.microsoft import MicrosoftOAuth
from deepinsights.core.integrations.oauth.wealthbox import Wealth<PERSON><PERSON><PERSON>uth
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.meetingsapp.models.structured_meeting_data import (
    StructuredMeetingDataTemplate,
    StructuredMeetingDataTemplateRule,
)
from deepinsights.users.models.user import User

router = APIRouter(tags=["settings"], generate_unique_id_function=lambda route: f"{route.tags[0]}-{route.name}")


# Routes


@router.get("/menu")
async def get_settings_menu_route(
    request: Request, user: User = Depends(user_from_authorization_header)
) -> list[MenuItem]:
    return await get_settings_menu(request, user)


@router.get("/details")
async def get_settings_details_route(
    request: Request,
    user: User = Depends(user_from_authorization_header),
    identifier: str = Query(""),
) -> SectionDetails:
    return await get_settings_details(request, user, identifier)


@router.post("/save", status_code=status.HTTP_200_OK)
def save(data: SaveRequest, user: User = Depends(user_from_authorization_header)) -> int:
    return post_save(data, user)


# Implementations


async def get_settings_menu(request: Request, user: User) -> list[MenuItem]:
    user_impersonation_settings = await _get_user_impersonation_settings(user)
    settings = [
        MenuItem(
            id=MenuItemId.MY_ACCOUNT,
            label="My Account",
            items=[
                MenuItem(
                    id=MenuItemId.INTEGRATIONS,
                    label="Integrations",
                ),
                MenuItem(
                    id=MenuItemId.SETTINGS,
                    label="Settings",
                ),
                MenuItem(
                    id=MenuItemId.PROFILE_DETAILS,
                    label="Profile Details",
                ),
            ],
        ),
    ]

    if user_impersonation_settings:
        settings.extend(
            [
                MenuItem(
                    id=MenuItemId.ADMIN,
                    label="Admin",
                    items=[
                        MenuItem(
                            id=MenuItemId.USER_IMPERSONATION,
                            label="User Impersonation",
                        ),
                    ],
                )
            ]
        )

    return settings


async def _get_user_impersonation_settings(user: User) -> bool:
    enable_user_impersonation = await sync_to_async(Flags.EnableUserImpersonation.is_active_for_user)(user)
    return bool(enable_user_impersonation and user.is_superuser)


async def get_settings_details(request: Request, user: User, identifier: str) -> SectionDetails:
    # use match case on `identifier` to determine which settings to return
    match identifier:
        case MenuItemId.INTEGRATIONS.value:
            return await _get_settings_integrations(request, user)
        case MenuItemId.SETTINGS.value:
            return await _get_settings_calendar(request, user)
        case MenuItemId.PROFILE_DETAILS.value:
            return await _get_profile_details(request, user)

        case _:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND)


async def _get_settings_integrations(request: Request, user: User) -> SectionDetails:
    oauth_integration_providers = set([o.integration async for o in OAuthCredentials.objects.filter(user=user)])
    integrations = await _get_integrations(request, oauth_integration_providers, user)
    return SectionDetails(
        label="Integrations",
        data=[
            SectionItemIntegrationCards(
                id="myIntegrations",
                label="My Integrations",
                filters=SettingsFilter,
                cards=[setting for setting in integrations if setting is not None and setting.isActive],
            ),
            SectionItemIntegrationCards(
                id="availableIntegrations",
                label="Available Integrations",
                filters=SettingsFilter,
                cards=[setting for setting in integrations if setting is not None and not setting.isActive],
            ),
        ],
    )


async def _get_integrations(
    request: Request, oauth_integration_providers: set[str], user: User
) -> list[SectionItemIntegrationCard | None]:
    salesforce_oauth_enabled = await sync_to_async(Flags.EnableSalesforceOAuthIntegration.is_active_for_user)(user)

    wealthbox_status = (
        ""
        if "wealthbox" not in oauth_integration_providers
        else (
            "Integrated" if await sync_to_async(WealthBoxOAuth().check_integration_status)(user) else "Needs attention"
        )
    )
    dynamics_config = user.get_crm_configuration().dynamics
    dynamics_enabled = (
        dynamics_config and hasattr(dynamics_config, "dynamics_resource_url") and dynamics_config.dynamics_resource_url
    )

    proto = request.headers.get("X-Forwarded-Proto")
    host = request.headers.get("X-Forwarded-Host")
    base_url = f"{proto}://{host}" if (proto and host) else settings.APP_DOMAIN
    return [
        SectionItemIntegrationCard(
            id="connect_redtail",
            label="Redtail",
            tag=IntegrationType.CRM,
            isActive=True if user.get_crm_configuration().redtail.user_key else False,
            redirectPath="/settings/redtail",
            value="Reconnect Redtail" if user.get_crm_configuration().redtail.user_key else "Connect Redtail",
            isInteractible=True,
        ),
        SectionItemIntegrationCard(
            id="connect_zoom",
            label="Zoom",
            tag=IntegrationType.MEETING_SOFTWARE,
            isActive=True if "zoom" in oauth_integration_providers else False,
            redirectPath=f"https://zoom.us/oauth/authorize?response_type=code&client_id={settings.ZOOM_CLIENT_ID}&redirect_uri={settings.ZOOM_REDIRECT_URL}",
            isInteractible=True,
        ),
        SectionItemIntegrationCard(
            id="connect_wealthbox",
            label="Wealthbox",
            tag=IntegrationType.CRM,
            isActive=wealthbox_status == "Integrated" if True else False,
            redirectPath=f"https://app.crmworkspace.com/oauth/authorize?client_id={settings.WEALTHBOX_CLIENT_ID}&redirect_uri={base_url}/oauth/wealthbox&response_type=code&scope=login+data",
            isInteractible=True,
        ),
        SectionItemIntegrationCard(
            id="connect_google_meet",
            label="Google Meet",
            tag=IntegrationType.MEETING_SOFTWARE,
            isActive=True,
        ),
        SectionItemIntegrationCard(
            id="connect_microsoft_teams",
            label="Microsoft Teams",
            tag=IntegrationType.MEETING_SOFTWARE,
            isActive=True,
        ),
        SectionItemIntegrationCard(
            id="connect_webex",
            label="Webex",
            tag=IntegrationType.MEETING_SOFTWARE,
            isActive=True,
        ),
        SectionItemIntegrationCard(
            id="connect_salesforce",
            label="Salesforce",
            tag=IntegrationType.CRM,
            isActive=True if "salesforce" in oauth_integration_providers else False,
            redirectPath=f"https://login.salesforce.com/services/oauth2/authorize?client_id={settings.SALESFORCE_CONSUMER_KEY}&redirect_uri={base_url}/oauth/salesforce&response_type=code",
            isInteractible=True,
        )
        if salesforce_oauth_enabled
        else SectionItemIntegrationCard(
            id="connect_salesforce_contact_support",
            label="Salesforce",
            tag=IntegrationType.CRM,
            isActive=False,
            redirectPath="mailto:<EMAIL>",
            value="Contact Support",
            isInteractible=True,
        ),
        SectionItemIntegrationCard(
            id="connect_ms_calendar",
            label="Microsoft Calendar",
            tag=IntegrationType.CALENDAR,
            isActive=True if "microsoft" in oauth_integration_providers else False,
            redirectPath=f"https://login.microsoftonline.com/common/oauth2/v2.0/authorize?client_id={settings.MSAL_CLIENT_ID}&response_type=code&redirect_uri={base_url}/auth/microsoft/callback&response_mode=query&scope=Calendars.Read+User.Read+offline_access&state=calendar_integration",
            isInteractible=True,
        ),
        SectionItemIntegrationCard(
            id="connect_google_calendar",
            label="Google Calendar",
            tag=IntegrationType.CALENDAR,
            isActive=True if "google" in oauth_integration_providers else False,
            redirectPath=f"https://accounts.google.com/o/oauth2/v2/auth?client_id={settings.GOOGLE_CLIENT_ID}&redirect_uri={base_url}/auth/google/callback&response_type=code&scope=https://www.googleapis.com/auth/calendar.readonly&state=calendar_integration&access_type=offline&prompt=consent",
            isInteractible=True,
        ),
        SectionItemIntegrationCard(
            id="connect_microsoft_dynamics",
            label="Microsoft Dynamics",
            tag=IntegrationType.CRM,
            isActive=True if "microsoft_dynamics" in oauth_integration_providers else False,
            redirectPath=f"https://login.microsoftonline.com/common/oauth2/v2.0/authorize?client_id={settings.MICROSOFT_DYNAMICS_CLIENT_ID}&response_type=code&redirect_uri={base_url}/oauth/microsoft_dynamics&response_mode=query&scope={dynamics_config.dynamics_resource_url}/user_impersonation+offline_access+openid+profile",
            isInteractible=True,
        )
        if dynamics_enabled
        else None,
    ]


async def _get_settings_calendar(request: Request, user: User) -> SectionDetails:
    oauth_integration_providers = set([o.integration async for o in OAuthCredentials.objects.filter(user=user)])
    account_preferences = await _get_account_preferences(oauth_integration_providers, user)
    return SectionDetails(label="Settings", data=account_preferences, showSaveButton=True)


async def _get_account_preferences(oauth_integration_providers: set[str], user: User) -> list[SectionItemType]:
    auto_join_toggle_enabled = await sync_to_async(Flags.EnableAutoJoinToggleSetting.is_active_for_user)(
        user
    ) and await sync_to_async(Flags.EnableAutoJoinBotsToCalendarEvents.is_active_for_user)(user)
    calendar_lookahead_setting_enabled = await sync_to_async(Flags.EnableCalendarLookaheadSetting.is_active_for_user)(
        user
    )
    show_meetings_without_urls_setting_enabled = await sync_to_async(
        Flags.EnableShowMeetingsWithoutURLsSetting.is_active_for_user
    )(user)

    enable_bento_toggle = await sync_to_async(Flags.EnableBentoIntegrationSettingsToggle.is_active_for_user)(user)
    has_any_applicable_bento_tags_rule = (
        await StructuredMeetingDataTemplateRule.relevant_follow_up_templates(None, user)
        .filter(kind=StructuredMeetingDataTemplate.Kind.BENTO_LIFE_EVENTS)
        .aexists()
    )

    wealthbox_status = (
        ""
        if "wealthbox" not in oauth_integration_providers
        else (
            "Integrated" if await sync_to_async(WealthBoxOAuth().check_integration_status)(user) else "Needs attention"
        )
    )

    settings = [
        SectionItemBooleanField(
            id="msCalendarAutojoin",
            label="Enable auto-join for calendar",
            value=user.recall_calendar_id is not None,
        )
        if "microsoft" in oauth_integration_providers and auto_join_toggle_enabled
        else None,
        # hide auto-join toggle if the one for MS is already enabled (to avoid redundancy)
        SectionItemBooleanField(
            id="googleCalendarAutojoin",
            label="Enable auto-join for calendar",
            value=user.recall_calendar_id is not None,
        )
        if "google" in oauth_integration_providers
        and "microsoft" not in oauth_integration_providers
        and auto_join_toggle_enabled
        else None,
        SectionItemBooleanField(
            id="calendarShowMeetingsWithoutUrls",
            label="Show calendar meetings without meeting URLs",
            value=user.show_events_without_meeting_urls,
        )
        if show_meetings_without_urls_setting_enabled
        else None,
        SectionItemSingleChoiceField(
            id="calendarLookahead",
            label="Calendar lookahead",
            options=[
                LabeledEntity(id=CalendarLookahead.ONE_HOUR, label="One hour"),
                LabeledEntity(id=CalendarLookahead.END_OF_DAY, label="End of day"),
                LabeledEntity(id=CalendarLookahead.ONE_DAY, label="One day"),
                LabeledEntity(id=CalendarLookahead.TWO_DAYS, label="Two days"),
                LabeledEntity(id=CalendarLookahead.END_OF_WEEK, label="End of week"),
                LabeledEntity(id=CalendarLookahead.END_OF_NEXT_WEEK, label="End of next week"),
            ],
            value=user.calendar_lookahead or DEFAULT_LOOKAHEAD,
        )
        if calendar_lookahead_setting_enabled
        else None,
        SectionItemBooleanField(
            id="bentoTagsEnabled",
            label="Enable Bento tags",
            value=has_any_applicable_bento_tags_rule,
        )
        if wealthbox_status == "Integrated" and enable_bento_toggle
        else None,
    ]

    return [item for item in settings if item is not None]


async def _get_profile_details(request: Request, user: User) -> SectionDetails:
    return SectionDetails(
        label="Profile Details",
        data=[
            SectionItemTextField(
                id="name",
                label="Full Name",
                placeholder="John Doe",
                value=user.name,
                disabled=True,
            ),
            SectionItemTextField(
                id="email",
                label="Email ID",
                placeholder="<EMAIL>",
                value=user.email,
                disabled=True,
            ),
            SectionItemLink(
                id="contactSupport",
                label="Note",
                description="Please connect with us if you want to edit your profile information",
                text="Contact Support",
                appTag="contact-support",
            ),
        ],
    )


# NOTE: For any data point, `None` indicates that the value should not be changed from its current server value
def post_save(data: SaveRequest, user: User) -> int:
    if data.msCalendarAutojoin is not None:
        update_recall_auto_join_integration(
            user,
            data.msCalendarAutojoin,
            MicrosoftOAuth(),
            RecallBotController().link_microsoft_calendar,
        )

    if data.googleCalendarAutojoin is not None:
        update_recall_auto_join_integration(
            user,
            data.googleCalendarAutojoin,
            GoogleOAuth(),
            RecallBotController().link_google_calendar,
        )

    if data.calendarShowMeetingsWithoutUrls is not None:
        user.show_events_without_meeting_urls = data.calendarShowMeetingsWithoutUrls
        user.save()

    if data.calendarLookahead is not None:
        # TODO: @debojyotighosh cross-check the logic here; the "save" part should be inside `else` block.
        # Also default value is not being set, so alter the warning message accordingly
        if data.calendarLookahead in CalendarLookahead.values():
            user.calendar_lookahead = CalendarLookahead(data.calendarLookahead)
            user.save()

    # TODO: Test this before merging!!
    if data.bentoTagsEnabled is not None:
        try:
            bento_tags_template = StructuredMeetingDataTemplate.objects.get(
                kind=StructuredMeetingDataTemplate.Kind.BENTO_LIFE_EVENTS
            )
        except StructuredMeetingDataTemplate.DoesNotExist:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR)
        if data.bentoTagsEnabled:
            # Even if the user already has an org-level Bento tags rule, this setting is specific to
            # the user, so we create the rule regardless. We assume that the frontend is preventing
            # users from doing this when they should not, so we don't block creation here.
            has_user_level_bento_tags_rule = StructuredMeetingDataTemplateRule.objects.filter(
                users=user, follow_up_templates=bento_tags_template
            ).exists()
            if has_user_level_bento_tags_rule:
                return status.HTTP_200_OK
            rule = StructuredMeetingDataTemplateRule.objects.create(meeting_categories=["client"], everyone=False)
            rule.users.add(user)
            rule.follow_up_templates.add(bento_tags_template)
            rule.save()
        else:
            StructuredMeetingDataTemplateRule.objects.filter(
                users=user, follow_up_templates=bento_tags_template
            ).delete()

    return status.HTTP_200_OK
