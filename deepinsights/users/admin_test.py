import datetime
from unittest import mock
from unittest.mock import MagicMock, patch

from django.contrib.admin.sites import AdminSite
from django.contrib.messages import get_messages
from django.contrib.messages.middleware import MessageMiddleware
from django.contrib.sessions.middleware import SessionMiddleware
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.http import HttpRequest
from django.test import Client, RequestFactory, TestCase
from django.urls import reverse
from django.utils import timezone
from django_json_widget.widgets import J<PERSON>NEditorWidget

from deepinsights.core.integrations.meetingbot.recall_ai import RecallBotController
from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.users.admin import UserAdmin
from deepinsights.users.admin_utils import CrmSystemFilter
from deepinsights.users.forms import UserAdminChangeForm, UserAdminCreationForm
from deepinsights.users.models.user import User


class UserAdminTest(TestCase):
    def setUp(self) -> None:
        self.site = AdminSite()
        self.user_admin = UserAdmin(User, self.site)
        self.factory = RequestFactory()

        # Create test user
        self.user = User.objects.create(
            email="<EMAIL>", username="testuser", name="Test User", is_staff=True, is_superuser=False
        )

        # Create test superuser
        self.superuser = User.objects.create(
            email="<EMAIL>", username="adminuser", name="Admin User", is_staff=True, is_superuser=True
        )

        self.user_with_calendar = User.objects.create(
            email="<EMAIL>",
            username="calendaruser",
            name="Calendar User",
            is_staff=True,
            is_superuser=False,
            metadata={"recall_calendar_id": "calendar_id_1"},
        )

    def _add_session_and_messages(self, request: HttpRequest) -> None:
        """Helper method to add session and message middleware to request"""
        SessionMiddleware(lambda response: response).process_request(request)
        MessageMiddleware(lambda response: response).process_request(request)
        request.session.save()

    def test_form_classes(self) -> None:
        """Test if correct form classes are assigned"""
        self.assertEqual(self.user_admin.form, UserAdminChangeForm)
        self.assertEqual(self.user_admin.add_form, UserAdminCreationForm)

    def test_list_display(self) -> None:
        """Test if list_display contains correct fields"""
        expected_fields = [
            "id",
            "email",
            "username",
            "name",
            "is_superuser",
            "notes_link",
            "license_type",
            "organization",
        ]
        self.assertEqual(self.user_admin.list_display, expected_fields)

    def test_search_fields(self) -> None:
        """Test if search_fields contains correct fields"""
        self.assertEqual(self.user_admin.search_fields, ["email", "uuid", "username", "name"])

    def test_ordering(self) -> None:
        """Test if ordering is set correctly"""
        self.assertEqual(self.user_admin.ordering, ("-date_joined",))

    def test_notes_link(self) -> None:
        """Test if notes_link method generates correct URL"""
        expected_url = f"/api/admin/meetingsapp/note/?note_owner__pk__exact={self.user.id}"
        result = self.user_admin.notes_link(self.user)
        self.assertIn(expected_url, result)
        self.assertIn("View Notes", result)

    @patch("deepinsights.users.admin_utils.generate_random_password")
    @patch("deepinsights.core.email_service.ZeplynEmailService.send_email")
    def test_generate_password_and_send_email_success(
        self, mock_send_email: MagicMock, mock_generate_password: MagicMock
    ) -> None:
        mock_generate_password.return_value = "test_password123"
        mock_send_email.return_value = {"message_id": "test-message-id"}

        request = self.factory.post("/")
        self._add_session_and_messages(request)

        queryset = User.objects.filter(pk=self.user.pk)
        self.user_admin.generate_password_and_send_email(request, queryset)

        self.user.refresh_from_db()
        mock_generate_password.assert_called_once()

        # Check success message
        mock_send_email.assert_called_once()
        call_args = mock_send_email.call_args[1]
        self.assertEqual(call_args["recipients"], self.user.email)
        self.assertEqual(call_args["subject"], "Zeplyn Login Credentials")
        self.assertIn("Your new Zeplyn password is: test_password123", call_args["body"])

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 2)
        self.assertIn("Password successfully generated and <NAME_EMAIL>.", str(messages[0]))
        self.assertIn("Successfully sent welcome emails to all 1 users", str(messages[1]))

    @patch("deepinsights.users.admin_utils.generate_random_password")
    @patch("deepinsights.core.email_service.ZeplynEmailService.send_email")
    def test_generate_password_and_send_email_failure(
        self, mock_send_email: MagicMock, mock_generate_password: MagicMock
    ) -> None:
        mock_generate_password.return_value = "test_password123"
        mock_send_email.side_effect = Exception("Email sending failed")

        request = self.factory.post("/")
        self._add_session_and_messages(request)

        queryset = User.objects.filter(pk=self.user.pk)
        self.user_admin.generate_password_and_send_email(request, queryset)

        mock_generate_password.assert_called_once()

        mock_send_email.assert_called_once()

        # Check error message
        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertIn(f"Failed to process user {self.user.email}", str(messages[0]))

    def test_fieldsets(self) -> None:
        """Test if fieldsets are properly configured"""
        fieldsets = self.user_admin.fieldsets

        # Check main sections exist
        section_names = [section[0] for section in fieldsets]
        self.assertIn(None, section_names)  # Basic section
        self.assertIn("Personal info", section_names)
        self.assertIn("Account Settings", section_names)
        self.assertIn("Permissions", section_names)
        self.assertIn("Important dates", section_names)

        # Extract and check permissions fields
        permissions_section = next(section for section in fieldsets if section[0] == "Permissions")
        permissions_fields = permissions_section[1]["fields"]

        # Check specific fields in permissions section
        self.assertIn("is_active", permissions_fields)
        self.assertIn("is_staff", permissions_fields)
        self.assertIn("is_superuser", permissions_fields)
        self.assertIn("groups", permissions_fields)
        self.assertIn("user_permissions", permissions_fields)

    @patch("deepinsights.users.admin.BotPreferencesMixin.update_bot_preferences")
    def test_update_bot_preferences_action(self, mock_update_preferences: MagicMock) -> None:
        """Test the update_bot_preferences action"""
        request = self.factory.post("/")
        self._add_session_and_messages(request)

        queryset = User.objects.filter(pk=self.user.pk)
        self.user_admin.update_bot_preferences(request, queryset)

        mock_update_preferences.assert_called_once_with(request, queryset)

    def test_formfield_overrides(self) -> None:
        """Test if JSONField is properly configured with JSONEditorWidget"""

        self.assertIn(JSONField, self.user_admin.formfield_overrides)
        self.assertIsInstance(self.user_admin.formfield_overrides[JSONField]["widget"], JSONEditorWidget)

    def test_crm_system_filter_queryset(self) -> None:
        user1 = User.objects.create(
            email="<EMAIL>", username="crm1", crm_configuration={"crm_system": "salesforce"}
        )
        user2 = User.objects.create(email="<EMAIL>", username="crm2", crm_configuration={"crm_system": "redtail"})

        crm_filter = CrmSystemFilter(request=HttpRequest(), params={}, model=User, model_admin=self.user_admin)

        all_users = User.objects.all()
        filtered_qs = crm_filter.queryset(HttpRequest(), all_users)
        self.assertEqual(filtered_qs, all_users)

        with mock.patch.object(crm_filter, "value", return_value="salesforce"):
            filtered_qs = crm_filter.queryset(HttpRequest(), all_users)
            self.assertEqual(filtered_qs.count(), 1)
            self.assertEqual(filtered_qs.first(), user1)

        with mock.patch.object(crm_filter, "value", return_value="redtail"):
            filtered_qs = crm_filter.queryset(HttpRequest(), all_users)
            self.assertEqual(filtered_qs.count(), 1)
            self.assertEqual(filtered_qs.first(), user2)

    def test_crm_system_filter_in_admin_list(self) -> None:
        self.assertIn(CrmSystemFilter, self.user_admin.list_filter)

    @patch("deepinsights.users.admin.sync_crm_clients")
    def test_fetch_clients_from_crm(self, mock_sync: MagicMock) -> None:
        request = self.factory.post("/")
        self._add_session_and_messages(request)

        queryset = User.objects.filter(pk=self.user.pk)
        self.user_admin.fetch_clients_from_crm(request, queryset)

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertIn("CRM fetch initiated for user(s). Check Celery job status for updates.", str(messages[0]))
        mock_sync.delay.assert_called_once_with(self.user.uuid)

    @patch("deepinsights.users.admin.sync_crm_clients")
    def test_fetch_clients_from_crm_multiple_users(self, mock_sync: MagicMock) -> None:
        request = self.factory.post("/")
        self._add_session_and_messages(request)

        self.user_admin.fetch_clients_from_crm(request, User.objects.all().order_by("pk"))

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertIn("CRM fetch initiated for user(s). Check Celery job status for updates.", str(messages[0]))
        mock_sync.delay.assert_has_calls([mock.call(self.user.uuid), mock.call(self.superuser.uuid)])

    @patch("deepinsights.users.admin.update_recall_auto_join_integration")
    def test_delete_recall_autojoin_calendar_integration_with_oauth(self, mock_update_integration: MagicMock) -> None:
        OAuthCredentials.objects.create(
            user=self.user,
            integration="google",
            access_token="dummy_access_token",
            refresh_token="dummy_refresh_token",
            expires_in=datetime.datetime.now(datetime.timezone.utc),
            refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=1),
        )
        request = self.factory.post("/")
        self._add_session_and_messages(request)

        queryset = User.objects.filter(pk=self.user.pk)
        self.user_admin.delete_recall_autojoin_calendar_integration(request, queryset)

        mock_update_integration.assert_called_once_with(
            self.user,
            enabled=False,
            oauth=mock.ANY,
            calendar_link_function=mock.ANY,
        )

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        expected_name = self.user.name or f"{self.user.first_name} {self.user.last_name}".strip()
        self.assertIn(
            f"Recall auto-join calendar integrations deleted for expired users {expected_name}", str(messages[0])
        )

    @patch("deepinsights.users.admin.update_recall_auto_join_integration")
    def test_delete_recall_autojoin_calendar_integration_without_oauth(
        self, mock_update_integration: MagicMock
    ) -> None:
        request = self.factory.post("/")
        self._add_session_and_messages(request)

        queryset = User.objects.filter(pk=self.user.pk)
        self.user_admin.delete_recall_autojoin_calendar_integration(request, queryset)

        mock_update_integration.assert_not_called()

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        expected_name = self.user.name or f"{self.user.first_name} {self.user.last_name}".strip()
        self.assertIn(f"No integration found for user {expected_name}, skipping deletion", str(messages[0]))

    @patch("deepinsights.users.admin.update_recall_auto_join_integration")
    def test_delete_recall_autojoin_calendar_integration_multiple_users(
        self, mock_update_integration: MagicMock
    ) -> None:
        test_user2 = User.objects.create(
            email="<EMAIL>", username="testuser2", name="Test User2", is_staff=True, is_superuser=False
        )
        test_user3 = User.objects.create(
            email="<EMAIL>", username="testuser3", name="Test User3", is_staff=True, is_superuser=False
        )
        OAuthCredentials.objects.create(
            user=test_user2,
            integration="google",
            access_token="dummy_access_token1",
            refresh_token="dummy_refresh_token1",
            expires_in=datetime.datetime.now(datetime.timezone.utc),
            refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=1),
        )
        OAuthCredentials.objects.create(
            user=test_user3,
            integration="microsoft",
            access_token="dummy_access_token2",
            refresh_token="dummy_refresh_token2",
            expires_in=datetime.datetime.now(datetime.timezone.utc),
            refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=1),
        )

        request = self.factory.post("/")
        self._add_session_and_messages(request)

        queryset = User.objects.filter(pk__in=[test_user2.pk, test_user3.pk]).order_by("username")
        self.user_admin.delete_recall_autojoin_calendar_integration(request, queryset)

        self.assertEqual(mock_update_integration.call_count, 2)

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 2)

        user1_name = test_user2.name or f"{test_user2.first_name} {test_user2.last_name}".strip()
        user2_name = test_user3.name or f"{test_user3.first_name} {test_user3.last_name}".strip()

        expected_messages = {
            f"Recall auto-join calendar integrations deleted for expired users {user1_name}",
            f"Recall auto-join calendar integrations deleted for expired users {user2_name}",
        }

        actual_messages = {str(msg) for msg in messages}

        self.assertEqual(expected_messages, actual_messages, "Messages don't match regardless of order")

    @patch.object(RecallBotController, "try_to_relink_calendar")
    def test_try_to_relink_disconnected_calendar(self, mock_try_to_relink_calendar: MagicMock) -> None:
        request = self.factory.post("/")
        self._add_session_and_messages(request)
        queryset = User.objects.filter(id=self.user_with_calendar.id)

        self.user_admin.try_to_relink_disconnected_calendar(request, queryset)

        mock_try_to_relink_calendar.assert_called_once_with("calendar_id_1")
        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(
            str(messages[0]),
            "Attempted to relink disconnected calendars. Check https://go/recallcalendar to confim. Users: ['Calendar User']",
        )

    @patch.object(RecallBotController, "try_to_relink_calendar")
    def test_try_to_relink_disconnected_calendar_no_users_with_autojoin(
        self, mock_try_to_relink_calendar: MagicMock
    ) -> None:
        request = self.factory.post("/")
        self._add_session_and_messages(request)
        queryset = User.objects.filter(id=self.user.id)

        self.user_admin.try_to_relink_disconnected_calendar(request, queryset)

        mock_try_to_relink_calendar.assert_not_called()
        messages = list(get_messages(request))
        self.assertEqual(str(messages[0]), "No calendars found to relink for users: ['Test User']")

    @patch.object(RecallBotController, "try_to_relink_calendar")
    def test_try_to_relink_disconnected_calendar_(self, mock_try_to_relink_calendar: MagicMock) -> None:
        request = self.factory.post("/")
        self._add_session_and_messages(request)
        queryset = User.objects.filter(id__in=[self.user.id, self.user_with_calendar.id, self.superuser.id]).order_by(
            "created"
        )

        self.user_admin.try_to_relink_disconnected_calendar(request, queryset)

        mock_try_to_relink_calendar.assert_called_once_with("calendar_id_1")
        messages = list(get_messages(request))
        self.assertEqual(len(messages), 2)
        self.assertEqual(str(messages[0]), "No calendars found to relink for users: ['Test User', 'Admin User']")
        self.assertEqual(
            str(messages[1]),
            "Attempted to relink disconnected calendars. Check https://go/recallcalendar to confim. Users: ['Calendar User']",
        )

    @patch("deepinsights.users.admin.load_demo_data_for_user")
    def test_load_demo_data_success(self, mock_load_demo_data: MagicMock) -> None:
        mock_load_demo_data.return_value = True

        request = self.factory.post("/")
        self._add_session_and_messages(request)

        # Create a test user with no notes
        with patch("deepinsights.users.admin.Note.objects.filter") as mock_filter:
            mock_filter.return_value.exists.return_value = False
            queryset = User.objects.filter(pk=self.user.pk)
            self.user_admin.load_demo_data(request, queryset)

        mock_load_demo_data.assert_called_once_with(self.user)

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertIn(f"Successfully loaded demo data for user {self.user.name}", str(messages[0]))

    @patch("deepinsights.users.admin.load_demo_data_for_user")
    def test_load_demo_data_failure(self, mock_load_demo_data: MagicMock) -> None:
        mock_load_demo_data.return_value = False

        request = self.factory.post("/")
        self._add_session_and_messages(request)

        with patch("deepinsights.users.admin.Note.objects.filter") as mock_filter:
            mock_filter.return_value.exists.return_value = False
            queryset = User.objects.filter(pk=self.user.pk)
            self.user_admin.load_demo_data(request, queryset)

        mock_load_demo_data.assert_called_once_with(self.user)

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertIn(f"Failed to load demo data for user {self.user.name}", str(messages[0]))

    @patch("deepinsights.users.admin.load_demo_data_for_user")
    def test_load_demo_data_exception(self, mock_load_demo_data: MagicMock) -> None:
        """Test handling of exceptions during demo data loading"""
        mock_load_demo_data.side_effect = Exception("Demo data loading failed")

        request = self.factory.post("/")
        self._add_session_and_messages(request)

        with patch("deepinsights.users.admin.Note.objects.filter") as mock_filter:
            mock_filter.return_value.exists.return_value = False
            queryset = User.objects.filter(pk=self.user.pk)
            self.user_admin.load_demo_data(request, queryset)

        mock_load_demo_data.assert_called_once_with(self.user)

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertIn(f"Error loading demo data for user {self.user.name}: Demo data loading failed", str(messages[0]))

    @patch("deepinsights.users.admin.load_demo_data_for_user")
    def test_load_demo_data_skip_existing_notes(self, mock_load_demo_data: MagicMock) -> None:
        request = self.factory.post("/")
        self._add_session_and_messages(request)

        with patch("deepinsights.users.admin.Note.objects.filter") as mock_filter:
            mock_filter.return_value.exists.return_value = True
            queryset = User.objects.filter(pk=self.user.pk)
            self.user_admin.load_demo_data(request, queryset)

        mock_load_demo_data.assert_not_called()

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertIn(f"Skipped demo data loading for user {self.user.name} - user already has notes", str(messages[0]))

    @patch("deepinsights.users.admin.load_demo_data_for_user")
    def test_load_demo_data_multiple_users(self, mock_load_demo_data: MagicMock) -> None:
        """Test loading demo data for multiple users with summary message"""
        mock_load_demo_data.side_effect = [True, False]

        request = self.factory.post("/")
        self._add_session_and_messages(request)

        with patch("deepinsights.users.admin.Note.objects.filter") as mock_filter:
            # First call for self.user (no notes), second for self.superuser (no notes), third for self.user_with_calendar (has notes)
            mock_filter.return_value.exists.side_effect = [False, False, True]
            queryset = User.objects.filter(
                pk__in=[self.user.pk, self.superuser.pk, self.user_with_calendar.pk]
            ).order_by("pk")
            self.user_admin.load_demo_data(request, queryset)

        self.assertEqual(mock_load_demo_data.call_count, 2)

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 4)

        self.assertIn(f"Successfully loaded demo data for user {self.user.name}", str(messages[0]))
        self.assertIn(f"Failed to load demo data for user {self.superuser.name}", str(messages[1]))
        self.assertIn(f"Skipped demo data loading for user {self.user_with_calendar.name}", str(messages[2]))

        self.assertIn(
            "Demo data loading complete: 1 successful, 1 skipped (already had data), 1 failed", str(messages[3])
        )

    @patch("deepinsights.users.admin.update_recall_auto_join_integration")
    @patch("deepinsights.users.admin.OAuthCredentials")
    def test_delete_and_create_autojoin_calendar_integration_no_calendar(
        self, mock_oauth_credentials: MagicMock, mock_update: MagicMock
    ) -> None:
        request = self.factory.post("/")
        self._add_session_and_messages(request)
        queryset = User.objects.filter(pk=self.user.pk)

        self.user_admin.delete_and_create_autojoin_calendar_integration(request, queryset)
        mock_oauth_credentials.assert_not_called()
        mock_update.assert_not_called()

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "No Recall calendar ID found for user Test User, skipping update")

    @patch("deepinsights.users.admin.update_recall_auto_join_integration")
    @patch("deepinsights.users.admin.MicrosoftOAuth")
    def test_update_auto_join_calendar_events_microsoft(self, mock_ms_oauth: MagicMock, mock_update: MagicMock) -> None:
        request = self.factory.post("/")
        self._add_session_and_messages(request)
        queryset = User.objects.filter(pk=self.user_with_calendar.pk)

        OAuthCredentials.objects.create(
            user=self.user_with_calendar,
            integration="microsoft",
            access_token="test",
            refresh_token="test",
            expires_in=timezone.now(),
            refresh_token_expires_in=timezone.now(),
        )

        self.user_admin.delete_and_create_autojoin_calendar_integration(request, queryset)

        mock_update.assert_any_call(
            self.user_with_calendar,
            enabled=False,
            oauth=mock_ms_oauth.return_value,
            calendar_link_function=RecallBotController().link_microsoft_calendar,
        )
        mock_update.assert_any_call(
            self.user_with_calendar,
            enabled=True,
            oauth=mock_ms_oauth.return_value,
            calendar_link_function=RecallBotController().link_microsoft_calendar,
        )

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(
            str(messages[0]), "Recall Microsoft auto-join calendar integration updated for user Calendar User"
        )

    @patch("deepinsights.users.admin.update_recall_auto_join_integration")
    @patch("deepinsights.users.admin.GoogleOAuth")
    def test_update_auto_join_calendar_events_google(
        self, mock_google_oauth: MagicMock, mock_update: MagicMock
    ) -> None:
        request = self.factory.post("/")
        self._add_session_and_messages(request)
        queryset = User.objects.filter(pk=self.user_with_calendar.pk)

        OAuthCredentials.objects.create(
            user=self.user_with_calendar,
            integration="google",
            access_token="test",
            refresh_token="test",
            expires_in=timezone.now(),
            refresh_token_expires_in=timezone.now(),
        )

        self.user_admin.delete_and_create_autojoin_calendar_integration(request, queryset)

        mock_update.assert_any_call(
            self.user_with_calendar,
            enabled=False,
            oauth=mock_google_oauth.return_value,
            calendar_link_function=RecallBotController().link_google_calendar,
        )
        mock_update.assert_any_call(
            self.user_with_calendar,
            enabled=True,
            oauth=mock_google_oauth.return_value,
            calendar_link_function=RecallBotController().link_google_calendar,
        )

        messages = list(get_messages(request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(
            str(messages[0]), "Recall Google auto-join calendar integration updated for user Calendar User"
        )


class TestUserAdmin:
    def test_changelist(self, admin_client: Client) -> None:
        url = reverse("admin:users_user_changelist")
        response = admin_client.get(url)
        assert response.status_code == 200

    def test_search(self, admin_client: Client) -> None:
        url = reverse("admin:users_user_changelist")
        response = admin_client.get(url, data={"q": "test"})
        assert response.status_code == 200

    def test_add(self, admin_client: Client) -> None:
        url = reverse("admin:users_user_add")
        response = admin_client.get(url)
        assert response.status_code == 200

        response = admin_client.post(
            url,
            data={
                "username": "test",
                "password1": "My_R@ndom-P@ssw0rd",
                "password2": "My_R@ndom-P@ssw0rd",
            },
        )
        assert not User.objects.filter(username="test").exists()

    def test_view_user(self, admin_client: Client) -> None:
        user = User.objects.get(username="admin")
        url = reverse("admin:users_user_change", kwargs={"object_id": user.pk})
        response = admin_client.get(url)
        assert response.status_code == 200
