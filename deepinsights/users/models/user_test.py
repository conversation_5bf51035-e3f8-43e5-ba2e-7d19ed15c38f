from typing import Any

import pytest
from pydantic import ValidationError

from api.routers.calendar_models import CalendarLookahead
from deepinsights.meetingsapp.models.phone_number import PhoneNumber
from deepinsights.users.models.user import User, UserMetadata


@pytest.mark.parametrize(
    "license_type, expected",
    [
        ("advisor", User.LicenseType.advisor),
        ("csa", User.LicenseType.csa),
        ("staff", User.LicenseType.staff),
        ("Advisor", User.LicenseType.advisor),
        ("adViSor", User.LicenseType.advisor),
        ("CsA", User.LicenseType.csa),
        ("staFF", User.LicenseType.staff),
        ("invalid", "invalid"),
        ("", ""),
    ],
)
def test_license_types(license_type: str, expected: User.LicenseType) -> None:
    user = User(license_type=license_type)
    assert user.license_type == expected
    assert expected == user.license_type


@pytest.mark.parametrize(
    "license_type, other",
    [
        (User.LicenseType.advisor, User.LicenseType.advisor),
        (User.LicenseType.advisor, "advisor"),
        (User.LicenseType.advisor, "Advisor"),
        (User.LicenseType.advisor, "adViSor"),
        (User.LicenseType.csa, User.LicenseType.csa),
        (User.LicenseType.csa, "csa"),
        (User.LicenseType.csa, "CsA"),
        (User.LicenseType.staff, User.LicenseType.staff),
        (User.LicenseType.staff, "staff"),
        (User.LicenseType.staff, "staFF"),
        ("advisor", User.LicenseType.advisor),
        ("Advisor", User.LicenseType.advisor),
        ("adViSor", User.LicenseType.advisor),
        ("csa", User.LicenseType.csa),
        ("CsA", User.LicenseType.csa),
        ("staFF", User.LicenseType.staff),
        ("staff", User.LicenseType.staff),
        ("invalid", "invalid"),
        ("", ""),
    ],
)
def test_license_type_equal(license_type: User.LicenseType | str, other: User.LicenseType | str):  # type: ignore[no-untyped-def]
    assert license_type == other


@pytest.mark.parametrize(
    "license_type, other",
    [
        (User.LicenseType.advisor, User.LicenseType.csa),
        (User.LicenseType.advisor, User.LicenseType.staff),
        (User.LicenseType.csa, User.LicenseType.advisor),
        (User.LicenseType.csa, User.LicenseType.staff),
        (User.LicenseType.advisor, "other"),
        (User.LicenseType.advisor, "csa"),
        (User.LicenseType.advisor, "staff"),
        (User.LicenseType.advisor, "unknown"),
        ("unknown", User.LicenseType.advisor),
    ],
)
def test_license_type_not_equal(license_type: User.LicenseType | str, other: User.LicenseType | str):  # type: ignore[no-untyped-def]
    assert license_type != other


def test_show_events_without_meeting_urls_default() -> None:
    user = User.objects.create()
    assert user.show_events_without_meeting_urls is True


@pytest.mark.django_db
@pytest.mark.parametrize(
    "initial_value, expected_value",
    [
        (True, True),
        (False, False),
    ],
)
def test_show_events_without_meeting_urls_initialization(initial_value: bool, expected_value: bool) -> None:
    user = User.objects.create()
    user.show_events_without_meeting_urls = initial_value
    user.save()
    user.refresh_from_db()
    assert user.show_events_without_meeting_urls is expected_value


@pytest.mark.django_db
@pytest.mark.parametrize(
    "metadata_dict, expected_bool",
    [
        ({}, True),
        ({"show_events_without_meeting_urls": True}, True),
        ({"show_events_without_meeting_urls": False}, False),
        ({"show_events_without_meeting_urls": None}, True),
    ],
)
def test_show_events_without_meeting_urls_direct_metadata_assignment(
    metadata_dict: dict[str, Any], expected_bool: bool
) -> None:
    user = User()
    user.metadata = metadata_dict
    user.save()
    user.refresh_from_db()
    assert user.show_events_without_meeting_urls is expected_bool


@pytest.mark.django_db
def test_show_events_without_meeting_urls_setter() -> None:
    user = User.objects.create()
    assert user.show_events_without_meeting_urls is True
    user.show_events_without_meeting_urls = False
    assert user.show_events_without_meeting_urls is False
    user.show_events_without_meeting_urls = True
    assert user.show_events_without_meeting_urls is True


@pytest.mark.django_db
@pytest.mark.parametrize(
    "primary_numbers, other_numbers, expected",
    [
        ([], [], None),
        (["+12125551212"], [], "+12125551212"),
        (["+12125551212"], ["+12125551213"], "+12125551212"),
        ([], ["+12125551213"], "+12125551213"),
        ([], ["+12125551213", "+12125551214"], None),
    ],
    ids=[
        "no_phone_numbers",
        "primary_phone_number_only",
        "primary_phone_number_with_other_numbers",
        "other_phone_number_only",
        "multiple_other_phone_numbers",
    ],
)
def test_primary_phone_number(primary_numbers: list[str], other_numbers: list[str], expected: str) -> None:
    user = User.objects.create()
    for number in primary_numbers:
        user.phone_numbers.add(PhoneNumber.objects.create(number=number, primary=True))
    for number in other_numbers:
        user.phone_numbers.add(PhoneNumber.objects.create(number=number, primary=False))
    user.save()
    assert user.primary_phone_number == expected


@pytest.mark.django_db
def test_recall_calendar_id_empty() -> None:
    user = User.objects.create()
    assert not user.recall_calendar_id


@pytest.mark.django_db
def test_recall_calendar_id_set() -> None:
    user = User.objects.create()
    user.recall_calendar_id = "test_calendar_id"
    user.save()
    user.refresh_from_db()

    assert user.recall_calendar_id == "test_calendar_id"

    expected_metadata = UserMetadata(recall_calendar_id="test_calendar_id").model_dump(exclude_none=True)
    assert user.metadata == expected_metadata


@pytest.mark.django_db
def test_recall_calendar_id_existing_metadata() -> None:
    user = User.objects.create(metadata={"recall_calendar_id": None, "other_key": "other_value"})
    user.recall_calendar_id = "test_calendar_id"
    user.save()
    user.refresh_from_db()

    assert user.recall_calendar_id == "test_calendar_id"
    assert user.metadata == {"recall_calendar_id": "test_calendar_id", "other_key": "other_value"}


@pytest.mark.django_db
def test_recall_calendar_id_set_to_none() -> None:
    user = User.objects.create(metadata={"recall_calendar_id": "test_calendar_id", "other_key": "other_value"})

    user.recall_calendar_id = None
    user.save()
    user.refresh_from_db()

    assert user.recall_calendar_id is None
    assert user.metadata == {"other_key": "other_value"}


@pytest.mark.django_db
def test_calendar_lookahead_property_empty() -> None:
    user = User.objects.create()
    assert not user.calendar_lookahead


@pytest.mark.django_db
def test_calendar_lookahead_property_set() -> None:
    user = User.objects.create()
    user.calendar_lookahead = CalendarLookahead.END_OF_DAY
    user.save()
    user.refresh_from_db()
    assert user.calendar_lookahead == CalendarLookahead.END_OF_DAY
    assert user.metadata == {"calendar_lookahead": CalendarLookahead.END_OF_DAY.value}


@pytest.mark.django_db
def test_calendar_lookahead_property_existing_metadata() -> None:
    user = User.objects.create(metadata={"other_key": "other_value"})
    user.calendar_lookahead = CalendarLookahead.END_OF_DAY
    user.save()
    user.refresh_from_db()

    assert user.calendar_lookahead == CalendarLookahead.END_OF_DAY
    assert user.metadata == {"calendar_lookahead": CalendarLookahead.END_OF_DAY.value, "other_key": "other_value"}


@pytest.mark.django_db
def test_calendar_lookahead_property_set_to_empty() -> None:
    user = User.objects.create(
        metadata={"calendar_lookahead": CalendarLookahead.END_OF_DAY.value, "other_key": "other_value"}
    )

    user.calendar_lookahead = None
    user.save()
    user.refresh_from_db()

    assert user.calendar_lookahead is None
    assert user.metadata == {"other_key": "other_value"}


@pytest.mark.django_db
def test_microsoft_id_property_empty() -> None:
    user = User.objects.create()
    assert not user.microsoft_id


@pytest.mark.django_db
def test_microsoft_id_property_set() -> None:
    user = User.objects.create()
    user.microsoft_id = "ms_id_123"
    user.save()
    user.refresh_from_db()

    assert user.microsoft_id == "ms_id_123"
    assert user.metadata == {"microsoft_id": "ms_id_123"}


@pytest.mark.django_db
def test_microsoft_id_property_existing_metadata() -> None:
    user = User.objects.create(metadata={"other_key": "other_value"})
    user.microsoft_id = "ms_id_456"
    user.save()
    user.refresh_from_db()

    assert user.microsoft_id == "ms_id_456"
    assert user.metadata == {"microsoft_id": "ms_id_456", "other_key": "other_value"}


@pytest.mark.django_db
def test_microsoft_id_property_set_to_none() -> None:
    user = User.objects.create(metadata={"microsoft_id": "ms_id_789", "other_key": "other_value"})

    user.microsoft_id = None
    user.save()
    user.refresh_from_db()

    assert user.microsoft_id is None
    assert user.metadata == {"other_key": "other_value"}


@pytest.mark.django_db
def test_google_id_property_empty() -> None:
    user = User.objects.create()
    assert not user.google_id


@pytest.mark.django_db
def test_google_id_property_set() -> None:
    user = User.objects.create()
    user.google_id = "google_id_123"
    user.save()
    user.refresh_from_db()
    assert user.google_id == "google_id_123"
    assert user.metadata == {"google_id": "google_id_123"}


@pytest.mark.django_db
def test_google_id_property_existing_metadata() -> None:
    user = User.objects.create(metadata={"other_key": "other_value"})
    user.google_id = "google_id_456"
    user.save()
    user.refresh_from_db()

    assert user.google_id == "google_id_456"
    assert user.metadata == {"google_id": "google_id_456", "other_key": "other_value"}


@pytest.mark.django_db
def test_google_id_property_set_to_none() -> None:
    user = User.objects.create(metadata={"google_id": "google_id_789", "other_key": "other_value"})
    user.google_id = None
    user.save()
    user.refresh_from_db()

    assert user.google_id is None
    assert user.metadata == {"other_key": "other_value"}


@pytest.mark.django_db
def test_recall_calendar_platform_empty() -> None:
    user = User.objects.create()
    assert user.recall_calendar_platform is None


@pytest.mark.django_db
def test_recall_calendar_platform_set() -> None:
    user = User.objects.create()
    user.recall_calendar_platform = "google"
    user.save()
    user.refresh_from_db()
    assert user.recall_calendar_platform == "google"
    assert user.metadata == {"recall_calendar_platform": "google"}


@pytest.mark.django_db
def test_recall_calendar_platform_existing_metadata() -> None:
    user = User.objects.create(metadata={"other_key": "other_value"})
    user.recall_calendar_platform = "microsoft"
    user.save()
    user.refresh_from_db()
    assert user.recall_calendar_platform == "microsoft"
    assert user.metadata == {"recall_calendar_platform": "microsoft", "other_key": "other_value"}


@pytest.mark.django_db
def test_recall_calendar_platform_set_to_none() -> None:
    user = User.objects.create(metadata={"other_key": "other_value", "recall_calendar_platform": "apple"})
    user.recall_calendar_platform = None
    user.save()
    user.refresh_from_db()
    assert user.recall_calendar_platform is None
    assert user.metadata == {"other_key": "other_value"}


class TestUserMetadata:
    def test_user_metadata_instantiation_full_valid_data(self) -> None:
        data = {
            "recall_calendar_id": "cal123",
            "calendar_lookahead": "eod",
            "show_events_without_meeting_urls": False,
            "microsoft_id": "ms123",
            "google_id": "goog123",
            "biasing_words": ["word1", "word2"],
            "user_context": "Test context",
        }
        metadata = UserMetadata(**data)
        assert metadata.recall_calendar_id == "cal123"
        assert metadata.calendar_lookahead == "eod"
        assert metadata.show_events_without_meeting_urls is False
        assert metadata.microsoft_id == "ms123"
        assert metadata.google_id == "goog123"
        assert metadata.biasing_words == ["word1", "word2"]
        assert metadata.user_context == "Test context"

    def test_user_metadata_defaults(self) -> None:
        metadata = UserMetadata()
        assert metadata.recall_calendar_id is None
        assert metadata.calendar_lookahead is None
        assert metadata.show_events_without_meeting_urls is None
        assert metadata.microsoft_id is None
        assert metadata.google_id is None
        assert metadata.biasing_words is None
        assert metadata.user_context is None

    def test_user_metadata_partial_data_uses_defaults(self) -> None:
        data = {"recall_calendar_id": "cal-partial"}
        metadata = UserMetadata(**data)
        assert metadata.recall_calendar_id == "cal-partial"
        assert metadata.calendar_lookahead is None
        assert metadata.show_events_without_meeting_urls is None
        assert metadata.microsoft_id is None
        assert metadata.google_id is None
        assert metadata.biasing_words is None
        assert metadata.user_context is None

        data_show_events = {"show_events_without_meeting_urls": False}
        metadata_show_events = UserMetadata(**data_show_events)
        assert metadata_show_events.show_events_without_meeting_urls is False

    def test_user_metadata_validation_error_incorrect_types(self) -> None:
        with pytest.raises(ValidationError):
            UserMetadata(recall_calendar_id=123)

        with pytest.raises(ValidationError):
            UserMetadata(show_events_without_meeting_urls="not_a_bool")

        with pytest.raises(ValidationError):
            UserMetadata(biasing_words="not_a_list")

        with pytest.raises(ValidationError):
            UserMetadata(biasing_words=[1, 2])

        with pytest.raises(ValidationError):
            UserMetadata(user_context=["list", "context"])

    def test_user_metadata_model_dump_exclude_none_full(self) -> None:
        metadata_full = UserMetadata(
            recall_calendar_id="cal123",
            calendar_lookahead="eod",
            show_events_without_meeting_urls=True,
            microsoft_id="ms123",
            google_id="goog123",
            biasing_words=["word1"],
            user_context="context",
        )
        dump_full = metadata_full.model_dump(exclude_none=True)
        assert "recall_calendar_id" in dump_full
        assert "calendar_lookahead" in dump_full
        assert "show_events_without_meeting_urls" in dump_full
        assert "microsoft_id" in dump_full
        assert "google_id" in dump_full
        assert "biasing_words" in dump_full
        assert "user_context" in dump_full

    def test_user_metadata_model_dump_exclude_none_partial(self) -> None:
        metadata_partial = UserMetadata(
            recall_calendar_id="cal123",
            show_events_without_meeting_urls=False,
        )
        dump_partial = metadata_partial.model_dump(exclude_none=True)
        assert "recall_calendar_id" in dump_partial
        assert "calendar_lookahead" not in dump_partial
        assert "show_events_without_meeting_urls" in dump_partial
        assert "microsoft_id" not in dump_partial
        assert "google_id" not in dump_partial
        assert "biasing_words" not in dump_partial
        assert "user_context" not in dump_partial

    def test_user_metadata_model_dump_exclude_none_empty(self) -> None:
        metadata_all_defaults = UserMetadata()
        dump_all_defaults = metadata_all_defaults.model_dump(exclude_none=True)
        assert not dump_all_defaults

    def test_user_metadata_biasing_words_can_be_empty_list(self) -> None:
        metadata = UserMetadata(biasing_words=[])
        assert metadata.biasing_words == []
        dump = metadata.model_dump(exclude_none=True)
        assert "biasing_words" in dump
        assert dump["biasing_words"] == []

    def test_user_metadata_explicit_none_values(self) -> None:
        data = {
            "recall_calendar_id": None,
            "calendar_lookahead": None,
            "show_events_without_meeting_urls": None,
            "microsoft_id": None,
            "google_id": None,
            "biasing_words": None,
            "user_context": None,
        }
        metadata = UserMetadata(**data)
        assert metadata.recall_calendar_id is None
        assert metadata.calendar_lookahead is None
        assert metadata.show_events_without_meeting_urls is None
        assert metadata.microsoft_id is None
        assert metadata.google_id is None
        assert metadata.biasing_words is None
        assert metadata.user_context is None

        dump = metadata.model_dump(exclude_none=True)
        assert not dump
