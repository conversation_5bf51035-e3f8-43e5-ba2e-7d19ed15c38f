from typing import Callable, Generator
from unittest.mock import MagicMock

import pytest
from django.contrib.auth.models import AnonymousUser
from django.core import cache
from django.http import HttpRequest
from django.test import TestCase
from waffle.models import Flag as WaffleFlag

from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.flags import Flag
from deepinsights.users.models.user import User


class FlagTestCase(TestCase):
    def setUp(self) -> None:
        self.flag: Flag = Flag.objects.create(name="test_flag")  # type: ignore[assignment]

    def test_get_flush_keys(self) -> None:
        flush_keys = self.flag.get_flush_keys()
        # Do not create a model object in the database.
        waffle_flag_flush_keys = WaffleFlag(name=self.flag.name).get_flush_keys()
        # Assert that our flag model has a key that the base flag model does not. This isn't an
        # ideal test, but the keys themselves are opaque, so it's difficult to assert against them
        # themselves.
        self.assertTrue(set(flush_keys) > set(waffle_flag_flush_keys))

    def test_not_active_when_not_active_for_environment(self) -> None:
        self.flag.everyone = True
        self.flag.override_enabled_by_environment = False
        self.assertFalse(self.flag.is_active(HttpRequest()))

    def test_active_when_active_for_environment(self) -> None:
        self.flag.everyone = False
        self.flag.override_enabled_by_environment = True
        self.assertTrue(self.flag.is_active(HttpRequest()))

    def test_active_for_environment_not_set(self) -> None:
        self.flag.everyone = False
        self.flag.override_enabled_by_environment = None
        self.assertFalse(self.flag.is_active(HttpRequest()))
        self.flag.everyone = True
        self.assertTrue(self.flag.is_active(HttpRequest()))


@pytest.mark.parametrize(
    "check_flag",
    [
        lambda flag, user: flag.is_active_for_user(user),
        lambda flag, user: flag.is_active(MagicMock(user=user)),
    ],
    ids=["is_active_for_user", "is_active"],
)
class TestUserEnablementForFlag:
    @pytest.fixture(autouse=True)
    def setUpTearDown(self) -> Generator[None, None, None]:
        # Because this test reuses flag names, we need to clear the cache between each run.
        # Otherwise, the cached flag state will persist between tests.
        cache.cache.clear()
        self.flag: Flag = Flag.objects.create(name="test_flag")  # type: ignore[assignment]
        self.organization = Organization.objects.create(name="test_organization")
        self.user = User.objects.create(username="test_user", organization=self.organization)
        yield

    def test_not_is_active_for_user(self, check_flag: Callable[[Flag, User], bool]) -> None:
        assert not check_flag(self.flag, self.user)

    def test_is_active_for_user(self, check_flag: Callable[[Flag, User], bool]) -> None:
        self.flag.users.add(self.user)
        assert check_flag(self.flag, self.user)

    def test_is_active_for_user_with_anonymous_user(self, check_flag: Callable[[Flag, User], bool]) -> None:
        # Since our project isn't mypy safe, it still makes sense to test for this case.
        assert not check_flag(self.flag, AnonymousUser())  # type: ignore[arg-type]

    def test_is_active_for_user_via_organization(self, check_flag: Callable[[Flag, User], bool]) -> None:
        self.flag.organizations.add(self.organization)
        assert check_flag(self.flag, self.user)

    def test_not_is_active_for_user_via_organization(self, check_flag: Callable[[Flag, User], bool]) -> None:
        self.flag.organizations.add(self.organization)
        user2: User = User.objects.create(username="test_user_2")
        assert not check_flag(self.flag, user2)
        user2.organization = self.organization
        user2.save()
        assert check_flag(self.flag, user2)

    def test_active_for_crm_null_systems(self, check_flag: Callable[[Flag, User], bool]) -> None:
        self.flag.crm_systems = None

        self.user.crm_configuration["crm_system"] = "redtail"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "wealthbox"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "sharefile"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "sequoia_sf"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "salesforce"
        assert not check_flag(self.flag, self.user)

    def test_active_for_crm_empty_systems(self, check_flag: Callable[[Flag, User], bool]) -> None:
        self.flag.crm_systems = []

        self.user.crm_configuration["crm_system"] = "redtail"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "wealthbox"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "sharefile"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "sequoia_sf"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "salesforce"
        assert not check_flag(self.flag, self.user)

    def test_active_for_crm(self, check_flag: Callable[[Flag, User], bool]) -> None:
        self.flag.crm_systems = [Flag.CRMSystem.REDTAIL]

        self.user.crm_configuration["crm_system"] = "redtail"
        assert check_flag(self.flag, self.user)

        self.user.crm_configuration["crm_system"] = "wealthbox"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "sharefile"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "sequoia_sf"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "salesforce"
        assert not check_flag(self.flag, self.user)

    def test_active_for_all_crms(self, check_flag: Callable[[Flag, User], bool]) -> None:
        self.flag.crm_systems = Flag.CRMSystem.values

        self.user.crm_configuration["crm_system"] = "redtail"
        assert check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "wealthbox"
        assert check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "sharefile"
        assert check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "sequoia_sf"
        assert check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "salesforce"
        assert check_flag(self.flag, self.user)

        self.user.crm_configuration["crm_system"] = "unknown"
        assert not check_flag(self.flag, self.user)

    def test_active_for_salesforce_base(self, check_flag: Callable[[Flag, User], bool]) -> None:
        self.flag.crm_systems = [Flag.CRMSystem.SALESFORCE_BASE.value]
        self.user.crm_configuration["crm_system"] = "salesforce"
        self.user.crm_configuration["salesforce"] = {"type": "base"}

        assert check_flag(self.flag, self.user)

        self.user.crm_configuration["salesforce"] = {"type": "something_else"}

        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "redtail"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "wealthbox"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "sharefile"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "sequoia_sf"
        assert not check_flag(self.flag, self.user)

    def test_active_for_salesforce_financial_cloud(self, check_flag: Callable[[Flag, User], bool]) -> None:
        self.flag.crm_systems = [Flag.CRMSystem.SALESFORCE_FINANCIAL_CLOUD.value]
        self.user.crm_configuration["crm_system"] = "salesforce"
        self.user.crm_configuration["salesforce"] = {"type": ""}

        assert check_flag(self.flag, self.user)

        self.user.crm_configuration["salesforce"] = {"type": "financial_cloud"}

        assert check_flag(self.flag, self.user)

        self.user.crm_configuration["crm_system"] = "redtail"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "wealthbox"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "sharefile"
        assert not check_flag(self.flag, self.user)
        self.user.crm_configuration["crm_system"] = "sequoia_sf"
        assert not check_flag(self.flag, self.user)

    def test_active_for_crm_by_org(self, check_flag: Callable[[Flag, User], bool]) -> None:
        self.flag.crm_systems = [Flag.CRMSystem.REDTAIL]

        self.organization.crm_configuration["crm_system"] = "redtail"
        assert check_flag(self.flag, self.user)

        self.organization.crm_configuration["crm_system"] = "wealthbox"
        assert not check_flag(self.flag, self.user)
        self.organization.crm_configuration["crm_system"] = "sharefile"
        assert not check_flag(self.flag, self.user)
        self.organization.crm_configuration["crm_system"] = "sequoia_sf"
        assert not check_flag(self.flag, self.user)
        self.organization.crm_configuration["crm_system"] = "salesforce"
        assert not check_flag(self.flag, self.user)

    def test_flag_disabled_organizations_only(self, check_flag: Callable[[Flag, User], bool]) -> None:
        self.flag.disabled_organizations.add(self.organization)
        assert not check_flag(self.flag, self.user)

    def test_flag_organizations_and_disabled_organizations(self, check_flag: Callable[[Flag, User], bool]) -> None:
        self.flag.organizations.add(self.organization)
        self.flag.disabled_organizations.add(self.organization)

        assert not check_flag(self.flag, self.user)

    def test_flag_users_and_disabled_organizations(self, check_flag: Callable[[Flag, User], bool]) -> None:
        self.flag.users.add(self.user)
        self.flag.disabled_organizations.add(self.organization)
        assert not check_flag(self.flag, self.user)

    def test_flag_everyone_and_disabled_organizations(self, check_flag: Callable[[Flag, User], bool]) -> None:
        self.flag.everyone = True
        self.flag.disabled_organizations.add(self.organization)

        assert not check_flag(self.flag, self.user)

    def test_flag_environment_enabled_and_disabled_organizations(
        self, check_flag: Callable[[Flag, User], bool]
    ) -> None:
        self.flag.override_enabled_by_environment = True
        self.flag.disabled_organizations.add(self.organization)

        assert check_flag(self.flag, self.user)

    def test_flag_environment_enabled_and_everyone_disabled(self, check_flag: Callable[[Flag, User], bool]) -> None:
        self.flag.override_enabled_by_environment = True
        self.flag.everyone = False

        assert check_flag(self.flag, self.user)

    def test_flag_environment_disabled_and_everyone_enabled(self, check_flag: Callable[[Flag, User], bool]) -> None:
        self.flag.override_enabled_by_environment = False
        self.flag.everyone = True

        assert not check_flag(self.flag, self.user)
