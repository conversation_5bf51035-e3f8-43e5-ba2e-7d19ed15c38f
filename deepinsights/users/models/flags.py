from django.contrib.auth.base_user import AbstractBaseUser
from django.db import models
from django.http import HttpRequest
from django.utils.translation import gettext_lazy as _
from waffle.models import CACHE_EMPTY, AbstractBaseSample, AbstractBaseSwitch, AbstractUserFlag
from waffle.utils import get_cache, keyfmt

from deepinsights.core.preferences.preferences import CrmConfiguration
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.utils.choice_array_field import ChoiceArrayField


# Base feature flag for the Zeplyn app.
#
# Overrides the default flag class from the django-waffle package to implement
# support for per-org feature flags. The code here is mainly taken from the
# django-waffle documentation:
# https://waffle.readthedocs.io/en/stable/types/flag.html
class Flag(AbstractUserFlag):
    _ORGANIZATIONS_CACHE_KEY = "flag:%s:organizations"
    _DISABLED_ORGANIZATIONS_CACHE_KEY = "flag:%s:disabled_organizations"
    _CRM_SYSTEMS_CACHE_KEY = "flag:%s:crm_systems"

    # Zeplyn organizations for which this flag should be active.
    organizations = models.ManyToManyField(
        Organization,
        blank=True,
        help_text="Organizations for which this flag should be active.",
    )

    # Zeplyn organizations for which this flag should **not** be active.
    disabled_organizations = models.ManyToManyField(
        Organization,
        blank=True,
        help_text=(
            "Organizations for which this flag should be inactive, regardless of the values of (most) other settings. "
            "If an organization is in this list, it overrides all other settings for users in that organization, except "
            "the environment-based override. Note specifically that enabling a flag for users in a disabled organization "
            "will not cause the flag to be enabled for those users."
        ),
        related_name="disabledflag_set",
    )

    # The environment-based override state of this flag.
    #
    # Set when the app loads. This will be reset each time the app is loaded based on the values
    # defined in deepinsights/flags/flagdefs.py.
    #
    # True: enabled for the environment regardless of other settings
    # False: disabled for the environment regardless of other settings
    # None: other flag settings determine the state
    override_enabled_by_environment = models.BooleanField(
        blank=True,
        null=True,
        help_text=_(
            "If on, the flag is forced on in this environment, overriding all other settings. "
            "If off, the flag is forced off in this environment, overriding all other settings. "
            "Otherwise, the other diversion criteria control the value of this flag. This is controlled by "
            "the values in deepinsights/flags/flagdefs.py. Do not adjust this flag by hand or in the shell.",
        ),
    )

    class CRMSystem(models.TextChoices):
        SALESFORCE_BASE = "salesforce_base", "Salesforce (base)"
        SALESFORCE_FINANCIAL_CLOUD = "salesforce_financial_cloud", "Salesforce (Financial Cloud)"
        SEQUOIA_SALESFORCE = "sequoia_sf", "Salesforce (Sequoia)"
        WEALTHBOX = "wealthbox", "Wealthbox"
        REDTAIL = "redtail", "Redtail"
        SHAREFILE = "sharefile", "ShareFile"
        SHAREPOINT = "sharepoint", "SharePoint"

    # CRM systems for which this flag should be active.
    #
    # This will cause the flag to be active for all users in organizations that have a CRM matching
    # any of the CRM systems in this array.
    crm_systems = ChoiceArrayField(models.CharField(choices=CRMSystem.choices), default=list, blank=True, null=True)

    def get_flush_keys(self, flush_keys: list[str] | None = None) -> list[str]:
        flush_keys = super(Flag, self).get_flush_keys(flush_keys)
        flush_keys.append(keyfmt(self._ORGANIZATIONS_CACHE_KEY, self.name))
        flush_keys.append(keyfmt(self._DISABLED_ORGANIZATIONS_CACHE_KEY, self.name))
        flush_keys.append(keyfmt(self._CRM_SYSTEMS_CACHE_KEY, self.name))
        return flush_keys

    def is_active(self, request: HttpRequest, read_only: bool = False) -> bool | None:
        # Before anything else, check the environment-level activation.
        if self.override_enabled_by_environment is not None:
            return self.override_enabled_by_environment

        # Then check the org denylist, so that the denylist takes precedence over any allowlists
        # (except environment-level forcing).
        #
        # See the parallel implementation below for details about the cascading check here.
        if (
            hasattr(request, "user")
            and (user := request.user)
            and hasattr(user, "organization")
            and user.organization
            and user.organization.pk in self._get_denylist_organization_ids()
        ):
            return False
        return super(Flag, self).is_active(request, read_only)

    def is_active_for_user(self, user: AbstractBaseUser) -> bool | None:
        # The code in this method is redundant with the code in `is_active`. This is intentional,
        # and is an attempt to defensively program against potential code paths that may call one
        # or the other of these methods. The flag library code is not code that we own, and we have
        # some slightly-complex enablement and disablement logic, so it's better to be safe than
        # sorry.

        # Before anything else, check the environment-level activation.
        if self.override_enabled_by_environment is not None:
            return self.override_enabled_by_environment

        # Then check the org denylist, so that the denylist takes precedence over any allowlists
        # (except environment-level forcing).
        #
        # This is also checked in `is_active`, but we check it here for extra certainty, since we are
        # interacting with the Waffle flag library and cannot necessarily predict every code path that
        # would call into this implementation.
        #
        # See below for details about the cascading check here.
        if (
            hasattr(user, "organization")
            and user.organization
            and user.organization.pk in self._get_denylist_organization_ids()
        ):
            return False

        # Check if the flag is active by criteria associated with the base flag class (e.g., users,
        # groups, everyone).
        if is_active := super(Flag, self).is_active_for_user(user):
            return is_active

        # Check if the flag is active for the user's organization.
        #
        # A anonymous user (or any other user that is not derived from our User model) will not have
        # an organization. We could check for that specifically, but checking that the user has an
        # organization attributed ensures that the next line of code will not crash.
        if hasattr(user, "organization") and user.organization and user.organization.pk in self._get_organization_ids():
            return True

        # If the user's CRM system matches any of the enabled CRM systems, then the flag is active.
        if (
            hasattr(user, "get_crm_configuration")
            and (crm_configuration := user.get_crm_configuration())
            and any(
                [
                    self._is_active_for_crm_system(crm_configuration, crm_system)
                    for crm_system in (self.crm_systems or [])
                ]
            )
        ):
            return True

        return False

    def _is_active_for_crm_system(self, user_crm_configuration: CrmConfiguration, crm_system: CRMSystem):  # type: ignore[no-untyped-def]
        user_crm = user_crm_configuration.crm_system
        salesforce_type = user_crm_configuration.salesforce.type
        if crm_system == self.CRMSystem.SALESFORCE_BASE:
            return user_crm == "salesforce" and salesforce_type == "base"
        if crm_system == self.CRMSystem.SALESFORCE_FINANCIAL_CLOUD:
            return user_crm == "salesforce" and salesforce_type != "base"
        return crm_system == user_crm

    def _get_organization_ids(self) -> set[int]:
        cache = get_cache()
        cache_key = keyfmt(self._ORGANIZATIONS_CACHE_KEY, self.name)
        cached = cache.get(cache_key)
        if cached == CACHE_EMPTY:
            return set()
        if cached:
            return cached  # type: ignore[no-any-return]

        organization_ids = set(self.organizations.all().values_list("pk", flat=True))
        if not organization_ids:
            cache.add(cache_key, CACHE_EMPTY)
            return set()

        cache.add(cache_key, organization_ids)
        return organization_ids

    def _get_denylist_organization_ids(self) -> set[int]:
        cache = get_cache()
        cache_key = keyfmt(self._DISABLED_ORGANIZATIONS_CACHE_KEY, self.name)
        cached = cache.get(cache_key)
        if cached == CACHE_EMPTY:
            return set()
        if cached:
            return cached  # type: ignore[no-any-return]

        organization_ids = set(self.disabled_organizations.all().values_list("pk", flat=True))
        if not organization_ids:
            cache.add(cache_key, CACHE_EMPTY)
            return set()

        cache.add(cache_key, organization_ids)
        return organization_ids


# Base sample for the Zeplyn app.
#
# This is an empty wrapper around the base django-waffle Sample, included in
# case we identify Zeplyn-specific requirements in the future. It's not possible
# to swap out this model after the fact.
class Sample(AbstractBaseSample):
    pass


# Base switch for the Zeplyn app.
#
# This is an empty wrapper around the base django-waffle Switch, included in
# case we identify Zeplyn-specific requirements in the future. It's not
# possible to swap out this model after the fact.
class Switch(AbstractBaseSwitch):
    pass
