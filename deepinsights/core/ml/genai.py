import logging

import botocore
import openai
from anthropic import AnthropicBedrock
from django.conf import settings

async_openai_client = openai.AsyncOpenAI(
    organization=settings.OPENAI_ORG_ID,
    api_key=settings.OPENAI_API_KEY,
)


openai_client = openai.OpenAI(
    organization=settings.OPENAI_ORG_ID,
    api_key=settings.OPENAI_API_KEY,
)


def call_gpt4o(prompt: str, temp: float = 0.1, max_tokens: int = 16384) -> str:
    """
    Call the GPT-4o model to generate a response to the given prompt.

    Args:
        prompt (str): The input prompt for the GPT-4o model.
        temp (float, optional): The temperature setting for the model.
        max_tokens (int, optional): The maximum number of tokens for the response.

    Returns:
        str: The generated response text from the GPT-4o model, or an error message if no response is received.
    """
    logging.info("calling gpt4o")

    response = openai_client.chat.completions.create(
        model="gpt-4o",
        messages=[{"role": "user", "content": prompt}],
        temperature=temp,
        max_tokens=max_tokens,
    )
    if response.choices and response.choices[0].message.content:
        return response.choices[0].message.content
    else:
        raise ValueError("Invalid response object received from GPT-4o")


async def call_gpt4o_async(prompt: str, temp: float = 0.1, max_tokens: int = 16384) -> str:
    """
    Call the GPT-4o model to generate a response to the given prompt.

    Args:
        prompt (str): The input prompt for the GPT-4o model.
        temp (float, optional): The temperature setting for the model.
        max_tokens (int, optional): The maximum number of tokens for the response.

    Returns:
        str: The generated response text from the GPT-4o model, or an error message if no response is received.
    """
    logging.info("calling gpt async")
    response = await async_openai_client.chat.completions.create(
        model="gpt-4o",
        messages=[{"role": "user", "content": prompt}],
        temperature=temp,
        max_tokens=max_tokens,
    )
    if response.choices and response.choices[0].message.content:
        return response.choices[0].message.content
    else:
        raise ValueError("Invalid response object received from GPT-4o")


def call_claude_sonnet(prompt: str, system_prompt: str, max_tokens: int = 8192) -> str:
    """
    Call the Claude Sonnet model to generate a response to the given prompt.

    Args:
        prompt (str): The input prompt for the Claude Sonnet model.
        system_prompt (str): The system prompt to guide the model's behavior.
        max_tokens (int): The maximum number of tokens for the response.

    Returns:
        str: The generated response text from the Claude Sonnet model, or an error message if no response is received.
    """
    logging.info("calling claude sonnet")

    client = AnthropicBedrock(
        aws_access_key=settings.AWS_ACCESS_KEY_ID,
        aws_secret_key=settings.AWS_SECRET_ACCESS_KEY,
        aws_region=settings.AWS_S3_REGION_NAME,
    )

    try:
        message = client.messages.create(
            model="anthropic.claude-3-5-sonnet-20240620-v1:0",
            max_tokens=max_tokens,
            messages=[{"role": "user", "content": prompt}],
            system=system_prompt,
        )

        if message and message.content:
            answer = ""
            for text_block in message.content:
                if hasattr(text_block, "text"):
                    answer += text_block.text
            return answer
        else:
            raise ValueError("Invalid response object received from Claude Sonnet")

    except botocore.exceptions.ClientError as error:
        if error.response["Error"]["Code"] == "AccessDeniedException":
            logging.error(
                "\x1b[41m%s\nTo troubleshoot this issue please refer to the following resources.\n"
                "https://docs.aws.amazon.com/IAM/latest/UserGuide/troubleshoot_access-denied.html\n"
                "https://docs.aws.amazon.com/bedrock/latest/userguide/security-iam.html\x1b[0m\n",
                error.response["Error"]["Message"],
            )
            raise ValueError("Email not available right now. Please try again later.")
        else:
            raise error
