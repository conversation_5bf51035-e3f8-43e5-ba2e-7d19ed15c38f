from unittest.mock import MagicMock, patch

from django.test import TestCase

from deepinsights.core.ml import voice_memo_utils as vmu
from deepinsights.core.ml.prompt_builder import DatabaseBasedPromptBuilder, DataSource, PromptContext
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.prompt import Prompt
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingDataTemplate
from deepinsights.meetingsapp.models.structured_meeting_data_schema import StructuredMeetingDataSchema
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User


class TestDatabasePromptBuilder(TestCase):
    def setUp(self) -> None:
        # Create user - not included in conftest fixtures
        self.user = User.objects.create(
            username="testuser", first_name="Test", last_name="User", metadata={"user_context": "Test context"}
        )

        # Use meeting type fixtures from conftest.py - use filter().first() to avoid MultipleObjectsReturned
        self.client_meeting_type = MeetingType.objects.filter(category=MeetingType.Category.CLIENT).first()
        self.internal_meeting_type = MeetingType.objects.filter(category=MeetingType.Category.INTERNAL).first()

        # Create note
        self.note = Note.objects.create(
            note_owner=self.user, meeting_type=self.client_meeting_type, metadata={"meeting_duration": 3600}
        )

        # Create attendees
        self.attendee1 = Attendee.objects.create(attendee_name="Jon Doe", note=self.note)
        self.attendee2 = Attendee.objects.create(attendee_name="Jane Doe", note=self.note)

        # Using version "test" for all test prompts to distinguish from production prompts
        Prompt.objects.get_or_create(
            name="Client Meeting Summary",
            unique_name="client_meeting_summary",
            version="test",
            defaults={"user_prompt": "Context: {context}\nTranscript: {transcript}"},
        )
        Prompt.objects.get_or_create(
            name="Client Meeting Tasks",
            unique_name="client_meeting_tasks_and_takeaways",
            version="test",
            defaults={"user_prompt": "REGULAR tasks and takeaways Context: {context}\nTranscript: {transcript}"},
        )
        Prompt.objects.get_or_create(
            name="Client Meeting Detailed Tasks",
            unique_name="client_meeting_tasks_and_takeaways_detailed",
            version="test",
            defaults={"user_prompt": "DETAILED tasks and takeaways Context: {context}\nTranscript: {transcript}"},
        )

        # For internal meeting type
        Prompt.objects.get_or_create(
            name="Internal Meeting Summary",
            unique_name="internal_meeting_summary",
            version="test",
            defaults={"user_prompt": "Context: {context}\nTranscript: {transcript}"},
        )
        Prompt.objects.get_or_create(
            name="Internal Meeting Tasks",
            unique_name="internal_meeting_tasks_and_takeaways",
            version="test",
            defaults={"user_prompt": "Context: {context}\nTranscript: {transcript}"},
        )

    def test_prompt_context(self) -> None:
        context = PromptContext(self.note)

        # Test the full context string
        context_str = context._get_meeting_context()
        self.assertIn("Test User", context_str)  # User name
        self.assertIn("Jon Doe", context_str)  # First attendee
        self.assertIn("Jane Doe", context_str)  # Second attendee
        self.assertIn("Test context", context_str)  # User context
        self.assertIn("3600 seconds", context_str)  # Meeting duration
        self.assertIn("client", context_str.lower())  # Meeting category

    def test_build_context_all_sources(self) -> None:
        """Test building context with all available data sources."""
        # Add test data to the note
        self.note.summary = {"sections": [{"topic": "Meeting Overview", "bullets": ["Test meeting summary"]}]}
        self.note.key_takeaways = ["Takeaway 1", "Takeaway 2"]
        self.note.advisor_notes = ["Advisor Note 1", "Advisor Note 2"]
        self.note.save()

        # Create tasks
        Task.objects.create(note=self.note, task_title="Task 1", completed=False)
        Task.objects.create(note=self.note, task_title="Task 2", completed=False)

        context = PromptContext(self.note)
        result = context.build_context(
            [
                DataSource.MEETING_CONTEXT,
                DataSource.SUMMARY,
                DataSource.TASKS,
                DataSource.TAKEAWAYS,
                DataSource.ADVISOR_NOTES,
            ]
        )

        # Test meeting context
        self.assertIn("[meeting_context]", result)
        self.assertIn("Test User", result)
        self.assertIn("Jon Doe", result)
        self.assertIn("Jane Doe", result)
        self.assertIn("Test context", result)
        self.assertIn("3600 seconds", result)

        # Test summary
        self.assertIn("[summary]", result)
        self.assertIn("Meeting Overview", result)
        self.assertIn("Test meeting summary", result)

        # Test tasks
        self.assertIn("[tasks]", result)
        self.assertIn("Task 1", result)
        self.assertIn("Task 2", result)

        # Test takeaways
        self.assertIn("[takeaways]", result)
        self.assertIn("Takeaway 1", result)
        self.assertIn("Takeaway 2", result)

        # Test advisor notes
        self.assertIn("[advisor_notes]", result)
        self.assertIn("Advisor Note 1", result)
        self.assertIn("Advisor Note 2", result)

    def test_build_context_empty_sources(self) -> None:
        """Test building context with empty data sources."""
        context = PromptContext(self.note)
        result = context.build_context(
            [
                DataSource.SUMMARY,
                DataSource.TASKS,
                DataSource.TAKEAWAYS,
                DataSource.ADVISOR_NOTES,
            ]
        )

        # Test that empty sources don't add extra newlines or brackets
        self.assertNotIn("[summary]\n\n", result)
        self.assertNotIn("[tasks]\n\n", result)
        self.assertNotIn("[takeaways]\n\n", result)
        self.assertNotIn("[advisor_notes]\n\n", result)

    def test_build_prompt_for_client_meeting_summary(self) -> None:
        """Test successfully building a prompt for a client meeting summary."""
        transcript = "Test client meeting transcript"
        result = DatabaseBasedPromptBuilder.build_prompt_for_meeting(
            self.note,
            DatabaseBasedPromptBuilder.MeetingPromptType.SUMMARY,
            transcript,
            version="test",
        )

        # Test that key context elements are present
        self.assertIn("Test User", result)  # User name
        self.assertIn("Jon Doe", result)  # Attendee name
        self.assertIn("Jane Doe", result)  # Attendee name
        self.assertIn("Test context", result)  # User context
        self.assertIn(transcript, result)  # Transcript

    def test_build_prompt_for_internal_meeting_summary(self) -> None:
        """Test building a prompt for an internal meeting summary."""
        self.note.meeting_type = self.internal_meeting_type
        self.note.save()

        transcript = "Test internal meeting transcript"

        result = DatabaseBasedPromptBuilder.build_prompt_for_meeting(
            self.note,
            DatabaseBasedPromptBuilder.MeetingPromptType.SUMMARY,
            transcript,
            version="test",
        )

        # Test that key context elements are present
        self.assertIn("Test User", result)
        self.assertIn("Jon Doe", result)
        self.assertIn("Jane Doe", result)
        self.assertIn("Test context", result)
        self.assertIn(transcript, result)

    def test_build_prompt_for_client_meeting_tasks(self) -> None:
        """Test building a prompt for client meeting tasks."""
        transcript = "Test client meeting transcript"

        result = DatabaseBasedPromptBuilder.build_prompt_for_meeting(
            self.note,
            DatabaseBasedPromptBuilder.MeetingPromptType.TASKS_AND_TAKEAWAYS,
            transcript,
            version="test",
        )

        # Test that key context elements are present
        self.assertIn("Test User", result)
        self.assertIn("Jon Doe", result)
        self.assertIn("Jane Doe", result)
        self.assertIn("Test context", result)
        self.assertIn(transcript, result)

    def test_build_prompt_for_internal_meeting_tasks(self) -> None:
        """Test building a prompt for internal meeting tasks."""
        self.note.meeting_type = self.internal_meeting_type
        self.note.save()
        transcript = "Test internal meeting transcript"

        result = DatabaseBasedPromptBuilder.build_prompt_for_meeting(
            self.note,
            DatabaseBasedPromptBuilder.MeetingPromptType.TASKS_AND_TAKEAWAYS,
            transcript,
            version="test",
        )

        # Test that key context elements are present
        self.assertIn("Test User", result)
        self.assertIn("Jon Doe", result)
        self.assertIn("Jane Doe", result)
        self.assertIn("Test context", result)
        self.assertIn(transcript, result)

    @patch("deepinsights.flags.flagdefs.Flags.EnableDetailedActionItems.is_active_for_user")
    def test_build_prompt_for_detailed_tasks_when_flag_active(self, mock_is_active_for_user: MagicMock) -> None:
        """Test that the detailed tasks prompt is used when the flag is active."""
        # Setup the flag to be active
        mock_is_active_for_user.return_value = True

        transcript = "Test client meeting transcript"

        # Call the method directly
        result = DatabaseBasedPromptBuilder.build_prompt_for_meeting(
            self.note,
            DatabaseBasedPromptBuilder.MeetingPromptType.TASKS_AND_TAKEAWAYS_DETAILED,
            transcript,
            version="test",
        )

        # Verify the detailed prompt was used
        self.assertIn("DETAILED Context:", result)
        self.assertIn("Test User", result)
        self.assertIn("Jon Doe", result)
        self.assertIn("Jane Doe", result)
        self.assertIn("Test context", result)
        self.assertIn(transcript, result)

    def test_build_prompt_for_meeting_prompt_not_found(self) -> None:
        """Test error handling when prompt doesn't exist in database."""
        # Save current meeting type category to trigger nonexistent prompt lookup
        if not self.note.meeting_type:
            self.fail("Note does not have a meeting type")
        original_category = self.note.meeting_type.category
        self.note.meeting_type.category = "nonexistent"
        self.note.save()

        transcript = "Test meeting transcript"
        with self.assertRaises(ValueError) as context:
            DatabaseBasedPromptBuilder.build_prompt_for_meeting(
                self.note,
                DatabaseBasedPromptBuilder.MeetingPromptType.SUMMARY,
                transcript,
                version="test",
            )

        self.assertIn("No prompt found for", str(context.exception))

        # Restore original category
        self.note.meeting_type.category = original_category
        self.note.save()

    def test_build_prompt_for_structured_data_success(self) -> None:
        # Use test prompt from conftest.py
        prompt = Prompt.objects.get(unique_name="test_prompt")

        # Use schema from conftest.py or create a new one if needed
        schema_definition, _ = StructuredMeetingDataSchema.objects.get_or_create(name="test", schema={"key": "value"})

        structured_data_template = StructuredMeetingDataTemplate.objects.create(
            title="Test Structured Data Template",
            schema_definition=schema_definition,
            initial_data={"initial_key": "initial_value"},
            context="Example context",
            prompt=prompt,
        )

        transcript = "This is a test transcript."

        prompt_text = DatabaseBasedPromptBuilder.build_prompt_for_structured_data(structured_data_template, transcript)
        expected = (
            'Schema: {\n  "key": "value"\n}\n'
            'Data: {\n  "initial_key": "initial_value"\n}\n'
            'Example: "Example context"\n'
            "Transcript: This is a test transcript."
        )
        self.assertEqual(prompt_text, expected)

    def test_build_prompt_for_structured_data_missing_prompt(self) -> None:
        # Use schema from conftest.py or create a new one if needed
        schema_definition, _ = StructuredMeetingDataSchema.objects.get_or_create(name="test", schema={"key": "value"})
        structured_data_template = StructuredMeetingDataTemplate.objects.create(
            title="Test Structured Data Template",
            schema_definition=schema_definition,
            initial_data={"initial_key": "initial_value"},
        )
        transcript = "This is a test transcript."

        with self.assertRaises(ValueError):
            DatabaseBasedPromptBuilder.build_prompt_for_structured_data(structured_data_template, transcript)

    def test_build_prompt_for_structured_data_format_unused_key(self) -> None:
        # Create a special prompt just for this test
        prompt, _ = Prompt.objects.get_or_create(
            name="Test Prompt Without Format Keys",
            unique_name="test_prompt_no_format",
            defaults={"user_prompt": "Prompt with no format keys."},
        )

        # Use schema from conftest.py or create a new one if needed
        schema_definition, _ = StructuredMeetingDataSchema.objects.get_or_create(name="test", schema={"key": "value"})

        structured_data_template = StructuredMeetingDataTemplate.objects.create(
            title="Test Structured Data Template No Format",
            schema_definition=schema_definition,
            initial_data={"initial_key": "initial_value"},
            context="Example context",
            prompt=prompt,
        )

        transcript = "This is a test transcript."

        prompt_text = DatabaseBasedPromptBuilder.build_prompt_for_structured_data(structured_data_template, transcript)

        self.assertEqual(prompt_text, "Prompt with no format keys.")

    async def test_get_search_prompt_async_db_success(self) -> None:
        await Prompt.objects.acreate(
            name="search prompt test",
            unique_name="search_prompt_test",
            user_prompt="Search for {query} in {input_data} with {additional_context}",
        )
        result = await DatabaseBasedPromptBuilder.get_search_prompt_async(
            input_data="test transcript",
            query="test query",
            additional_context="test context",
            prompt_name="search_prompt_test",
        )

        self.assertEqual(result, "Search for test query in test transcript with test context")

    async def test_get_search_prompt_async_fallback(self) -> None:
        # Test fallback to default prompt when database prompt not found
        result = await DatabaseBasedPromptBuilder.get_search_prompt_async(
            input_data="test transcript",
            query="test query",
            additional_context="test context",
            prompt_name="nonexistent_prompt",
        )

        prompt_text = (await Prompt.objects.aget(unique_name="search_prompt_v1")).user_prompt
        self.assertEqual(
            result,
            (prompt_text or "").format(
                query="test query", input_data="test transcript", additional_context="test context"
            ),
        )

    async def test_get_search_prompt_async_fallback_missing(self) -> None:
        await Prompt.objects.filter(unique_name="search_prompt_v1").adelete()

        with self.assertRaises(ValueError):
            result = await DatabaseBasedPromptBuilder.get_search_prompt_async(
                input_data="test transcript",
                query="test query",
                additional_context="test context",
                prompt_name="nonexistent_prompt",
            )
            self.assertIsNone(result)

    async def test_get_search_prompt_async_db_no_text(self) -> None:
        await Prompt.objects.acreate(
            name="search prompt no text",
            unique_name="search_prompt_no_text",
        )

        with self.assertRaises(ValueError):
            result = await DatabaseBasedPromptBuilder.get_search_prompt_async(
                input_data="test transcript",
                query="test query",
                additional_context="test context",
                prompt_name="search_prompt_no_text",
            )
            self.assertIsNone(result)

    async def test_get_search_prompt_async_format_error(self) -> None:
        await Prompt.objects.acreate(
            name="search prompt format error",
            unique_name="search_prompt_format_error",
            user_prompt="Invalid format {invalid_placeholder}",
        )

        with self.assertRaises(ValueError) as context:
            await DatabaseBasedPromptBuilder.get_search_prompt_async(
                input_data="test transcript",
                query="test query",
                additional_context="test context",
                prompt_name="search_prompt_format_error",
            )
        self.assertTrue("Failed to format prompt" in str(context.exception))

    def test_attendee_names_formatting(self) -> None:
        """Test that attendee names are properly formatted in the meeting context."""
        context = PromptContext(self.note)
        context_str = context._get_meeting_context()

        self.assertIn("Jon Doe", context_str)
        self.assertIn("Jane Doe", context_str)

        # Test the full context string format
        expected_format = (
            f"This is a {self.note.category} meeting with advisor Test User. "
            f"The meeting attendee(s) were the following:\n"
            f"client(s): No clients were present or specified by the user.\n advisor(s): Jon Doe, Jane Doe. "
            f"Meeting occurred on {vmu.get_time_string_from_timestamp(self.note.created, 'America/New_York')}.\n"
            "Additional context: Test context\n"
            "Meeting duration: 3600 seconds"
        )
        print(context_str)
        self.assertEqual(context_str, expected_format)
