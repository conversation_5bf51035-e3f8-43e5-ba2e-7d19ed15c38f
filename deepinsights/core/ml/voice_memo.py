import json
import logging
import re
from typing import Any, Callable

from django.conf import settings
from jsonschema import ValidationError, validate

from deepinsights.core.ml.genai import call_gpt4o
from deepinsights.core.ml.models import ActionItem, Summary, SummarySection, TasksAndTakeaways
from deepinsights.core.ml.prompt_builder import DatabaseBasedPromptBuilder, DataSource, PromptContext
from deepinsights.core.ml.voice_memo_utils import parse_llm_response, parse_response_into_json
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.client_interaction import ClientInteraction
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.prompt import Prompt
from deepinsights.meetingsapp.models.structured_meeting_data import (
    StructuredMeetingDataTemplate,
    StructuredMeetingDataTemplateRule,
)
from deepinsights.meetingsapp.models.structured_meeting_data_schema import StructuredMeetingDataSchema

_MINIMUM_VALID_TRANSCRIPT_LENGTH = 10


def get_tasks_and_takeaways(note: Note, transcript: str, version: str = "1") -> tuple[TasksAndTakeaways, bool]:
    """
    Process transcript to get tasks and takeaways.

    Args:
        note: Note object containing meeting details
        transcript: Text transcript of the meeting
        version: Version of the prompt to use

    Returns:
        A tuple, the first element being the TasksAndTakeaways object, and the second element being
        True if there is LLM generated content, and False otherwise. If there is no LLM generated
        content, the TasksAndTakeaways object may not be empty--it may have default content--but the
        boolean value will be False. In other words, do not depend on the presence or absence of
        values in the Summary to determine whether there is LLM-generated content.

    """
    if not transcript or len(transcript.split()) < _MINIMUM_VALID_TRANSCRIPT_LENGTH:
        logging.warning("Transcript is empty or too short for note %s", note.uuid)
        return TasksAndTakeaways(), False

    retries_left = settings.LLM_MAX_RETRIES
    while retries_left >= 0:
        retries_left -= 1
        try:
            prompt_text = DatabaseBasedPromptBuilder.build_prompt_for_meeting(
                note, DatabaseBasedPromptBuilder.MeetingPromptType.TASKS_AND_TAKEAWAYS, transcript, version
            )
            if (
                note.note_owner
                and note.category == MeetingType.Category.CLIENT
                and Flags.EnableDetailedActionItems.is_active_for_user(note.note_owner)
            ):
                prompt_text = DatabaseBasedPromptBuilder.build_prompt_for_meeting(
                    note, DatabaseBasedPromptBuilder.MeetingPromptType.TASKS_AND_TAKEAWAYS_DETAILED, transcript, version
                )
            llm_response = call_gpt4o(prompt_text)
            tasks_and_takeaways = parse_llm_response(llm_response, TasksAndTakeaways)
            if not tasks_and_takeaways:
                raise ValueError("Failed to parse LLM response for note %s", note.uuid)
            action_items_with_assignee_in_description: list[ActionItem] = []
            # Add the assignee to the action item descriptions for now. Later we can surface the
            # assignee properly in the frontend.
            for action in tasks_and_takeaways.action_items:
                assignee = action.assignee or "Unassigned"
                action.description = "[Assignee: " + assignee + "] " + action.description
                action_items_with_assignee_in_description.append(action)
            tasks_and_takeaways.action_items = action_items_with_assignee_in_description

            # Add special note for internal meetings
            if note.category == MeetingType.Category.INTERNAL:
                tasks_and_takeaways.advisor_notes = ["There are no Advisor Notes since this was an internal meeting."]

            return tasks_and_takeaways, True

        except Exception as e:
            logging.error(
                "Error processing tasks and takeaways for note %s. Trying again (retries left: %d)",
                note.uuid,
                retries_left,
                exc_info=e,
            )

    return TasksAndTakeaways(), False


def get_summary(note: Note, transcript: str, version: str = "1") -> tuple[Summary, bool]:
    """
    Process transcript to get meeting summary. First tries to get a personalized summary if available,
    then falls back to default summary if no personalized summary is available.

    Args:
        note: Note object containing meeting details
        transcript: Text transcript of the meeting
        version: Version of the prompt to use

    Returns:
        A tuple, the first element being the Summary object containing the summary takeaways, and
        the second element being True if there is LLM-generated content, and False otherwise. If
        there is no LLM generated content, the Summary object may not be empty--it may have default
        content--but the boolean value will be False. In other words, do not depend on the presence
        or absence of values in the Summary to determine whether there is LLM-generated content.
    """
    # We don't generate summaries for debrief meetings
    if note.category == MeetingType.Category.DEBRIEF:
        logging.info("Skipping summary for debrief meeting %s", note.uuid)
        return Summary(), False
    # Don't generate summaries for very short transcripts
    if not transcript or len(transcript.split()) < _MINIMUM_VALID_TRANSCRIPT_LENGTH:
        return Summary(), False

    # Try to get personalized summary first
    summary, has_content = get_personalized_summary(note, transcript)
    if has_content:
        return summary, True

    # Fall back to default summary if personalized summary failed or wasn't available
    retries_left = settings.LLM_MAX_RETRIES
    logging.info("retries left: %d for note %s", retries_left, note.uuid)
    while retries_left >= 0:
        retries_left -= 1
        try:
            prompt_text = DatabaseBasedPromptBuilder.build_prompt_for_meeting(
                note, DatabaseBasedPromptBuilder.MeetingPromptType.SUMMARY, transcript, version
            )
            llm_response = call_gpt4o(prompt_text)
            logging.info("llm response for summary: %s for note %s", llm_response, note.uuid)
            # Remove thinking tags
            pattern = r"<thinking>.*?</thinking>"
            llm_response = re.sub(pattern, "", llm_response, flags=re.DOTALL)
            llm_summary = parse_llm_response(llm_response, Summary)
            if llm_summary is not None:
                return llm_summary, True
            raise ValueError("Failed to parse LLM response for note %s", note.uuid)

        except Exception as e:
            logging.warning(
                "Error processing summary for note %s. Trying again (retries left: %d)",
                note.uuid,
                retries_left,
                exc_info=e,
            )

    return Summary(), False


def _get_agenda_for_note(note: Note) -> dict[str, Any] | None:
    # Check if note has an associated client interaction with an agenda
    client_interaction = None
    try:
        client_interaction = ClientInteraction.objects.filter(note=note).first()
    except Exception as e:
        logging.warning("Error finding client interaction for note %s: %s", note.uuid, str(e))

    agenda_data = None

    # First try to use the client interaction's existing agenda
    if client_interaction and client_interaction.agenda:
        logging.info(
            "Found client interaction with agenda for note %s, client interaction %s",
            note.uuid,
            client_interaction.uuid,
        )
        agenda_data = client_interaction.agenda.data
    # Fall back to the meeting type's agenda template
    elif note.meeting_type:
        logging.info(
            "Trying to use meeting type agenda template for note %s, meeting type %s",
            note.uuid,
            note.meeting_type.uuid,
        )
        # Get the agenda template from the meeting type
        agenda_templates = note.meeting_type.agenda_templates.filter(is_deleted=False)
        # If no templates exist, we just show empty editor
        if not agenda_templates.exists():
            logging.warning("Meeting type %s has no agenda template", note.meeting_type.uuid)
            return None

        agenda_template = agenda_templates.first()
        if agenda_template and agenda_template.initial_data:
            agenda_data = agenda_template.initial_data
    else:
        logging.warning("Note %s has no meeting type or client interaction with agenda", note.uuid)
        return None

    return agenda_data


def _get_llm_prompt_output_with_retries(
    prompt: Prompt,
    schema: StructuredMeetingDataSchema | None,
    agenda_data: dict[str, Any],
    note: Note,
    transcript: str,
    validate_function: Callable[[str], str | None],
) -> str:
    if not prompt.user_prompt:
        logging.error("Prompt text is empty for agenda")
        return ""
    if schema:
        schema_json = schema.schema
        schema_text = json.dumps(schema_json, indent=2)
        # Format the prompt with the schema, agenda data and transcript
        formatted_prompt = prompt.user_prompt.format(
            schema=schema_text,
            agenda_template=agenda_data,
            transcript=transcript,
        )
    else:
        # No schema, format prompt with agenda data and transcript
        formatted_prompt = prompt.user_prompt.format(
            agenda_template=agenda_data,
            transcript=transcript,
        )
    # Call the LLM to generate structured data
    retries_left = settings.LLM_MAX_RETRIES
    while retries_left >= 0:
        retries_left -= 1
        try:
            logging.info("Calling LLM with client info for note %s", note.uuid)
            # Call LLM and parse response
            response = call_gpt4o(formatted_prompt)
            validated_response = validate_function(response)

            if not validated_response:
                raise ValueError("Invalid response for agenda follow-up from LLM for note %s", note.uuid)

            return validated_response

        except Exception as e:
            logging.warning(
                "Could not get structured data for agenda follow-up. Trying again (retries left: %d)",
                retries_left,
                exc_info=e,
            )
            if retries_left <= 0:
                break
    # If we've exhausted retries, return empty value
    return ""


def _clean_prompt_result_plain_text(text: str) -> str:
    return text.strip().strip("`").strip()


def get_agenda_completion_info(note: Note, transcript: str) -> str:
    """
    Generate structured data based on a meeting's agenda.
    We want, for each item in the agenda, was it covered in the transcript.

    This takes either the agenda from the client interaction associated with the note,
    or the agenda template that is attached to a meeting type for a note,
    and uses the contents of the meeting to create structured data following
    the structured data schema.

    Args:
        note: The meeting note to process
        transcript: The transcript of the meeting

    Returns:
        Structured data for agenda follow-up
    """

    # For agenda completion mid-meeting, we only want to get results if a customized agenda has been generated
    # We do NOT want to default to the default agenda
    try:
        client_interaction = ClientInteraction.objects.filter(note=note).first()
    except ClientInteraction.DoesNotExist as e:
        logging.warning("Error finding client interaction for note %s: %s", note.uuid, str(e))

    agenda_data = None

    if client_interaction and client_interaction.agenda:
        logging.info(
            "Found client interaction with agenda for note %s, client interaction %s",
            note.uuid,
            client_interaction.uuid,
        )
        agenda_data = client_interaction.agenda.data

    if not agenda_data:
        return ""

    try:
        # Get the prompt for agenda follow-up
        prompt = Prompt.objects.get(unique_name="agenda_completion_generator")

        return _clean_prompt_result_plain_text(
            _get_llm_prompt_output_with_retries(prompt, None, agenda_data, note, transcript, lambda x: x)
        )

    except Exception as e:
        logging.error("Error processing agenda coverage for note %s: ", note.uuid, exc_info=e)
        return ""


def _validate_schema_and_json(response: str, schema: StructuredMeetingDataSchema, logging_uuid: str) -> str | None:
    # Returns original text of response if it's parseable, not the JSON it parses into! Have to parse a second time if it parses.
    structured_data = parse_response_into_json(response)

    if not structured_data:
        logging.error("Invalid JSON response from LLM for note %s", logging_uuid)
        return None

    # Validate against schema if schema exists
    if schema.schema:
        try:
            validate(structured_data, schema.schema)
        except ValidationError as e:
            return None

    return response


def get_agenda_follow_up_data(note: Note, transcript: str) -> dict[str, Any]:
    """
    Generate agenda follow-up structured data based on a meeting's agenda.

    This takes either the agenda from the client interaction associated with the note,
    or the agenda template that is attached to a meeting type for a note,
    and uses the contents of the meeting to create structured follow-up data following
    the structured data schema.

    Args:
        note: The meeting note to process
        transcript: The transcript of the meeting

    Returns:
        Structured data for agenda follow-up
    """
    # TODO: Simplify this flow if possible. Currently this is using various data sources to get the agenda data.
    # TODO: Investigate if we can use a single data source, maybe by copying the agenda template to the client interaction.

    agenda_data = _get_agenda_for_note(note)

    if not agenda_data:
        return {}

    try:
        # Get the prompt for agenda follow-up
        prompt = Prompt.objects.get(unique_name="agenda_followup_generator")

        # Get structured data schema
        schema = StructuredMeetingDataSchema.objects.get(name="structured_data_schema")

        response = _get_llm_prompt_output_with_retries(
            prompt,
            schema,
            agenda_data,
            note,
            transcript,
            lambda x: _validate_schema_and_json(x, schema, str(note.uuid)),
        )

        return parse_response_into_json(response)

    except Exception as e:
        logging.error("Error processing agenda follow-up for note %s: ", note.uuid, exc_info=e)
        return {}


def get_follow_up_structured_data(
    structured_meeting_data_template: StructuredMeetingDataTemplate, transcript: str, note: Note
) -> dict[str, Any]:
    """
    Process a transcript to get meeting follow up structured data.

    Args:
        structured_meeting_data_template: The structured meeting data template for which to generate structured data
        transcript: Meeting transcript

    Returns:
        The structured data generated (if any).
    """
    try:
        prompt = DatabaseBasedPromptBuilder.build_prompt_for_structured_data(
            structured_meeting_data_template, transcript
        )
    except Exception as e:
        logging.error(
            "Error processing structured meeting data %s for note %s: ",
            structured_meeting_data_template.uuid,
            note.uuid,
            exc_info=e,
        )
        raise e

    retries_left = settings.LLM_MAX_RETRIES
    while retries_left >= 0:
        retries_left -= 1
        try:
            response = parse_response_into_json(call_gpt4o(prompt))
            if not response:
                raise ValueError("Invalid JSON response from LLM for note %s", note.uuid)
            if not structured_meeting_data_template.schema_definition:
                raise ValueError(
                    "No schema definition found for structured meeting data template for note %s", note.uuid
                )
            validate(response, structured_meeting_data_template.schema_definition.schema)
            return response
        except Exception as e:
            logging.warning(
                "Could not get structured data for structured meeting data template %s for note %s. "
                "Trying again (retries left: %d)",
                structured_meeting_data_template.uuid,
                note.uuid,
                retries_left,
                exc_info=e,
            )
    return {}


def parse_personalized_summary_to_standard_format(personalized_data: dict[str, Any]) -> Summary:
    """
    Parse personalized summary data into the standard summary format.

    Args:
        personalized_data: The personalized summary data in the format:
            {
                "review_entries": [
                    {
                        "id": str,
                        "kind": str,
                        "topic": str,
                        "subentries": [
                            {
                                "id": str,
                                "kind": str,
                                "topic": str
                            },
                            ...
                        ]
                    },
                    ...
                ]
            }

    Returns:
        Summary object with sections and bullets
    """
    sections = []

    for entry in personalized_data.get("review_entries", []):
        # Skip entries that aren't headers or have no subentries
        if entry.get("kind") != "header" or not entry.get("subentries"):
            continue

        topic = entry.get("topic", "").strip(":")
        bullets = []

        # Process each subentry as a bullet point
        for subentry in entry.get("subentries", []):
            if subentry.get("topic"):
                bullets.append(subentry["topic"])

        if topic and bullets:
            sections.append(SummarySection(topic=topic, bullets=bullets))

    return Summary(sections=sections)


def get_personalized_summary(note: Note, transcript: str) -> tuple[Summary, bool]:
    """
    Generate a personalized summary for a note if a personalized summary template exists.

    Args:
        note: The note to generate summary for
        transcript: The transcript text

    Returns:
        Tuple of (Summary object, bool indicating if content was generated)
    """
    try:
        # Get all templates that apply to this note and user
        if note.note_owner is None:
            return Summary(sections=[]), False
        templates = StructuredMeetingDataTemplateRule.relevant_follow_up_templates(note.meeting_type, note.note_owner)
        # Find the personalized summary template
        personalized_summary_template = templates.filter(kind="personalized_summary").first()

        if not personalized_summary_template or not personalized_summary_template.initial_data:
            return Summary(sections=[]), False

        prompt_context = PromptContext(note)
        # Build meeting context
        meeting_context = prompt_context.build_context([DataSource.MEETING_CONTEXT])

        # Add context to beginning of transcript
        transcript_with_context = f"{meeting_context}\n\n{transcript}"

        # Generate structured data using the personalized summary template
        structured_data = get_follow_up_structured_data(personalized_summary_template, transcript_with_context, note)
        if not structured_data:
            return Summary(sections=[]), False

        # Convert structured data to standard summary format
        summary = parse_personalized_summary_to_standard_format(structured_data)
        return summary, True

    except Exception as e:
        logging.error("Error generating personalized summary for note %s", note.uuid, exc_info=e)
        return Summary(sections=[]), False
