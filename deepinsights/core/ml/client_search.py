import logging

from api.routers.note_models import SummarySection
from deepinsights.core.ml.agenda import get_client_history_async
from deepinsights.core.ml.genai import call_gpt4o_async
from deepinsights.core.ml.prompt_builder import DatabaseBasedPromptBuilder
from deepinsights.core.ml.voice_memo_utils import parse_llm_response
from deepinsights.meetingsapp.models.client import Client
from deepinsights.users.models.user import User


async def search_client_notes(client: Client, user: User, query: str) -> SummarySection:
    """
    Search across all notes and information for a client and answer questions.

    Args:
        client: The client to search notes for
        user: The requesting user
        query: The search query/question

    Returns:
        SummarySection containing the answer with topic and bullets
    """
    try:
        # Get client history and household info using existing agenda functionality
        client_history, household_info = await get_client_history_async(user, client)

        # Format the context from client history
        context = {
            "client_history": client_history.to_prompt_format(),
            "household_info": str(household_info) if household_info else "No household information available",
        }

        # Get the search prompt from database
        search_prompt = await DatabaseBasedPromptBuilder.get_search_prompt_async(
            input_data=str(context), query=query, additional_context="", prompt_name="prompt_client_search_v1"
        )

        if not search_prompt:
            raise ValueError("No search prompt returned.")

        # Call LLM with the prompt
        response = await call_gpt4o_async(search_prompt, max_tokens=4096)

        # Parse response into structured format
        structured_response = parse_llm_response(response, SummarySection)
        if structured_response is None:
            logging.error("Failed to parse llm search response: %s", response)
            raise ValueError("Failed to parse llm search response")

        return structured_response

    except ValueError as e:
        logging.error("Failed to search client notes:", exc_info=True)
        raise ValueError(f"Failed to search client notes: {e}") from e
