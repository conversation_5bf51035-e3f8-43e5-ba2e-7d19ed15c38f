import asyncio
import datetime
import logging
from typing import Any

from asgiref.sync import sync_to_async

from deepinsights.core.integrations.crm.crm_manager import get_crm_interface, get_crm_interface_async
from deepinsights.core.integrations.crm.crm_models import CRMNote
from deepinsights.core.ml.genai import call_gpt4o
from deepinsights.core.ml.models import Client<PERSON><PERSON>ory, MeetingNote, Summary, SummarySection
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.prompt import Prompt
from deepinsights.users.models.user import User


def _parse_crm_note(note: CRMNote) -> MeetingNote | None:
    """Parse a CRM note into a MeetingNote model."""
    return MeetingNote(
        id=note.crm_id,
        source=note.crm_system,
        created_at=note.created_at,
        summary=note.content,
    )


def _parse_zeplyn_note(note: Note) -> MeetingNote:
    """Parse a Zeplyn note into a MeetingNote model."""
    sections = []
    if note.summary is not None:
        sections = [
            SummarySection(topic=section["topic"], bullets=section["bullets"])
            for section in note.summary.get("sections", [])
        ]

    return MeetingNote(
        id=str(note.uuid),
        source="Zeplyn",
        created_at=note.created,
        summary=Summary(sections=sections),
    )


async def get_client_history_async(user: User, client: Client) -> tuple[ClientHistory, dict[str, Any] | None]:
    """Get all relevant meeting notes and summaries from both CRM and Zeplyn asynchronously."""
    try:
        notes: list[MeetingNote] = []
        crm_handler = await get_crm_interface_async(user)
        household_info = None

        # Get CRM notes and household info if available
        if crm_handler:
            logging.info("Fetching CRM notes for client %s", client.uuid)
            try:
                # Wrap the entire CRM operation in sync_to_async to handle potential internal DB calls
                fetch_notes = sync_to_async(
                    lambda: crm_handler.fetch_notes_for_client(user, client, datetime.timedelta(days=365))
                )
                get_info = sync_to_async(
                    lambda: crm_handler.get_client_basic_info(client, user, include_household=True)
                )

                # Execute CRM operations concurrently
                crm_notes, household_info = await asyncio.gather(fetch_notes(), get_info())

                if crm_notes:
                    notes.extend(filter(None, [_parse_crm_note(note) for note in crm_notes]))

            except Exception as e:
                logging.error("Error fetching CRM data: %s", str(e), exc_info=True)
                # Continue execution even if CRM fails - we can still get Zeplyn notes

        try:
            # Get recent Zeplyn notes - optimize the queryset chain
            notes_qs = Note.objects.filter(client__uuid=str(client.uuid))
            notes_qs = notes_qs.order_by("-created")  # Use '-created' for reverse order
            notes_qs = notes_qs.only("uuid", "summary", "created")

            # Wrap the entire queryset evaluation in a single sync_to_async call
            zeplyn_notes = await sync_to_async(lambda: list(notes_qs[:10]))()

            # Add Zeplyn notes that have summaries
            notes.extend(_parse_zeplyn_note(note) for note in zeplyn_notes if note.summary is not None)
        except Exception as e:
            logging.error("Error fetching Zeplyn notes: %s", str(e), exc_info=True)
            # If we can't get Zeplyn notes but have CRM notes, continue
            if not notes:
                raise  # Only raise if we have no notes at all

        return ClientHistory(notes=notes), household_info
    except Exception as e:
        logging.error("Failed to get client history: %s", str(e), exc_info=True)
        raise ValueError(f"Failed to get client history: {str(e)}") from e


def _get_client_history(user: User, client: Client) -> tuple[ClientHistory, dict[str, Any] | None]:
    """Get all relevant meeting notes and summaries from both CRM and Zeplyn."""
    notes: list[MeetingNote] = []
    crm_handler = get_crm_interface(user)
    household_info = None

    # Get CRM notes and household info if available
    if crm_handler:
        logging.info(f"Fetching CRM notes for client {client.uuid}")
        if crm_notes := crm_handler.fetch_notes_for_client(user, client, datetime.timedelta(days=365)):
            notes.extend(filter(None, [_parse_crm_note(note) for note in crm_notes]))

        household_info = crm_handler.get_client_basic_info(client, user, include_household=True)

    # Get recent Zeplyn notes
    zeplyn_notes = (
        Note.objects.filter(client__uuid=str(client.uuid)).order_by("-created").only("uuid", "summary", "created")[:10]
    )

    # Add Zeplyn notes that have summaries
    notes.extend(_parse_zeplyn_note(note) for note in zeplyn_notes if note.summary)

    return ClientHistory(notes=notes), household_info


def generate_filled_agenda(user: User, client: Client, agenda_template: str) -> dict[str, Any]:
    """Generate meeting agenda that provides relevant client information."""
    client_history, household_info = _get_client_history(user, client)
    # Convert household info to string if present
    household_str = str(household_info) if household_info else "No household information available"

    # Check if we have any useful client data
    if not client_history.notes and not household_info:
        # If no historical data available, just return the template as is
        logging.info(
            f"No client history or household info available for client {client.uuid}. Using agenda template as is."
        )
        return {"content": agenda_template, "format": "markdown"}
    # Get prompt from database
    prompt = Prompt.objects.get(unique_name="meeting_agenda_generator")

    if not prompt.user_prompt:
        logging.error("Prompt template has no prompt text: %s", prompt.uuid)
        return {"content": agenda_template, "format": "markdown"}

    # Format prompt with inputs
    prompt_text = prompt.user_prompt.format(
        client_history=client_history.to_prompt_format(), household_info=household_str, agenda_template=agenda_template
    )

    # Generate filled agenda using LLM
    raw_response = call_gpt4o(prompt_text)
    return {"content": raw_response, "format": "markdown"}
