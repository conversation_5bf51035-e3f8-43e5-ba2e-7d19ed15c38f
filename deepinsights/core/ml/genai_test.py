from unittest.mock import MagicMock, patch

from anthropic.types import Message, TextBlock, Usage
from django.test import TestCase

from deepinsights.core.ml.genai import call_claude_sonnet


class TestCallClaudeSonnet(TestCase):
    def setUp(self) -> None:
        self.test_prompt = "Test prompt"
        self.test_system_prompt = "Test system prompt"
        self.model = "anthropic.claude-3-5-sonnet-20240620-v1:0"
        self.mock_message = Message(
            id="test_id",
            content=[TextBlock(type="text", text="Test response")],
            model=self.model,
            role="assistant",
            stop_reason="end_turn",
            type="message",
            usage=Usage(input_tokens=0, output_tokens=0),
        )

    @patch("deepinsights.core.ml.genai.AnthropicBedrock")
    def test_successful_call(self, mock_anthropic: MagicMock) -> None:
        """Test successful call to Claude Sonnet with system prompt."""
        mock_anthropic.return_value.messages.create.return_value = self.mock_message

        result = call_claude_sonnet(
            self.test_prompt,
            system_prompt=self.test_system_prompt,
        )

        # Verify the result
        self.assertEqual(result, "Test response")

        # Verify the call to AnthropicBedrock
        mock_anthropic.return_value.messages.create.assert_called_once_with(
            model=self.model,
            max_tokens=8192,
            messages=[{"role": "user", "content": self.test_prompt}],
            system=self.test_system_prompt,
        )

    @patch("deepinsights.core.ml.genai.AnthropicBedrock")
    def test_invalid_response_object(self, mock_anthropic: MagicMock) -> None:
        """Test handling of invalid response object."""
        # Create empty response
        self.mock_message.content = []
        mock_anthropic.return_value.messages.create.return_value = self.mock_message

        with self.assertRaises(ValueError) as context:
            call_claude_sonnet(self.test_prompt, self.test_system_prompt)

        self.assertEqual(str(context.exception), "Invalid response object received from Claude Sonnet")

    @patch("deepinsights.core.ml.genai.AnthropicBedrock")
    def test_multiple_text_blocks(self, mock_anthropic: MagicMock) -> None:
        """Test handling of multiple text blocks in response."""
        # Modify the mock_message content to have multiple text blocks
        self.mock_message.content = [
            TextBlock(type="text", text="First part "),
            TextBlock(type="text", text="second part"),
        ]
        mock_anthropic.return_value.messages.create.return_value = self.mock_message

        result = call_claude_sonnet(self.test_prompt, self.test_system_prompt)

        # Verify the concatenated result
        self.assertEqual(result, "First part second part")
