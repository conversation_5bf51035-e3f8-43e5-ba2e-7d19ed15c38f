import logging

from api.routers.note_models import SummarySection
from deepinsights.core.ml.genai import call_gpt4o_async
from deepinsights.core.ml.prompt_builder import DatabaseBasedPromptBuilder
from deepinsights.core.ml.voice_memo_utils import parse_llm_response


async def search_note(input_data: str, query: str, additional_context: str) -> SummarySection:
    try:
        search_prompt = await DatabaseBasedPromptBuilder.get_search_prompt_async(
            input_data=input_data, query=query, additional_context=additional_context, prompt_name="search_prompt_v1"
        )
        if not search_prompt:
            raise ValueError("No search prompt returned.")

        response = await call_gpt4o_async(search_prompt)
        structured_response = parse_llm_response(response, SummarySection)
        if structured_response is None:
            logging.error("Failed to parse llm search response: %s", response)
            raise ValueError("Failed to parse llm search response")
        return structured_response
    except ValueError as e:
        logging.error("Failed with error:", exc_info=True)
        raise ValueError(f"Failed to search note: {e}") from e
