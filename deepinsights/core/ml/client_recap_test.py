import json
from typing import Any, Dict
from unittest.mock import MagicMock, PropertyMock, patch

from django.test import TestCase
from pydantic import HttpUrl

from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import CRMNote
from deepinsights.core.ml.client_recap import (
    __get_structured_summary as get_structured_summary,
)
from deepinsights.core.ml.client_recap import (
    __has_content as has_content,
)
from deepinsights.core.ml.client_recap import (
    build_client_recap_for_client,
    convert_client_recap_to_markdown,
    generate_client_recap,
)
from deepinsights.core.ml.models import Summary, SummarySection
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.client_recap import ClientRecap, ClientRecapStatus
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User


class BaseClientRecapTest(TestCase):
    def setUp(self) -> None:
        self.organization = Organization.objects.create(name="Test Org")
        self.user: User = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            organization=self.organization,
            crm_configuration={"crm_system": "dummy_crm"},
            preferences={},
            metadata={},
        )

        self.test_client = Client.objects.create(
            first_name="Client",
            last_name="Test",
            organization=self.organization,
            crm_id="crm123",
        )


class TestClientRecapUtils(BaseClientRecapTest):
    def test_has_content(self) -> None:
        # Case 1: Valid content exists
        self.assertTrue(has_content({"recap": [{"topic": "Topic", "bullets": [{"text": "Valid bullet"}]}]}))
        # Case 2: Empty recap list
        self.assertFalse(has_content({"recap": []}))
        # Case 3: Recap with bullet missing text
        self.assertFalse(has_content({"recap": [{"topic": "Topic", "bullets": [{"other": "val"}]}]}))
        # Case 4: Recap with bullet text being None or empty
        self.assertFalse(has_content({"recap": [{"topic": "Topic", "bullets": [{"text": None}]}]}))
        self.assertFalse(has_content({"recap": [{"topic": "Topic", "bullets": [{"text": ""}]}]}))
        # Case 5: No recap key
        self.assertFalse(has_content({"other_key": []}))
        # Case 6: Topic exists but bullets list is empty
        self.assertFalse(has_content({"recap": [{"topic": "Topic", "bullets": []}]}))
        # Case 7: Multiple sections, only one _has content
        self.assertTrue(
            has_content(
                {
                    "recap": [
                        {"topic": "Empty Topic", "bullets": []},
                        {"topic": "Content Topic", "bullets": [{"text": "Valid bullet"}]},
                    ]
                }
            )
        )
        # Case 8: Multiple bullets, only one _has content
        self.assertTrue(
            has_content({"recap": [{"topic": "Topic", "bullets": [{"text": ""}, {"text": "Valid bullet"}]}]})
        )

    def test_get_structured_summary(self) -> None:
        note_id: str = "uuid-123"
        summary = Summary(sections=[SummarySection(topic="Topic", bullets=["Bullet 1", "Bullet 2"])])
        source: str = "Test Source"
        expected: str = (
            f"<Meeting><Source>{source}</Source>\n"
            f"<ID>{note_id}</ID>\n"
            "<ReferenceLink></ReferenceLink>\n"
            "<Summary>\n"
            f"{summary.model_dump()}\n"
            "</Summary></Meeting>\n\n"
        )
        self.assertEqual(get_structured_summary(note_id, summary.model_dump(), source, None), expected)

    def test_get_structured_summary_with_link(self) -> None:
        note_id: str = "uuid-123"
        summary = Summary(sections=[SummarySection(topic="Topic", bullets=["Bullet 1", "Bullet 2"])])
        source: str = "Test Source"
        expected: str = (
            f"<Meeting><Source>{source}</Source>\n"
            f"<ID>{note_id}</ID>\n"
            "<ReferenceLink>http://example.com/</ReferenceLink>\n"
            "<Summary>\n"
            f"{summary.model_dump()}\n"
            "</Summary></Meeting>\n\n"
        )
        self.assertEqual(
            get_structured_summary(note_id, summary.model_dump(), source, HttpUrl("http://example.com/")), expected
        )

    def test_convert_client_recap_to_markdown(self) -> None:
        no_data_message: str = "No client recap data available."
        # Case 1: Valid data
        valid_data: Dict[str, Any] = {
            "recap": [
                {"topic": "Topic 1", "bullets": [{"text": "Bullet 1"}]},
                {"topic": "Topic 2", "bullets": [{"text": "Bullet 2a"}, {"text": "Bullet 2b"}]},
            ]
        }
        expected_valid: str = "## Topic 1\n- Bullet 1\n\n## Topic 2\n- Bullet 2a\n- Bullet 2b"
        self.assertEqual(convert_client_recap_to_markdown(valid_data), expected_valid)

        # Case 2: Invalid/Empty data structures
        self.assertEqual(convert_client_recap_to_markdown({}), no_data_message, "Test empty dict")
        self.assertEqual(convert_client_recap_to_markdown(None), no_data_message, "Test None input")  # type: ignore
        self.assertEqual(convert_client_recap_to_markdown({"other": []}), no_data_message, "Test wrong key")
        self.assertEqual(convert_client_recap_to_markdown({"recap": []}), "", "Test empty recap list")

        # Case 3: Edge cases within recap structure
        self.assertEqual(
            convert_client_recap_to_markdown({"recap": [{"topic": "Topic 1", "bullets": []}]}),
            "## Topic 1",
            "Test topic with empty bullets list",
        )
        # Topic with bullets having empty/None text should render topic header but no bullets
        self.assertEqual(
            convert_client_recap_to_markdown(
                {"recap": [{"topic": "Topic 1", "bullets": [{"text": ""}, {"text": None}]}]}
            ),
            "## Topic 1",
            "Test topic with empty/None bullet text",
        )
        # Section with None or empty topic should be skipped entirely
        self.assertEqual(
            convert_client_recap_to_markdown({"recap": [{"topic": None, "bullets": [{"text": "B1"}]}]}),
            "",
            "Test section with None topic",
        )
        self.assertEqual(
            convert_client_recap_to_markdown({"recap": [{"topic": "", "bullets": [{"text": "B1"}]}]}),
            "",
            "Test section with empty string topic",
        )
        # Multiple sections with mixed valid/invalid content
        self.assertEqual(
            convert_client_recap_to_markdown(
                {"recap": [{"topic": "Topic 1", "bullets": [{"text": "B1"}]}, {"topic": "Topic 2", "bullets": []}]}
            ),
            "## Topic 1\n- B1\n\n## Topic 2",
            "Test multiple sections, one empty",
        )
        self.assertEqual(
            convert_client_recap_to_markdown(
                {
                    "recap": [
                        {"topic": "Topic 1", "bullets": [{"text": "B1"}]},
                        {"topic": None, "bullets": [{"text": "B2"}]},
                    ]
                }
            ),
            "## Topic 1\n- B1\n",  # Second section skipped
            "Test multiple sections, one with None topic",
        )


@patch("deepinsights.core.ml.client_recap.call_gpt4o")
class TestBuildClientRecapForClient(BaseClientRecapTest):
    def test_build_success_path(self, mock_call_gpt4o: MagicMock) -> None:
        note_summary = {"sections": [{"topic": "Test Topic", "bullets": ["Test bullet 1", "Test bullet 2"]}]}
        note = Note.objects.create(
            note_owner=self.user,
            summary=note_summary,
            client={"uuid": str(self.test_client.uuid)},
        )
        content = "Generate a summary of at least one hundred characters so that there is enough content to pass to the model."
        crm_notes = [
            CRMNote(crm_id="crm1", crm_system="test_crm", content=content, created_at=None),
            CRMNote(
                crm_id="crm2",
                crm_system="test_crm",
                content=content,
                created_at=None,
                web_link="http://example.com/reference",
            ),
        ]
        mock_final_prep = {
            "recap": [{"topic": "Test Topic", "bullets": [{"text": "Test bullet 1"}, {"text": "Test bullet 2"}]}]
        }

        mock_call_gpt4o.return_value = json.dumps(mock_final_prep)

        with patch(
            "deepinsights.core.ml.client_recap.User.crm_handler", new_callable=PropertyMock, spec=CrmBase
        ) as mock_crm_handler:
            mock_crm_handler.return_value.get_client_basic_info.return_value = {}
            mock_crm_handler.return_value.fetch_notes_for_client.return_value = crm_notes
            result = build_client_recap_for_client(self.user, self.test_client)

        self.assertEqual(result, mock_final_prep)
        mock_call_gpt4o.assert_called_once()
        prompt = mock_call_gpt4o.call_args[0][0]
        expected_crm_note_one = (
            "<Meeting><Source>test_crm</Source>\n"
            "<ID>crm1</ID>\n"
            "<ReferenceLink></ReferenceLink>\n"
            "<Summary>\n"
            "{'sections': [{'topic': 'Notes for meeting crm1', 'bullets': ['Generate a summary of at least one hundred characters so that there is enough content to pass to the model.']}]}\n"
            "</Summary></Meeting>"
        )
        self.assertIn(expected_crm_note_one, prompt)
        expected_crm_note_two = (
            "<Meeting><Source>test_crm</Source>\n"
            "<ID>crm2</ID>\n"
            "<ReferenceLink>http://example.com/reference</ReferenceLink>\n"
            "<Summary>\n"
            "{'sections': [{'topic': 'Notes for meeting crm2', 'bullets': ['Generate a summary of at least one hundred characters so that there is enough content to pass to the model.']}]}\n"
            "</Summary></Meeting>"
        )
        self.assertIn(expected_crm_note_two, prompt)
        expected_note = (
            "<Meeting><Source>Zeplyn</Source>\n"
            f"<ID>{note.uuid}</ID>\n"
            "<ReferenceLink></ReferenceLink>\n"
            "<Summary>\n"
            f"{note_summary}\n"
            "</Summary></Meeting>"
        )
        self.assertIn(expected_note, prompt)

    def test_build_with_empty_data(self, mock_call_gpt4o: MagicMock) -> None:
        with patch(
            "deepinsights.core.ml.client_recap.User.crm_handler", new_callable=PropertyMock, spec=CrmBase
        ) as mock_crm_handler:
            mock_crm_handler.return_value.get_client_basic_info.return_value = {}
            mock_crm_handler.return_value.fetch_notes_for_client.return_value = []
            result = build_client_recap_for_client(self.user, self.test_client)

        self.assertEqual(
            result,
            {
                "recap": [
                    {
                        "topic": "",
                        "scratchpad": "No detailed information available",
                    }
                ]
            },
        )

        mock_call_gpt4o.assert_not_called()

    @patch("deepinsights.core.ml.client_recap.__get_summaries_and_notes")
    def test_build_with_exception(self, mock_get_summaries_and_notes: MagicMock, mock_call_gpt4o: MagicMock) -> None:
        mock_get_summaries_and_notes.side_effect = Exception("Test error")

        with self.assertRaises(Exception) as context:
            build_client_recap_for_client(self.user, self.test_client)

        self.assertEqual(str(context.exception), "Test error")

    @patch("deepinsights.core.ml.client_recap.__create_client_recap_from_summaries")
    def test_build_with_create_prep_exception(
        self, mock_create_client_recap: MagicMock, mock_call_gpt4o: MagicMock
    ) -> None:
        mock_create_client_recap.side_effect = Exception("Failed to create client recap")

        with self.assertRaises(Exception) as context:
            build_client_recap_for_client(self.user, self.test_client)
        with patch(
            "deepinsights.core.ml.client_recap.User.crm_handler", new_callable=PropertyMock, spec=CrmBase
        ) as mock_crm_handler, self.assertRaises(Exception) as context:
            mock_crm_handler.return_value.get_client_basic_info.return_value = {}
            mock_crm_handler.return_value.fetch_notes_for_client.return_value = []
            build_client_recap_for_client(self.user, self.test_client)

        self.assertEqual(str(context.exception), "Failed to create client recap")


@patch("deepinsights.core.ml.client_recap.build_client_recap_for_client", new_callable=MagicMock)
class TestGenerateClientRecap(BaseClientRecapTest):
    """
    Tests for the main pathways within the generate_client_recap function as follows:
    1. Success: Client recap is generated with valid content -> Status set to 'processed' with summary.
    2. No Content: Client recap is generated but has no meaningful content -> Status set to 'failed'.
    3. Build Exception: build_client_recap_for_client raises an exception -> Status set to 'failed'.
    4. Client recap Not Found: ClientRecap with given ID doesn't exist -> DoesNotExist exception raised.
    5. User Not Found: User with given ID doesn't exist -> Status set to 'failed'.
    """

    def setUp(self) -> None:
        super().setUp()
        self.client_recap = ClientRecap.objects.create(client=self.test_client, user=self.user, summary={})

    def test_generate_success(
        self,
        mock_build_client_recap_for_client: MagicMock,
    ) -> None:
        # Create a prep summary that will pass _has_content check
        prep_summary: Dict[str, Any] = {"recap": [{"topic": "Test Topic", "bullets": [{"text": "Test bullet"}]}]}
        mock_build_client_recap_for_client.return_value = prep_summary

        # Initial state check
        self.assertEqual(self.client_recap.status, ClientRecapStatus.CREATED)
        self.assertEqual(self.client_recap.summary, {})

        generate_client_recap(self.client_recap.uuid, self.user.uuid)

        # Verify mock called as expected
        mock_build_client_recap_for_client.assert_called_once_with(self.user, self.test_client)

        # Verify final DB state by reloading the object
        self.client_recap.refresh_from_db()
        self.assertEqual(self.client_recap.status, ClientRecapStatus.PROCESSED)
        self.assertEqual(self.client_recap.summary, prep_summary)

    def test_generate_no_content(
        self,
        mock_build_client_recap_for_client: MagicMock,
    ) -> None:
        """Test pathway where build succeeds but has no meaningful content."""
        # Create a prep summary that will fail _has_content check
        prep_summary: Dict[str, Any] = {"recap": [{"topic": "Test Topic", "bullets": [{"text": ""}]}]}
        mock_build_client_recap_for_client.return_value = prep_summary

        # Initial state check
        self.assertEqual(self.client_recap.status, ClientRecapStatus.CREATED)

        generate_client_recap(self.client_recap.uuid, self.user.uuid)

        # Verify mock called as expected
        mock_build_client_recap_for_client.assert_called_once_with(self.user, self.test_client)

        # Verify DB state
        self.client_recap.refresh_from_db()
        self.assertEqual(self.client_recap.status, ClientRecapStatus.FAILED)

    def test_generate_build_exception(
        self,
        mock_build_client_recap_for_client: MagicMock,
    ) -> None:
        """Test pathway where build_client_recap_for_client raises an exception."""
        error_msg: str = "Internal Server Error during build"
        mock_build_client_recap_for_client.side_effect = Exception(error_msg)

        # Initial state check
        self.assertEqual(self.client_recap.status, ClientRecapStatus.CREATED)

        generate_client_recap(self.client_recap.uuid, self.user.uuid)

        # Verify mock called as expected
        mock_build_client_recap_for_client.assert_called_once_with(self.user, self.test_client)

        # Verify DB state
        self.client_recap.refresh_from_db()
        self.assertEqual(self.client_recap.status, ClientRecapStatus.FAILED)
