import datetime
import logging
from typing import Any, Iterable
from uuid import UUID

from django.db.models import Q
from pydantic import HttpUrl

from deepinsights.core.integrations.crm.crm_models import CRMNote
from deepinsights.core.ml.genai import call_gpt4o
from deepinsights.core.ml.models import Summary, SummarySection
from deepinsights.core.ml.prompt_builder import build_prompt_for_client_recap
from deepinsights.core.ml.voice_memo_utils import parse_response_into_json
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.client_recap import ClientRecap, ClientRecapStatus
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


def __has_content(client_recap_summary: dict[str, Any]) -> bool:
    has_content = False
    for section in client_recap_summary.get("recap", []):
        if section.get("topic") and section.get("bullets", []):
            for bullet in section.get("bullets", []):
                if bullet.get("text"):
                    has_content = True
                    break
            if has_content:
                break
    return has_content


def __get_structured_summary(
    id: str, summary: dict[str, list[dict[str, Any]]], source: str, link: HttpUrl | None
) -> str:
    return (
        f"<Meeting><Source>{source}</Source>\n"
        f"<ID>{id}</ID>\n"
        f"<ReferenceLink>{link or ''}</ReferenceLink>\n"
        f"<Summary>\n{summary}\n</Summary></Meeting>\n\n"
    )


def __create_summaries_from_crm_notes(crm_notes: list[CRMNote]) -> str:
    summaries = ""
    for note in crm_notes:
        summary = Summary(sections=[SummarySection(topic=f"Notes for meeting {note.crm_id}", bullets=[note.content])])
        summaries += __get_structured_summary(note.crm_id, summary.model_dump(), note.crm_system, note.web_link)
    return summaries


def __create_client_recap_from_summaries(
    notes: Iterable[Note],
    crm_notes: list[CRMNote],
    structured_data: str,
    max_retries: int = 3,
) -> dict[str, Any]:
    """Create client recap from notes and CRM notes"""
    summaries = ""

    if crm_notes:
        summaries += __create_summaries_from_crm_notes(crm_notes)

    for note in notes:
        if not note.summary:
            continue
        summaries += __get_structured_summary(str(note.uuid), note.summary, "Zeplyn", None)

    if not summaries or len(summaries) < 100:
        logging.error("Summary not present or too short for any of the note.")

        # Return a properly structured object matching the expected format in agenda.py
        return {
            "recap": [
                {
                    "topic": "",
                    "scratchpad": "No detailed information available",
                }
            ]
        }

    client_recap_prompt = build_prompt_for_client_recap(
        "agenda_builder", extractions=summaries, structured_data=structured_data
    )

    for attempt in range(max_retries):
        try:
            response = call_gpt4o(client_recap_prompt)
            json_response = parse_response_into_json(response)
            if json_response:  # if we got a valid JSON response
                return json_response
            logging.warning("Attempt %s: Invalid JSON response, retrying...", attempt + 1)
        except Exception as e:
            logging.error("Attempt %s failed with error: %s", attempt + 1, str(e))

    raise Exception("Failed to get valid JSON response after {} attempts".format(max_retries))


def __get_summaries_and_notes(user: User, client: Client) -> tuple[Iterable[Note], list[CRMNote], str | None]:
    crm_system_name = user.crm_configuration.get("crm_system", "unknown") if user.crm_configuration else "unknown"
    logging.info("Fetching CRM notes for client %s for crm %s", client.uuid, crm_system_name)
    crm_notes = user.crm_handler.fetch_notes_for_client(user, client, datetime.timedelta(days=365))
    logging.info("Fetched %s notes for crm %s for client %s", len(crm_notes), crm_system_name, client.uuid)

    notes = (
        Note.objects.filter(Q(client__uuid=str(client.uuid))).order_by("created").only("uuid", "summary").reverse()[:10]
    )

    logging.info("Fetched %s Zeplyn notes for client %s", len(notes), client.uuid)

    if not notes and not crm_notes:
        logging.error("No notes found for %s", client.uuid)
        return [], [], None

    basic_info = user.crm_handler.get_client_basic_info(client, user, include_household=True)
    return notes, crm_notes, str(basic_info) if basic_info else None


def build_client_recap_for_client(user: User, client: Client) -> dict[str, Any]:
    try:
        notes, crm_notes, structured_data = __get_summaries_and_notes(user, client)
        client_recap_summary = __create_client_recap_from_summaries(
            notes=notes, crm_notes=crm_notes, structured_data=structured_data or ""
        )
        return client_recap_summary
    except Exception as e:
        logging.error("Error building client recap: %s", str(e), exc_info=True)
        raise e


def generate_client_recap(client_recap_uuid: UUID, user_uuid: UUID) -> None:
    client_recap = ClientRecap.objects.get(uuid=client_recap_uuid)
    client_recap.status = ClientRecapStatus.PROCESSING
    client_recap.save()
    user = User.objects.get(uuid=user_uuid)
    client = client_recap.client
    try:
        client_recap_summary = build_client_recap_for_client(user, client)

        # Check if we got a valid structure with content
        if not __has_content(client_recap_summary):
            raise Exception("Client recap contained no meaningful content for client {}".format(client.uuid))

        logging.info("Client recap Summary generated for : %s", user.uuid)
        client_recap.summary = client_recap_summary
        client_recap.status = ClientRecapStatus.PROCESSED
        client_recap.save()

    except Exception as e:
        logging.error("Failed to generate client recap: %s", str(e), exc_info=True)
        client_recap.status = ClientRecapStatus.FAILED
        client_recap.save()


def convert_client_recap_to_markdown(json_data: dict[str, Any]) -> str:
    """
    Convert client recap JSON data to markdown format.

    Args:
        json_data: The structured data from the agenda template

    Returns:
        A markdown string with topics as headers and bullet points
    """
    if not json_data or not isinstance(json_data, dict) or "recap" not in json_data:
        return "No client recap data available."

    markdown = []

    for i, topic_section in enumerate(json_data.get("recap", [])):
        topic = topic_section.get("topic")
        bullets = topic_section.get("bullets", [])

        if topic:
            markdown.append(f"## {topic}")
            for bullet in bullets:
                bullet_text = bullet.get("text")
                if bullet_text:
                    markdown.append(f"- {bullet_text}")
            # Only add blank line if this is not the last section
            if i < len(json_data.get("recap", [])) - 1:
                markdown.append("")

    return "\n".join(markdown)
