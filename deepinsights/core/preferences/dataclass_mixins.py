from typing import Any, TypeVar

from dataclasses_json import DataClassJsonMixin, Undefined, config

T = TypeVar("T", bound="ZeplynDataclassJsonMixin")


def is_falsy(f: Any) -> bool:
    return not bool(f)


class ZeplynDataclassJsonMixin(DataClassJsonMixin):
    dataclass_json_config = config(undefined=Undefined.EXCLUDE, exclude=is_falsy)["dataclasses_json"]

    @classmethod
    def from_dict(
        cls: type[T], data: dict[Any, Any] | list[Any] | str | int | float | bool | None, infer_missing: bool = True
    ) -> T:
        return super().from_dict(data, infer_missing=infer_missing)

    def update(self, data: dict[str, Any] | T) -> None:
        if isinstance(data, dict):
            for key, value in data.items():
                if hasattr(self, key):
                    if isinstance(self.__getattribute__(key), ZeplynDataclassJsonMixin):
                        self.__getattribute__(key).update(value)
                    else:
                        setattr(self, key, value)
        if isinstance(data, self.__class__):
            for key in self.schema().declared_fields.keys():
                val = data.__getattribute__(key)
                if isinstance(val, ZeplynDataclassJsonMixin):
                    self.__getattribute__(key).update(val)
                elif val:
                    setattr(self, key, val)
