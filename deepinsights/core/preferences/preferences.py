from dataclasses import dataclass, field
from enum import StrEnum

from dataclasses_json.core import <PERSON>son

from deepinsights.core.integrations.meetingbot import recall_b64data
from deepinsights.core.preferences.dataclass_mixins import ZeplynDataclassJsonMixin


@dataclass
class SalesForceConfiguration(ZeplynDataclassJsonMixin):
    type: str = field(default="")
    salesforce_username: str = field(default="")
    salesforce_password: str = field(default="")
    salesforce_consumer_key: str = field(default="")
    salesforce_consumer_secret: str = field(default="")
    salesforce_security_token: str = field(default="")
    salesforce_endpoint: str = field(default="")


@dataclass
class RedtailConfiguration(ZeplynDataclassJsonMixin):
    user_key: str = field(default="")


@dataclass
class SharePointConfiguration(ZeplynDataclassJsonMixin):
    client_id: str = field(default="")
    client_secret: str = field(default="")
    site_url: str = field(default="")
    parent_folder: str = field(default="")


@dataclass
class DynamicsConfiguration(ZeplynDataclassJsonMixin):
    type: str = field(default="")
    dynamics_resource_url: str = field(default="")


@dataclass
class SyncPreferences(ZeplynDataclassJsonMixin):
    advisor_certification_required: bool = field(default=False)
    advisor_certification_synced_text: str = field(
        default="Reviewed and certified by {user_email}, at {timestamp_with_tz}"
    )
    advisor_certification_display_text: str = field(
        default="I certify that the summary of notes has been reviewed and verified, and is accurate to the best of my knowledge."
    )


class DefaultClientFilter(StrEnum):
    ALL = "all"
    OWNED = "owned"

    @classmethod
    def _missing_(cls, _: object) -> "DefaultClientFilter":
        return cls.ALL


@dataclass
class CrmConfiguration(ZeplynDataclassJsonMixin):
    crm_system: str = field(default="")
    default_client_filter: DefaultClientFilter = field(default=DefaultClientFilter.ALL)
    salesforce: SalesForceConfiguration = field(default_factory=SalesForceConfiguration)
    redtail: RedtailConfiguration = field(default_factory=RedtailConfiguration)
    sharepoint: SharePointConfiguration = field(default_factory=SharePointConfiguration)
    dynamics: DynamicsConfiguration = field(default_factory=DynamicsConfiguration)
    summary_template: str = field(default="")


@dataclass
class EmailConfiguration(ZeplynDataclassJsonMixin):
    ccs: list[str] = field(default_factory=list)
    bccs: list[str] = field(default_factory=list)
    followup_email_format_prompt: str = field(default="")
    meeting_notes_email_template: str = field(default="")


@dataclass
class BotPreferences(ZeplynDataclassJsonMixin):
    not_recording_image_b64: str = field(default="")
    recording_image_b64: str = field(default="")
    recording_message_b64: str = field(default="")
    notetaker_name: str = field(default="")
    enable_video: bool = field(default=False)
    enable_audio_output: bool = field(default=False)
    enable_recording_free_zoom: bool = field(default=False)

    def recording_image_b64_or_default(self) -> str:
        """Return the recording image in base64 format or a default image if one is not set."""
        return self.recording_image_b64 or recall_b64data.recording_default_jpeg

    def not_recording_image_b64_or_default(self) -> str:
        """Return the not recording image in base64 format or a default image if one is not set."""
        return self.not_recording_image_b64 or recall_b64data.not_recording_default_jpeg

    def recording_message_b64_or_default(self) -> str:
        """Return the recording message in base64 format or a default message if one is not set."""
        return self.recording_message_b64 or recall_b64data.start_audio


@dataclass
class NotificationPreferences(ZeplynDataclassJsonMixin):
    meeting_processed_notification_enabled: bool = field(default=False)
    email_meeting_notes_post_sync: bool = field(default=False)
    mid_meeting_nudge_minutes_before_end: int = field(default=10)


@dataclass
class Preferences(ZeplynDataclassJsonMixin):
    attach_transcript_to_follow_up_emails: bool = field(default=False)
    show_transcript_in_frontend: bool = field(default=False)
    send_followup_email: bool = field(default=False)
    send_task_reminder_email: bool = field(default=False)
    delete_buffer: bool = field(default=False)
    due_date_offset_seconds: int = field(default=0)
    email_settings: EmailConfiguration = field(default_factory=EmailConfiguration)
    bot_preferences: BotPreferences = field(default_factory=BotPreferences)
    asr_language_code: str = field(default="")
    sync_preferences: SyncPreferences = field(default_factory=SyncPreferences)
    notification_preferences: NotificationPreferences = field(default_factory=NotificationPreferences)


def get_default_crm_configuration() -> dict[str, Json]:
    return CrmConfiguration().to_dict()


def get_default_preferences() -> dict[str, Json]:
    p = Preferences()
    p.delete_buffer = True
    p.asr_language_code = "en"
    p.bot_preferences.enable_video = True
    p.bot_preferences.enable_audio_output = True
    p.show_transcript_in_frontend = True
    p.notification_preferences.mid_meeting_nudge_minutes_before_end = 10
    return p.to_dict()
