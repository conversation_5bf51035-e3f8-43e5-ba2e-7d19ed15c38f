import inspect
from typing import Any

import pytest

from deepinsights.core.integrations.meetingbot import recall_b64data
from deepinsights.core.preferences import preferences
from deepinsights.core.preferences.dataclass_mixins import ZeplynDataclassJsonMixin

list_of_preferences = [
    field[1]
    for field in inspect.getmembers(
        preferences,
        lambda x: isinstance(x, type) and issubclass(x, ZeplynDataclassJsonMixin) and not x == ZeplynDataclassJsonMixin,
    )
]


@pytest.mark.parametrize("cls", list_of_preferences)
def test_dataclass_initialization(cls: Any) -> None:
    obj = cls()
    assert isinstance(obj.to_dict(), dict)  # Just verify the object can be initialized and converted to dict

    match cls:
        case preferences.Preferences:
            assert obj.to_dict() == {
                "sync_preferences": {
                    "advisor_certification_synced_text": "Reviewed and certified by {user_email}, at {timestamp_with_tz}",
                    "advisor_certification_display_text": "I certify that the summary of notes has been reviewed and verified, and is accurate to the best of my knowledge.",
                },
                "notification_preferences": {"mid_meeting_nudge_minutes_before_end": 10},
            }, f"{cls.__name__} should have the default values by default"
        case preferences.SyncPreferences:
            assert obj.to_dict() == {
                "advisor_certification_synced_text": "Reviewed and certified by {user_email}, at {timestamp_with_tz}",
                "advisor_certification_display_text": "I certify that the summary of notes has been reviewed and verified, and is accurate to the best of my knowledge.",
            }, f"{cls.__name__} should have the default values by default"
        case preferences.CrmConfiguration:
            assert obj.to_dict() == {
                "default_client_filter": "all",
            }, f"{cls.__name__} should have the default values by default"
        case preferences.NotificationPreferences:
            assert obj.to_dict() == {"mid_meeting_nudge_minutes_before_end": 10}
        case _:
            assert obj.to_dict() == {}, f"{cls.__name__} should have an empty dict by default"


def test_get_default_crm_configuration() -> None:
    obj = preferences.get_default_crm_configuration()
    assert obj == {"default_client_filter": "all"}


def test_get_default_preferences() -> None:
    obj = preferences.get_default_preferences()
    assert obj == {
        "show_transcript_in_frontend": True,
        "delete_buffer": True,
        "bot_preferences": {"enable_video": True, "enable_audio_output": True},
        "asr_language_code": "en",
        "sync_preferences": {
            "advisor_certification_synced_text": "Reviewed and certified by {user_email}, at {timestamp_with_tz}",
            "advisor_certification_display_text": "I certify that the summary of notes has been reviewed and verified, and is accurate to the best of my knowledge.",
        },
        "notification_preferences": {
            "mid_meeting_nudge_minutes_before_end": 10,
        },
    }


class TestBotPreferences:
    def test_bot_preferences_initialization(self) -> None:
        obj = preferences.BotPreferences()
        assert obj.to_dict() == {}

    def test_recording_image_b64_or_default_default(self) -> None:
        obj = preferences.BotPreferences()
        assert obj.recording_image_b64_or_default() == recall_b64data.recording_default_jpeg

    def test_recording_image_b64_or_default_custom(self) -> None:
        obj = preferences.BotPreferences(recording_image_b64="test")
        assert obj.recording_image_b64_or_default() == "test"

    def test_not_recording_image_b64_or_default_default(self) -> None:
        obj = preferences.BotPreferences()
        assert obj.not_recording_image_b64_or_default() == recall_b64data.not_recording_default_jpeg

    def test_not_recording_image_b64_or_default_custom(self) -> None:
        obj = preferences.BotPreferences(not_recording_image_b64="test")
        assert obj.not_recording_image_b64_or_default() == "test"
