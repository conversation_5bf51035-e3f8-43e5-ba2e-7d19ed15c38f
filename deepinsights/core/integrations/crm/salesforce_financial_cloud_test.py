from datetime import datetime, <PERSON><PERSON><PERSON>
from datetime import timezone as datetime_timezone
from typing import <PERSON>ple
from unittest.mock import MagicMock, patch
from uuid import uuid4

from django.core import cache
from django.test import TestCase, override_settings
from django.utils import timezone
from simple_mockforce import mock_salesforce
from simple_salesforce.api import Salesforce
from simple_salesforce.exceptions import SalesforceResourceNotFound

from deepinsights.core.integrations.crm.crm_models import CRMAccount, CRMNote
from deepinsights.core.integrations.crm.salesforce_financial_cloud import SalesforceFinancialCloud
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.flags import Flag
from deepinsights.users.models.user import User


class SalesforceFinancialCloudTestCase(TestCase):
    def setUp(self) -> None:
        cache.cache.clear()
        self.simple_salesforce = Salesforce(instance="test.salesforce.com", session_id="")
        self.salesforce = SalesforceFinancialCloud(sf=self.simple_salesforce)
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, False)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, False)

    # Sets the provided flag to the given active state.
    def setFlagActive(self, flag: Flags, is_active: bool) -> None:
        f = Flag.objects.get(name=flag.name)
        f.everyone = is_active
        f.override_enabled_by_environment = None  # To ensure no environment override
        f.save()

    # Populate the required Salesforce types in the mock, so that they can be used in queries even
    # if they have never been created.
    def populateSalesforceTypes(self) -> None:
        for type in [
            self.simple_salesforce.Interaction,
            self.simple_salesforce.InteractionSummary,
            self.simple_salesforce.Task,
            self.simple_salesforce.User,
        ]:
            id = type.create({})["id"]  # type: ignore[operator]
            type.delete(id)  # type: ignore[operator]

    # Creates a new account with the given name (or a randomized name if the name parameter is None).
    def newAccountAndClient(
        self, name: str | None, owner_email: str, org: Organization, *, phone: str | None = None
    ) -> Tuple[str, Client]:
        account_name = name.replace("'", "\\'") if name else f"Test Account {uuid4()}"
        account_data = {
            "Name": account_name,
            "Owner.Email": owner_email.replace("'", "\\'"),
        }
        if phone:
            account_data["Phone"] = phone
        account = self.simple_salesforce.Account.create(account_data)  # type: ignore[operator]
        self.assertFalse(account["errors"])
        client = Client.objects.create(crm_id=account["id"], name=account_name, organization=org)
        return account["id"], client

    # Creates a new arbitrary user, both in the Salesforce and Zeplyn databases.
    #
    # Returns a tuple of (salesforceUserId, userEmail).
    def newUser(self, org: Organization | None = None) -> Tuple[str, str]:
        email = f"{uuid4()}@example.com"
        user = self.simple_salesforce.User.create({"Email": email})  # type: ignore[operator]
        zeplyn_user = User.objects.create(username=email, email=email, organization=org)
        zeplyn_user.crm_configuration["crm_system"] = "salesforce"
        zeplyn_user.save()
        return (user["id"], email)

    @override_settings(SALESFORCE_USER="user", SALESFORCE_PASSWORD="password", SALESFORCE_INSTANCE="instance")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_from_settings(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        SalesforceFinancialCloud(user=user)
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="", instance_url="instance"
        )

    @override_settings(
        SALESFORCE_USER="settings_user",
        SALESFORCE_PASSWORD="settings_password",
        SALESFORCE_INSTANCE="settings_instance",
    )
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_with_params(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        SalesforceFinancialCloud(
            user=user, username="user", password="password", security_token="token", instance_url="instance"
        )
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_for_comsumer_key_missing_values(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        SalesforceFinancialCloud(
            user=user, username="user", password="password", security_token="token", instance_url="instance"
        )
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @override_settings(SALESFORCE_ZEPLYN_APP_DOMAIN="app_domain")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_for_comsumer_key_flag_disabled(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, False)
        SalesforceFinancialCloud(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @override_settings(SALESFORCE_ZEPLYN_APP_DOMAIN="app_domain")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_for_comsumer_key(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        SalesforceFinancialCloud(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
            security_token="token",
            instance_url="instance",
            domain="app_domain",
        )

    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_for_user_oauth_missing_values(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, True)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        SalesforceFinancialCloud(
            user=user, username="user", password="password", security_token="token", instance_url="instance"
        )
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @override_settings(SALESFORCE_ZEPLYN_APP_DOMAIN="app_domain")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.SalesforceOAuth")
    def test_init_for_user_oauth_flag_disabled(self, sf_oauth: MagicMock, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, False)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, False)
        sf_oauth.return_value.get_access_token.return_value = "access_token"
        SalesforceFinancialCloud(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @override_settings(SALESFORCE_ZEPLYN_APP_DOMAIN="app_domain")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.SalesforceOAuth")
    def test_init_for_user_oauth_token_exception(self, sf_oauth: MagicMock, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, True)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        sf_oauth.return_value.get_access_token.side_effect = Exception("Test error")
        SalesforceFinancialCloud(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
            security_token="token",
            instance_url="instance",
            domain="app_domain",
        )

    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.SalesforceOAuth")
    def test_init_for_user_oauth(self, sf_oauth: MagicMock, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, True)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        sf_oauth.return_value.get_access_token.return_value = "access_token"
        SalesforceFinancialCloud(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(session_id="access_token", instance_url="instance")

    @mock_salesforce
    def test_get_accounts_by_owner_email_and_name(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        owner_one = "<EMAIL>"
        owner_two = "<EMAIL>"

        user1_id = self.simple_salesforce.User.create({"Email": owner_one})["id"]  # type: ignore[operator]
        user2_id = self.simple_salesforce.User.create({"Email": owner_two})["id"]  # type: ignore[operator]

        (account_one_id, _) = self.newAccountAndClient("one", owner_one, org=org, phone="**********")
        (account_two_id, _) = self.newAccountAndClient("two", owner_one, org=org, phone="invalid")
        (account_three_id, _) = self.newAccountAndClient("three", owner_two, org=org, phone="+*************")

        self.simple_salesforce.Account.update(account_one_id, {"OwnerId": user1_id})  # type: ignore[operator]
        self.simple_salesforce.Account.update(account_two_id, {"OwnerId": user1_id})  # type: ignore[operator]
        self.simple_salesforce.Account.update(account_three_id, {"OwnerId": user2_id})  # type: ignore[operator]

        expected_accounts_for_owner_one = [
            CRMAccount(
                crm_id=account_one_id,
                name="one",
                phone_number="**********",
                client_type="account",
                crm_system="salesforce",
                is_owned_by_user=True,
            ),
            CRMAccount(
                crm_id=account_two_id,
                name="two",
                client_type="account",
                crm_system="salesforce",
                is_owned_by_user=True,
            ),
            CRMAccount(
                crm_id=account_three_id,
                name="three",
                phone_number="+*************",
                client_type="account",
                crm_system="salesforce",
                is_owned_by_user=False,
            ),
        ]

        expected_accounts_for_owner_two = [
            CRMAccount(
                crm_id=account_one_id,
                name="one",
                phone_number="**********",
                client_type="account",
                crm_system="salesforce",
                is_owned_by_user=False,
            ),
            CRMAccount(
                crm_id=account_two_id,
                name="two",
                client_type="account",
                crm_system="salesforce",
                is_owned_by_user=False,
            ),
            CRMAccount(
                crm_id=account_three_id,
                name="three",
                phone_number="+*************",
                client_type="account",
                crm_system="salesforce",
                is_owned_by_user=True,
            ),
        ]

        actual_accounts_owner_one = self.salesforce.get_accounts_by_owner_email_and_name(owner_one)
        self.assertEqual(actual_accounts_owner_one, expected_accounts_for_owner_one)

        actual_accounts_owner_two = self.salesforce.get_accounts_by_owner_email_and_name(owner_two)
        self.assertEqual(actual_accounts_owner_two, expected_accounts_for_owner_two)

    @mock_salesforce
    def test_get_accounts_by_owner_email_and_name_with_quotes(self) -> None:
        self.populateSalesforceTypes()

        owner_one = "<EMAIL>"
        org = Organization.objects.create(name="Test Organization")

        user_id = self.simple_salesforce.User.create({"Email": owner_one})["id"]  # type: ignore[operator]

        (account_one_id, _) = self.newAccountAndClient("o'n'e", owner_one, org)
        self.simple_salesforce.Account.update(account_one_id, {"OwnerId": user_id})  # type: ignore[operator]
        self.assertTrue(account_one_id)
        self.assertEqual(
            self.salesforce.get_accounts_by_owner_email_and_name(owner_one, "o'"),
            [
                CRMAccount(
                    crm_id=account_one_id,
                    name="o\\'n\\'e",
                    client_type="account",
                    crm_system="salesforce",
                    is_owned_by_user=True,
                ),
            ],
        )

    @mock_salesforce
    def test_add_interaction_with_client_with_no_account(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>", organization=org)
        client = Client.objects.create(crm_id="123", name="Client", organization=org)
        note = Note(
            note_owner=user,
            status="scheduled",
            client={"uuid": str(client.uuid)},
            metadata={},
        )

        with self.assertRaisesRegex(
            ValueError, "Could not find a Salesforce User with the given email: <EMAIL>"
        ):
            self.salesforce.add_interaction_with_client(note)

        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Interaction")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM InteractionSummary")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"])

    @mock_salesforce
    def test_add_interaction_with_client_with_no_client_id(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        (_, user_email) = self.newUser(org=org)
        self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
        )

        with self.assertRaisesRegex(ValueError, "Note does not have an associated Salesforce CRM client."):
            self.salesforce.add_interaction_with_client(note)

        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Interaction")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM InteractionSummary")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"])

    @mock_salesforce
    def test_add_interaction_creation_with_salesforce_error(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        (_, user_email) = self.newUser(org)
        _, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )
        note.save()

        class MockInteraction:
            def create(self, *args, **kwargs):  # type: ignore[no-untyped-def]
                raise AssertionError("Test error")

        self.simple_salesforce.Interaction = MockInteraction  # type: ignore[attr-defined]
        with self.assertRaisesRegex(RuntimeError, "error recording interaction: Test error. Not updating note."):
            self.salesforce.add_interaction_with_client(note)

        note.refresh_from_db()
        self.assertFalse(note.metadata.get("interactionId"))  # type: ignore[union-attr]
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Interaction")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM InteractionSummary")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"])

    @mock_salesforce
    def test_add_interaction_with_client_creates_interaction(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        self.salesforce.add_interaction_with_client(note)
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM Interaction")["records"]), 1)
        interaction = self.simple_salesforce.Interaction.get(note.metadata["interactionId"])  # type: ignore[index, operator]
        self.assertEqual(interaction["AccountId"], account_id)

    @mock_salesforce
    def test_add_interaction_uses_uuid_as_crm_id(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        account_id, _ = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": account_id},
            created=timezone.now(),
        )

        self.salesforce.add_interaction_with_client(note)
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM Interaction")["records"]), 1)
        interaction = self.simple_salesforce.Interaction.get(note.metadata["interactionId"])  # type: ignore[index, operator]
        self.assertEqual(interaction["AccountId"], account_id)

    @mock_salesforce
    def test_add_interaction_with_client_populates_meeting_title(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        _, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={"meeting_name": "Meeting"},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        self.salesforce.add_interaction_with_client(note)
        interaction = self.simple_salesforce.Interaction.get(note.metadata["interactionId"])  # type: ignore[index, operator]
        self.assertEqual(interaction["Name"], "Meeting")

    @mock_salesforce
    def test_add_interaction_with_client_does_not_update_interaction(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        _, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )
        self.salesforce.add_interaction_with_client(note)

        # Adding a new interaction for the same note should not change anything in the Salesforce database.
        note.metadata["meeting_name"] = "New meeting name"  # type: ignore[index]
        self.salesforce.add_interaction_with_client(note)
        interaction = self.simple_salesforce.Interaction.get(note.metadata["interactionId"])  # type: ignore[index, operator]
        self.assertEqual(interaction["Name"], "Meeting with a client")

        # Remove the link between the meeting and the interaction.
        note.metadata["interactionId"] = None  # type: ignore[index]
        self.salesforce.add_interaction_with_client(note)
        interaction = self.simple_salesforce.Interaction.get(note.metadata["interactionId"])  # type: ignore[index, operator]
        self.assertEqual(interaction["Name"], "New meeting name")

    @mock_salesforce
    def test_add_interaction_with_client_generates_interaction_summary(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={"meeting_name": "Meeting"},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        self.salesforce.add_interaction_with_client(note)
        interaction_summary = self.simple_salesforce.InteractionSummary.get(note.metadata["interactionSummaryId"])  # type: ignore[index, operator]
        self.assertEqual(interaction_summary["OwnerId"], user_id)
        self.assertEqual(interaction_summary["Name"], "Meeting")
        self.assertEqual(interaction_summary["InteractionId"], note.metadata["interactionId"])  # type: ignore[index]
        self.assertIsNotNone(interaction_summary["MeetingNotes"])
        self.assertEqual(interaction_summary["AccountId"], account_id)
        self.assertEqual(interaction_summary["NextSteps"], "No next steps identified")

    @mock_salesforce
    def test_add_interaction_with_client_does_not_update_interaction_summary(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        _, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={"meeting_name": "Meeting"},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        self.salesforce.add_interaction_with_client(note)

        # Adding a new interaction for the same note should not change the summary in the Salesforce database.
        note.metadata["meeting_name"] = "New meeting name"  # type: ignore[index]
        self.salesforce.add_interaction_with_client(note)
        interaction_summary = self.simple_salesforce.InteractionSummary.get(note.metadata["interactionSummaryId"])  # type: ignore[index, operator]
        self.assertEqual(interaction_summary["Name"], "Meeting")

        # Remove the link between the meeting and the interaction summary.
        note.metadata["interactionSummaryId"] = None  # type: ignore[index]
        self.salesforce.add_interaction_with_client(note)
        interaction_summary = self.simple_salesforce.InteractionSummary.get(note.metadata["interactionSummaryId"])  # type: ignore[index, operator]
        self.assertEqual(interaction_summary["Name"], "New meeting name")

    @mock_salesforce
    def test_add_interaction_with_client_summary_meeting_notes(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        user = User.objects.get(email=user_email)
        note = Note(
            note_owner=user,
            status="scheduled",
            metadata={"meeting_name": "Meeting"},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )
        user.crm_configuration["crm_system"] = "salesforce"

        self.salesforce.add_interaction_with_client(note)
        interaction_summary = self.simple_salesforce.InteractionSummary.get(note.metadata["interactionSummaryId"])  # type: ignore[index, operator]
        self.assertEqual(interaction_summary["OwnerId"], user_id)
        self.assertEqual(interaction_summary["Name"], "Meeting")
        self.assertEqual(interaction_summary["InteractionId"], note.metadata["interactionId"])  # type: ignore[index]
        self.assertIsNotNone(interaction_summary["MeetingNotes"])
        self.assertEqual(interaction_summary["AccountId"], account_id)
        self.assertEqual(interaction_summary["NextSteps"], "No next steps identified")

    @mock_salesforce
    def test_add_interaction_creation_with_salesforce_summary_error(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        _, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        def raiseException(*args, **kwargs):  # type: ignore[no-untyped-def]
            raise AssertionError("Test error")

        raiseException.create = raiseException  # type: ignore[attr-defined]
        self.simple_salesforce.InteractionSummary = raiseException  # type: ignore[attr-defined]

        with self.assertRaisesRegex(
            RuntimeError, "error recording interaction summary: Test error. Not updating note."
        ):
            self.salesforce.add_interaction_with_client(note)

        note.refresh_from_db()
        self.assertTrue(self.simple_salesforce.query_all("SELECT Id FROM Interaction")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM InteractionSummary")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"])
        self.assertTrue(note.metadata.get("interactionId"))  # type: ignore[union-attr]
        self.assertFalse(note.metadata.get("interactionSummaryId"))  # type: ignore[union-attr]

    @mock_salesforce
    def test_add_interaction_creation_task_creation(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )
        note.save()

        task = Task(task_title="Task One", note=note)
        no_metadata_task = Task(task_title="Task Two", task_desc="Task two description", note=note, metadata=None)
        completed_task = Task(
            task_title="Completed Task", task_desc="Completed task description", note=note, completed=True
        )
        already_existing_task = Task(
            task_title="Already exsting Task",
            task_desc="Already existing task description",
            note=note,
            metadata={"taskId": "123"},
        )
        task.save()
        no_metadata_task.save()
        completed_task.save()
        already_existing_task.save()

        note.task_set.add(task)
        note.task_set.add(no_metadata_task)
        note.task_set.add(completed_task)
        note.task_set.add(already_existing_task)

        self.salesforce.add_interaction_with_client(note)

        self.assertTrue(len(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"]), 3)

        task = Task.objects.get(pk=task.pk)
        salesforce_task = self.simple_salesforce.Task.get(task.metadata["taskId"])  # type: ignore[index, operator]
        self.assertEqual(salesforce_task["Subject"], "Task One")
        self.assertEqual(salesforce_task["Description"], "Follow up from client meeting.")
        self.assertEqual(salesforce_task["Status"], "Not Started")
        self.assertEqual(salesforce_task["Priority"], "Normal")
        self.assertEqual(salesforce_task["WhatId"], account_id)
        self.assertEqual(
            salesforce_task["ActivityDate"],
            (datetime.today().date() + timedelta(7)).strftime("%Y-%m-%dT%H:%M:%S.%f%z"),
        )
        self.assertEqual(salesforce_task["OwnerId"], user_id)

        no_metadata_task = Task.objects.get(pk=no_metadata_task.pk)
        salesforce_no_metadata_task = self.simple_salesforce.Task.get(no_metadata_task.metadata["taskId"])  # type: ignore[index, operator]
        self.assertEqual(salesforce_no_metadata_task["Subject"], "Task Two")
        self.assertEqual(
            salesforce_no_metadata_task["Description"], "Follow up from client meeting.\nTask two description"
        )
        self.assertEqual(salesforce_no_metadata_task["Status"], "Not Started")
        self.assertEqual(salesforce_no_metadata_task["Priority"], "Normal")
        self.assertEqual(salesforce_no_metadata_task["WhatId"], account_id)
        self.assertEqual(
            salesforce_task["ActivityDate"],
            (datetime.today().date() + timedelta(7)).strftime("%Y-%m-%dT%H:%M:%S.%f%z"),
        )
        self.assertEqual(salesforce_task["OwnerId"], user_id)

        completed_task = Task.objects.get(pk=completed_task.pk)
        salesforce_completed_task = self.simple_salesforce.Task.get(completed_task.metadata["taskId"])  # type: ignore[index, operator]
        self.assertEqual(salesforce_completed_task["Subject"], "Completed Task")
        self.assertEqual(
            salesforce_completed_task["Description"], "Follow up from client meeting.\nCompleted task description"
        )
        self.assertEqual(salesforce_completed_task["Status"], "Completed")
        self.assertEqual(salesforce_completed_task["Priority"], "Normal")
        self.assertEqual(salesforce_completed_task["WhatId"], account_id)
        self.assertEqual(
            salesforce_task["ActivityDate"],
            (datetime.today().date() + timedelta(7)).strftime("%Y-%m-%dT%H:%M:%S.%f%z"),
        )
        self.assertEqual(salesforce_task["OwnerId"], user_id)

        already_existing_task = Task.objects.get(pk=already_existing_task.pk)
        with self.assertRaises(SalesforceResourceNotFound):
            self.simple_salesforce.Task.get(already_existing_task.metadata["taskId"])  # type: ignore[index, operator]

    @mock_salesforce
    def test_preview_before_syncing_with_crm(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="completed",
            metadata={"meeting_name": "Test Meeting", "interactionId": "test_interaction_id"},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        Task.objects.create(task_title="Task 1", task_desc="Description 1", note=note)
        Task.objects.create(task_title="Task 2", task_desc="Description 2", note=note, completed=True)

        with patch.object(Note, "get_summary_for_crm", return_value="Test summary"):
            preview = self.salesforce.preview_before_syncing_with_crm(note)

        self.assertIsNotNone(preview)
        self.assertEqual(preview["Owner ID"], user_id)
        self.assertEqual(preview["Name"], "Test Meeting")
        self.assertEqual(preview["Meeting Notes"], "Test summary")
        self.assertEqual(preview["Account Id"], account_id)
        self.assertEqual(preview["Next Steps"], "*Task 1\n*Task 2")

        self.assertEqual(len(preview["tasks"]), 2)
        self.assertEqual(preview["tasks"][0]["Subject"], "Task 1")
        self.assertEqual(preview["tasks"][0]["Description"], "Follow up from client meeting.\nDescription 1")
        self.assertEqual(preview["tasks"][0]["Status"], "Not Started")
        self.assertEqual(preview["tasks"][0]["Related To"], account_id)
        self.assertEqual(preview["tasks"][0]["Owner Email"], user_email)

        self.assertEqual(preview["tasks"][1]["Subject"], "Task 2")
        self.assertEqual(preview["tasks"][1]["Description"], "Follow up from client meeting.\nDescription 2")
        self.assertEqual(preview["tasks"][1]["Status"], "Completed")

    @mock_salesforce
    def test_preview_before_syncing_with_crm_uses_uuid_as_crm_id(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, _ = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="completed",
            metadata={"meeting_name": "Test Meeting", "interactionId": "test_interaction_id"},
            client={"uuid": account_id},
            created=timezone.now(),
        )

        Task.objects.create(task_title="Task 1", task_desc="Description 1", note=note)
        Task.objects.create(task_title="Task 2", task_desc="Description 2", note=note, completed=True)

        with patch.object(Note, "get_summary_for_crm", return_value="Test summary"):
            preview = self.salesforce.preview_before_syncing_with_crm(note)

        self.assertIsNotNone(preview)
        self.assertEqual(preview["Owner ID"], user_id)
        self.assertEqual(preview["Name"], "Test Meeting")
        self.assertEqual(preview["Meeting Notes"], "Test summary")
        self.assertEqual(preview["Account Id"], account_id)
        self.assertEqual(preview["Next Steps"], "*Task 1\n*Task 2")

        self.assertEqual(len(preview["tasks"]), 2)
        self.assertEqual(preview["tasks"][0]["Subject"], "Task 1")
        self.assertEqual(preview["tasks"][0]["Description"], "Follow up from client meeting.\nDescription 1")
        self.assertEqual(preview["tasks"][0]["Status"], "Not Started")
        self.assertEqual(preview["tasks"][0]["Related To"], account_id)
        self.assertEqual(preview["tasks"][0]["Owner Email"], user_email)

        self.assertEqual(preview["tasks"][1]["Subject"], "Task 2")
        self.assertEqual(preview["tasks"][1]["Description"], "Follow up from client meeting.\nDescription 2")
        self.assertEqual(preview["tasks"][1]["Status"], "Completed")

    @mock_salesforce
    def test_preview_before_syncing_with_crm_no_owner(self) -> None:
        self.populateSalesforceTypes()
        note = Note.objects.create(
            note_owner=User.objects.create(email="<EMAIL>"),
            status="completed",
            metadata={},
            client={"uuid": "invalid"},
            created=timezone.now(),
        )

        preview = self.salesforce.preview_before_syncing_with_crm(note)
        self.assertEqual(preview, {})

    @mock_salesforce
    def test_preview_before_syncing_with_crm_no_client(self) -> None:
        self.populateSalesforceTypes()
        (_, user_email) = self.newUser()
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="completed",
            metadata={},
            client=None,
            created=timezone.now(),
        )

        preview = self.salesforce.preview_before_syncing_with_crm(note)
        self.assertEqual(preview, {})

    @mock_salesforce
    def test_preview_before_syncing_with_crm_no_interaction_id(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        _, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="uploaded",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        preview = self.salesforce.preview_before_syncing_with_crm(note)
        self.assertEqual(preview, {})

    @mock_salesforce
    def test_preview_before_syncing_with_crm_exception_handling(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        _, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="completed",
            metadata={"interactionId": "test_interaction_id"},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        with patch.object(Note, "get_summary_for_crm", side_effect=Exception("Test exception")):
            with self.assertRaises(Exception):
                self.salesforce.preview_before_syncing_with_crm(note)

    @mock_salesforce
    def test_fetch_notes_for_client_successful(self) -> None:
        """Test successful retrieval of notes with interactions and summaries"""
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        interaction_start_time = datetime.now(datetime_timezone.utc)
        # Create test interaction
        interaction = self.simple_salesforce.Interaction.create(  # type: ignore[operator]
            {
                "Name": "Test Interaction",
                "AccountId": account_id,
                "StartTime": interaction_start_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
            }
        )

        # Create test interaction summary
        summary = self.simple_salesforce.InteractionSummary.create(  # type: ignore[operator]
            {
                "Name": "Test Summary",
                "InteractionId": interaction["id"],
                "AccountId": account_id,
                "MeetingNotes": "Test meeting notes",
                "NextSteps": "Test next steps",
            }
        )

        # Fetch notes
        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

        self.assertEqual(
            notes,
            [
                CRMNote(
                    crm_id=interaction["id"],
                    crm_system="salesforce",
                    content="Test meeting notes\nTest next steps",
                    created_at=interaction_start_time,
                    web_link=f"https://test.salesforce.com/{interaction['id']}",
                )
            ],
        )

    @mock_salesforce
    def test_fetch_notes_for_client_no_interactions(self) -> None:
        """Test when no interactions are found"""
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        _, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))
        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_notes_for_client_no_summaries(self) -> None:
        """Test when interactions exist but no summaries are found"""
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        # Create test interaction without summary
        interaction_start_time = datetime.now(datetime_timezone.utc)
        interaction = self.simple_salesforce.Interaction.create(  # type: ignore[operator]
            {
                "Name": "Test Interaction",
                "AccountId": account_id,
                "StartTime": interaction_start_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
            }
        )

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

        self.assertEqual(
            notes,
            [
                CRMNote(
                    crm_id=interaction["id"],
                    crm_system="salesforce",
                    content="No notes available\nNo next steps available",
                    created_at=interaction_start_time,
                    web_link=f"https://test.salesforce.com/{interaction['id']}",
                )
            ],
        )

    @mock_salesforce
    def test_fetch_notes_for_client_no_crm_id(self) -> None:
        """Test when client has no CRM ID"""
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        client = Client.objects.create(name="Test Client", organization=org)  # No CRM ID

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))
        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_notes_for_client_date_filter(self) -> None:
        """Test that the date filter correctly excludes old interactions"""
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        # Create old interaction (more than 30 days ago)
        old_date = (datetime.now(datetime_timezone.utc) - timedelta(days=40)).strftime("%Y-%m-%dT%H:%M:%S.%f%z")
        self.simple_salesforce.Interaction.create(  # type: ignore[operator]
            {"Name": "Old Interaction", "AccountId": account_id, "StartTime": old_date}
        )

        # Create recent interaction
        recent_date = datetime.now(datetime_timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f%z")
        recent_interaction = self.simple_salesforce.Interaction.create(  # type: ignore[operator]
            {"Name": "Recent Interaction", "AccountId": account_id, "StartTime": recent_date}
        )

        # Test with 30 day filter
        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=30))
        self.assertEqual(len(notes), 1)
        self.assertEqual(notes[0].crm_id, recent_interaction["id"])

    @mock_salesforce
    def test_fetch_notes_for_client_salesforce_error(self) -> None:
        """Test handling of Salesforce API errors"""
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        _, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        def raise_error(*args, **kwargs):  # type: ignore[no-untyped-def]
            raise Exception("Salesforce API Error")

        # Mock Salesforce query to raise an error
        self.simple_salesforce.query_all = raise_error  # type: ignore[method-assign]

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))
        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_notes_for_client_multiple_interactions(self) -> None:
        """Test fetching multiple interactions and summaries"""
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        # Create multiple interactions and summaries
        interaction_ids: list[str] = []
        interaction_start_time = datetime.now(datetime_timezone.utc)
        for i in range(3):
            interaction = self.simple_salesforce.Interaction.create(  # type: ignore[operator]
                {
                    "Name": f"Test Interaction {i}",
                    "AccountId": account_id,
                    "StartTime": interaction_start_time.strftime("%Y-%m-%dT%H:%M:%S.%f%z"),
                }
            )

            self.simple_salesforce.InteractionSummary.create(  # type: ignore[operator]
                {
                    "Name": f"Test Summary {i}",
                    "InteractionId": interaction["id"],
                    "AccountId": account_id,
                    "MeetingNotes": f"Test meeting notes {i}",
                    "NextSteps": f"Test next steps {i}",
                }
            )

            interaction_ids.append(interaction["id"])

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

        self.assertEqual(
            notes,
            [
                CRMNote(
                    crm_id=interaction_ids[0],
                    crm_system="salesforce",
                    content="Test meeting notes 0\nTest next steps 0",
                    created_at=interaction_start_time,
                    web_link=f"https://test.salesforce.com/{interaction_ids[0]}",
                ),
                CRMNote(
                    crm_id=interaction_ids[1],
                    crm_system="salesforce",
                    content="Test meeting notes 1\nTest next steps 1",
                    created_at=interaction_start_time,
                    web_link=f"https://test.salesforce.com/{interaction_ids[1]}",
                ),
                CRMNote(
                    crm_id=interaction_ids[2],
                    crm_system="salesforce",
                    content="Test meeting notes 2\nTest next steps 2",
                    created_at=interaction_start_time,
                    web_link=f"https://test.salesforce.com/{interaction_ids[2]}",
                ),
            ],
        )

    @mock_salesforce
    def test_fetch_notes_for_client_invalid_date(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_email=user_email, org=org)

        interaction = self.simple_salesforce.Interaction.create(  # type: ignore[operator]
            {"Name": "Test Interaction", "AccountId": account_id, "StartTime": "invalid"}
        )
        self.simple_salesforce.InteractionSummary.create(  # type: ignore[operator]
            {
                "Name": "Test Summary",
                "InteractionId": interaction["id"],
                "AccountId": account_id,
                "MeetingNotes": "Test meeting notes",
                "NextSteps": "Test next steps",
            }
        )

        self.assertEqual(
            self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365)),
            [
                CRMNote(
                    crm_id=interaction["id"],
                    crm_system="salesforce",
                    content="Test meeting notes\nTest next steps",
                    created_at=None,
                    web_link=f"https://test.salesforce.com/{interaction['id']}",
                )
            ],
        )
