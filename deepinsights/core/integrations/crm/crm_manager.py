import logging

from asgiref.sync import sync_to_async

from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.dummy_crm import DummyCrm
from deepinsights.core.integrations.crm.microsoft_dynamics import MicrosoftDynamics
from deepinsights.core.integrations.crm.redtail import Redtail
from deepinsights.core.integrations.crm.salesforce import Salesforce
from deepinsights.core.integrations.crm.salesforce_financial_cloud import SalesforceFinancialCloud
from deepinsights.core.integrations.crm.sequoia_salesforce import SequoiaSalesforce
from deepinsights.core.integrations.crm.sharefile import ShareFile
from deepinsights.core.integrations.crm.sharepoint import SharePoint
from deepinsights.core.integrations.crm.tamarac_microsoft_dynamics import TamaracMicrosoftDynamics
from deepinsights.core.integrations.crm.wealthbox import Wealthbox
from deepinsights.core.integrations.crm.zeplyn_internal import ZeplynInternal
from deepinsights.core.preferences.preferences import CrmConfiguration
from deepinsights.users.models.user import User


def get_crm_interface(user: User) -> CrmBase:
    crm_config: CrmConfiguration = user.get_crm_configuration()
    crm_system = crm_config.crm_system
    if crm_system == "salesforce":
        salesforce_config = crm_config.salesforce
        if salesforce_config.type == "base":
            logging.debug("Using Salesforce base/core CRM for user %s", user.uuid)
            return Salesforce(
                user=user,
                username=salesforce_config.salesforce_username,
                password=salesforce_config.salesforce_password,
                consumer_key=salesforce_config.salesforce_consumer_key,
                consumer_secret=salesforce_config.salesforce_consumer_secret,
                security_token=salesforce_config.salesforce_security_token,
                instance_url=salesforce_config.salesforce_endpoint,
            )
        logging.debug("Using Salesforce financial cloud CRM for user %s", user.uuid)
        return SalesforceFinancialCloud(
            user=user,
            username=salesforce_config.salesforce_username,
            password=salesforce_config.salesforce_password,
            consumer_key=salesforce_config.salesforce_consumer_key,
            consumer_secret=salesforce_config.salesforce_consumer_secret,
            security_token=salesforce_config.salesforce_security_token,
            instance_url=salesforce_config.salesforce_endpoint,
        )
    if crm_system == "redtail":
        redtail_config = crm_config.redtail
        logging.debug("Using Redtail CRM for user %s", user.uuid)
        return Redtail(user_key=redtail_config.user_key)  # type: ignore[no-untyped-call]
    if crm_system == "sequoia_sf":
        logging.debug("Using Sequoia SF CRM for user %s", user.uuid)
        return SequoiaSalesforce()
    if crm_system == "wealthbox":
        logging.debug("Using Wealthbox CRM for user %s", user.uuid)
        return Wealthbox()
    if crm_system == "sharefile":
        logging.debug("Using ShareFile CRM for user %s", user.uuid)
        return ShareFile()
    if crm_system == "sharepoint":
        sharepoint_config = crm_config.sharepoint
        logging.debug("Using Sharepoint CRM user %s", user.uuid)
        return SharePoint(sharepoint_config)
    if crm_system == "microsoft_dynamics":
        dynamics_config = crm_config.dynamics
        if dynamics_config.type == "base":
            logging.debug("Using Microsoft Dynamics base/core CRM for user %s", user.uuid)
            return MicrosoftDynamics(user=user)
        elif dynamics_config.type == "tamarac":
            logging.debug("Using Tamarac Overlay Microsoft Dynamics CRM for user %s", user.uuid)
            return TamaracMicrosoftDynamics(user=user)

    if crm_system == "zeplyn_internal":
        logging.debug("Using Zeplyn Internal CRM for user %s", user.uuid)
        return ZeplynInternal()

    logging.warning("Failed to fetch valid CRM handler for user %s", user.uuid)
    return DummyCrm()


async def get_crm_interface_async(user) -> CrmBase:  # type: ignore[no-untyped-def]
    """Async wrapper for get_crm_interface"""
    return await sync_to_async(get_crm_interface)(user)
