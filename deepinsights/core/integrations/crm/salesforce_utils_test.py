from unittest.mock import MagicMock, patch

from simple_salesforce.api import Salesforce

from deepinsights.core.integrations.crm.crm_models import CRMAccount
from deepinsights.core.integrations.crm.salesforce_utils import get_salesforce_entity_by_owner_email_and_name


@patch("deepinsights.core.integrations.crm.salesforce_utils.get_user_id_by_email")
def test_get_salesforce_entity_by_owner_email_and_name_no_entity_filter(mock_get_user_id: MagicMock) -> None:
    mock_get_user_id.return_value = "001USER1"

    mock_sf = MagicMock(spec=Salesforce)
    mock_sf.query_all.return_value = {
        "totalSize": 2,
        "records": [
            {"Id": "1", "Name": "Test Account 1", "Phone": "***********", "OwnerId": "001USER1"},
            {"Id": "2", "Name": "Test Account 2", "Phone": "***********", "OwnerId": "002USER2"},
        ],
    }

    result = get_salesforce_entity_by_owner_email_and_name(
        mock_sf,
        "Account",
        "<EMAIL>",
    )

    assert result == [
        CRMAccount(
            crm_id="1",
            name="Test Account 1",
            phone_number="***********",
            client_type="account",
            crm_system="salesforce",
            is_owned_by_user=True,
        ),
        CRMAccount(
            crm_id="2",
            name="Test Account 2",
            phone_number="***********",
            client_type="account",
            crm_system="salesforce",
            is_owned_by_user=False,
        ),
    ]

    mock_sf.query_all.assert_called_once_with("SELECT Id, Name, Phone, OwnerId FROM Account")


@patch("deepinsights.core.integrations.crm.salesforce_utils.get_user_id_by_email")
def test_get_salesforce_entity_by_owner_email_and_name_contact(mock_get_user_id: MagicMock) -> None:
    mock_get_user_id.return_value = "001USER1"

    mock_sf = MagicMock(spec=Salesforce)
    mock_sf.query_all.return_value = {"totalSize": 0, "records": []}

    result = get_salesforce_entity_by_owner_email_and_name(
        mock_sf,
        "Contact",
        "<EMAIL>",
    )

    assert not result

    mock_sf.query_all.assert_called_once_with("SELECT Id, Name, Phone, OwnerId FROM Contact")


@patch("deepinsights.core.integrations.crm.salesforce_utils.get_user_id_by_email")
def test_get_salesforce_entity_by_owner_email_and_name_with_entity_filter(mock_get_user_id: MagicMock) -> None:
    mock_get_user_id.return_value = "001USER1"

    mock_sf = MagicMock(spec=Salesforce)
    mock_sf.query_all.return_value = {
        "totalSize": 1,
        "records": [
            {"Id": "3", "Name": "Filtered Account", "Phone": None, "OwnerId": "001USER1"},
        ],
    }

    result = get_salesforce_entity_by_owner_email_and_name(
        sf=mock_sf,
        entity_name="Account",
        owner_email="<EMAIL>",
        entity_filter="Filtered",
    )

    assert result == [
        CRMAccount(
            crm_id="3",
            name="Filtered Account",
            phone_number=None,
            client_type="account",
            crm_system="salesforce",
            is_owned_by_user=True,
        )
    ]

    mock_sf.query_all.assert_called_once_with(
        "SELECT Id, Name, Phone, OwnerId FROM Account WHERE Name LIKE '%Filtered%'"
    )


@patch("deepinsights.core.integrations.crm.salesforce_utils.get_user_id_by_email")
def test_get_salesforce_entity_by_owner_email_and_name_escapes_input(mock_get_user_id: MagicMock) -> None:
    mock_get_user_id.return_value = "001USER1"

    mock_sf = MagicMock(spec=Salesforce)
    mock_sf.query_all.return_value = {"totalSize": 0, "records": []}

    get_salesforce_entity_by_owner_email_and_name(
        sf=mock_sf,
        entity_name="Account",
        owner_email="owner'<EMAIL>",
        entity_filter="Name'Filter",
    )

    mock_sf.query_all.assert_called_once_with(
        "SELECT Id, Name, Phone, OwnerId FROM Account WHERE Name LIKE '%Name\\'Filter%'"
    )
