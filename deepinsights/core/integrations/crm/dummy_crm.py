import datetime
from typing import Any

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import CRMAccount, CRMNote
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class DummyCrm(CrmBase):
    def preview_before_syncing_with_crm(self, note: Note) -> dict[str, Any]:
        return {"status": "preview", "note_id": note.id}

    def add_interaction_with_client(self, note: Note) -> None:
        return

    def get_accounts_by_owner_email_and_name(self, owner_email: str, account_name_filter=None) -> list[CRMAccount]:  # type: ignore[no-untyped-def]
        return []

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        return None

    def fetch_events(self, user: User, interval: datetime.timedelta) -> list[CalendarEvent]:
        return []

    def fetch_notes_for_client(
        self, user: User, client: Client, lookback_interval: datetime.timedelta
    ) -> list[CRMNote]:
        return []
