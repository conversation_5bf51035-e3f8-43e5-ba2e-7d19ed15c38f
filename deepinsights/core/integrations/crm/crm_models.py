import datetime
import enum

import pydantic

from deepinsights.utils.phone_number import PhoneNumberE164OrNone


# A standardized representation of a CRM account.
class CRMAccount(pydantic.BaseModel):
    # The identifier provided by the CRM, regularlized to a string.
    crm_id: str

    # The name related to the account.
    name: str

    # The first name of the account holder (if available).
    first_name: str | None = None

    # The last name of the account holder (if available).
    last_name: str | None = None

    # The email address of the account holder (if available).
    email: str | None = None

    # The phone number of the account holder (if available).
    phone_number: PhoneNumberE164OrNone = None

    # The type of client this is; e.g., "account", "contact", "household", "client".
    client_type: str

    # Which CRM this client is from.
    crm_system: str

    # Whether this client is owned by the user who made the request to fetch this client.
    #
    # Note that this will change depending on the user making the request, so there is no
    # guarantee that two users will receive equivalent `CRMAccount` objects when fetching
    # clients from the CRM.
    is_owned_by_user: bool | None = None


class CRMNoteType(enum.StrEnum):
    SALESFORCE_STANDARD_NOTE = "salesforce_standard_note"
    SALESFORCE_CONTENT_NOTE = "salesforce_content_note"


# A standardized representation of data from a CRM note (i.e., a piece of textual information
# associated with a client).
class CRMNote(pydantic.BaseModel):
    # The identifier provided by the CRM, regularlized to a string.
    crm_id: str

    # Which CRM this note is from.
    crm_system: str

    # The contents of the note.
    content: str

    # The creation timestamp associated with this CRM note.
    created_at: datetime.datetime | None

    # Type of note.
    type: CRMNoteType | None = None

    # A link that can be used to access this note in the CRM.
    #
    # This is meant to be a link that can be used to access this note via a browser, not a link in
    # the context of an API.
    web_link: pydantic.HttpUrl | None = None
