import datetime
from unittest import mock
from unittest.mock import MagicMock, patch

from django.test import TestCase

from deepinsights.core.integrations.crm.sharepoint import SharePoint
from deepinsights.core.preferences.preferences import SharePointConfiguration
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class TestSharePoint(TestCase):
    def setUp(self) -> None:
        self.user = User.objects.create(email="<EMAIL>", name="Test User")

        self.note = Note.objects.create(note_owner=self.user, metadata={"meeting_name": "Test Meeting"})

        self.note.created = datetime.datetime(2023, 1, 15, 10, 30, 0)
        self.note.save()

        self.title_patcher = patch.object(Note, "title", return_value="Test Note Title")
        self.summary_patcher = patch.object(Note, "get_summary_for_crm", return_value="Test Note Summary")
        self.attendees_patcher = patch.object(Note, "get_attendees", return_value=["Client 1", "Client 2"])

        self.mock_title = self.title_patcher.start()
        self.mock_summary = self.summary_patcher.start()
        self.mock_attendees = self.attendees_patcher.start()

        self.config = SharePointConfiguration(
            client_id="test_client_id",
            client_secret="test_client_secret",
            site_url="https://test.sharepoint.com/sites/test",
            parent_folder="test_folder",
        )

        self.client_context_patcher = patch("deepinsights.core.integrations.crm.sharepoint.ClientContext")
        self.client_credential_patcher = patch("deepinsights.core.integrations.crm.sharepoint.ClientCredential")

        self.mock_client_context = self.client_context_patcher.start()
        self.mock_client_credential = self.client_credential_patcher.start()

        self.mock_context_instance = MagicMock()
        self.mock_web = MagicMock()
        self.mock_context_instance.web = self.mock_web
        self.mock_web.properties = {"Title": "Test Site"}
        self.mock_client_context.return_value.with_credentials.return_value = self.mock_context_instance

        self.sharepoint = SharePoint(self.config)

    def tearDown(self) -> None:
        self.title_patcher.stop()
        self.summary_patcher.stop()
        self.attendees_patcher.stop()

        self.client_context_patcher.stop()
        self.client_credential_patcher.stop()

    def test_init_with_valid_config(self) -> None:
        self.assertEqual(self.sharepoint.client_id, "test_client_id")
        self.assertEqual(self.sharepoint.client_secret, "test_client_secret")
        self.assertEqual(self.sharepoint.site_url, "https://test.sharepoint.com/sites/test")
        self.assertEqual(self.sharepoint.sharepoint_parent_folder, "test_folder")
        self.assertEqual(self.sharepoint.client_context, self.mock_context_instance)

    def test_init_with_invalid_config(self) -> None:
        invalid_config = SharePointConfiguration(
            client_id="",
            client_secret="test_secret",
            site_url="https://test.sharepoint.com",
            parent_folder="test_folder",
        )

        with self.assertRaises(ValueError):
            SharePoint(invalid_config)

    def test_authenticate(self) -> None:
        self.mock_client_credential.reset_mock()
        self.mock_client_context.reset_mock()
        self.mock_context_instance.reset_mock()

        self.sharepoint.client_context = None
        self.sharepoint.authenticate()

        self.mock_client_credential.assert_called_once_with("test_client_id", "test_client_secret")
        self.mock_client_context.assert_called_once_with("https://test.sharepoint.com/sites/test")
        self.mock_client_context.return_value.with_credentials.assert_called_once()
        self.assertEqual(self.sharepoint.client_context, self.mock_context_instance)
        self.mock_context_instance.load.assert_called_with(self.mock_web)
        self.mock_context_instance.execute_query.assert_called()

    def test_authenticate_failure(self) -> None:
        self.mock_client_credential.reset_mock()
        self.mock_client_context.reset_mock()
        self.mock_context_instance.reset_mock()

        self.sharepoint.client_context = None
        self.mock_context_instance.execute_query.side_effect = Exception("Authentication failed")

        with self.assertRaises(Exception):
            self.sharepoint.authenticate()

        self.assertIsNone(self.sharepoint.client_context)

    def test_ensure_folder_exists_with_new_folder(self) -> None:
        self.mock_context_instance.reset_mock()
        self.mock_context_instance.web.reset_mock()
        self.mock_context_instance.web.lists.reset_mock()

        mock_doc_lib = MagicMock()
        mock_root_folder = MagicMock()
        mock_root_folder.properties = {"ServerRelativeUrl": "/sites/test/Documents"}
        mock_doc_lib.properties = {"RootFolder": mock_root_folder}

        self.mock_context_instance.web.lists.get_by_title.return_value = mock_doc_lib

        mock_parent_folder = MagicMock()
        mock_new_folder = MagicMock()
        mock_parent_folder.folders.add.return_value = mock_new_folder

        folder_lookup_responses = {
            "/sites/test/Documents/test_folder": mock_parent_folder,
            "/sites/test/Documents/test_folder/new_folder": Exception("Folder not found"),
            "/sites/test/Documents": mock_parent_folder,
        }

        def folder_side_effect(path: str):  # type: ignore[no-untyped-def]
            if path in folder_lookup_responses:
                result = folder_lookup_responses[path]
                if isinstance(result, Exception):
                    raise result
                return result
            raise ValueError(f"Unexpected path: {path}")

        self.mock_context_instance.web.get_folder_by_server_relative_url.side_effect = folder_side_effect

        self.sharepoint.ensure_folder_exists("test_folder/new_folder")

        self.mock_context_instance.web.lists.get_by_title.assert_called_with("Documents")

        load_calls = [
            call
            for call in self.mock_context_instance.load.call_args_list
            if call == mock.call(mock_doc_lib, ["RootFolder"])
        ]
        self.assertTrue(len(load_calls) > 0, "load was not called with the expected arguments")

        mock_parent_folder.folders.add.assert_called_once_with("new_folder")

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.ensure_folder_exists")
    def test_upload_file(self, mock_ensure_folder_exists: MagicMock) -> None:
        mock_doc_lib = MagicMock()
        mock_root_folder = MagicMock()
        mock_root_folder.properties = {"ServerRelativeUrl": "/sites/test/Documents"}
        mock_doc_lib.properties = {"RootFolder": mock_root_folder}

        self.mock_context_instance.web.lists.get_by_title.return_value = mock_doc_lib

        mock_target_folder = MagicMock()
        mock_target_file = MagicMock()
        self.mock_context_instance.web.get_folder_by_server_relative_url.return_value = mock_target_folder
        mock_target_folder.upload_file.return_value = mock_target_file

        result = self.sharepoint.upload_file("test_folder", "test_file.txt", "Test content")

        mock_ensure_folder_exists.assert_called_once_with("test_folder")
        self.mock_context_instance.web.lists.get_by_title.assert_called_once_with("Documents")
        self.mock_context_instance.web.get_folder_by_server_relative_url.assert_called_once()
        mock_target_folder.upload_file.assert_called_once_with("test_file.txt", "Test content".encode("utf-8"))
        self.assertTrue(result)

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.upload_file")
    def test_add_interaction_with_client(self, mock_upload_file: MagicMock) -> None:
        mock_upload_file.return_value = True

        self.sharepoint.add_interaction_with_client(self.note)

        expected_folder_path = f"test_folder/{self.note.note_owner.name}" if self.note.note_owner else ""
        expected_filename = f"{self.note.created.strftime('%Y-%m-%d-%H-%M')}_Test Meeting.txt"
        expected_content = "Title: Test Note Title\n\nSummary: Test Note Summary\n\nClients: Client 1, Client 2"

        mock_upload_file.assert_called_once_with(expected_folder_path, expected_filename, expected_content)

    @patch("deepinsights.core.integrations.crm.sharepoint.SharePoint.upload_file")
    def test_add_interaction_with_client_failure(self, mock_upload_file: MagicMock) -> None:
        mock_upload_file.return_value = False

        with self.assertRaises(Exception):
            self.sharepoint.add_interaction_with_client(self.note)

    def test_get_accounts_by_owner_email_and_name(self) -> None:
        result = self.sharepoint.get_accounts_by_owner_email_and_name("<EMAIL>")
        self.assertEqual(result, [])

    def test_preview_before_syncing_with_crm(self) -> None:
        result = self.sharepoint.preview_before_syncing_with_crm(self.note)
        self.assertEqual(result, {})

    def test_get_client_basic_info(self) -> None:
        mock_client = MagicMock(spec=Client)
        mock_user = MagicMock(spec=User)
        self.assertIsNone(self.sharepoint.get_client_basic_info(mock_client, mock_user))

    def test_fetch_events(self) -> None:
        mock_user = MagicMock(spec=User)

        result = self.sharepoint.fetch_events(mock_user, datetime.timedelta(days=7))
        self.assertEqual(result, [])
