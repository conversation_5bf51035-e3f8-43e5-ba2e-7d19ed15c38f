import datetime
import logging
from datetime import timed<PERSON><PERSON>
from typing import Any

from simple_salesforce.api import Salesforce

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm import salesforce_utils
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import CRMAccount, CRMNote
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User


class SalesforceFinancialCloud(CrmBase):
    def __init__(
        self,
        *,
        user: User | None = None,
        username: str | None = None,
        password: str | None = None,
        consumer_key: str | None = None,
        consumer_secret: str | None = None,
        security_token: str = "",
        instance_url: str | None = None,
        sf: Salesforce | None = None,
    ) -> None:
        super().__init__()
        self.sf = salesforce_utils.simple_salesforce_intstance_with_credentials(
            user=user,
            username=username,
            password=password,
            consumer_key=consumer_key,
            consumer_secret=consumer_secret,
            security_token=security_token,
            instance_url=instance_url,
            sf=sf,
        )

    def get_accounts_by_owner_email_and_name(
        self, owner_email: str, account_name_filter: str | None = None
    ) -> list[CRMAccount]:
        return salesforce_utils.get_salesforce_entity_by_owner_email_and_name(
            self.sf, "Account", owner_email, account_name_filter
        )

    def preview_before_syncing_with_crm(self, note: Note) -> dict:  # type: ignore[type-arg]
        try:
            owner_email = note.note_owner.email  # type: ignore[union-attr]
            owner = salesforce_utils.get_user_id_by_email(self.sf, owner_email)
            if not owner:
                logging.error(
                    f"Could not find a Salesforce User with the given email: {owner_email}. Cannot provide preview."
                )
                return {}
            return salesforce_utils.get_crm_preview(note, owner, use_html_formatting=True)
        except Exception as e:
            logging.error(
                f"Error previewing interaction with client {note.note_owner.uuid} for note {note.uuid}: {e}",  # type: ignore[union-attr]
                exc_info=True,
            )
            raise

    def add_interaction_with_client(self, note: Note):  # type: ignore[no-untyped-def]
        owner_email = note.note_owner.email  # type: ignore[union-attr]
        owner = salesforce_utils.get_user_id_by_email(self.sf, owner_email)
        if not owner:
            error_msg = "Could not find a Salesforce User with the given email: %s" % (owner_email,)
            logging.error(error_msg)
            raise ValueError(error_msg)

        client_uuid_or_crm_id = (note.client or {}).get("uuid", "")
        if not client_uuid_or_crm_id:
            error_msg = "Note does not have an associated Salesforce CRM client. Not adding interaction."
            logging.error(error_msg)
            raise ValueError(error_msg)

        crm_id_from_database = None
        try:
            crm_id_from_database = Client.objects.get(uuid=client_uuid_or_crm_id).crm_id
        except Exception:
            logging.info("Client not found in the database. Assuming client.uuid is Salesforce CRM ID.")
        client_crm_id = crm_id_from_database or client_uuid_or_crm_id

        meeting_name = note.metadata.get("meeting_name") or "Meeting with a client"  # type: ignore[union-attr]
        interaction = {
            "OwnerId": owner,
            "Name": meeting_name,
            "AccountId": client_crm_id,
            "StartTime": salesforce_utils.salesforceTime(note.created),
        }
        logging.info("recording interaction: %s", interaction)
        if not note.metadata.get("interactionId"):  # type: ignore[union-attr]
            try:
                interaction_result = self.sf.Interaction.create(interaction)  # type: ignore[operator]
                note.metadata["interactionId"] = interaction_result["id"]  # type: ignore[index]
                note.save()
                logging.info("interaction saved")
            except Exception as e:
                error_msg = "error recording interaction: %s. Not updating note." % (str(e),)
                logging.error(error_msg)
                raise RuntimeError(error_msg) from e
        else:
            logging.info("Already associated with an interaction: %s", note.metadata.get("interactionId"))  # type: ignore[union-attr]

        task_titles = [task.task_title for task in Task.objects.filter(note=note)]
        next_steps = "*" + "\n*".join(task_titles) if task_titles else "No next steps identified"

        interaction_id = note.metadata.get("interactionId")  # type: ignore[union-attr]
        if not interaction_id:
            error_msg = "No interaction ID associated with note. Skipping summary and task generation."
            logging.error(error_msg)
            raise RuntimeError(error_msg)
        interaction_summary = {
            "OwnerId": owner,
            "Name": meeting_name,
            "InteractionId": note.metadata["interactionId"],  # type: ignore[index]
            "MeetingNotes": note.get_summary_for_crm(use_html_formatting=True),
            "AccountId": client_crm_id,
            "NextSteps": next_steps,
        }
        logging.info("recording interaction summary: %s", interaction_summary)
        if not note.metadata.get("interactionSummaryId"):  # type: ignore[union-attr]
            try:
                summary_result = self.sf.InteractionSummary.create(interaction_summary)  # type: ignore[operator]
                note.metadata["interactionSummaryId"] = summary_result["id"]  # type: ignore[index]
                note.save()
                logging.info("interaction summary saved")
            except Exception as e:
                error_msg = "error recording interaction summary: %s. Not updating note." % (str(e),)
                logging.error(error_msg)
                raise RuntimeError(error_msg) from e
        else:
            logging.info(
                "Already associated with an interaction summary: %s",
                note.metadata.get("interactionSummaryId"),  # type: ignore[union-attr]
            )
        for task in note.task_set.all():
            try:
                salesforce_utils.add_task(
                    self.sf,
                    task,
                    owner_email,
                    {
                        "WhatId": client_crm_id,
                    },
                )
            except Exception as e:
                error_msg = "Error adding task: %s" % (str(e),)
                logging.error(error_msg)
                raise RuntimeError(error_msg)

    def fetch_notes_for_client(self, user: User, client: Client, lookback_interval: timedelta) -> list[CRMNote]:
        client_crm_id = client.crm_id
        if not client_crm_id:
            logging.error("Client does not have a CRM ID. Cannot fetch notes %s, for user %s", client.uuid, user.uuid)
            return []

        try:
            interaction_query = (
                f"SELECT Id, Name, StartTime, AccountId "
                f"FROM Interaction "
                f"WHERE AccountId = '{client_crm_id}' "
                f"AND StartTime >= {salesforce_utils.salesforceTime(datetime.datetime.now(tz=datetime.timezone.utc) - lookback_interval)} "
            )
            interactions_result = self.sf.query_all(interaction_query)
            interactions = interactions_result.get("records", [])

            if not interactions:
                logging.info(
                    "No interactions found in the past year for the client %s, for user %s", client.uuid, user.uuid
                )
                return []

            interaction_ids = [interaction["Id"] for interaction in interactions]
            interaction_ids_str = ",".join([f"'{id}'" for id in interaction_ids])
            summary_query = (
                f"SELECT Id, Name, InteractionId, MeetingNotes, NextSteps, AccountId "
                f"FROM InteractionSummary "
                f"WHERE InteractionId IN ({interaction_ids_str})"
            )
            summaries_result = self.sf.query_all(summary_query)
            summaries = summaries_result.get("records", [])

            summary_dict = {s["InteractionId"]: s for s in summaries}

            parsed_interactions: list[CRMNote] = []

            for interaction in interactions:
                summary = summary_dict.get(interaction["Id"], {})
                meeting_notes = summary.get("MeetingNotes") or "No notes available"
                next_steps = summary.get("NextSteps") or "No next steps available"
                try:
                    created_at = salesforce_utils.datetime_from_salesforce_time(interaction["StartTime"])
                except Exception:
                    created_at = None
                parsed_interactions.append(
                    CRMNote(
                        crm_id=interaction["Id"],
                        crm_system="salesforce",
                        content=f"{meeting_notes}\n{next_steps}",
                        created_at=created_at,
                        web_link=f"https://{self.sf.sf_instance}/{interaction['Id']}",
                    )
                )

            logging.info("Fetched %s interactions for client %s", len(parsed_interactions), client_crm_id)
            return parsed_interactions

        except Exception as e:
            logging.error("Error fetching interactions for client UUID %s, %s:", client_crm_id, str(e), exc_info=True)
            return []

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        logging.warning("get_client_basic_info not implemented for Salesforce")
        return None

    def fetch_events(self, user: User, interval: timedelta) -> list[CalendarEvent]:
        logging.warning("fetch_crm_events not implemented for SalesforceFinancial")
        return []
