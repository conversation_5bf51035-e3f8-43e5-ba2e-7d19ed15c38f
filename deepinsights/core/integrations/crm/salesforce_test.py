import base64
from datetime import datetime, timed<PERSON>ta
from datetime import timezone as datetime_timezone
from typing import Any
from unittest.mock import ANY, MagicMock, call, patch
from uuid import uuid4

import simple_salesforce
from django.core import cache
from django.test import TestCase, override_settings
from django.utils import timezone
from simple_mockforce import mock_salesforce
from simple_salesforce.api import Salesforce as SimpleSalesforce

from deepinsights.core.integrations.crm.crm_models import CRMAccount, CRMNote, CRMNoteType
from deepinsights.core.integrations.crm.salesforce import Salesforce
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.flags import Flag
from deepinsights.users.models.user import User


class SalesforceTestCase(TestCase):
    def setUp(self) -> None:
        cache.cache.clear()
        self.simple_salesforce = SimpleSalesforce(instance="test.salesforce.com", session_id="")
        self.salesforce = Salesforce(sf=self.simple_salesforce)
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, False)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, False)

    # Sets the provided flag to the given active state.
    def setFlagActive(self, flag: Flags, is_active: bool) -> None:
        f = Flag.objects.get(name=flag.name)
        f.everyone = is_active
        f.override_enabled_by_environment = None  # To ensure no environment override
        f.save()

    # Populate the required Salesforce types in the mock, so that they can be used in queries even
    # if they have never been created.
    def populateSalesforceTypes(self) -> None:
        for type in [
            self.simple_salesforce.Note,
            self.simple_salesforce.Task,
            self.simple_salesforce.User,
            self.simple_salesforce.ContentNote,
            self.simple_salesforce.ContentDocumentLink,
        ]:
            id = type.create({})["id"]  # type: ignore[operator]
            type.delete(id)  # type: ignore[operator]

    # Populate ContentNote and ContentDocumentType mock, so that they can be used in queries even if
    # they have never been created.
    def populateContentNoteAndContentDocumentLink(self) -> None:
        for type in [
            self.simple_salesforce.ContentNote,
            self.simple_salesforce.ContentDocumentLink,
        ]:
            id = type.create({})["id"]  # type: ignore[operator]
            type.delete(id)  # type: ignore[operator]

    # Creates a new contact with the given name (or a randomized name if the name parameter is None).
    def newContactAndClient(
        self, name: str | None, owner_id: str | None, org: Organization, *, phone: str | None = None
    ) -> tuple[str, Client]:
        contact_name = name.replace("'", "\\'") if name else f"Test Account {uuid4()}"
        data: dict[str, Any] = {
            "Name": contact_name,
            "OwnerId": owner_id,
        }
        if phone:
            data["Phone"] = phone
        contact = self.simple_salesforce.Contact.create(data)  # type: ignore[operator]
        client = Client.objects.create(crm_id=contact["id"], name=contact_name, organization=org)
        self.assertFalse(contact["errors"])
        return (contact["id"], client)

    # Creates a new Account with the given name (or a randomized name if the name parameter is None).
    def newAccountAndClient(
        self, name: str | None, owner_id: str | None, org: Organization, *, phone: str | None = None
    ) -> tuple[str, Client]:
        account_name = name.replace("'", "\\'") if name else f"Test Contact {uuid4()}"
        data: dict[str, Any] = {
            "Name": account_name,
            "OwnerId": owner_id,
        }
        if phone:
            data["Phone"] = phone
        account = self.simple_salesforce.Account.create(data)  # type: ignore[operator]
        self.assertFalse(account["errors"])
        client = Client.objects.create(crm_id=account["id"], name=account_name, organization=org)
        return (account["id"], client)

    # Creates a new arbitrary user, both in the Salesforce and Zeplyn databases.
    #
    # Returns a tuple of (salesforceUserId, userEmail).
    def newUser(self, org: Organization) -> tuple[str, str]:
        email = f"{uuid4()}@example.com"
        user = self.simple_salesforce.User.create({"Email": email})  # type: ignore[operator]
        zeplyn_user = User.objects.create(username=email, email=email, organization=org)
        zeplyn_user.crm_configuration["crm_system"] = "salesforce"
        zeplyn_user.save()
        return (user["id"], email)

    @override_settings(SALESFORCE_USER="user", SALESFORCE_PASSWORD="password", SALESFORCE_INSTANCE="instance")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_from_settings(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        Salesforce(user=user)
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="", instance_url="instance"
        )

    @override_settings(
        SALESFORCE_USER="settings_user",
        SALESFORCE_PASSWORD="settings_password",
        SALESFORCE_INSTANCE="settings_instance",
    )
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_with_params(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        Salesforce(user=user, username="user", password="password", security_token="token", instance_url="instance")
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_for_comsumer_key_missing_values(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        Salesforce(user=user, username="user", password="password", security_token="token", instance_url="instance")
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @override_settings(SALESFORCE_ZEPLYN_APP_DOMAIN="app_domain")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_for_comsumer_key_flag_disabled(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, False)
        Salesforce(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @override_settings(SALESFORCE_ZEPLYN_APP_DOMAIN="app_domain")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_for_comsumer_key(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        Salesforce(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
            security_token="token",
            instance_url="instance",
            domain="app_domain",
        )

    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    def test_init_for_user_oauth_missing_values(self, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, True)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        Salesforce(user=user, username="user", password="password", security_token="token", instance_url="instance")
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @override_settings(SALESFORCE_ZEPLYN_APP_DOMAIN="app_domain")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.SalesforceOAuth")
    def test_init_for_user_oauth_flag_disabled(self, sf_oauth: MagicMock, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, False)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, False)
        sf_oauth.return_value.get_access_token.return_value = "access_token"
        Salesforce(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(
            username="user", password="password", security_token="token", instance_url="instance"
        )

    @override_settings(SALESFORCE_ZEPLYN_APP_DOMAIN="app_domain")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.SalesforceOAuth")
    def test_init_for_user_oauth_token_exception(self, sf_oauth: MagicMock, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, True)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        sf_oauth.return_value.get_access_token.side_effect = Exception("Test error")
        Salesforce(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
            security_token="token",
            instance_url="instance",
            domain="app_domain",
        )

    @patch("deepinsights.core.integrations.crm.salesforce_utils.Salesforce")
    @patch("deepinsights.core.integrations.crm.salesforce_utils.SalesforceOAuth")
    def test_init_for_user_oauth(self, sf_oauth: MagicMock, simple_salesforce: MagicMock) -> None:
        user = User.objects.create(username="<EMAIL>")
        self.setFlagActive(Flags.EnableSalesforceOAuthIntegration, True)
        self.setFlagActive(Flags.EnableSalesforceClientCredentialsOAuthIntegration, True)
        sf_oauth.return_value.get_access_token.return_value = "access_token"
        Salesforce(
            user=user,
            username="user",
            password="password",
            security_token="token",
            instance_url="instance",
            consumer_key="consumer_key",
            consumer_secret="consumer_secret",
        )
        simple_salesforce.assert_called_once_with(session_id="access_token", instance_url="instance")

    @mock_salesforce
    def test_get_accounts_by_owner_email_and_name(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_one_id, user_one_email = self.newUser(org=org)
        user_two_id, user_two_email = self.newUser(org=org)
        contact_one_id, _ = self.newContactAndClient("one", user_one_id, org)
        contact_two_id, _ = self.newContactAndClient("two", user_one_id, org, phone="**********")
        contact_three_id, _ = self.newContactAndClient("three", user_two_id, org)
        account_one_id, _ = self.newAccountAndClient("one", user_one_id, org, phone="**********")
        account_two_id, _ = self.newAccountAndClient("two", user_one_id, org, phone="invalid")
        account_three_id, _ = self.newAccountAndClient("three", user_two_id, org, phone="+*************")

        def expected(user_one_is_owner: bool) -> list[CRMAccount]:
            return [
                CRMAccount(
                    crm_id=account_one_id,
                    name="one",
                    phone_number="**********",
                    client_type="account",
                    crm_system="salesforce",
                    is_owned_by_user=user_one_is_owner,
                ),
                CRMAccount(
                    crm_id=account_two_id,
                    name="two",
                    client_type="account",
                    crm_system="salesforce",
                    is_owned_by_user=user_one_is_owner,
                ),
                CRMAccount(
                    crm_id=account_three_id,
                    name="three",
                    phone_number="+*************",
                    client_type="account",
                    crm_system="salesforce",
                    is_owned_by_user=not user_one_is_owner,
                ),
                CRMAccount(
                    crm_id=contact_one_id,
                    name="one",
                    client_type="contact",
                    crm_system="salesforce",
                    is_owned_by_user=user_one_is_owner,
                ),
                CRMAccount(
                    crm_id=contact_two_id,
                    name="two",
                    phone_number="**********",
                    client_type="contact",
                    crm_system="salesforce",
                    is_owned_by_user=user_one_is_owner,
                ),
                CRMAccount(
                    crm_id=contact_three_id,
                    name="three",
                    client_type="contact",
                    crm_system="salesforce",
                    is_owned_by_user=not user_one_is_owner,
                ),
            ]

        self.assertEqual(self.salesforce.get_accounts_by_owner_email_and_name(user_one_email), expected(True))
        self.assertEqual(self.salesforce.get_accounts_by_owner_email_and_name(user_two_email), expected(False))
        self.assertEqual(self.salesforce.get_accounts_by_owner_email_and_name("doesnotexist"), [])

    @patch("deepinsights.core.integrations.crm.salesforce_utils.get_user_id_by_email")
    def test_get_accounts_by_owner_email_and_name_filtering(self, mock_get_user_id: MagicMock) -> None:
        mock_salesforce = MagicMock()
        mock_get_user_id.return_value = "001USER1"

        # simple_mockforce does not properly mock LIKE in where clauses, so we need to test this by-hand.
        salesforce = Salesforce(sf=mock_salesforce)
        salesforce.get_accounts_by_owner_email_and_name("test_example.com", "o'ne")

        self.assertEqual(mock_salesforce.query_all.call_count, 2)
        mock_salesforce.query_all.assert_has_calls(
            [
                call("SELECT Id, Name, Phone, OwnerId FROM Account WHERE Name LIKE '%o\\'ne%'"),
                call("SELECT Id, Name, Phone, OwnerId FROM Contact WHERE Name LIKE '%o\\'ne%'"),
            ],
            any_order=True,
        )

    @mock_salesforce
    def test_get_accounts_by_owner_email_and_name_with_quotes(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_one_id, user_one_email = self.newUser(org)
        contact_id, _ = self.newContactAndClient("o'n'e", user_one_id, org)
        account_id, _ = self.newAccountAndClient("t'w'o", user_one_id, org)

        actual_result = self.salesforce.get_accounts_by_owner_email_and_name(user_one_email, "o'")

        expected_accounts = [
            CRMAccount(
                crm_id=account_id,
                name="t\\'w\\'o",
                client_type="account",
                crm_system="salesforce",
                is_owned_by_user=True,
            ),
            CRMAccount(
                crm_id=contact_id,
                name="o\\'n\\'e",
                client_type="contact",
                crm_system="salesforce",
                is_owned_by_user=True,
            ),
        ]

        self.assertEqual(actual_result, expected_accounts)

    @mock_salesforce
    def test_add_interaction_with_client_with_no_note_owner(self) -> None:
        self.populateSalesforceTypes()
        User.objects.create(username="<EMAIL>", email="<EMAIL>")
        note = Note(
            status="scheduled",
            client={"uuid": "123"},
            metadata={},
        )

        with self.assertRaisesRegex(
            ValueError,
            "Could not find a Salesforce User with the given email: None. Not adding interaction.",
        ):
            self.salesforce.add_interaction_with_client(note)

        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Note")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"])

    @mock_salesforce
    def test_add_interaction_with_client_with_no_contact(self) -> None:
        self.populateSalesforceTypes()
        user = User.objects.create(username="<EMAIL>", email="<EMAIL>")
        note = Note(
            note_owner=user,
            status="scheduled",
            client={"uuid": "123"},
            metadata={},
        )

        with self.assertRaisesRegex(
            ValueError,
            "Could not find a Salesforce User with the given email: <EMAIL>. Not adding interaction.",
        ):
            self.salesforce.add_interaction_with_client(note)

        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Note")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"])

    @mock_salesforce
    def test_add_interaction_with_client_with_no_client_id(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
        )

        with self.assertRaisesRegex(
            ValueError, "Note does not have an associated Salesforce CRM client. Not adding interaction."
        ):
            self.salesforce.add_interaction_with_client(note)

        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Note")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"])

    @mock_salesforce
    def test_add_interaction_creation_with_salesforce_all_error(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        _, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        def raiseException(*args, **kwargs):  # type: ignore[no-untyped-def]
            raise AssertionError("Test error")

        raiseException.create = raiseException  # type: ignore[attr-defined]
        self.simple_salesforce.Note = raiseException  # type: ignore[attr-defined]
        self.simple_salesforce.ContentNote = raiseException  # type: ignore[attr-defined]

        with self.assertRaisesRegex(RuntimeError, "error recording interaction: Test error. Not updating note."):
            self.salesforce.add_interaction_with_client(note)

        note.refresh_from_db()
        # Verify no records were created
        self.assertFalse((note.metadata or {}).get("interactionId"))
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Note")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"])

    @mock_salesforce
    def test_add_interaction_with_client_creates_content_note(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        with patch.object(note, "get_summary_for_crm", return_value="Test summary") as get_summary_for_crm:
            self.salesforce.add_interaction_with_client(note)
            get_summary_for_crm.assert_called_once_with(use_html_formatting=True)

        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM Note")["records"])
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"]), 1)
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"]), 1)
        salesforce_content_note = self.simple_salesforce.ContentNote.get((note.metadata or {})["interactionId"])  # type: ignore[operator]
        self.assertEqual(salesforce_content_note["OwnerId"], user_id)
        self.assertEqual(salesforce_content_note["Title"], "Meeting with a client")
        self.assertEqual(
            salesforce_content_note["Content"], base64.b64encode("Test summary".encode("utf-8")).decode("utf-8")
        )
        document_link_id = self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"][0]["Id"]
        salesforce_content_document_link = self.simple_salesforce.ContentDocumentLink.get(document_link_id)  # type: ignore[operator]
        self.assertEqual(salesforce_content_document_link["ContentDocumentId"], (note.metadata or {})["interactionId"])
        self.assertEqual(salesforce_content_document_link["LinkedEntityId"], account_id)

    @mock_salesforce
    def test_add_interaction_with_client_uses_uuid_as_crm_id(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, _ = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": account_id},
            created=timezone.now(),
        )

        with patch.object(note, "get_summary_for_crm", return_value="Test summary") as get_summary_for_crm:
            self.salesforce.add_interaction_with_client(note)

        document_link_id = self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"][0]["Id"]
        salesforce_content_document_link = self.simple_salesforce.ContentDocumentLink.get(document_link_id)  # type: ignore[operator]
        self.assertEqual(salesforce_content_document_link["LinkedEntityId"], account_id)

    @mock_salesforce
    def test_add_interaction_with_client_creates_note_for_cotent_document_failure(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        def raiseException(*args, **kwargs):  # type: ignore[no-untyped-def]
            raise AssertionError

        self.simple_salesforce.ContentDocumentLink = raiseException  # type: ignore[attr-defined]

        with patch.object(note, "get_summary_for_crm", return_value="Test summary"):
            self.salesforce.add_interaction_with_client(note)
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"])
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM Note")["records"]), 1)
        salesforce_note = self.simple_salesforce.Note.get((note.metadata or {})["interactionId"])  # type: ignore[operator]
        self.assertEqual(salesforce_note["OwnerId"], user_id)
        self.assertEqual(salesforce_note["Title"], "Meeting with a client")
        self.assertEqual(salesforce_note["ParentId"], account_id)
        self.assertEqual(salesforce_note["Body"], "Test summary")

    @mock_salesforce
    def test_add_interaction_with_client_creates_note(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        def raiseException(*args, **kwargs):  # type: ignore[no-untyped-def]
            raise AssertionError

        self.simple_salesforce.ContentNote = raiseException  # type: ignore[attr-defined]

        with patch.object(note, "get_summary_for_crm", return_value="Test summary"):
            self.salesforce.add_interaction_with_client(note)
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentNote")["records"])
        self.assertFalse(self.simple_salesforce.query_all("SELECT Id FROM ContentDocumentLink")["records"])
        self.assertEqual(len(self.simple_salesforce.query_all("SELECT Id FROM Note")["records"]), 1)
        salesforce_note = self.simple_salesforce.Note.get((note.metadata or {})["interactionId"])  # type: ignore[operator]
        self.assertEqual(salesforce_note["OwnerId"], user_id)
        self.assertEqual(salesforce_note["Title"], "Meeting with a client")
        self.assertEqual(salesforce_note["ParentId"], account_id)
        self.assertEqual(salesforce_note["Body"], "Test summary")

    @mock_salesforce
    def test_add_interaction_with_client_populates_meeting_title_for_content_note(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        _, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={"meeting_name": "Meeting"},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        self.salesforce.add_interaction_with_client(note)

        salesforce_content_note = self.simple_salesforce.ContentNote.get((note.metadata or {})["interactionId"])  # type: ignore[operator]
        self.assertEqual(salesforce_content_note["Title"], "Meeting")

    @mock_salesforce
    def test_add_interaction_with_client_populates_meeting_title_for_note(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        _, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={"meeting_name": "Meeting"},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        def raiseException(*args, **kwargs):  # type: ignore[no-untyped-def]
            raise AssertionError

        self.simple_salesforce.ContentNote = raiseException  # type: ignore[attr-defined]

        self.salesforce.add_interaction_with_client(note)
        salesforce_note = self.simple_salesforce.Note.get((note.metadata or {})["interactionId"])  # type: ignore[operator]
        self.assertEqual(salesforce_note["Title"], "Meeting")

    @mock_salesforce
    def test_add_interaction_with_client_does_not_update_interaction(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        _, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )
        self.salesforce.add_interaction_with_client(note)

        # Adding a new interaction for the same note should not change anything in the Salesforce database.
        note.metadata = {**(note.metadata or {}), "meeting_name": "New meeting name"}
        self.salesforce.add_interaction_with_client(note)
        salesforce_note = self.simple_salesforce.ContentNote.get((note.metadata or {})["interactionId"])  # type: ignore[operator]
        self.assertEqual(salesforce_note["Title"], "Meeting with a client")

        # Remove the link between the meeting and the interaction and retry.
        note.metadata = {**(note.metadata or {}), "interactionId": None}
        self.salesforce.add_interaction_with_client(note)
        salesforce_note = self.simple_salesforce.ContentNote.get((note.metadata or {})["interactionId"])  # type: ignore[operator]
        self.assertEqual(salesforce_note["Title"], "New meeting name")

    @mock_salesforce
    def test_add_interaction_creation_task_creation(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newContactAndClient("Account one", user_id, org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )
        note.save()

        task = Task(task_title="Task One", note=note)
        no_metadata_task = Task(task_title="Task Two", task_desc="Task two description", note=note, metadata=None)
        completed_task = Task(
            task_title="Completed Task", task_desc="Completed task description", note=note, completed=True
        )
        already_existing_task = Task(
            task_title="Already exsting Task",
            task_desc="Already existing task description",
            note=note,
            metadata={"taskId": "123"},
        )
        task.save()
        no_metadata_task.save()
        completed_task.save()
        already_existing_task.save()

        note.task_set.add(task)
        note.task_set.add(no_metadata_task)
        note.task_set.add(completed_task)
        note.task_set.add(already_existing_task)

        self.salesforce.add_interaction_with_client(note)

        self.assertTrue(len(self.simple_salesforce.query_all("SELECT Id FROM Task")["records"]), 3)

        task = Task.objects.get(pk=task.pk)
        salesforce_task = self.simple_salesforce.Task.get(task.metadata["taskId"])  # type: ignore[index, operator]
        self.assertEqual(salesforce_task["Subject"], "Task One")
        self.assertEqual(salesforce_task["Description"], "Follow up from client meeting.")
        self.assertEqual(salesforce_task["Status"], "Not Started")
        self.assertEqual(salesforce_task["Priority"], "Normal")
        self.assertEqual(salesforce_task["WhoId"], account_id)
        self.assertEqual(
            salesforce_task["ActivityDate"],
            (datetime.today().date() + timedelta(7)).strftime("%Y-%m-%dT%H:%M:%S.%f%z"),
        )
        self.assertEqual(salesforce_task["OwnerId"], user_id)

        no_metadata_task = Task.objects.get(pk=no_metadata_task.pk)
        salesforce_no_metadata_task = self.simple_salesforce.Task.get(no_metadata_task.metadata["taskId"])  # type: ignore[index, operator]
        self.assertEqual(salesforce_no_metadata_task["Subject"], "Task Two")
        self.assertEqual(
            salesforce_no_metadata_task["Description"], "Follow up from client meeting.\nTask two description"
        )
        self.assertEqual(salesforce_no_metadata_task["Status"], "Not Started")
        self.assertEqual(salesforce_no_metadata_task["Priority"], "Normal")
        self.assertEqual(salesforce_no_metadata_task["WhoId"], account_id)
        self.assertEqual(
            salesforce_task["ActivityDate"],
            (datetime.today().date() + timedelta(7)).strftime("%Y-%m-%dT%H:%M:%S.%f%z"),
        )
        self.assertEqual(salesforce_task["OwnerId"], user_id)

        completed_task = Task.objects.get(pk=completed_task.pk)
        salesforce_completed_task = self.simple_salesforce.Task.get(completed_task.metadata["taskId"])  # type: ignore[index, operator]
        self.assertEqual(salesforce_completed_task["Subject"], "Completed Task")
        self.assertEqual(
            salesforce_completed_task["Description"], "Follow up from client meeting.\nCompleted task description"
        )
        self.assertEqual(salesforce_completed_task["Status"], "Completed")
        self.assertEqual(salesforce_completed_task["Priority"], "Normal")
        self.assertEqual(salesforce_completed_task["WhoId"], account_id)
        self.assertEqual(
            salesforce_task["ActivityDate"],
            (datetime.today().date() + timedelta(7)).strftime("%Y-%m-%dT%H:%M:%S.%f%z"),
        )
        self.assertEqual(salesforce_task["OwnerId"], user_id)

        already_existing_task = Task.objects.get(pk=already_existing_task.pk)
        with self.assertRaises(simple_salesforce.exceptions.SalesforceResourceNotFound):
            self.simple_salesforce.Task.get(already_existing_task.metadata["taskId"])  # type: ignore[index, operator]

    @mock_salesforce
    def test_preview_before_syncing_with_crm(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newContactAndClient("Account one", user_id, org)

        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="completed",
            metadata={"meeting_name": "Test Meeting", "interactionId": "test_interaction_id"},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        Task.objects.create(task_title="Task 1", task_desc="Description 1", note=note)
        Task.objects.create(task_title="Task 2", task_desc="Description 2", note=note, completed=True)

        with patch.object(Note, "get_summary_for_crm", return_value="Test summary"):
            preview = self.salesforce.preview_before_syncing_with_crm(note)

        self.assertIsNotNone(preview)
        self.assertEqual(preview["Owner ID"], user_id)
        self.assertEqual(preview["Name"], "Test Meeting")
        self.assertEqual(preview["Meeting Notes"], "Test summary")
        self.assertEqual(preview["Account Id"], account_id)
        self.assertEqual(preview["Next Steps"], "*Task 1\n*Task 2")

        self.assertEqual(len(preview["tasks"]), 2)
        self.assertEqual(preview["tasks"][0]["Subject"], "Task 1")
        self.assertEqual(preview["tasks"][0]["Description"], "Follow up from client meeting.\nDescription 1")
        self.assertEqual(preview["tasks"][0]["Status"], "Not Started")
        self.assertEqual(preview["tasks"][0]["Related To"], account_id)
        self.assertEqual(preview["tasks"][0]["Owner Email"], user_email)

        self.assertEqual(preview["tasks"][1]["Subject"], "Task 2")
        self.assertEqual(preview["tasks"][1]["Description"], "Follow up from client meeting.\nDescription 2")
        self.assertEqual(preview["tasks"][1]["Status"], "Completed")

    @mock_salesforce
    def test_preview_before_syncing_with_crm_no_owner(self) -> None:
        self.populateSalesforceTypes()
        note = Note.objects.create(
            note_owner=User.objects.create(email="<EMAIL>"),
            status="completed",
            metadata={},
            client={"uuid": "fake_account_id"},
            created=timezone.now(),
        )

        preview = self.salesforce.preview_before_syncing_with_crm(note)
        self.assertEqual(preview, {})

    @mock_salesforce
    def test_preview_before_syncing_with_crm_no_client(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        (_, user_email) = self.newUser(org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="completed",
            metadata={},
            client=None,
            created=timezone.now(),
        )

        preview = self.salesforce.preview_before_syncing_with_crm(note)
        self.assertEqual(preview, {})

    @mock_salesforce
    def test_preview_before_syncing_with_crm_no_interaction_id(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        _, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="completed",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        preview = self.salesforce.preview_before_syncing_with_crm(note)
        self.assertEqual(preview, {})

    @mock_salesforce
    def test_preview_before_syncing_with_crm_exception_handling(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        _, client = self.newContactAndClient("Account one", user_id, org)
        note = Note.objects.create(
            note_owner=User.objects.get(email=user_email),
            status="completed",
            metadata={"interactionId": "test_interaction_id"},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )

        with patch.object(Note, "get_summary_for_crm", side_effect=Exception("Test exception")):
            with self.assertRaises(Exception):
                self.salesforce.preview_before_syncing_with_crm(note)

    @mock_salesforce
    def test_add_interaction_task_id_association(self) -> None:
        self.populateSalesforceTypes()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Account one", user_id, org)
        note = Note(
            note_owner=User.objects.get(email=user_email),
            status="scheduled",
            metadata={},
            client={"uuid": str(client.uuid)},
            created=timezone.now(),
        )
        note.save()

        task = Task(task_title="Task One", note=note)
        task.save()

        note.task_set.add(task)

        with patch("deepinsights.core.integrations.crm.salesforce.salesforce_utils.add_task") as add_task_mock:
            self.salesforce.add_interaction_with_client(note)
            add_task_mock.assert_called_once_with(
                ANY,
                ANY,
                ANY,
                {"WhoId": account_id},
            )

            # Trigger an add failure to see if the addition is retried with a different ID association.
            add_task_mock.reset()
            add_task_mock.side_effect = [Exception("Test error"), None]

            self.salesforce.add_interaction_with_client(note)

            add_task_mock.assert_has_calls(
                [call(ANY, ANY, ANY, {"WhoId": account_id}), call(ANY, ANY, ANY, {"WhatId": account_id})]
            )

    @mock_salesforce
    def test_fetch_notes_for_client_account_successful(self) -> None:
        self.populateContentNoteAndContentDocumentLink()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_id=user_id, org=org)
        contact_id, _ = self.newContactAndClient("Client one", owner_id=user_id, org=org)

        note_creation_time = datetime.now(tz=datetime_timezone.utc)

        note = self.simple_salesforce.Note.create(  # type: ignore[operator]
            {
                "Title": "Test Summary",
                "Body": "Test meeting notes",
                "ParentId": account_id,
            }
        )
        # Update the created date to be a timezone-aware date. There's a bug in simple-mockforce
        # which makes "system" dates (like `CreatedDate`) not timezone-aware.
        self.simple_salesforce.Note.update(  # type: ignore[operator]
            note["id"], {"CreatedDate": note_creation_time.strftime("%Y-%m-%dT%H:%M:%S.%f%z")}
        )

        self.simple_salesforce.Note.create(  # type: ignore[operator]
            {
                "Title": "Test Summary two",
                "Body": "Test meeting notes two",
                "ParentId": contact_id,
            }
        )

        result = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))
        self.assertEqual(
            result,
            [
                CRMNote(
                    crm_id=note["id"],
                    crm_system="salesforce",
                    content="Test Summary\nTest meeting notes",
                    created_at=note_creation_time,
                    type=CRMNoteType.SALESFORCE_STANDARD_NOTE,
                    web_link=f"https://test.salesforce.com/{note['id']}",
                )
            ],
        )

    @mock_salesforce
    def test_fetch_notes_for_client_contact_successful(self) -> None:
        self.populateContentNoteAndContentDocumentLink()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, _ = self.newAccountAndClient("Account one", owner_id=user_id, org=org)
        contact_id, client = self.newContactAndClient("Client one", owner_id=user_id, org=org)

        note_creation_time = datetime.now(tz=datetime_timezone.utc)

        self.simple_salesforce.Note.create(  # type: ignore[operator]
            {
                "Title": "Test Summary",
                "Body": "Test meeting notes",
                "ParentId": account_id,
            }
        )
        # Update the created date to be a timezone-aware date. There's a bug in simple-mockforce
        # which makes "system" dates (like `CreatedDate`) not timezone-aware.

        note = self.simple_salesforce.Note.create(  # type: ignore[operator]
            {
                "Title": "Test Summary two",
                "Body": "Test meeting notes two",
                "ParentId": contact_id,
            }
        )
        self.simple_salesforce.Note.update(  # type: ignore[operator]
            note["id"], {"CreatedDate": note_creation_time.strftime("%Y-%m-%dT%H:%M:%S.%f%z")}
        )

        self.assertEqual(
            self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365)),
            [
                CRMNote(
                    crm_id=note["id"],
                    crm_system="salesforce",
                    content="Test Summary two\nTest meeting notes two",
                    created_at=note_creation_time,
                    type=CRMNoteType.SALESFORCE_STANDARD_NOTE,
                    web_link=f"https://test.salesforce.com/{note['id']}",
                )
            ],
        )

    @mock_salesforce
    def test_fetch_notes_for_client_no_notes(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        _, client = self.newAccountAndClient("Account one", owner_id=user_id, org=org)

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))
        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_notes_for_client_no_crm_id(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        _, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        client = Client.objects.create(name="Test Client", organization=org)  # No CRM ID

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))
        self.assertEqual(notes, [])

    @mock_salesforce
    def test_fetch_notes_for_client_date_filter(self) -> None:
        self.populateContentNoteAndContentDocumentLink()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_id=user_id, org=org)

        # Create old note (more than 30 days ago)
        old_date = (datetime.now(tz=datetime_timezone.utc) - timedelta(days=40)).strftime("%Y-%m-%dT%H:%M:%S.%f%z")
        old_note = self.simple_salesforce.Note.create(  # type: ignore[operator]
            {
                "Title": "Test Summary old",
                "Body": "Test meeting notes old",
                "ParentId": account_id,
            }
        )
        self.simple_salesforce.Note.update(  # type: ignore[operator]
            old_note["id"], {"CreatedDate": old_date}
        )

        # Create recent note
        recent_date = datetime.now(datetime_timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f%z")
        recent_note = self.simple_salesforce.Note.create(  # type: ignore[operator]
            {
                "Title": "Test Summary recent",
                "Body": "Test meeting notes recent",
                "ParentId": account_id,
            }
        )
        self.simple_salesforce.Note.update(  # type: ignore[operator]
            recent_note["id"], {"CreatedDate": recent_date}
        )

        # Test with 30 day filter
        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=30))
        self.assertEqual(len(notes), 1)
        self.assertEqual(notes[0].crm_id, recent_note["id"])

    @mock_salesforce
    def test_fetch_notes_for_client_salesforce_error(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        _, client = self.newAccountAndClient("Account one", owner_id=user_id, org=org)

        with patch.object(self.simple_salesforce, "query_all", side_effect=Exception("Salesforce API error")):
            # Fetch notes should handle the exception and return an empty list
            result = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))
            self.assertEqual(result, [])

    @mock_salesforce
    def test_fetch_notes_for_client_multiple_notes(self) -> None:
        self.populateContentNoteAndContentDocumentLink()
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_id=user_id, org=org)

        note_ids: list[str] = []
        note_start_time = datetime.now(tz=datetime_timezone.utc)
        for i in range(3):
            note = self.simple_salesforce.Note.create(  # type: ignore[operator]
                {
                    "Title": f"Test Summary {i}",
                    "Body": f"Test meeting notes {i}",
                    "ParentId": account_id,
                }
            )
            self.simple_salesforce.Note.update(  # type: ignore[operator]
                note["id"], {"CreatedDate": note_start_time.strftime("%Y-%m-%dT%H:%M:%S.%f%z")}
            )
            note_ids.append(note["id"])

        notes = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

        self.assertEqual(
            notes,
            [
                CRMNote(
                    crm_id=note_ids[0],
                    crm_system="salesforce",
                    content="Test Summary 0\nTest meeting notes 0",
                    created_at=note_start_time,
                    type=CRMNoteType.SALESFORCE_STANDARD_NOTE,
                    web_link=f"https://test.salesforce.com/{note_ids[0]}",
                ),
                CRMNote(
                    crm_id=note_ids[1],
                    crm_system="salesforce",
                    content="Test Summary 1\nTest meeting notes 1",
                    created_at=note_start_time,
                    type=CRMNoteType.SALESFORCE_STANDARD_NOTE,
                    web_link=f"https://test.salesforce.com/{note_ids[1]}",
                ),
                CRMNote(
                    crm_id=note_ids[2],
                    crm_system="salesforce",
                    content="Test Summary 2\nTest meeting notes 2",
                    created_at=note_start_time,
                    type=CRMNoteType.SALESFORCE_STANDARD_NOTE,
                    web_link=f"https://test.salesforce.com/{note_ids[2]}",
                ),
            ],
        )

    @mock_salesforce
    def test_fetch_notes_for_client_invalid_date(self) -> None:
        # Create empty ContentNote and ContentDocumentLink objects to popualte them in the mock
        # Salesforce instance.
        #
        # ContentDocumentLink is ordered by its ContentDocumentId, so the fake ContentDocumentLink
        # needs to have that key present.
        self.simple_salesforce.ContentNote.create({})  # type: ignore[operator]
        self.simple_salesforce.ContentDocumentLink.create({"ContentDocumentId": ""})  # type: ignore[operator]

        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        user = User.objects.get(email=user_email)
        account_id, client = self.newAccountAndClient("Account one", owner_id=user_id, org=org)

        note = self.simple_salesforce.Note.create(  # type: ignore[operator]
            {
                "Title": "Test Summary",
                "Body": "Test meeting notes",
                "ParentId": account_id,
            }
        )
        self.simple_salesforce.Note.update(  # type: ignore[operator]
            note["id"], {"CreatedDate": "invalid"}
        )

        self.assertEqual(
            self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365)),
            [
                CRMNote(
                    crm_id=note["id"],
                    crm_system="salesforce",
                    content="Test Summary\nTest meeting notes",
                    created_at=None,
                    type=CRMNoteType.SALESFORCE_STANDARD_NOTE,
                    web_link=f"https://test.salesforce.com/{note['id']}",
                )
            ],
        )

    @mock_salesforce
    def test_fetch_notes_for_client_with_no_crm_id(self) -> None:
        """Test that fetch_notes_for_client returns an empty list when the client has no CRM ID."""
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="test_user", email="<EMAIL>", organization=org)
        client = Client.objects.create(name="Test Client", organization=org)  # No CRM ID

        result = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

        self.assertEqual(result, [])

    @mock_salesforce
    def test_fetch_notes_for_client_with_content_version_data(self) -> None:
        """Test fetching ContentVersion data from Salesforce API."""
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Test Account", user_id, org)

        content_doc_id = str(uuid4())

        # Mock ContentDocumentLink query results
        content_doc_link_records = [{"ContentDocumentId": content_doc_id, "LinkedEntityId": account_id}]
        content_doc_link_result = {"records": content_doc_link_records}

        # Mock ContentVersion query results
        creation_date = timezone.now()
        version_id = str(uuid4())
        version_data_url = "/services/data/v53.0/sobjects/ContentVersion/123456/VersionData"
        version_records = [
            {
                "Id": version_id,
                "ContentDocumentId": content_doc_id,
                "VersionData": version_data_url,
                "Title": "Content Version Title",
                "CreatedDate": creation_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
            }
        ]
        version_result = {"records": version_records}

        # Create patched query_all that returns our mock data
        def mock_query_all(query: str) -> dict[str, Any]:
            if "ContentDocumentLink" in query:
                return content_doc_link_result
            elif "ContentVersion" in query:
                return version_result
            return {"records": []}

        with patch.object(self.simple_salesforce, "query_all", side_effect=mock_query_all):
            # Mock the session attributes and get method
            self.simple_salesforce.sf_instance = "test.salesforce.com"
            self.simple_salesforce.session_id = "fake_session_id"

            with patch.object(self.simple_salesforce.session, "get") as mock_get:
                mock_response = MagicMock()
                mock_response.text = "This is the ContentVersion data"
                mock_response.raise_for_status = MagicMock()
                mock_get.return_value = mock_response

                # Fetch notes
                user = User.objects.get(email=user_email)
                result = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

                # Verify session.get was called with correct URL and headers
                expected_url = f"https://test.salesforce.com{version_data_url}"
                expected_headers = {"Authorization": "Bearer fake_session_id"}
                mock_get.assert_called_with(expected_url, headers=expected_headers)

                # Verify content note data was processed correctly
                self.assertEqual(
                    result,
                    [
                        CRMNote(
                            crm_id=content_doc_id,
                            crm_system="salesforce",
                            content="Content Version Title\nThis is the ContentVersion data",
                            created_at=creation_date,
                            type=CRMNoteType.SALESFORCE_CONTENT_NOTE,
                            web_link=f"https://test.salesforce.com/{content_doc_id}",
                        ),
                    ],
                )

    @mock_salesforce
    def test_fetch_notes_for_client_handles_content_version_errors(self) -> None:
        """Test that fetch_notes_for_client handles errors when retrieving ContentVersion data."""
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account_id, client = self.newAccountAndClient("Test Account", user_id, org)

        # Create ContentNote
        content_doc_id = str(uuid4())

        # Mock ContentDocumentLink query results
        content_doc_link_records = [{"ContentDocumentId": content_doc_id, "LinkedEntityId": account_id}]
        content_doc_link_result = {"records": content_doc_link_records}

        # Mock ContentVersion query results with VersionData URL
        creation_date = timezone.now()
        version_id = str(uuid4())
        version_data_url = "/services/data/v53.0/sobjects/ContentVersion/123456/VersionData"
        version_records = [
            {
                "Id": version_id,
                "ContentDocumentId": content_doc_id,
                "VersionData": version_data_url,
                "Title": "Content Version Title",
                "CreatedDate": creation_date.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
            }
        ]
        version_result = {"records": version_records}

        # Create patched query_all that returns our mock data
        def mock_query_all(query: str) -> dict[str, Any]:
            if "ContentDocumentLink" in query:
                return content_doc_link_result
            elif "ContentVersion" in query:
                return version_result
            return {"records": []}

        with patch.object(self.simple_salesforce, "query_all", side_effect=mock_query_all):
            # Mock the session attributes
            self.simple_salesforce.sf_instance = "test.salesforce.com"
            self.simple_salesforce.session_id = "fake_session_id"

            # Mock the session.get to raise an exception when fetching content
            with patch.object(self.simple_salesforce.session, "get") as mock_get:
                mock_get.side_effect = Exception("Error fetching content")

                # Fetch notes
                user = User.objects.get(email=user_email)
                result: list[CRMNote] = self.salesforce.fetch_notes_for_client(user, client, timedelta(days=365))

                # Verify note was still created despite the error fetching content
                self.assertEqual(
                    result,
                    [
                        CRMNote(
                            crm_id=content_doc_id,
                            crm_system="salesforce",
                            content="Content Version Title\n[Error retrieving content]",
                            created_at=creation_date,
                            type=CRMNoteType.SALESFORCE_CONTENT_NOTE,
                            web_link=f"https://test.salesforce.com/{content_doc_id}",
                        )
                    ],
                )
