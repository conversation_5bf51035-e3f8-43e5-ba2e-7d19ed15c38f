import logging
from datetime import datetime, timedelta, timezone
from typing import Any, List, Tuple
from unittest.mock import patch
from uuid import uuid4

import zoneinfo
from django.test import TestCase
from django.utils import timezone as django_timezone
from simple_mockforce import mock_salesforce
from simple_salesforce.api import Salesforce

from deepinsights.core.integrations.crm.crm_models import CRMAccount
from deepinsights.core.integrations.crm.sequoia_salesforce import SequoiaSalesforce
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingData, StructuredMeetingDataTemplate
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User


class SequoiaSalesforceTestCase(TestCase):
    def setUp(self) -> None:
        self.salesforce = Salesforce(instance="test.salesforce.com", session_id="")

    # Creates a new account with the given service team.
    def newAccount(self, service_team: list[str]) -> dict[str, Any]:
        return self.salesforce.Account.create(  # type: ignore[no-any-return, operator]
            {
                "Name": f"Test Account {uuid4()}",
                "Service_Team_User_Ids__c": ",".join(service_team),
            }
        )

    # Creates a new arbitrary user, both in the Salesforce and Zeplyn databases.
    #
    # Returns a tuple of (userId, userEmail).
    def newUser(self, org: Organization) -> tuple[str, str]:
        email = f"{uuid4()}@example.com"
        user = self.salesforce.User.create({"Email": email})  # type: ignore[operator]
        User.objects.create(username=email, email=email, organization=org)
        return (user["id"], email)

    # Creates a new Salesforce case.
    def newCaseAndClient(
        self,
        *,
        account_id: str,
        attendees: str = "",
        zeplyn_id: str | None = None,
        timestamp: datetime = django_timezone.now(),
        closed: bool = False,
        org: Organization,
    ) -> Tuple[dict[str, Any], Client]:
        case = self.salesforce.Case.create(  # type: ignore[operator]
            {
                "AccountId": account_id,
                "Attendees__c": attendees,
                "CaseNumber": str(uuid4()),
                "Client_Review_Start_Time__c": timestamp.strftime("%Y-%m-%dT%H:%M:%S.%f%z"),
                "Subject": "Meeting",
                "Zeplyn_Id__c": zeplyn_id,
                "Client_Review_Location__c": "test",
                "Meeting_Link__c": None,
                "IsClosed": closed,
            }
        )
        client = Client.objects.create(
            crm_id=case["id"], name="Meeting", organization=org, client_type="test", crm_system="test"
        )
        return (case, client)


class SequoiaSalesforceTests(SequoiaSalesforceTestCase):
    # Tests that the Zeplyn note is removed for a closed, scheduled note.
    @mock_salesforce
    def testRemoveClosedScheduledNote(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        accounts = []
        for _ in range(1, 10):
            users = []
            for _ in range(1, 5):
                users.append(self.newUser(org))
            ids = []
            for user_id, _ in users:
                ids.append(user_id)
            account = self.newAccount(ids)
            accounts.append(account)

        note_id = str(uuid4())
        timestamp = django_timezone.now()
        case, _ = self.newCaseAndClient(
            account_id=accounts[0]["id"], zeplyn_id=note_id, timestamp=timestamp, closed=True, org=org
        )

        # Create a note
        user = User.objects.all()[0]
        note = Note(
            uuid=note_id,
            note_owner=user,
            status="scheduled",
            metadata={"scheduled_at": str(timestamp)},
            salesforce_case_id=case["id"],
        )
        note.save()
        note.authorized_users.add(user)
        note.save()
        self.assertEqual(len(Note.objects.all()), 1)

        # Grab the existing case for later comparison.
        expected_case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]

        # Run a SalesForce<->Zeplyn sync.
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        cases = sequoia_sf.get_sf_cases()["records"]
        sequoia_sf.generate_notes_from_cases(cases)

        # Confirm that the note has been removed, but the case has not been touched.
        self.assertEqual(len(Note.objects.all()), 0)
        self.assertEqual(expected_case, self.salesforce.Case.get(case["id"]))  # type: ignore[operator]

    # Tests that the Zeplyn note is not removed for a closed, finalized note.
    @mock_salesforce
    def testDoesNotRemoveClosedFinalizedNote(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        accounts = []
        for _ in range(1, 10):
            users = []
            for _ in range(1, 5):
                users.append(self.newUser(org))
            ids = []
            for user_id, _ in users:
                ids.append(user_id)
            account = self.newAccount(ids)
            accounts.append(account)

        note_id = str(uuid4())
        timestamp = django_timezone.now()
        case, _ = self.newCaseAndClient(
            account_id=accounts[0]["id"], zeplyn_id=note_id, timestamp=timestamp, closed=True, org=org
        )

        # Create a note
        user = User.objects.all()[0]
        note = Note(
            uuid=note_id,
            note_owner=user,
            status="finalized",
            metadata={"scheduled_at": str(timestamp)},
            salesforce_case_id=case["id"],
        )
        note.save()
        note.authorized_users.add(user)
        note.save()
        self.assertEqual(len(Note.objects.all()), 1)

        # Grab the existing case for later comparison.
        expected_case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]

        # Run a SalesForce<->Zeplyn sync.
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        cases = sequoia_sf.get_sf_cases()["records"]
        sequoia_sf.generate_notes_from_cases(cases)

        # Confirm that the note still exists and the Salesforce case is unchanged.
        self.assertEqual(len(Note.objects.all()), 1)
        self.assertIsNotNone(self.salesforce.Case.get(case["id"])["Zeplyn_Id__c"])  # type: ignore[operator]
        self.assertEqual(expected_case, self.salesforce.Case.get(case["id"]))  # type: ignore[operator]

    # Tests that the Zeplyn note updates a corresponding Salesforce case.
    @mock_salesforce
    def testAddInteractionWithClientWithCaseId(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        note_id = str(uuid4())
        timestamp = django_timezone.now()
        case, _ = self.newCaseAndClient(account_id=account["id"], zeplyn_id=note_id, timestamp=timestamp, org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        note = Note(
            uuid=note_id,
            note_owner=user,
            status="finalized",
            metadata={
                "meeting_name": "Meeting",
                "tags": ["tag1", "tag2"],
                "meeting_duration": 3600,
                "scheduled_at": str(timestamp),
            },
            salesforce_case_id=case["id"],
        )
        note.save()
        Attendee.objects.create(attendee_name="Bob", note=note).save()
        Attendee.objects.create(attendee_name="Paul", note=note).save()
        note.authorized_users.add(user)
        note.save()

        Task.objects.create(task_title="Task one", task_owner=user, note=note, due_date=django_timezone.now())
        Task.objects.create(task_title="Task two", task_owner=user, note=note, due_date=django_timezone.now())

        # Add an interaction
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(note)

        # Confirm that the case was updated (and correctly)
        case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]
        self.assertEqual(case["Subject"], "Meeting")
        self.assertEqual(case["Zeplyn_Meeting_Tags__c"], "tag1;tag2")
        self.assertIn(case["Attendee_Speaker_Allocations_2__c"], ["Bob, Paul", "Paul, Bob"])
        self.assertIsNotNone(case["Cases_To_Create__c"])
        self.assertIsNotNone(case["Meeting_Summary__c"])
        self.assertEqual(case["Meeting_Duration__c"], 1)

    # Tests that adding an interaction for a note with a client works as expected.
    @mock_salesforce
    def testAddInteractionWithClientWithClientMetadataUUIDAsCRMID(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        note_id = str(uuid4())
        timestamp = django_timezone.now()
        case, client = self.newCaseAndClient(account_id=account["id"], org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        note = Note(
            uuid=note_id,
            note_owner=user,
            status="finalized",
            metadata={
                "meeting_name": "Zeplyn meeting",
                "tags": ["tag1", "tag2"],
                "meeting_duration": 3600,
                "scheduled_at": str(timestamp),
            },
            client={"name": "client", "uuid": case["id"]},
            # No salesforce_case_id, because that takes precedence over using the client UUID as a CRM ID.
        )
        note.save()
        note.authorized_users.add(user)
        note.save()

        Task.objects.create(
            task_title="Task one",
            task_owner=user,
            note=note,
            due_date=datetime(2024, 8, 4, 0, 0, 1, tzinfo=timezone.utc),
        )
        Task.objects.create(
            task_title="Task two",
            task_owner=user,
            note=note,
            due_date=datetime(2024, 8, 12, 9, 8, 7, tzinfo=timezone.utc),
        )
        Task.objects.create(
            task_owner=user,
            note=note,
        )

        # Add an interaction
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(note)

        # Confirm that the case was updated
        case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]
        self.assertEqual(case["Subject"], "Meeting")
        self.assertEqual(case["Cases_To_Create__c"], "Task one<br>\nTask two<br>\nNo title")

    # Tests that adding an interaction for a note with a client works as expected.
    @mock_salesforce
    def testAddInteractionWithClientWithClientMetadataUUID(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        note_id = str(uuid4())
        timestamp = django_timezone.now()
        case, client = self.newCaseAndClient(account_id=account["id"], org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        note = Note(
            uuid=note_id,
            note_owner=user,
            status="finalized",
            metadata={
                "meeting_name": "Zeplyn meeting",
                "tags": ["tag1", "tag2"],
                "meeting_duration": 3600,
                "scheduled_at": str(timestamp),
            },
            client={"name": "client", "uuid": str(client.uuid)},
            salesforce_case_id="other_case_id",
        )
        note.save()
        note.authorized_users.add(user)
        note.save()

        Task.objects.create(
            task_title="Task one",
            task_owner=user,
            note=note,
            due_date=datetime(2024, 8, 4, 0, 0, 1, tzinfo=timezone.utc),
        )
        Task.objects.create(
            task_title="Task two",
            task_owner=user,
            note=note,
            due_date=datetime(2024, 8, 12, 9, 8, 7, tzinfo=timezone.utc),
        )
        Task.objects.create(
            task_owner=user,
            note=note,
        )

        # Add an interaction
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(note)

        # Confirm that the case was updated
        case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]
        self.assertEqual(case["Subject"], "Meeting")
        self.assertEqual(case["Cases_To_Create__c"], "Task one<br>\nTask two<br>\nNo title")

    # Tests that adding an interaction for a note with a client but no UUID works as expected.
    @mock_salesforce
    def testAddInteractionWithClientWithoutID(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        note_id = str(uuid4())
        timestamp = django_timezone.now()
        case, _ = self.newCaseAndClient(account_id=account["id"], org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        note = Note(
            uuid=note_id,
            note_owner=user,
            status="finalized",
            metadata={
                "meeting_name": "Meeting",
                "tags": ["tag1", "tag2"],
                "meeting_duration": 3600,
                "scheduled_at": str(timestamp),
            },
            client={"name": "client"},
        )
        note.save()
        note.authorized_users.add(user)
        note.save()

        Task.objects.create(task_title="Task one", task_owner=user, note=note, due_date=django_timezone.now())
        Task.objects.create(task_title="Task two", task_owner=user, note=note, due_date=django_timezone.now())

        expected_case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]
        # Add an interaction
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(note)

        # Confirm that the case was not updated
        self.assertEqual(expected_case, self.salesforce.Case.get(case["id"]))  # type: ignore[operator]

    # Tests that adding an interaction for a note with no client and not case ID works as expected.
    @mock_salesforce
    def testAddInteractionWithoutCaseOrClient(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        note_id = str(uuid4())
        timestamp = django_timezone.now()
        case, _ = self.newCaseAndClient(account_id=account["id"], org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        note = Note(
            uuid=note_id,
            note_owner=user,
            status="finalized",
            metadata={
                "meeting_name": "Meeting",
                "tags": ["tag1", "tag2"],
                "meeting_duration": 3600,
                "scheduled_at": str(timestamp),
            },
        )
        note.save()
        note.authorized_users.add(user)
        note.save()

        Task.objects.create(task_title="Task one", task_owner=user, note=note, due_date=django_timezone.now())
        Task.objects.create(task_title="Task two", task_owner=user, note=note, due_date=django_timezone.now())

        expected_case = self.salesforce.Case.get(case["id"])  # type: ignore[operator]
        # Add an interaction
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(note)

        # Confirm that the case was not updated
        self.assertEqual(expected_case, self.salesforce.Case.get(case["id"]))  # type: ignore[operator]

    @mock_salesforce
    def testGetAccountsByOwnerEmailAndNameClosed(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        self.newCaseAndClient(account_id=account["id"], closed=True, org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        accounts = sequoia_sf.get_accounts_by_owner_email_and_name(user.email, "")
        self.assertEqual(len(accounts), 0)

    @mock_salesforce
    def testGetAccountsByOwnerEmailAndNameOpen(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        self.newCaseAndClient(account_id=account["id"], closed=False, org=org)

        user = User.objects.get(email=user_email)
        user.crm_configuration["crm_system"] = "sequoia_sf"
        user.save()

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        cases = sequoia_sf.get_accounts_by_owner_email_and_name(user.email, "")
        self.assertEqual(len(cases), 1)

    def create_test_note_with_tasks(
        self, user: User, note_metadata: dict[str, Any] | None = None, task_titles: list[str] | None = None
    ) -> Note:
        note = Note.objects.create(
            uuid=str(uuid4()),
            note_owner=user,
            status=Note.PROCESSING_STATUS.finalized,
            metadata=note_metadata or {},
            salesforce_case_id="some_case_id",
        )

        if task_titles:
            due_date = django_timezone.now() + timedelta(days=7)
            logging.info(f"Calculated due date: {due_date}")
            for title in task_titles:
                Task.objects.create(
                    note=note,
                    task_owner=user,
                    task_title=title,
                    due_date=due_date,
                )

        return note

    @mock_salesforce
    def test_preview_before_syncing_with_crm(self) -> None:
        # Create a test user
        user = User.objects.create(username="testuser", email="<EMAIL>")

        # Create a test note with some metadata and tasks
        note_metadata = {
            "tags": ["tag1", "tag2"],
            "meeting_duration": 7200,  # 2 hours in seconds
        }
        task_titles = ["Task 1", "Task 2"]
        note = self.create_test_note_with_tasks(user, note_metadata, task_titles)
        note.client = {"crm_id": "some_crm_id", "uuid": "some_uuid"}

        # Initialize SequoiaSalesforce
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)

        # Get the preview data
        preview_data = sequoia_sf.preview_before_syncing_with_crm(note)

        # Verify the preview data
        self.assertIsNotNone(preview_data)
        self.assertEqual(preview_data["Zeplyn Meeting Tags"], "tag1;tag2")
        self.assertEqual(preview_data["Attendee Speaker Allocations"], "")
        self.assertEqual(preview_data["Cases To Create"], "Task 1<br>\nTask 2")
        self.assertEqual(preview_data["Meeting Summary"], note.get_summary_for_crm(use_html_formatting=True))
        self.assertEqual(preview_data["Meeting Duration"], 2)
        self.assertEqual(preview_data["Case ID"], "some_case_id")

    @mock_salesforce
    def test_preview_before_syncing_with_crm_no_case_id_or_client_uuid(self) -> None:
        # Create a test user
        user = User.objects.create(username="testuser", email="<EMAIL>")

        # Create a test note without a salesforce_case_id and client UUID
        note = Note.objects.create(
            uuid=str(uuid4()),
            note_owner=user,
            status=Note.PROCESSING_STATUS.finalized,
            metadata={"tags": ["tag1"]},
            salesforce_case_id=None,
            client=None,
        )

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)

        with self.assertLogs(level="ERROR") as log:
            preview_data = sequoia_sf.preview_before_syncing_with_crm(note)
            self.assertEqual(preview_data, {})
            self.assertIn(
                "Note has neither case ID nor valid client UUID. Not updating in Salesforce",
                log.output[0],
            )

    @mock_salesforce
    def test_preview_before_syncing_with_uuid_as_crm_id(self) -> None:
        user = User.objects.create(username="testuser", email="<EMAIL>")

        note = Note.objects.create(
            uuid=str(uuid4()),
            note_owner=user,
            status=Note.PROCESSING_STATUS.finalized,
            metadata={"tags": ["tag1"]},
            salesforce_case_id=None,
            client={"uuid": "some_crm_id"},
        )

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)

        preview_data = sequoia_sf.preview_before_syncing_with_crm(note)
        self.assertIsNotNone(preview_data)
        self.assertEqual(preview_data["Case ID"], "some_crm_id")

    @mock_salesforce
    def test_preview_before_syncing_with_uuid(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)
        client = Client.objects.create(crm_id="some_crm_id", name="Client", organization=org)
        note = Note.objects.create(
            uuid=str(uuid4()),
            note_owner=user,
            status=Note.PROCESSING_STATUS.finalized,
            metadata={"tags": ["tag1"]},
            salesforce_case_id=None,
            client={"uuid": str(client.uuid)},
        )

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)

        preview_data = sequoia_sf.preview_before_syncing_with_crm(note)
        self.assertIsNotNone(preview_data)
        self.assertEqual(preview_data["Case ID"], "some_crm_id")

    @mock_salesforce
    def test_preview_before_syncing_with_client_and_salesforce_case_id(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)
        client = Client.objects.create(crm_id="some_crm_id", name="Client", organization=org)
        note = Note.objects.create(
            uuid=str(uuid4()),
            note_owner=user,
            status=Note.PROCESSING_STATUS.finalized,
            metadata={"tags": ["tag1"]},
            salesforce_case_id="salesforce_case_id",
            client={"uuid": str(client.uuid)},
        )

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)

        preview_data = sequoia_sf.preview_before_syncing_with_crm(note)
        self.assertIsNotNone(preview_data)
        self.assertEqual(preview_data["Case ID"], "some_crm_id")

    @mock_salesforce
    def test_preview_before_syncing_with_salesforce_case_id(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)
        client = Client.objects.create(crm_id="some_crm_id", name="Client", organization=org)
        note = Note.objects.create(
            uuid=str(uuid4()),
            note_owner=user,
            status=Note.PROCESSING_STATUS.finalized,
            metadata={"tags": ["tag1"]},
            salesforce_case_id="salesforce_case_id",
        )

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)

        preview_data = sequoia_sf.preview_before_syncing_with_crm(note)
        self.assertIsNotNone(preview_data)
        self.assertEqual(preview_data["Case ID"], "salesforce_case_id")

    @mock_salesforce
    def test_preview_before_syncing_with_crm_logs_exception(self) -> None:
        # Create a test user
        user = User.objects.create(username="testuser", email="<EMAIL>")

        # Create a test note
        note = Note.objects.create(
            uuid=str(uuid4()),
            note_owner=user,
            status=Note.PROCESSING_STATUS.finalized,
            metadata={"tags": ["tag1"]},
            salesforce_case_id="some_case_id",
        )

        # Initialize SequoiaSalesforce
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)

        with patch(
            "deepinsights.meetingsapp.models.note.Note.get_summary_for_crm", side_effect=Exception("Test exception")
        ), self.assertLogs(level="ERROR") as log, self.assertRaises(Exception):
            # This should handle the exception and raise it
            sequoia_sf.preview_before_syncing_with_crm(note)

            # Check that the appropriate log message was created
            self.assertIn(
                f"Error previewing interaction with client {note.note_owner.uuid if note.note_owner else ''} for note {note.uuid}: Test exception",
                log.output[0],
            )

    @mock_salesforce
    def test_preview_before_syncing_with_crm_compliance_checklist(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)
        note_metadata = {
            "tags": ["tag1", "tag2"],
            "meeting_duration": 7200,
            "scheduled_at": "2023-10-10 10:00:00.000000+0000",
        }
        note = self.create_test_note_with_tasks(user, note_metadata, ["Task 1", "Task 2"])
        note.client = {"crm_id": "crm_id", "uuid": "uuid"}
        note.save()

        # Create a compliance template and associate it with the note
        compliance_template = StructuredMeetingData.objects.create(
            kind=StructuredMeetingDataTemplate.Kind.SEQUOIA_COMPLIANCE_CHECKLIST,
            data={
                "review_entries": [
                    {"id": "financial_status", "discussed": True},
                    {"id": "allocations_holdings", "discussed": True},
                    {"id": "investment_performance", "discussed": False},
                    {"id": "insurance_needs", "discussed": True},
                ]
            },
        )
        note.structuredmeetingdata_set.add(compliance_template)
        note.save()

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        preview_data = sequoia_sf.preview_before_syncing_with_crm(note)

        self.assertIsNotNone(preview_data)
        self.assertEqual(preview_data["Zeplyn Meeting Tags"], "tag1;tag2")
        self.assertEqual(preview_data["Attendee Speaker Allocations"], "")
        self.assertEqual(preview_data["Cases To Create"], "Task 1<br>\nTask 2")
        self.assertEqual(preview_data["Meeting Summary"], note.get_summary_for_crm(use_html_formatting=True))
        self.assertEqual(preview_data["Meeting Duration"], 2)
        self.assertEqual(preview_data["Case ID"], "some_case_id")
        self.assertEqual(preview_data["Zep_Current_Fin_Status_Discussed_Date__c"], "2023-10-10")
        self.assertEqual(preview_data["Zep_Asset_Alloc_Holdings_Discussed_Date__c"], "2023-10-10")
        self.assertEqual(preview_data["Zep_Insurance_Planning_Discussed_Date__c"], "2023-10-10")
        self.assertFalse("Zep_Inv_Performance_Discussed_Date__c" in preview_data)

    @mock_salesforce
    def testGetAccountsByOwnerIDOrEmailProvidesCorrectResults(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        case_one, _ = self.newCaseAndClient(account_id=account["id"], org=org)
        case_two, _ = self.newCaseAndClient(account_id=account["id"], org=org)

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        crm_accounts = sequoia_sf.get_accounts_by_owner_email_and_name(user_email)

        salesforce_case_one = self.salesforce.Case.get(case_one["id"])  # type: ignore[operator]
        salesforce_case_two = self.salesforce.Case.get(case_two["id"])  # type: ignore[operator]
        salesforce_account = self.salesforce.Account.get(account["id"])  # type: ignore[operator]

        self.assertEqual(
            crm_accounts,
            [
                CRMAccount(
                    crm_id=salesforce_case_one["Id"],
                    name=f"Meeting with {salesforce_account['Name']} - {salesforce_case_one['CaseNumber']}",
                    client_type="case",
                    crm_system="sequoia_sf",
                ),
                CRMAccount(
                    crm_id=salesforce_case_two["Id"],
                    name=f"Meeting with {salesforce_account['Name']} - {salesforce_case_two['CaseNumber']}",
                    client_type="case",
                    crm_system="sequoia_sf",
                ),
            ],
        )

    # Tests creating a note with Attendees that are None.
    @mock_salesforce
    def testGenerateNotesFromCaseWithNoneAttendees(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        users: list[tuple[str, str]] = []
        for _ in range(1, 5):
            users.append(self.newUser(org))
        ids = []
        for user_id, _ in users:
            ids.append(user_id)
        account = self.newAccount(ids)

        User.objects.create(username="<EMAIL>", email="<EMAIL>")
        case, client = self.newCaseAndClient(account_id=account["id"], attendees=None, org=org)  # type: ignore[arg-type]
        # Remove the created client to test client creation logic.
        client.delete()

        # Run a SalesForce<->Zeplyn sync.
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        cases = sequoia_sf.get_sf_cases()["records"]
        sequoia_sf.generate_notes_from_cases(cases)

        client = Client.objects.get(crm_id=case["id"])

        self.assertEqual(len(Note.objects.all()), 1)
        note = Note.objects.all()[0]
        self.assertEqual(self.salesforce.Case.get(case["id"])["Zeplyn_Id__c"], str(note.uuid))  # type: ignore[operator]
        self.assertEqual(note.meeting_type, MeetingType.objects.get(key="client"))
        self.assertNotIn("meeting_type", note.metadata or {})
        self.assertEqual(note.client, {"uuid": str(client.uuid), "name": client.name})
        self.assertIsNone(note.salesforce_case_id)

        # Ensure that a client is created.
        self.assertEqual(client.organization, org)
        account = self.salesforce.Account.get(account["id"])  # type: ignore[operator]
        self.assertEqual(client.name, f"Meeting with {account['Name']} - {cases[0]['CaseNumber']}")
        self.assertEqual(client.client_type, "case")
        self.assertEqual(client.crm_system, "sequoia_sf")
        self.assertEqual(client.organization, org)
        self.assertQuerySetEqual(note.authorized_users.all(), client.authorized_users.all())  # type: ignore[arg-type]
        self.assertEqual(set([u[1] for u in users]), set(client.authorized_users.all().values_list("email", flat=True)))

    # Tests creating a note with an existing client.
    @mock_salesforce
    def testGenerateNotesFromCaseWithExistingClient(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        users: list[(str, str)] = []  # type: ignore[type-arg]
        for _ in range(1, 5):
            users.append(self.newUser(org))
        ids = []
        for user_id, _ in users:
            ids.append(user_id)
        account = self.newAccount(ids)

        User.objects.create(username="<EMAIL>", email="<EMAIL>")
        case, client = self.newCaseAndClient(account_id=account["id"], attendees=None, org=org)  # type: ignore[arg-type]

        # Run a SalesForce<->Zeplyn sync.
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        cases = sequoia_sf.get_sf_cases()["records"]
        sequoia_sf.generate_notes_from_cases(cases)

        client.refresh_from_db()

        self.assertEqual(len(Note.objects.all()), 1)
        note = Note.objects.all()[0]
        self.assertEqual(self.salesforce.Case.get(case["id"])["Zeplyn_Id__c"], str(note.uuid))  # type: ignore[operator]
        self.assertEqual(note.meeting_type, MeetingType.objects.get(key="client"))
        self.assertNotIn("meeting_type", note.metadata or {})
        self.assertIsNone(note.salesforce_case_id)
        self.assertEqual(note.client, {"uuid": str(client.uuid), "name": client.name})

        # Ensure that a client is updated.
        self.assertEqual(client.organization, org)
        account = self.salesforce.Account.get(account["id"])  # type: ignore[operator]
        self.assertEqual(client.name, f"Meeting with {account['Name']} - {cases[0]['CaseNumber']}")
        self.assertEqual(client.client_type, "case")
        self.assertEqual(client.crm_system, "sequoia_sf")
        self.assertEqual(client.organization, org)
        self.assertQuerySetEqual(note.authorized_users.all(), client.authorized_users.all())  # type: ignore[arg-type]
        self.assertEqual(set([u[1] for u in users]), set(client.authorized_users.all().values_list("email", flat=True)))

    # Tests generating notes from cases when a note creation fails and throws an exception.
    @mock_salesforce
    def testGenerateNotesFromCaseWithCreationFailure(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        accounts = []
        users: List[(str, str)] = []  # type: ignore[type-arg]
        for _ in range(1, 10):
            users = []
            for _ in range(1, 5):
                users.append(self.newUser(org))
            ids = []
            for user_id, _ in users:
                ids.append(user_id)
            account = self.newAccount(ids)
            accounts.append(account)

        User.objects.create(username="<EMAIL>", email="<EMAIL>")
        case, client = self.newCaseAndClient(account_id=accounts[0]["id"], org=org)
        case_two, client_two = self.newCaseAndClient(account_id=accounts[0]["id"], org=org)

        # Run a SalesForce<->Zeplyn sync.
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)

        # Patch out the method that creates notes to raise an exception on the first call, then replace it with
        # the real method.
        real_create_method = sequoia_sf._create_new_note

        def fake_create_method(case: dict[str, Any]) -> Note | None:
            sequoia_sf._create_new_note = real_create_method  # type: ignore[method-assign]
            raise Exception

        sequoia_sf._create_new_note = fake_create_method  # type: ignore[method-assign]

        cases = sequoia_sf.get_sf_cases()["records"]
        sequoia_sf.generate_notes_from_cases(cases)

        self.assertEqual(len(Note.objects.all()), 1)
        note = Note.objects.all()[0]
        self.assertIsNone(self.salesforce.Case.get(case["id"])["Zeplyn_Id__c"])  # type: ignore[operator]
        self.assertEqual(self.salesforce.Case.get(case_two["id"])["Zeplyn_Id__c"], str(note.uuid))  # type: ignore[operator]

        clients = Client.objects.all()
        self.assertEqual(clients.count(), 2)

        client.refresh_from_db()
        client_two.refresh_from_db()

        self.assertEqual(client.crm_id, case["id"])
        self.assertEqual(client.name, "Meeting")
        self.assertEqual(client.client_type, "test")
        self.assertEqual(client.crm_system, "test")
        self.assertEqual(client.authorized_users.count(), 0)

        self.assertEqual(client_two.crm_id, case_two["id"])
        self.assertNotEqual(client_two.name, "Meeting")
        self.assertEqual(client_two.client_type, "case")
        self.assertEqual(client_two.crm_system, "sequoia_sf")
        self.assertQuerySetEqual(client_two.authorized_users.all(), list(note.authorized_users.all()))

    @mock_salesforce
    def test_generate_notes_from_cases_updating_existing_note_with_salesforce_case_id(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        users: list[tuple[str, str]] = []
        for _ in range(1, 5):
            users.append(self.newUser(org))
        ids = []
        for user_id, _ in users:
            ids.append(user_id)
        account = self.newAccount(ids)

        User.objects.create(username="<EMAIL>", email="<EMAIL>")
        case, _ = self.newCaseAndClient(account_id=account["id"], attendees=None, org=org)  # type: ignore[arg-type]

        # Create an existing note
        timestamp = django_timezone.now()
        user = User.objects.all()[0]
        note = Note(
            note_owner=user,
            status="scheduled",
            metadata={"scheduled_at": str(timestamp)},
            salesforce_case_id=case["id"],
        )
        note.save()
        note.authorized_users.add(user)
        note.save()

        # Run a SalesForce<->Zeplyn sync.
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        cases = sequoia_sf.get_sf_cases()["records"]
        sequoia_sf.generate_notes_from_cases(cases)

        # Confirm that the note has been updated
        note.refresh_from_db()
        self.assertEqual(
            datetime.fromisoformat((note.metadata or {})["scheduled_at"]),
            datetime.fromisoformat(cases[0]["Client_Review_Start_Time__c"]),
        )
        self.assertEqual(note.salesforce_case_id, case["id"])
        self.assertEqual(note.status, "scheduled")
        self.assertIsNone(note.client)
        self.assertIsNone(note.meeting_type)

    @mock_salesforce
    def test_generate_notes_from_cases_updating_existing_notes_with_client(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        users: list[tuple[str, str]] = []
        for _ in range(1, 5):
            users.append(self.newUser(org))
        ids = []
        for user_id, _ in users:
            ids.append(user_id)
        account = self.newAccount(ids)

        User.objects.create(username="<EMAIL>", email="<EMAIL>")
        case, client = self.newCaseAndClient(account_id=account["id"], attendees=None, org=org)  # type: ignore[arg-type]

        # Create an existing note
        timestamp = django_timezone.now()
        user = User.objects.all()[0]
        note = Note.objects.create(
            note_owner=user,
            status="scheduled",
            metadata={"scheduled_at": str(timestamp)},
            salesforce_case_id="123",
            client={"uuid": str(client.uuid), "name": client.name},
        )
        note.authorized_users.add(user)
        note.save()

        # Create another note with the same client, but not scheduled
        note_two = Note.objects.create(
            note_owner=user,
            status="uploaded",
            metadata={"scheduled_at": str(timestamp)},
            salesforce_case_id="123",
            client={"uuid": str(client.uuid), "name": client.name},
        )
        note_two.authorized_users.add(user)
        note_two.save()

        # Create a third note, also scheduled
        note_three = Note.objects.create(
            note_owner=user,
            status="scheduled",
            metadata={"scheduled_at": str(timestamp)},
            client={"uuid": str(client.uuid), "name": client.name},
        )
        note_three.authorized_users.add(user)
        note_three.save()

        # Run a SalesForce<->Zeplyn sync.
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        cases = sequoia_sf.get_sf_cases()["records"]
        sequoia_sf.generate_notes_from_cases(cases)

        # Confirm that the notes have been updated as required
        note.refresh_from_db()
        self.assertEqual(
            datetime.fromisoformat((note.metadata or {})["scheduled_at"]),
            datetime.fromisoformat(cases[0]["Client_Review_Start_Time__c"]),
        )
        self.assertEqual(note.salesforce_case_id, "123")
        self.assertEqual(note.status, "scheduled")
        self.assertEqual(note.client, {"uuid": str(client.uuid), "name": client.name})

        note_two.refresh_from_db()
        self.assertNotEqual(
            datetime.fromisoformat((note_two.metadata or {})["scheduled_at"]),
            datetime.fromisoformat(cases[0]["Client_Review_Start_Time__c"]),
        )
        self.assertEqual(note_two.status, "uploaded")
        self.assertEqual(note_two.client, {"uuid": str(client.uuid), "name": client.name})

        note_three.refresh_from_db()
        self.assertEqual(
            datetime.fromisoformat((note.metadata or {})["scheduled_at"]),
            datetime.fromisoformat(cases[0]["Client_Review_Start_Time__c"]),
        )
        self.assertEqual(note.salesforce_case_id, "123")
        self.assertEqual(note.status, "scheduled")
        self.assertEqual(note.client, {"uuid": str(client.uuid), "name": client.name})

    @mock_salesforce
    def test_generate_notes_from_cases_updating_existing_note_with_missing_client(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        users: list[tuple[str, str]] = []
        for _ in range(1, 5):
            users.append(self.newUser(org))
        ids = []
        for user_id, _ in users:
            ids.append(user_id)
        account = self.newAccount(ids)

        User.objects.create(username="<EMAIL>", email="<EMAIL>")
        case, client = self.newCaseAndClient(account_id=account["id"], attendees=None, org=org)  # type: ignore[arg-type]

        # Create an existing note
        timestamp = django_timezone.now()
        user = User.objects.all()[0]
        note = Note(
            note_owner=user,
            status="scheduled",
            metadata={"scheduled_at": str(timestamp)},
            salesforce_case_id=case["id"],
            client={"uuid": "missing", "name": client.name},
        )
        note.save()
        note.authorized_users.add(user)
        note.save()

        # Run a SalesForce<->Zeplyn sync.
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        cases = sequoia_sf.get_sf_cases()["records"]
        sequoia_sf.generate_notes_from_cases(cases)

        # Confirm that the note has been updated
        note.refresh_from_db()
        self.assertEqual(
            datetime.fromisoformat((note.metadata or {})["scheduled_at"]),
            datetime.fromisoformat(cases[0]["Client_Review_Start_Time__c"]),
        )
        self.assertEqual(note.salesforce_case_id, case["id"])
        self.assertEqual(note.status, "scheduled")
        self.assertEqual(note.client, {"uuid": "missing", "name": client.name})

    def test_parse_isolike_time(self) -> None:
        """Tests the _parse_isolike_time method with various timestamp formats."""
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)

        # Test with fractional seconds and T separator
        timestamp = "2025-01-28T20:00:00.123+0000"
        parsed = sequoia_sf._parse_isolike_time(timestamp)
        self.assertEqual(parsed.year, 2025)
        self.assertEqual(parsed.month, 1)
        self.assertEqual(parsed.day, 28)
        self.assertEqual(parsed.hour, 20)
        self.assertEqual(parsed.minute, 0)
        self.assertEqual(parsed.second, 0)
        self.assertEqual(parsed.microsecond, 123000)
        self.assertEqual(parsed.utcoffset().total_seconds(), 0)  # type: ignore[union-attr]

        # Test with fractional seconds and space separator
        timestamp = "2025-01-28 20:00:00.123+0000"
        parsed = sequoia_sf._parse_isolike_time(timestamp)
        self.assertEqual(parsed.year, 2025)
        self.assertEqual(parsed.month, 1)
        self.assertEqual(parsed.day, 28)
        self.assertEqual(parsed.hour, 20)
        self.assertEqual(parsed.minute, 0)
        self.assertEqual(parsed.second, 0)
        self.assertEqual(parsed.microsecond, 123000)
        self.assertEqual(parsed.utcoffset().total_seconds(), 0)  # type: ignore[union-attr]

        # Test without fractional seconds
        timestamp = "2025-01-28T20:00:00+0000"
        parsed = sequoia_sf._parse_isolike_time(timestamp)
        self.assertEqual(parsed.year, 2025)
        self.assertEqual(parsed.month, 1)
        self.assertEqual(parsed.day, 28)
        self.assertEqual(parsed.hour, 20)
        self.assertEqual(parsed.minute, 0)
        self.assertEqual(parsed.second, 0)
        self.assertEqual(parsed.microsecond, 0)
        self.assertEqual(parsed.utcoffset().total_seconds(), 0)  # type: ignore[union-attr]

        # space separated without fractional seconds
        timestamp = "2025-01-28 20:00:00+0000"
        parsed = sequoia_sf._parse_isolike_time(timestamp)
        self.assertEqual(parsed.year, 2025)
        self.assertEqual(parsed.month, 1)
        self.assertEqual(parsed.day, 28)
        self.assertEqual(parsed.hour, 20)
        self.assertEqual(parsed.minute, 0)
        self.assertEqual(parsed.second, 0)
        self.assertEqual(parsed.microsecond, 0)
        self.assertEqual(parsed.utcoffset().total_seconds(), 0)  # type: ignore[union-attr]

        # Test with different timezone offset
        timestamp = "2025-01-28T20:00:00.000-0700"
        parsed = sequoia_sf._parse_isolike_time(timestamp)
        self.assertEqual(parsed.year, 2025)
        self.assertEqual(parsed.month, 1)
        self.assertEqual(parsed.day, 28)
        self.assertEqual(parsed.hour, 20)
        self.assertEqual(parsed.minute, 0)
        self.assertEqual(parsed.second, 0)
        self.assertEqual(parsed.microsecond, 0)
        self.assertEqual(parsed.utcoffset().total_seconds(), -7 * 3600)  # type: ignore[union-attr]

    def test_parse_isolike_time_invalid_format(self) -> None:
        """Tests that _parse_isolike_time raises ValueError for invalid formats."""
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)

        invalid_timestamps = [
            "2025-01-28",  # Missing time component
            "2025-01-28T20:00:00",  # Missing timezone
            "2025-01-28T20:00:00:00:00",  # Invalid time format
            "2025-01-28T20:00:00+ABC",  # Invalid timezone format
            "invalid_timestamp",  # Completely invalid format
            "2025-13-45T25:61:61+0000",  # Invalid date/time values
        ]

        for timestamp in invalid_timestamps:
            with self.assertRaises(ValueError) as context:
                sequoia_sf._parse_isolike_time(timestamp)
            self.assertIn("does not match any expected formats", str(context.exception))


class SequoiaSalesforceComplianceTemplatesTests(SequoiaSalesforceTestCase):
    # Ideally we would do this in setUp, but because we use the @mock_salesforce decorator, we need
    # this code to be executed in the contest of the test case.
    def _configure(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        user_id, user_email = self.newUser(org)
        account = self.newAccount([user_id])

        self.user = User.objects.get(email=user_email)
        self.user.crm_configuration["crm_system"] = "sequoia_sf"
        self.user.save()

        self.case, self.test_client = self.newCaseAndClient(account_id=account["id"], org=org)
        self.timestamp = django_timezone.now()
        self.case_date = self.timestamp.astimezone(zoneinfo.ZoneInfo("America/New_York")).strftime("%Y-%m-%d")

        self.note = Note(
            note_owner=self.user,
            status="finalized",
            metadata={
                "meeting_name": "Meeting",
                "tags": ["tag1", "tag2"],
                "meeting_duration": 3600,
                "scheduled_at": str(self.timestamp),
            },
            client={"name": "client", "uuid": str(self.test_client.uuid)},
        )
        self.note.save()
        self.note.authorized_users.add(self.user)
        self.note.save()

        # Create a compliance template and associate it with the note
        self.compliance_template = StructuredMeetingData.objects.create(
            kind=StructuredMeetingDataTemplate.Kind.SEQUOIA_COMPLIANCE_CHECKLIST,
            data={
                "review_entries": [
                    {"id": "financial_status", "discussed": True},
                    {"id": "allocations_holdings", "discussed": True},
                    {"id": "investment_performance", "discussed": False},
                    {"id": "insurance_needs", "discussed": True},
                ]
            },
        )
        self.note.structuredmeetingdata_set.add(self.compliance_template)
        self.note.save()

    @mock_salesforce
    def test_add_interaction_with_compliance_checklist(self) -> None:
        self._configure()

        Task.objects.create(
            task_title="Task one",
            task_owner=self.user,
            note=self.note,
            due_date=datetime(2024, 8, 4, 0, 0, 1, tzinfo=timezone.utc),
        )
        Task.objects.create(
            task_title="Task two",
            task_owner=self.user,
            note=self.note,
            due_date=datetime(2024, 8, 12, 9, 8, 7, tzinfo=timezone.utc),
        )
        Task.objects.create(
            task_owner=self.user,
            note=self.note,
        )

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(self.note)

        case = self.salesforce.Case.get(self.case["id"])  # type: ignore[operator]
        self.assertEqual(case["Subject"], "Meeting")
        self.assertEqual(case["Zeplyn_Meeting_Tags__c"], "tag1;tag2")
        self.assertEqual(case["Attendee_Speaker_Allocations_2__c"], "")
        self.assertEqual(case["Cases_To_Create__c"], "Task one<br>\nTask two<br>\nNo title")
        self.assertEqual(case["Meeting_Summary__c"], self.note.get_summary_for_crm(use_html_formatting=True))
        self.assertEqual(case["Meeting_Duration__c"], 1)
        self.assertEqual(case["Zep_Current_Fin_Status_Discussed_Date__c"], self.case_date)
        self.assertEqual(case["Zep_Asset_Alloc_Holdings_Discussed_Date__c"], self.case_date)
        self.assertFalse("Zep_Inv_Performance_Discussed_Date__c" in self.case_date)
        self.assertEqual(case["Zep_Insurance_Planning_Discussed_Date__c"], self.case_date)

    @mock_salesforce
    def test_add_interaction_with_compliance_checklist_creation_time(self) -> None:
        self._configure()

        metadata = self.note.metadata or {}
        del metadata["scheduled_at"]
        self.note.created = self.timestamp
        self.note.metadata = metadata
        self.note.save()

        # Add an interaction
        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(self.note)

        # Confirm that the case was updated
        case = self.salesforce.Case.get(self.case["id"])  # type: ignore[operator]
        self.assertEqual(case["Zep_Current_Fin_Status_Discussed_Date__c"], self.case_date)
        self.assertEqual(case["Zep_Asset_Alloc_Holdings_Discussed_Date__c"], self.case_date)
        self.assertFalse("Zep_Inv_Performance_Discussed_Date__c" in case)
        self.assertEqual(case["Zep_Insurance_Planning_Discussed_Date__c"], self.case_date)

    @mock_salesforce
    def test_add_interaction_with_compliance_checklist_all_discussed(self) -> None:
        self._configure()

        self.compliance_template.data = {
            "review_entries": [
                {"id": "financial_status", "discussed": True},
                {"id": "allocations_holdings", "discussed": True},
                {"id": "investment_performance", "discussed": True},
                {"id": "insurance_needs", "discussed": True},
            ],
        }
        self.compliance_template.save()

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(self.note)

        case = self.salesforce.Case.get(self.case["id"])  # type: ignore[operator]
        self.assertEqual(case["Zep_Current_Fin_Status_Discussed_Date__c"], self.case_date)
        self.assertEqual(case["Zep_Asset_Alloc_Holdings_Discussed_Date__c"], self.case_date)
        self.assertEqual(case["Zep_Inv_Performance_Discussed_Date__c"], self.case_date)
        self.assertEqual(case["Zep_Insurance_Planning_Discussed_Date__c"], self.case_date)

    @mock_salesforce
    def test_add_interaction_with_compliance_checklist_none_discussed(self) -> None:
        self._configure()

        self.compliance_template.data = {
            "review_entries": [
                {"id": "financial_status", "discussed": False},
                {"id": "allocations_holdings", "discussed": False},
                {"id": "investment_performance", "discussed": False},
                {"id": "insurance_needs", "discussed": False},
            ],
        }
        self.compliance_template.save()

        sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
        sequoia_sf.add_interaction_with_client(self.note)

        case = self.salesforce.Case.get(self.case["id"])  # type: ignore[operator]
        self.assertFalse("Zep_Current_Fin_Status_Discussed_Date__c" in case)
        self.assertFalse("Zep_Asset_Alloc_Holdings_Discussed_Date__c" in case)
        self.assertFalse("Zep_Inv_Performance_Discussed_Date__c" in case)
        self.assertFalse("Zep_Insurance_Planning_Discussed_Date__c" in case)

    @mock_salesforce
    def test_add_interaction_with_compliance_checklist_invalid_template(self) -> None:
        self._configure()

        self.compliance_template.data = {"invalid": "template"}
        self.compliance_template.save()

        # Add an interaction
        with self.assertLogs(level="ERROR") as cm:
            sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
            sequoia_sf.add_interaction_with_client(self.note)
            self.assertEqual(len(cm.output), 1)
            self.assertIn("Compliance template has invalid format.", cm.output[0])

        # Confirm that the case was updated
        case = self.salesforce.Case.get(self.case["id"])  # type: ignore[operator]
        self.assertFalse("Zep_Current_Fin_Status_Discussed_Date__c" in case)
        self.assertFalse("Zep_Asset_Alloc_Holdings_Discussed_Date__c" in case)
        self.assertFalse("Zep_Inv_Performance_Discussed_Date__c" in case)
        self.assertFalse("Zep_Insurance_Planning_Discussed_Date__c" in case)

    @mock_salesforce
    def test_add_interaction_with_compliance_checklist_invalid_entries(self) -> None:
        self._configure()

        self.compliance_template.data = {
            "unknown": "entry",
            "review_entries": [
                {"invalid": "entry"},
                {"id": "unknown"},
                # {"id": "financial_status", "discussed": True},
                {"id": "allocations_holdings"},
                {"id": "allocations_holdings", "discussed": None},
                {"id": "investment_performance", "discussed": False},
                {"id": "insurance_needs", "discussed": True},
            ],
        }
        self.compliance_template.save()

        # Add an interaction
        with self.assertLogs(level="INFO") as cm:
            sequoia_sf = SequoiaSalesforce(salesforce=self.salesforce)
            sequoia_sf.add_interaction_with_client(self.note)

            first_log_index = cm.output.index(
                "ERROR:root:Compliance template entry has no topic ID. Skipping compliance field."
            )
            self.assertGreater(first_log_index, 0)
            self.assertIn(
                "Compliance template entry has no mapping for topic ID 'unknown'", cm.output[first_log_index + 1]
            )
            self.assertIn(
                "Compliance template entry for topic ID 'allocations_holdings' has no 'discussed' field or it is None",
                cm.output[first_log_index + 2],
            )
            self.assertIn(
                "Compliance template entry for topic ID 'allocations_holdings' has no 'discussed' field or it is None",
                cm.output[first_log_index + 3],
            )

        # Confirm that the case was updated
        case = self.salesforce.Case.get(self.case["id"])  # type: ignore[operator]
        self.assertFalse("Zep_Current_Fin_Status_Discussed_Date__c" in case)
        self.assertFalse("Zep_Asset_Alloc_Holdings_Discussed_Date__c" in case)
        self.assertFalse("Zep_Inv_Performance_Discussed_Date__c" in case)
        self.assertEqual(case["Zep_Insurance_Planning_Discussed_Date__c"], self.case_date)
