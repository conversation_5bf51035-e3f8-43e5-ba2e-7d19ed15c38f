import datetime
from abc import ABC, abstractmethod
from typing import Any

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm.crm_models import CRMAccount, CRMNote
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class CrmBase(ABC):
    """A representation of a CRM that can be used to (among other things) sync notes and fetch client information."""

    @abstractmethod
    def preview_before_syncing_with_crm(self, note: Note) -> dict[str, Any]:
        """
        Try to preview note before syncing with CRM based on user setup and return status.

        Args:
            note: The Note object to be previewed.

        Returns:
            A dictionary containing the preview data.
        """
        pass

    @abstractmethod
    def add_interaction_with_client(self, note: Note) -> None:
        """
        Try to upload note to CRM based on user setup and return status.

        Args:
            note: The Note object to be uploaded.

        Returns:
            None
        """
        pass

    @abstractmethod
    def get_accounts_by_owner_email_and_name(
        self, owner_email: str, account_name_filter: str | None = None
    ) -> list[CRMAccount]:
        """
        Based on user CRM setup, fetch list of all their clients.

        Args:
            user: The User object.
            account_name_filter: Optional filter for account names.

        Returns:
            A list of dictionaries containing account information.
        """
        pass

    @abstractmethod
    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        """
        Fetch basic info of client from CRM.

        Args:
            client: The Client object whose information to fetch.
            user: The User object requesting the information.
            include_household: Whether to include household information.

        Returns:
            A dictionary containing client information or None if not found.
        """
        pass

    @abstractmethod
    def fetch_events(self, user: User, interval: datetime.timedelta) -> list[CalendarEvent]:
        """
        Fetch events from CRM.

        Returns:
            A list of CalendarEvent objects.
        """
        pass

    @abstractmethod
    def fetch_notes_for_client(
        self, user: User, client: Client, lookback_interval: datetime.timedelta
    ) -> list[CRMNote]:
        """
        Fetch notes for a specific client from CRM.

        Args:
            user: The User requesting the notes.
            client: The Client whose notes should be fetched.
            lookback_interval: Lookback time interval for fetching notes. Note that this must be a
            positive datetime.timedelta: it will be subtracted from the current time to get the
            start time for fetching notes.

        Returns:
            A list of CRMNote objects.
        """
        pass
