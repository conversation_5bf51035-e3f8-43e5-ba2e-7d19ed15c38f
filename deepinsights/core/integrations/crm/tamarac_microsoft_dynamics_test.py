import datetime
from datetime import timedelta
from typing import Any
from unittest.mock import MagicMock, patch

import pytest
from django.utils import timezone as django_timezone

from deepinsights.core.integrations.crm.tamarac_microsoft_dynamics import TamaracMicrosoftDynamics
from deepinsights.core.preferences.preferences import CrmConfiguration, DynamicsConfiguration
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User

pytestmark = [pytest.mark.django_db]


@pytest.fixture
def test_organization() -> Organization:
    return Organization.objects.create(name="Test Organization")


@pytest.fixture
def test_user(test_organization: Organization) -> User:
    user = User.objects.create(email="<EMAIL>", organization=test_organization)

    dynamics_config = DynamicsConfiguration(dynamics_resource_url="https://tamarac.example.com")
    crm_config = CrmConfiguration(crm_system="microsoft_dynamics", dynamics=dynamics_config)

    def get_crm_configuration() -> CrmConfiguration:
        return crm_config

    user.get_crm_configuration = get_crm_configuration  # type: ignore[method-assign]
    return user


@pytest.fixture
def test_client(test_user: User, test_organization: Organization) -> Client:
    client = Client.objects.create(
        name="Test Household Client", organization=test_organization, crm_id="household123", client_type="Account"
    )
    client.authorized_users.add(test_user)
    client.save()
    return client


@pytest.fixture
def test_meeting_type_debrief() -> MeetingType:
    return MeetingType.objects.create(name="Debrief", category="debrief")


@pytest.fixture
def test_note(test_user: User, test_client: Client) -> Note:
    return Note.objects.create(
        note_owner=test_user, client={"uuid": str(test_client.uuid)}, summary="Test Tamarac Note"
    )


@pytest.fixture
def test_note_debrief(test_user: User, test_client: Client, test_meeting_type_debrief: MeetingType) -> Note:
    return Note.objects.create(
        note_owner=test_user,
        client={"uuid": str(test_client.uuid)},
        summary="Test Debrief Note",
        meeting_type=test_meeting_type_debrief,
    )


@pytest.fixture
def test_tasks(test_note: Note) -> list[Task]:
    tasks = [
        Task.objects.create(
            note=test_note,
            task_title="Review portfolio allocation",
            task_desc="Check current allocation vs target",
            due_date=django_timezone.now() + datetime.timedelta(days=7),
        ),
        Task.objects.create(
            note=test_note,
            task_title="Schedule follow-up meeting",
            task_desc="Book next quarterly review",
            due_date=django_timezone.now() + datetime.timedelta(days=14),
        ),
        Task.objects.create(
            note=test_note, task_title="Send tax documents", due_date=django_timezone.now() + datetime.timedelta(days=3)
        ),
    ]
    return tasks


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_accounts_by_owner_email_and_name_household_only(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    account_types_response = {
        "value": [
            {
                "tam_accounttypeid": "0e3c9bf0-54af-e111-b4b4-b8ac6f153db6",
                "tam_name": "Household",
            },
            {
                "tam_accounttypeid": "4256867a-399d-e611-80e3-005056951f51",
                "tam_name": "Household - Courtesy",
            },
        ]
    }

    household_accounts_response = {
        "value": [
            {
                "name": "Smith Household",
                "accountid": "household123",
                "telephone1": "**********",
                "emailaddress1": "<EMAIL>",
                "tam_uploadid": "TAM001",
                "_tam_accounttypeid_value": "0e3c9bf0-54af-e111-b4b4-b8ac6f153db6",
                "statecode": 0,
            },
            {
                "name": "Johnson Household - Courtesy",
                "accountid": "household456",
                "telephone1": "**********",
                "emailaddress1": "<EMAIL>",
                "tam_uploadid": "TAM002",
                "_tam_accounttypeid_value": "4256867a-399d-e611-80e3-005056951f51",
                "statecode": 0,
            },
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: account_types_response),
            MagicMock(status_code=200, json=lambda: household_accounts_response),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        accounts = dynamics.get_accounts_by_owner_email_and_name("<EMAIL>")

        assert len(accounts) == 2
        assert accounts[0].name == "Smith Household"
        assert accounts[0].client_type == "Account"
        assert accounts[0].crm_system == "microsoft_dynamics"
        assert accounts[1].name == "Johnson Household - Courtesy"

        assert mock_requests_get.call_count == 2

        account_types_call = mock_requests_get.call_args_list[0]
        assert "tam_accounttypes" in account_types_call[0][0]
        assert "contains(tam_name, 'Household')" in account_types_call[1]["params"]["$filter"]

        accounts_call = mock_requests_get.call_args_list[1]
        assert "accounts" in accounts_call[0][0]
        filter_param = accounts_call[1]["params"]["$filter"]
        assert "0e3c9bf0-54af-e111-b4b4-b8ac6f153db6" in filter_param
        assert "4256867a-399d-e611-80e3-005056951f51" in filter_param
        assert "statecode eq 0" in filter_param


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_accounts_by_owner_email_and_name_with_filter(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    account_types_response = {
        "value": [
            {
                "tam_accounttypeid": "household-type-123",
                "tam_name": "Household",
            },
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: account_types_response),
            MagicMock(status_code=200, json=lambda: {"value": []}),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        dynamics.get_accounts_by_owner_email_and_name("<EMAIL>", "Smith")

        accounts_call = mock_requests_get.call_args_list[1]
        filter_param = accounts_call[1]["params"]["$filter"]
        assert "contains(name, 'Smith')" in filter_param
        assert "statecode eq 0" in filter_param
        assert "_tam_accounttypeid_value eq 'household-type-123'" in filter_param


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_household_account_type_ids_success(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    account_types_response = {
        "value": [
            {
                "tam_accounttypeid": "household-123",
                "tam_name": "Household",
            },
            {
                "tam_accounttypeid": "household-courtesy-456",
                "tam_name": "Household - Courtesy",
            },
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: account_types_response)

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url, access_token = dynamics._get_api_url(test_user)
        headers = dynamics._get_headers(access_token)

        household_ids = dynamics._get_household_account_type_ids(api_url, headers)

        assert len(household_ids) == 2
        assert "household-123" in household_ids
        assert "household-courtesy-456" in household_ids

        call_args = mock_requests_get.call_args
        assert "tam_accounttypes" in call_args[0][0]
        params = call_args[1]["params"]
        assert "statecode eq 0" in params["$filter"]
        assert "contains(tam_name, 'Household')" in params["$filter"]


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_household_account_type_ids_empty_response(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: {"value": []})

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url, access_token = dynamics._get_api_url(test_user)
        headers = dynamics._get_headers(access_token)

        household_ids = dynamics._get_household_account_type_ids(api_url, headers)

        assert household_ids == []


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_household_account_type_ids_api_error(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        mock_requests_get.return_value = MagicMock(status_code=500, text="Server Error")

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url, access_token = dynamics._get_api_url(test_user)
        headers = dynamics._get_headers(access_token)

        household_ids = dynamics._get_household_account_type_ids(api_url, headers)

        assert household_ids == []
        mock_error.assert_called()
        error_messages = [str(call) for call in mock_error.call_args_list]
        assert any("Error fetching account types" in msg for msg in error_messages)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_accounts_no_household_types_found(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.get") as mock_requests_get, patch("logging.warning") as mock_warning:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: {"value": []})

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        accounts = dynamics.get_accounts_by_owner_email_and_name("<EMAIL>")

        assert accounts == []
        mock_warning.assert_called()
        warning_messages = [str(call) for call in mock_warning.call_args_list]
        assert any("No household account types found" in msg for msg in warning_messages)

        # Should only call account types endpoint, not accounts endpoint
        assert mock_requests_get.call_count == 1


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_preview_before_syncing_with_crm_appointment(
    mock_oauth: MagicMock, test_note: Note, test_tasks: list[Task]
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]

    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "Test meeting summary"
        mock_title.return_value = "Client Meeting with Smith"

        preview = dynamics.preview_before_syncing_with_crm(test_note)

    # Check note preview
    assert preview["note"]["activity_type"] == "Appointment"
    assert preview["note"]["title"] == "Client Meeting with Smith"
    assert preview["note"]["content"] == "Test meeting summary"
    assert preview["note"]["regarding"] == "Test Household Client"

    # Check individual tasks
    assert len(preview["individual_tasks"]) == 3
    assert preview["individual_tasks"][0]["subject"] == "Review portfolio allocation"
    assert preview["individual_tasks"][1]["subject"] == "Schedule follow-up meeting"
    assert preview["individual_tasks"][2]["subject"] == "Send tax documents"

    # Check consolidated task preview
    consolidated = preview["consolidated_task"]
    assert consolidated is not None
    assert consolidated["subject"] == "Client Meeting with Smith - Follow Up"
    assert "Follow-up tasks from meeting" in consolidated["description"]
    assert "1. Review portfolio allocation" in consolidated["description"]
    assert "2. Schedule follow-up meeting" in consolidated["description"]
    assert "3. Send tax documents" in consolidated["description"]
    assert consolidated["status"] == "Open"
    assert consolidated["priority"] == "Normal"
    assert consolidated["note"] == "This task consolidates 3 individual tasks"

    # Check sync summary
    sync_summary = preview["sync_summary"]
    assert sync_summary["will_create_activity"] is True
    assert sync_summary["activity_type"] == "Appointment"
    assert sync_summary["will_create_tasks"] is True
    assert sync_summary["tasks_count"] == 3
    assert sync_summary["will_consolidate_tasks"] is True


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_preview_before_syncing_with_crm_phone_call(mock_oauth: MagicMock, test_note_debrief: Note) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    dynamics = TamaracMicrosoftDynamics(user=test_note_debrief.note_owner)  # type: ignore[arg-type]

    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "Debrief summary"
        mock_title.return_value = "Team Debrief"

        preview = dynamics.preview_before_syncing_with_crm(test_note_debrief)

    assert preview["note"]["activity_type"] == "Phone Call"
    assert preview["sync_summary"]["activity_type"] == "Phone Call"


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_preview_before_syncing_with_crm_no_tasks(mock_oauth: MagicMock, test_note: Note) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]

    with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
        mock_summary.return_value = "No tasks meeting"
        mock_title.return_value = "Simple Meeting"

        preview = dynamics.preview_before_syncing_with_crm(test_note)

    assert len(preview["individual_tasks"]) == 0
    assert preview["consolidated_task"] is None
    assert preview["sync_summary"]["will_create_tasks"] is False
    assert preview["sync_summary"]["tasks_count"] == 0
    assert preview["sync_summary"]["will_consolidate_tasks"] is False


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_with_client_as_appointment(
    mock_oauth: MagicMock, test_note: Note, test_tasks: list[Task]
) -> None:
    """Test adding appointment interaction with consolidated tasks."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.post") as mock_requests_post:
        appointment_response = MagicMock(status_code=201)
        appointment_response.headers = {
            "OData-EntityId": "https://tamarac.example.com/api/data/v9.2/appointments(appt123)"
        }

        task_response = MagicMock(status_code=201)
        task_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/tasks(task456)"}

        mock_requests_post.side_effect = [appointment_response, task_response]

        with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
            mock_summary.return_value = "Meeting summary"
            mock_title.return_value = "Client Meeting"

            dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
            dynamics.add_interaction_with_client(test_note)

        assert mock_requests_post.call_count == 2

        appointment_call = mock_requests_post.call_args_list[0]
        assert "appointments" in appointment_call[0][0]
        appointment_data = appointment_call[1]["json"]
        assert appointment_data["subject"] == "Client Meeting"
        assert "<EMAIL>" in appointment_data
        assert "scheduledstart" in appointment_data
        assert "scheduledend" in appointment_data

        task_call = mock_requests_post.call_args_list[1]
        assert "tasks" in task_call[0][0]
        task_data = task_call[1]["json"]
        assert task_data["subject"] == "Client Meeting - Follow Up"
        assert "Follow-up tasks from meeting" in task_data["description"]
        assert "1. Review portfolio allocation" in task_data["description"]
        assert task_data["statecode"] == 0  # Open
        assert task_data["prioritycode"] == 1  # Normal

        updated_note = Note.objects.get(pk=test_note.pk)
        assert updated_note.metadata["interactionId"] == "appt123"  # type: ignore[index]
        assert updated_note.metadata["activityType"] == "appointment"  # type: ignore[index]

        for task in test_tasks:
            updated_task = Task.objects.get(pk=task.pk)
            assert updated_task.metadata["consolidatedTaskId"] == "task456"  # type: ignore[index]


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_with_client_as_phone_call(mock_oauth: MagicMock, test_note_debrief: Note) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.post") as mock_requests_post:
        phone_call_response = MagicMock(status_code=201)
        phone_call_response.headers = {
            "OData-EntityId": "https://tamarac.example.com/api/data/v9.2/phonecalls(call123)"
        }
        mock_requests_post.return_value = phone_call_response

        with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
            mock_summary.return_value = "Debrief notes"
            mock_title.return_value = "Team Debrief"

            dynamics = TamaracMicrosoftDynamics(user=test_note_debrief.note_owner)  # type: ignore[arg-type]
            dynamics.add_interaction_with_client(test_note_debrief)

        assert mock_requests_post.call_count == 1

        call_args = mock_requests_post.call_args
        assert "phonecalls" in call_args[0][0]
        call_data = call_args[1]["json"]
        assert call_data["subject"] == "Team Debrief"

        updated_note = Note.objects.get(pk=test_note_debrief.pk)
        assert updated_note.metadata["interactionId"] == "call123"  # type: ignore[index]
        assert updated_note.metadata["activityType"] == "phonecall"  # type: ignore[index]


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_with_client_already_exists(mock_oauth: MagicMock, test_note: Note) -> None:
    """Test that existing interactions are not duplicated."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    test_note.metadata = {"interactionId": "existing123"}
    test_note.save()

    with patch("requests.post") as mock_requests_post:
        dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
        dynamics.add_interaction_with_client(test_note)

        mock_requests_post.assert_not_called()


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_create_consolidated_follow_up_task_with_assignees(
    mock_oauth: MagicMock, test_note: Note, test_user: User
) -> None:
    """Test consolidated task creation with task assignees."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    assignee_user = User.objects.create(
        email="<EMAIL>",
        username="assignee_user",
        organization=test_user.organization,
    )
    task1 = Task.objects.create(
        note=test_note,
        task_title="Task with assignee",
        task_desc="Description",
        assignee=assignee_user,
        due_date=django_timezone.now() + datetime.timedelta(days=5),
    )
    task2 = Task.objects.create(
        note=test_note, task_title="Task without assignee", due_date=django_timezone.now() + datetime.timedelta(days=10)
    )

    with patch("requests.post") as mock_requests_post:
        mock_response = MagicMock(status_code=201)
        mock_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/tasks(consolidated123)"}
        mock_requests_post.return_value = mock_response

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url = "https://tamarac.example.com/api/data/v9.2/"
        headers = dynamics._get_headers("mock_token")
        client_info = {"id": "client123", "type": "account"}

        with patch.object(Note, "title") as mock_title:
            mock_title.return_value = "Test Meeting"

            dynamics._create_consolidated_follow_up_task(
                api_url, headers, test_note, test_note.task_set.all(), client_info
            )

        call_args = mock_requests_post.call_args[1]
        task_data = call_args["json"]

        assert task_data["subject"] == "Test Meeting - Follow Up"
        assert "Follow-up tasks from meeting: Test Meeting" in task_data["description"]
        assert "1. Task with assignee: Description (Assigned to: <EMAIL>)" in task_data["description"]
        assert "2. Task without assignee" in task_data["description"]
        assert task_data["statecode"] == 0
        assert task_data["prioritycode"] == 1

        updated_task1 = Task.objects.get(pk=task1.pk)
        updated_task2 = Task.objects.get(pk=task2.pk)
        assert updated_task1.metadata["consolidatedTaskId"] == "consolidated123"  # type: ignore[index]
        assert updated_task2.metadata["consolidatedTaskId"] == "consolidated123"  # type: ignore[index]


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_create_consolidated_follow_up_task_earliest_due_date(
    mock_oauth: MagicMock, test_note: Note, test_user: User
) -> None:
    """Test that consolidated task uses earliest due date."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    earliest_date = django_timezone.now() + datetime.timedelta(days=2)
    later_date = django_timezone.now() + datetime.timedelta(days=10)

    Task.objects.create(note=test_note, task_title="Later task", due_date=later_date)
    Task.objects.create(note=test_note, task_title="Earlier task", due_date=earliest_date)

    with patch("requests.post") as mock_requests_post:
        mock_response = MagicMock(status_code=201)
        mock_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/tasks(consolidated123)"}
        mock_requests_post.return_value = mock_response

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url = "https://tamarac.example.com/api/data/v9.2/"
        headers = dynamics._get_headers("mock_token")
        client_info = {"id": "client123", "type": "account"}

        with patch.object(Note, "title") as mock_title:
            mock_title.return_value = "Test Meeting"

            dynamics._create_consolidated_follow_up_task(
                api_url, headers, test_note, test_note.task_set.all(), client_info
            )

        call_args = mock_requests_post.call_args[1]
        task_data = call_args["json"]
        scheduled_end = datetime.datetime.fromisoformat(task_data["scheduledend"].replace("Z", "+00:00"))

        assert abs((scheduled_end - earliest_date).total_seconds()) < 60


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_create_consolidated_follow_up_task_no_due_dates(
    mock_oauth: MagicMock, test_note: Note, test_user: User
) -> None:
    """Test consolidated task creation when no tasks have due dates (should default to 7 days)."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    Task.objects.create(note=test_note, task_title="Task without due date")

    with patch("requests.post") as mock_requests_post:
        mock_response = MagicMock(status_code=201)
        mock_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/tasks(consolidated123)"}
        mock_requests_post.return_value = mock_response

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url = "https://tamarac.example.com/api/data/v9.2/"
        headers = dynamics._get_headers("mock_token")
        client_info = {"id": "client123", "type": "account"}

        with patch.object(Note, "title") as mock_title:
            mock_title.return_value = "Test Meeting"

            with patch(
                "deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.datetime"
            ) as mock_datetime_module:
                fixed_now = datetime.datetime(2023, 1, 1, 12, 0, 0, tzinfo=datetime.timezone.utc)
                mock_datetime_module.datetime.now.return_value = fixed_now
                mock_datetime_module.timedelta = datetime.timedelta  # Keep timedelta working

                dynamics._create_consolidated_follow_up_task(
                    api_url, headers, test_note, test_note.task_set.all(), client_info
                )

        call_args = mock_requests_post.call_args[1]
        task_data = call_args["json"]
        expected_due = fixed_now + datetime.timedelta(days=7)
        assert task_data["scheduledend"] == expected_due.isoformat()


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_no_client(mock_oauth: MagicMock, test_user: User) -> None:
    """Test error handling when note has no client."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    note = Note.objects.create(note_owner=test_user, summary="No client note")

    dynamics = TamaracMicrosoftDynamics(user=test_user)

    with pytest.raises(Exception) as exc_info:
        dynamics.add_interaction_with_client(note)

    assert "No primary client specified in the note" in str(exc_info.value)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_client_no_crm_id(
    mock_oauth: MagicMock, test_note: Note, test_organization: Organization
) -> None:
    """Test error handling when client has no CRM ID."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    client_no_crm = Client.objects.create(name="Client Without CRM ID", organization=test_organization)
    test_note.client = {"uuid": str(client_no_crm.uuid)}
    test_note.save()

    dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]

    with pytest.raises(Exception) as exc_info:
        dynamics.add_interaction_with_client(test_note)

    assert "does not have a CRM ID" in str(exc_info.value)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_oauth_error_handling(mock_oauth: MagicMock, test_user: User) -> None:
    """Test OAuth error handling."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.side_effect = Exception("OAuth failed")

    dynamics = TamaracMicrosoftDynamics(user=test_user)

    with patch("logging.error") as mock_error:
        accounts = dynamics.get_accounts_by_owner_email_and_name("<EMAIL>")

        assert accounts == []
        mock_error.assert_called()
        error_messages = [str(call) for call in mock_error.call_args_list]
        assert any("Error getting Tamarac household accounts" in msg for msg in error_messages)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_accounts_api_error(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        mock_requests_get.return_value = MagicMock(status_code=401, text="Unauthorized")

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        accounts = dynamics.get_accounts_by_owner_email_and_name("<EMAIL>")

        assert accounts == []
        mock_error.assert_called()
        error_messages = [str(call) for call in mock_error.call_args_list]
        assert any("Error fetching account types" in msg for msg in error_messages)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_accounts_malformed_response(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    account_types_response = {
        "value": [
            {
                "tam_accounttypeid": "household-type-123",
                "tam_name": "Household",
            },
        ]
    }

    accounts_response = {
        "value": [
            {
                "name": "Good Household",
                "accountid": "good123",
                "telephone1": "**********",
                "emailaddress1": "<EMAIL>",
                "_tam_accounttypeid_value": "household-type-123",
                "statecode": 0,
            },
            {
                # Missing required fields to cause error
                "name": None,
                "accountid": None,
            },
        ]
    }

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: account_types_response),
            MagicMock(status_code=200, json=lambda: accounts_response),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        accounts = dynamics.get_accounts_by_owner_email_and_name("<EMAIL>")

        # Should get one good account despite the malformed one
        assert len(accounts) == 1
        assert accounts[0].name == "Good Household"

        # Should log error for malformed account
        mock_error.assert_called()
        assert any("Error creating CRMAccount object" in str(call) for call in mock_error.call_args_list)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_preview_no_client_error(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    note_no_client = Note.objects.create(note_owner=test_user, summary="Note without client")

    dynamics = TamaracMicrosoftDynamics(user=test_user)

    with pytest.raises(Exception) as exc_info:
        dynamics.preview_before_syncing_with_crm(note_no_client)

    assert "No clients associated with the note" in str(exc_info.value)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_successful_activity_no_id_header(mock_oauth: MagicMock, test_note: Note) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.post") as mock_requests_post, patch("logging.warning") as mock_warning:
        response = MagicMock(status_code=201)
        response.headers = {}  # No OData-EntityId header
        mock_requests_post.return_value = response

        with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
            mock_summary.return_value = "Test summary"
            mock_title.return_value = "Test Meeting"

            dynamics = TamaracMicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
            dynamics.add_interaction_with_client(test_note)

        mock_warning.assert_called()
        warning_messages = [str(call) for call in mock_warning.call_args_list]
        assert any("created but couldn't extract ID from response headers" in msg for msg in warning_messages)

        updated_note = Note.objects.get(pk=test_note.pk)
        assert not updated_note.metadata or "interactionId" not in updated_note.metadata


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_consolidated_task_creation_successful_no_id_header(
    mock_oauth: MagicMock, test_note: Note, test_user: User
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    task = Task.objects.create(note=test_note, task_title="Test task")

    with patch("requests.post") as mock_requests_post, patch("logging.warning") as mock_warning:
        response = MagicMock(status_code=201)
        response.headers = {}  # No OData-EntityId header
        mock_requests_post.return_value = response

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url = "https://tamarac.example.com/api/data/v9.2/"
        headers = dynamics._get_headers("mock_token")
        client_info = {"id": "client123", "type": "account"}

        with patch.object(Note, "title") as mock_title:
            mock_title.return_value = "Test Meeting"

            dynamics._create_consolidated_follow_up_task(
                api_url, headers, test_note, test_note.task_set.all(), client_info
            )

        mock_warning.assert_called()
        warning_messages = [str(call) for call in mock_warning.call_args_list]
        assert any("created but couldn't extract ID from response headers" in msg for msg in warning_messages)

        updated_task = Task.objects.get(pk=task.pk)
        assert not updated_task.metadata or "consolidatedTaskId" not in updated_task.metadata


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_complex_task_descriptions_formatting(mock_oauth: MagicMock, test_note: Note, test_user: User) -> None:
    """Test that complex task descriptions are properly formatted in consolidated task."""
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    assignee = User.objects.create(
        email="<EMAIL>",
        username="advisor_user",  # Add unique username
        organization=test_user.organization,
    )

    tasks = [
        Task.objects.create(
            note=test_note,
            task_title="Review investment strategy",
            task_desc="Analyze current portfolio allocation and recommend rebalancing",
            assignee=assignee,
            due_date=django_timezone.now() + datetime.timedelta(days=5),
        ),
        Task.objects.create(
            note=test_note,
            task_title="Schedule client call",
            assignee=assignee,
            # No description or due date
        ),
        Task.objects.create(
            note=test_note,
            task_title="Send market update",
            task_desc="Quarterly market commentary and outlook",
            due_date=django_timezone.now() + datetime.timedelta(days=2),
            # No assignee
        ),
        Task.objects.create(
            note=test_note,
            task_title="Update client database",
            # Only title
        ),
    ]

    with patch("requests.post") as mock_requests_post:
        mock_response = MagicMock(status_code=201)
        mock_response.headers = {"OData-EntityId": "https://tamarac.example.com/api/data/v9.2/tasks(consolidated123)"}
        mock_requests_post.return_value = mock_response

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url = "https://tamarac.example.com/api/data/v9.2/"
        headers = dynamics._get_headers("mock_token")
        client_info = {"id": "client123", "type": "account"}

        with patch.object(Note, "title") as mock_title:
            mock_title.return_value = "Quarterly Review Meeting"

            dynamics._create_consolidated_follow_up_task(
                api_url, headers, test_note, test_note.task_set.all(), client_info
            )

        call_args = mock_requests_post.call_args[1]
        task_data = call_args["json"]
        description = task_data["description"]

        assert "Follow-up tasks from meeting: Quarterly Review Meeting" in description

        assert (
            "1. Review investment strategy: Analyze current portfolio allocation and recommend rebalancing (Assigned to: <EMAIL>)"
            in description
        )
        assert "2. Schedule client call (Assigned to: <EMAIL>)" in description
        assert "3. Send market update: Quarterly market commentary and outlook" in description
        assert "4. Update client database" in description

        assert "(Due:" in description  # Should appear for tasks with due dates


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_success(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    appointments_response = {
        "value": [
            {
                "activityid": "appt123",
                "subject": "Client Meeting",
                "description": "Discussed portfolio strategy",
                "createdon": "2023-12-01T10:00:00Z",
                "scheduledstart": "2023-12-01T09:00:00Z",
                "scheduledend": "2023-12-01T10:00:00Z",
            }
        ]
    }

    phonecalls_response = {
        "value": [
            {
                "activityid": "call456",
                "subject": "Follow-up Call",
                "description": "Discussed action items",
                "createdon": "2023-12-02T14:00:00Z",
                "scheduledstart": "2023-12-02T14:00:00Z",
                "scheduledend": "2023-12-02T14:30:00Z",
            }
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: appointments_response),
            MagicMock(status_code=200, json=lambda: phonecalls_response),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, timedelta(days=30))

        assert len(notes) == 2
        assert notes[0].crm_id == "call456"
        assert notes[0].content == "[Phone Call] Follow-up Call\nDiscussed action items"
        assert notes[0].crm_system == "microsoft_dynamics"
        assert (
            str(notes[0].web_link)
            == "https://tamarac.example.com/main.aspx?pagetype=entityrecord&etn=phonecall&id=call456"
        )

        assert notes[1].crm_id == "appt123"
        assert notes[1].content == "[Appointment] Client Meeting\nDiscussed portfolio strategy"
        assert (
            str(notes[1].web_link)
            == "https://tamarac.example.com/main.aspx?pagetype=entityrecord&etn=appointment&id=appt123"
        )

        assert mock_requests_get.call_count == 2

        appt_call = mock_requests_get.call_args_list[0]
        assert "appointments" in appt_call[0][0]
        appt_params = appt_call[1]["params"]
        assert f"_regardingobjectid_value eq '{test_client.crm_id}'" in appt_params["$filter"]
        assert "createdon ge" in appt_params["$filter"]

        phone_call = mock_requests_get.call_args_list[1]
        assert "phonecalls" in phone_call[0][0]


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_no_crm_id(
    mock_oauth: MagicMock, test_user: User, test_organization: Organization
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    client_no_crm = Client.objects.create(name="Client Without CRM ID", organization=test_organization)

    dynamics = TamaracMicrosoftDynamics(user=test_user)

    with patch("logging.error") as mock_error:
        notes = dynamics.fetch_notes_for_client(test_user, client_no_crm, timedelta(days=30))

        assert notes == []
        mock_error.assert_called()
        error_messages = [str(call) for call in mock_error.call_args_list]
        assert any("does not have a CRM ID" in msg for msg in error_messages)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_empty_results(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    empty_response: dict[str, Any] = {"value": []}

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: empty_response)

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, timedelta(days=30))

        assert notes == []
        assert mock_requests_get.call_count == 2


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_date_parsing_error(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    appointments_response = {
        "value": [
            {
                "activityid": "appt123",
                "subject": "Meeting",
                "description": "Description",
                "createdon": "invalid-date-format",
            }
        ]
    }

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: appointments_response),
            MagicMock(status_code=200, json=lambda: {"value": []}),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, timedelta(days=30))

        assert len(notes) == 1
        assert notes[0].created_at is None
        mock_error.assert_called()
        error_messages = [str(call) for call in mock_error.call_args_list]
        assert any("Error parsing date" in msg for msg in error_messages)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_malformed_activity_data(
    mock_oauth: MagicMock, test_user: User, test_client: Client
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    appointments_response = {
        "value": [
            {
                "activityid": "good123",
                "subject": "Good Meeting",
                "description": "Valid description",
                "createdon": "2023-12-01T10:00:00Z",
            },
            {
                # Missing activityid to cause error
                "subject": "Bad Meeting",
                "description": "Missing ID",
            },
        ]
    }

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: appointments_response),
            MagicMock(status_code=200, json=lambda: {"value": []}),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, timedelta(days=30))

        assert len(notes) == 1
        assert notes[0].crm_id == "good123"
        mock_error.assert_called()
        error_messages = [str(call) for call in mock_error.call_args_list]
        assert any("Error creating CRMNote object" in msg for msg in error_messages)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_subject_only(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    appointments_response = {
        "value": [
            {
                "activityid": "appt123",
                "subject": "Meeting Subject Only",
                "description": "",
                "createdon": "2023-12-01T10:00:00Z",
            }
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: appointments_response),
            MagicMock(status_code=200, json=lambda: {"value": []}),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, timedelta(days=30))

        assert len(notes) == 1
        assert notes[0].content == "[Appointment] Meeting Subject Only\n"


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_description_only(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    phonecalls_response = {
        "value": [
            {
                "activityid": "call123",
                "subject": "",
                "description": "Just description content",
                "createdon": "2023-12-01T10:00:00Z",
            }
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: {"value": []}),
            MagicMock(status_code=200, json=lambda: phonecalls_response),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, timedelta(days=30))

        assert len(notes) == 1
        assert notes[0].content == "[Phone Call] Just description content"


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_oauth_error(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.side_effect = Exception("OAuth failed")

    dynamics = TamaracMicrosoftDynamics(user=test_user)

    with patch("logging.error") as mock_error:
        notes = dynamics.fetch_notes_for_client(test_user, test_client, timedelta(days=30))

        assert notes == []
        mock_error.assert_called()
        error_messages = [str(call) for call in mock_error.call_args_list]
        assert any("Error fetching notes from Tamarac Dynamics" in msg for msg in error_messages)


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_activities_for_client_direct_call(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    activities_response = {
        "value": [
            {
                "activityid": "test123",
                "subject": "Test Activity",
                "description": "Test Description",
                "createdon": "2023-12-01T10:00:00Z",
            }
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: activities_response)

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        api_url = "https://tamarac.example.com/api/data/v9.2/"
        headers = dynamics._get_headers("mock_access_token")
        formatted_date = "2023-11-01T00:00:00Z"

        notes = dynamics._fetch_activities_for_client(
            api_url=api_url,
            headers=headers,
            client_crm_id=test_client.crm_id,  # type: ignore[arg-type]
            formatted_date=formatted_date,
            user=test_user,
            endpoint="appointments",
            label="Appointment",
            entity="appointment",
        )

        assert len(notes) == 1
        assert notes[0].crm_id == "test123"
        assert notes[0].content == "[Appointment] Test Activity\nTest Description"
        assert (
            str(notes[0].web_link)
            == "https://tamarac.example.com/main.aspx?pagetype=entityrecord&etn=appointment&id=test123"
        )

        call_args = mock_requests_get.call_args
        assert "appointments" in call_args[0][0]
        params = call_args[1]["params"]
        assert f"_regardingobjectid_value eq '{test_client.crm_id}'" in params["$filter"]
        assert "createdon ge 2023-11-01T00:00:00Z" in params["$filter"]


@patch("deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_sorting_order(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    appointments_response = {
        "value": [
            {
                "activityid": "older_appt",
                "subject": "Older Appointment",
                "description": "",
                "createdon": "2023-11-01T10:00:00Z",
            }
        ]
    }

    phonecalls_response = {
        "value": [
            {
                "activityid": "newer_call",
                "subject": "Newer Phone Call",
                "description": "",
                "createdon": "2023-12-01T10:00:00Z",
            }
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: appointments_response),
            MagicMock(status_code=200, json=lambda: phonecalls_response),
        ]

        dynamics = TamaracMicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, timedelta(days=30))

        assert len(notes) == 2
        assert notes[0].crm_id == "newer_call"  # Newer one should be first
        assert notes[1].crm_id == "older_appt"


def test_unimplemented_methods_updated(test_user: User, test_client: Client) -> None:
    with patch(
        "deepinsights.core.integrations.crm.tamarac_microsoft_dynamics.MicrosoftDynamicsOAuth"
    ) as mock_oauth, patch("logging.warning") as mock_warning:
        mock_oauth_instance = mock_oauth.return_value
        mock_oauth_instance.get_access_token.return_value = "mock_access_token"

        dynamics = TamaracMicrosoftDynamics(user=test_user)

        client_info = dynamics.get_client_basic_info(test_client, test_user, include_household=True)
        assert client_info == {}

        events = dynamics.fetch_events(test_user, datetime.timedelta(days=7))
        assert events == []

        assert mock_warning.call_count == 2
        warning_messages = [call[0][0] for call in mock_warning.call_args_list]
        assert any("fetch_events not implemented" in msg for msg in warning_messages)
