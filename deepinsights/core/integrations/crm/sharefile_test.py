from unittest.mock import MagicMock, patch

from django.test import TestCase

from deepinsights.core.integrations.crm.sharefile import ShareFile
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class TestShareFile(TestCase):
    def setUp(self):  # type: ignore[no-untyped-def]
        self.sharefile = ShareFile(
            client_id="test_client_id",
            client_secret="test_client_secret",
            hostname="test.sharefile.com",
            username="test_user",
            password="test_password",
            sharefile_dir="test_dir",
        )

    @patch("requests.post")
    def test_authenticate(self, mock_post):  # type: ignore[no-untyped-def]
        # Mock the response from requests.post
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"access_token": "test_access_token", "refresh_token": "test_refresh_token"}
        mock_post.return_value = mock_response

        # Call the authenticate method
        self.sharefile.authenticate()  # type: ignore[no-untyped-call]

        mock_post.assert_called_once_with(
            f"https://{self.sharefile.hostname}/oauth/token",
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            data={
                "grant_type": "password",
                "client_id": self.sharefile.client_id,
                "client_secret": self.sharefile.client_secret,
                "username": self.sharefile.username,
                "password": self.sharefile.password,
            },
        )

        # Assert that the access_token and refresh_token were set correctly
        self.assertEqual(self.sharefile.access_token, "test_access_token")
        self.assertEqual(self.sharefile.refresh_token, "test_refresh_token")

    @patch("requests.post")
    def test_refresh_auth_token(self, mock_post):  # type: ignore[no-untyped-def]
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"access_token": "new_access_token", "refresh_token": "new_refresh_token"}
        mock_post.return_value = mock_response

        self.sharefile.refresh_token = "old_refresh_token"  # type: ignore[assignment]
        self.sharefile.refresh_auth_token()  # type: ignore[no-untyped-call]

        self.assertEqual(self.sharefile.access_token, "new_access_token")
        self.assertEqual(self.sharefile.refresh_token, "new_refresh_token")

    @patch("http.client.HTTPSConnection")
    def test_get_folder_id(self, mock_connection):  # type: ignore[no-untyped-def]
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.read.return_value = b'{"value": [{"Id": "test_folder_id"}]}'
        mock_connection.return_value.getresponse.return_value = mock_response

        folder_id = self.sharefile.get_folder_id("test_folder")  # type: ignore[no-untyped-call]

        self.assertEqual(folder_id, "test_folder_id")

    @patch("http.client.HTTPSConnection")
    def test_create_folder(self, mock_connection):  # type: ignore[no-untyped-def]
        mock_response = MagicMock()
        mock_response.status = 200
        mock_response.read.return_value = b'{"Id": "new_folder_id"}'
        mock_connection.return_value.getresponse.return_value = mock_response

        folder_id = self.sharefile.create_folder("parent_id", "new_folder")  # type: ignore[no-untyped-call]

        self.assertEqual(folder_id, "new_folder_id")

    @patch.object(ShareFile, "get_folder_id")
    @patch.object(ShareFile, "create_folder")
    @patch.object(ShareFile, "upload_file")
    def test_add_interaction_with_client(self, mock_upload_file, mock_create_folder, mock_get_folder_id):  # type: ignore[no-untyped-def]
        mock_get_folder_id.side_effect = ["parent_id", "owner_folder_id"]
        mock_create_folder.return_value = "new_owner_folder_id"
        mock_upload_file.return_value = True

        note = MagicMock(spec=Note)
        note.note_owner.name = "Test Owner"
        note.metadata = {"meeting_name": "Test Meeting"}
        note.title.return_value = "Test Title"
        note.get_summary_for_crm.return_value = "Test Summary"
        note.get_attendees.return_value = ["Client 1", "Client 2"]

        self.sharefile.add_interaction_with_client(note)

        mock_get_folder_id.assert_called()
        mock_upload_file.assert_called()

    def test_get_accounts_by_owner_email_and_name(self):  # type: ignore[no-untyped-def]
        user = MagicMock(spec=User)
        result = self.sharefile.get_accounts_by_owner_email_and_name(user.email)
        self.assertEqual(result, [])

    def test_preview_before_syncing_with_crm(self):  # type: ignore[no-untyped-def]
        note = MagicMock(spec=Note)
        result = self.sharefile.preview_before_syncing_with_crm(note)
        self.assertEqual(result, {})

    @patch.object(ShareFile, "authenticate")
    def test_sharefile_initialization_with_settings(self, mock_authenticate):  # type: ignore[no-untyped-def]
        with self.settings(
            SHAREFILE_CLIENT_ID="settings_client_id",
            SHAREFILE_CLIENT_SECRET="settings_client_secret",
            SHAREFILE_HOSTNAME="settings.sharefile.com",
            SHAREFILE_USERNAME="settings_user",
            SHAREFILE_PASSWORD="settings_password",
            SHAREFILE_PARENTDIR="settings_dir",
        ):
            sharefile = ShareFile()

            self.assertEqual(sharefile.client_id, "settings_client_id")
            self.assertEqual(sharefile.client_secret, "settings_client_secret")
            self.assertEqual(sharefile.hostname, "settings.sharefile.com")
            self.assertEqual(sharefile.username, "settings_user")
            self.assertEqual(sharefile.password, "settings_password")
            self.assertEqual(sharefile.sharefile_parentdir, "settings_dir")

            mock_authenticate.assert_called_once()
