"""Utility functions for interacting with Salesforce CRM implementations."""

import datetime
import logging
from typing import Any, Literal

from django.conf import settings
from simple_salesforce.api import Salesforce
from simple_salesforce.format import format_soql

from deepinsights.core.integrations.crm.crm_models import CRMAccount
from deepinsights.core.integrations.oauth.salesforce import SalesforceOAuth
from deepinsights.flags.flagdefs import Flags
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User


# Returns a configured simple_salesforce.Salesforce instance from the given credentials.
def simple_salesforce_intstance_with_credentials(
    *,
    user: User | None = None,
    username: str | None = None,
    password: str | None = None,
    consumer_key: str | None = None,
    consumer_secret: str | None = None,
    security_token: str = "",
    instance_url: str | None = None,
    sf: Salesforce | None = None,
) -> Salesforce:
    if sf:
        return sf
    try:
        if (
            user
            and Flags.EnableSalesforceOAuthIntegration.is_active_for_user(user)
            and (access_token := SalesforceOAuth().get_access_token(user))
        ):
            return Salesforce(
                session_id=access_token,
                instance_url=instance_url,
            )
    except Exception as e:
        logging.error("Error while trying to get Salesforce OAuth access token", exc_info=e)
    if (
        user
        and Flags.EnableSalesforceClientCredentialsOAuthIntegration.is_active_for_user(user)
        and consumer_key
        and consumer_secret
    ):
        return Salesforce(
            consumer_key=consumer_key,
            consumer_secret=consumer_secret,
            security_token=security_token,
            instance_url=instance_url,
            domain=settings.SALESFORCE_ZEPLYN_APP_DOMAIN,
        )
    return Salesforce(
        username=username or settings.SALESFORCE_USER,
        password=password or settings.SALESFORCE_PASSWORD,
        security_token=security_token,
        instance_url=instance_url or settings.SALESFORCE_INSTANCE,
    )


# Returns the Salesforce ID for the user with the given email address (or None if that user is not
# found, or that email does not uniqley identify a user).
def get_user_id_by_email(simple_salesforce: Salesforce, user_email: str) -> str | None:
    """
    Get the Salesforce User ID by email address.
    :param user_email: Email address of the Salesforce user
    :return: Salesforce User ID or None if not found
    """
    # Query for the user ID based on email
    result = simple_salesforce.query_all(format_soql("SELECT Id FROM User WHERE Email = {} LIMIT 1", user_email))

    # Extract user ID if found
    if result["totalSize"] == 1:
        user_id = result["records"][0]["Id"]
        return user_id  # type: ignore[no-any-return]
    else:
        return None


# Returns a list of `CRMAccount`s for the provided entity, filtered by the entity_filter.
def get_salesforce_entity_by_owner_email_and_name(
    sf: Salesforce, entity_name: Literal["Contact", "Account"], owner_email: str, entity_filter: str | None = None
) -> list[CRMAccount]:
    owner_id = get_user_id_by_email(sf, owner_email)
    if not owner_id:
        logging.warning("Could not find Salesforce user ID for email: %s", owner_email)
        return []

    query = format_soql("SELECT Id, Name, Phone, OwnerId FROM {:literal}", entity_name)
    if entity_filter:
        query += format_soql(" WHERE Name LIKE '%{:like}%'", entity_filter)

    entities = (sf.query_all(query) or {}).get("records", [])
    logging.info("Got %s accounts for owner %s (Salesforce ID: %s)", len(entities), owner_email, owner_id)

    return [
        CRMAccount(
            crm_id=entity["Id"],
            name=entity["Name"],
            phone_number=entity["Phone"],
            client_type=entity_name.lower(),
            crm_system="salesforce",
            is_owned_by_user=(entity.get("OwnerId") == owner_id),
        )
        for entity in entities
    ]


_SALESFORCE_DATE_FORMAT = "%Y-%m-%dT%H:%M:%S.%f%z"  # Returns a Salesforce-formatted time for the given datetime.


def salesforceTime(date: datetime.datetime) -> str:
    return date.strftime(_SALESFORCE_DATE_FORMAT)


# Returns a datetime from a Salesforce-formatted time.
def datetime_from_salesforce_time(date: str) -> datetime.datetime:
    return datetime.datetime.strptime(date, _SALESFORCE_DATE_FORMAT)


# Given a note and the Salesforce ID of the note owner, returns a dictionary with data that can be
# used to preview what will be sent to the CRM.
def get_crm_preview(note: Note, owner_id: str, use_html_formatting: bool) -> dict[str, Any]:
    client_uuid_or_crm_id = (note.client or {}).get("uuid", "")
    if not client_uuid_or_crm_id:
        logging.error("Note does not have an associated Salesforce CRM client. Not adding interaction.")
        return {}

    crm_id_from_database = None
    try:
        crm_id_from_database = Client.objects.get(uuid=client_uuid_or_crm_id).crm_id
    except Exception:
        logging.info("Client not found in the database. Assuming client.uuid is Salesforce CRM ID.")
    client_crm_id = crm_id_from_database or client_uuid_or_crm_id

    meeting_name = note.metadata.get("meeting_name") or "Meeting with a client"  # type: ignore[union-attr]
    interaction = {
        "Owner Id": owner_id,
        "Name": meeting_name,
        "Account Id": client_crm_id,
        "Start Time": salesforceTime(note.created),
    }
    logging.info("interaction preview: %s", interaction)

    task_titles = [task.task_title for task in note.task_set.all()]
    next_steps = "*" + "\n*".join(task_titles) if task_titles else "No next steps identified"

    interaction_id = note.metadata.get("interactionId")  # type: ignore[union-attr]
    if not interaction_id:
        logging.error("No interaction ID associated with note. Skipping summary and task generation.")
        return {}
    data = {
        "Owner ID": owner_id,
        "Name": meeting_name,
        "Meeting Notes": note.get_summary_for_crm(use_html_formatting=use_html_formatting),
        "Account Id": client_crm_id,
        "Next Steps": next_steps,
    }
    logging.info("interaction summary preview: %s", data)

    tasks = []
    for task in note.task_set.all():
        task_details = {
            "Subject": task.task_title,
            "Description": "Follow up from client meeting." + (f"\n{task.task_desc}" if task.task_desc else ""),
            "Status": "Completed" if task.completed else "Not Started",
            "Priority": "Normal",
            "Related To": client_crm_id,
            "Activity Date": salesforceTime((datetime.datetime.today().date() + datetime.timedelta(days=7))),  # type: ignore[arg-type]
            "Owner Email": task.note.note_owner.email,  # type: ignore[union-attr]
        }
        logging.info("task preview: %s", task_details)
        tasks.append(task_details)

    data["tasks"] = tasks
    return data


# Adds a given task to the Salesforce CRM as a Salesforce `Task`.
#
# Can throw; if it throws, the task was not added successfully.
def add_task(simple_salesforce: Salesforce, task: Task, owner_email: str, identifier_details: dict[str, Any]):  # type: ignore[no-untyped-def]
    task_details = {
        "Subject": task.task_title,
        "Description": "Follow up from client meeting." + (f"\n{task.task_desc}" if task.task_desc else ""),
        "Status": "Completed" if task.completed else "Not Started",
        "Priority": "Normal",
        "ActivityDate": salesforceTime((datetime.datetime.today().date() + datetime.timedelta(7))),  # type: ignore[arg-type]
        "OwnerId": get_user_id_by_email(simple_salesforce, owner_email),
    }
    task_details.update(identifier_details)
    logging.info("recording task : %s", task_details)

    # Required because the default is to have metadata empty
    if task.metadata is None:
        task.metadata = {}
        task.save()
    if task.metadata.get("taskId"):
        logging.info("Not writing to CRM since this task already exists")
        return True
    # Create a new task
    new_task = simple_salesforce.Task.create(task_details)  # type: ignore[operator]
    task_id = new_task["id"]
    logging.info(f"Task added successfully. Task ID: {task_id}")
    task.metadata["taskId"] = task_id
    task.save()
