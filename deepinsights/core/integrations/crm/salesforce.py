import base64
import datetime
import enum
import logging
from datetime import timed<PERSON>ta
from typing import Any

from simple_salesforce.api import Salesforce as SimpleSalesforce
from simple_salesforce.format import format_soql

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm import salesforce_utils
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import (
    CRMAccount,
    CRMNote,
    CRMNoteType,
)
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class Salesforce(CrmBase):
    def __init__(
        self,
        *,
        user: User | None = None,
        username: str | None = None,
        password: str | None = None,
        consumer_key: str | None = None,
        consumer_secret: str | None = None,
        security_token: str = "",
        instance_url: str | None = None,
        sf: SimpleSalesforce | None = None,
    ) -> None:
        super().__init__()
        self.sf = salesforce_utils.simple_salesforce_intstance_with_credentials(
            user=user,
            username=username,
            password=password,
            consumer_key=consumer_key,
            consumer_secret=consumer_secret,
            security_token=security_token,
            instance_url=instance_url,
            sf=sf,
        )

    class NoteType(enum.StrEnum):
        STANDARD_NOTE = "standard_note"
        CONTENT_NOTE = "content_note"
        TASK = "task"

    def get_accounts_by_owner_email_and_name(
        self, owner_email: str, account_name_filter: str | None = None
    ) -> list[CRMAccount]:
        entities = salesforce_utils.get_salesforce_entity_by_owner_email_and_name(
            self.sf, "Account", owner_email, account_name_filter
        )
        entities.extend(
            salesforce_utils.get_salesforce_entity_by_owner_email_and_name(
                self.sf, "Contact", owner_email, account_name_filter
            )
        )
        return entities

    def preview_before_syncing_with_crm(self, note: Note) -> dict:  # type: ignore[type-arg]
        try:
            owner_email = note.note_owner.email  # type: ignore[union-attr]
            owner = salesforce_utils.get_user_id_by_email(self.sf, owner_email)
            if not owner:
                logging.error(
                    f"Could not find a Salesforce User with the given email: {owner_email}. Cannot provide preview."
                )
                return {}
            return salesforce_utils.get_crm_preview(note, owner, use_html_formatting=False)
        except Exception as e:
            logging.error(
                f"Error previewing interaction with client {note.note_owner.uuid} for note {note.uuid}: {e}",  # type: ignore[union-attr]
                exc_info=True,
            )
            raise

    # Attempts to add a ContentNote and ContentDocumentLink to Salesforce.
    #
    # This does not catch any errors, but throws them up the stack so that the caller can handle them.
    def __add_content_note(self, owner_id: str, meeting_title: str, client_id: str, content: str) -> str:
        salesforce_note = {
            "OwnerId": owner_id,
            "Title": meeting_title,
            "Content": base64.b64encode(content.encode("utf-8")).decode("utf-8"),
        }
        logging.info("recording ContentNote: %s", salesforce_note)
        note_id = self.sf.ContentNote.create(salesforce_note)["id"]  # type: ignore[operator]
        try:
            self.sf.ContentDocumentLink.create(  # type: ignore[operator]
                {
                    "ContentDocumentId": note_id,
                    "LinkedEntityId": client_id,
                }
            )
        except Exception as e:
            self.sf.ContentNote.delete(note_id)  # type: ignore[operator]
            raise
        return note_id  # type: ignore[no-any-return]

    # Attempts to add a Note to Salesforce.
    #
    # This does not catch any errors, but throws them up the stack so that the caller can handle them.
    def __add_note(self, owner_id: str, meeting_title: str, client_id: str, content: str) -> str:
        salesforce_note = {
            "OwnerId": owner_id,
            "Title": meeting_title,
            "ParentId": client_id,
            "Body": content,
        }
        logging.info("recording note: %s", salesforce_note)
        return self.sf.Note.create(salesforce_note)["id"]  # type: ignore[no-any-return, operator]

    def add_interaction_with_client(self, note: Note):  # type: ignore[no-untyped-def]
        owner_email = note.note_owner.email if note.note_owner else None
        owner = salesforce_utils.get_user_id_by_email(self.sf, owner_email)  # type: ignore[arg-type]
        if not owner:
            error_msg = "Could not find a Salesforce User with the given email: %s. Not adding interaction." % (
                owner_email,
            )
            logging.error(error_msg)
            raise ValueError(error_msg)

        client_uuid_or_crm_id = (note.client or {}).get("uuid", "")
        if not client_uuid_or_crm_id:
            error_msg = "Note does not have an associated Salesforce CRM client. Not adding interaction."
            logging.error(error_msg)
            raise ValueError(error_msg)

        crm_id_from_database = None
        try:
            crm_id_from_database = Client.objects.get(uuid=client_uuid_or_crm_id).crm_id
        except Exception:
            logging.info("Client not found in the database. Assuming client.uuid is Salesforce CRM ID.")
        client_crm_id = crm_id_from_database or client_uuid_or_crm_id

        meeting_name = note.metadata.get("meeting_name") or "Meeting with a client"  # type: ignore[union-attr]

        interaction_id = note.metadata.get("interactionId")  # type: ignore[union-attr]
        if not interaction_id:
            try:
                # Try recording the interaction with a ContentNote.
                interaction_id = self.__add_content_note(
                    owner, meeting_name, client_crm_id, note.get_summary_for_crm(use_html_formatting=True)
                )
            except Exception as e:
                # If the ContentNote can't be created, try creating a Note instead.
                try:
                    interaction_id = self.__add_note(
                        owner, meeting_name, client_crm_id, note.get_summary_for_crm(use_html_formatting=False)
                    )
                except Exception as e:
                    error_msg = "error recording interaction: %s. Not updating note." % (str(e),)
                    logging.error(error_msg)
                    raise RuntimeError(error_msg) from e
            note.metadata["interactionId"] = interaction_id  # type: ignore[index]
            note.save()
            logging.info("interaction saved")
        else:
            logging.info("Already associated with an interaction: %s", note.metadata.get("interactionId"))  # type: ignore[union-attr]

        for task in note.task_set.all():
            # First try to add task with a WhoId association (for Contacts)
            try:
                salesforce_utils.add_task(
                    self.sf,
                    task,
                    owner_email,  # type: ignore[arg-type]
                    {
                        "WhoId": client_crm_id,
                    },
                )
            except Exception as e:
                try:
                    # If that fails, add the task with a WhatId association (for Accounts)
                    salesforce_utils.add_task(
                        self.sf,
                        task,
                        owner_email,  # type: ignore[arg-type]
                        {
                            "WhatId": client_crm_id,
                        },
                    )
                except Exception as e:
                    error_msg = "Error adding task: %s" % (str(e),)
                    logging.error(error_msg)
                    raise RuntimeError(error_msg) from e

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        logging.warning("get_client_basic_info not implemented for Salesforce")
        return None

    def fetch_events(self, user: User, interval: timedelta) -> list[CalendarEvent]:
        logging.warning("fetch_crm_events not implemented for Salesforce")
        return []

    def fetch_notes_for_client(self, user: User, client: Client, lookback_interval: timedelta) -> list[CRMNote]:
        if not client.crm_id:
            logging.error("Client does not have a CRM ID. Cannot fetch notes %s, for user %s", client.uuid, user.uuid)
            return []

        parsed_notes = []

        # --- 1. Standard Notes ---
        try:
            notes_query = format_soql(
                (
                    "SELECT Id, Title, Body, CreatedDate "
                    "FROM Note "
                    "WHERE ParentId = {} "
                    "AND CreatedDate >= {:literal} "
                    "ORDER BY CreatedDate DESC"
                ),
                client.crm_id,
                salesforce_utils.salesforceTime(datetime.datetime.now(datetime.timezone.utc) - lookback_interval),
            )

            note_records = self.sf.query_all(notes_query).get("records", [])
            logging.info("Found %d standard Note records", len(note_records))

            logging.info("Found %d standard Note records", len(note_records))

            for note in note_records:
                try:
                    created_at = salesforce_utils.datetime_from_salesforce_time(note["CreatedDate"])
                except Exception as e:
                    logging.error("Error parsing CreatedDate for note %s", note["Id"], exc_info=e)
                    created_at = None

                parsed_notes.append(
                    CRMNote(
                        crm_system="salesforce",
                        crm_id=note["Id"],
                        content=f"{note.get('Title', '')}\n{note.get('Body', '')}",
                        created_at=created_at,
                        type=CRMNoteType.SALESFORCE_STANDARD_NOTE,
                        web_link=f"https://{self.sf.sf_instance}/{note['Id']}",
                    )
                )

        except Exception as e:
            logging.error("Error fetching standard Notes: %s", str(e))

        # --- 2. ContentNotes via ContentDocumentLink and ContentVersion ---
        try:
            content_document_link_query = f"""
                SELECT ContentDocumentId, LinkedEntityId
                FROM ContentDocumentLink
                WHERE LinkedEntityId = '{client.crm_id}'
                ORDER BY ContentDocumentId DESC
            """
            content_document_links = self.sf.query_all(content_document_link_query).get("records", [])
            content_doc_ids = [link["ContentDocumentId"] for link in content_document_links]
            logging.info("Found %d ContentDocumentLinks", len(content_doc_ids))

            if not content_doc_ids:
                logging.info("No ContentDocumentLinks found for client %s", client.crm_id)

            for content_doc_id in content_doc_ids:
                try:
                    content_version_query = format_soql(
                        (
                            "SELECT Id, ContentDocumentId, VersionData, Title, CreatedDate "
                            "FROM ContentVersion "
                            "WHERE ContentDocumentId = {} "
                            "AND CreatedDate >= {:literal} "
                            "ORDER BY CreatedDate DESC"
                        ),
                        content_doc_id,
                        salesforce_utils.salesforceTime(
                            datetime.datetime.now(datetime.timezone.utc) - lookback_interval
                        ),
                    )
                    content_versions = self.sf.query_all(content_version_query).get("records", [])

                    for version in content_versions:
                        version_data_url = version.get("VersionData")
                        if not version_data_url:
                            continue

                        instance_url = self.sf.sf_instance
                        full_url = f"https://{instance_url}{version_data_url}"
                        try:
                            headers = {"Authorization": f"Bearer {self.sf.session_id}"}
                            response = self.sf.session.get(full_url, headers=headers)
                            response.raise_for_status()
                            content = response.text
                        except Exception as fetch_err:
                            logging.error("Error fetching content from %s: %s", full_url, str(fetch_err))
                            content = "[Error retrieving content]"
                        logging.info(
                            "fetching content for content_doc_id: %s, version_id: %s", content_doc_id, version["Id"]
                        )
                        parsed_notes.append(
                            CRMNote(
                                crm_system="salesforce",
                                crm_id=content_doc_id,
                                content=f"{version.get('Title', '')}\n{content}",
                                created_at=version["CreatedDate"],
                                type=CRMNoteType.SALESFORCE_CONTENT_NOTE,
                                web_link=f"https://{self.sf.sf_instance}/{content_doc_id}",
                            )
                        )

                except Exception as content_note_error:
                    logging.error(
                        "Error fetching ContentVersions for doc ID %s: %s", content_doc_id, str(content_note_error)
                    )

            logging.info("Fetched %s notes/tasks for client %s", len(parsed_notes), client.crm_id)
            return parsed_notes

        except Exception as e:
            logging.error("Error fetching notes for client UUID %s: %s", client.crm_id, str(e), exc_info=True)
            return []
