import datetime
import logging
import traceback
from typing import Any

from office365.runtime.auth.client_credential import ClientCredential
from office365.sharepoint.client_context import Client<PERSON>ontext

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import CRMAccount, CRMNote
from deepinsights.core.preferences.preferences import SharePointConfiguration
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class SharePoint(CrmBase):
    def __init__(self, sharepoint_config: SharePointConfiguration) -> None:
        if not all(
            [
                sharepoint_config.client_id,
                sharepoint_config.client_secret,
                sharepoint_config.site_url,
                sharepoint_config.parent_folder,
            ]
        ):
            logging.error("Missing required SharePoint configuration")
            raise ValueError("Missing required SharePoint configuration")
        self.client_id = sharepoint_config.client_id
        self.client_secret = sharepoint_config.client_secret
        self.site_url = sharepoint_config.site_url
        self.sharepoint_parent_folder = sharepoint_config.parent_folder
        self.client_context = None
        self.authenticate()

    def authenticate(self) -> None:
        try:
            credentials = ClientCredential(self.client_id, self.client_secret)
            self.client_context = ClientContext(self.site_url).with_credentials(credentials)

            if self.client_context is None:
                raise ValueError("Failed to create SharePoint client context")

            self.client_context.load(self.client_context.web)
            self.client_context.execute_query()
            logging.info(
                "Successfully authenticated to SharePoint site: %s", self.client_context.web.properties["Title"]
            )
        except Exception as e:
            logging.error("SharePoint authentication failed: %s", str(e))
            self.client_context = None
            raise

    def ensure_folder_exists(self, folder_path: str) -> None:
        if self.client_context is None:
            self.authenticate()
            if self.client_context is None:
                raise ValueError("Failed to authenticate with SharePoint")

        try:
            parts = folder_path.strip("/").split("/")

            doc_lib = self.client_context.web.lists.get_by_title("Documents")
            self.client_context.load(doc_lib, ["RootFolder"])
            self.client_context.execute_query()

            parent_folder_url = doc_lib.properties["RootFolder"].properties["ServerRelativeUrl"]

            for part in parts:
                if not part:
                    continue

                folder_url = f"{parent_folder_url}/{part}"

                try:
                    folder = self.client_context.web.get_folder_by_server_relative_url(folder_url)
                    self.client_context.load(folder)
                    self.client_context.execute_query()
                    # Folder exists, continue to next part
                    parent_folder_url = folder_url
                except Exception:
                    # Folder doesn't exist, create it
                    parent_folder = self.client_context.web.get_folder_by_server_relative_url(parent_folder_url)
                    new_folder = parent_folder.folders.add(part)
                    self.client_context.load(new_folder)
                    self.client_context.execute_query()
                    logging.info(f"Created folder: {folder_url}")
                    parent_folder_url = folder_url

        except Exception as e:
            logging.error("Error ensuring folder exists: %s", str(e))
            raise

    def upload_file(self, folder_path: str, filename: str, content: str) -> bool:
        try:
            if self.client_context is None:
                self.authenticate()
                if self.client_context is None:
                    raise ValueError("Failed to authenticate with SharePoint")

            self.ensure_folder_exists(folder_path)

            doc_lib = self.client_context.web.lists.get_by_title("Documents")
            self.client_context.load(doc_lib, ["RootFolder"])
            self.client_context.execute_query()
            root_folder_url = doc_lib.properties["RootFolder"].properties["ServerRelativeUrl"]

            target_folder_url = f"{root_folder_url}/{folder_path}".replace("//", "/")

            target_folder = self.client_context.web.get_folder_by_server_relative_url(target_folder_url)
            target_file = target_folder.upload_file(filename, content.encode("utf-8"))
            self.client_context.execute_query()

            logging.info("File uploaded successfully to SharePoint: %s/%s", target_folder_url, filename)
            return True

        except Exception as e:
            logging.error("Failed to upload file to SharePoint: %s", str(e))
            return False

    def add_interaction_with_client(self, note: Note) -> None:
        try:
            if self.client_context is None:
                self.authenticate()
                if self.client_context is None:
                    raise ValueError("Failed to authenticate with SharePoint")

            note_owner = note.note_owner.name
            meeting_name = note.metadata.get("meeting_name", "unnamed_meeting")
            current_time = note.created.strftime("%Y-%m-%d-%H-%M")
            filename = f"{current_time}_{meeting_name}.txt"

            folder_path = f"{self.sharepoint_parent_folder}/{note_owner}"

            content = f"Title: {note.title()}\n\n"
            content += f"Summary: {note.get_summary_for_crm(use_html_formatting=False)}\n\n"
            content += f"Clients: {', '.join(note.get_attendees())}"

            file_uploaded = self.upload_file(folder_path, filename, content)

            if not file_uploaded:
                raise Exception(f"Failed to upload file {filename} to SharePoint")

        except Exception as e:
            logging.error("Error in add_interaction_with_client: %s", str(e))
            logging.error("Traceback: %s", traceback.format_exc())
            raise

    def get_accounts_by_owner_email_and_name(self, owner_email: str, account_name_filter=None) -> list[CRMAccount]:  # type: ignore[no-untyped-def]
        logging.warning("get_accounts_by_owner_email_and_name is not implemented for SharePoint")
        return []

    def preview_before_syncing_with_crm(self, note: Note) -> dict:  # type: ignore[type-arg]
        logging.warning("preview_before_syncing_with_crm is not implemented for SharePoint")
        return {}

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        logging.warning("get_client_basic_info not implemented for SharePoint")
        return None

    def fetch_events(self, user: User, interval: datetime.timedelta) -> list[CalendarEvent]:
        logging.warning("fetch_crm_events not implemented for SharePoint")
        return []

    def fetch_notes_for_client(
        self, user: User, client: Client, lookback_interval: datetime.timedelta
    ) -> list[CRMNote]:
        logging.warning("fetch_notes_for_client not implemented for SharePoint")
        return []
