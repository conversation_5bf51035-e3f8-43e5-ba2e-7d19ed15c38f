import datetime
import http.client
import json
import logging
import time
import traceback
import urllib.parse
import urllib.request
from typing import Any

import requests
from django.conf import settings

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import CRMAccount, CRMNote
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class ShareFile(CrmBase):
    def __init__(  # type: ignore[no-untyped-def]
        self, client_id=None, client_secret=None, hostname=None, username=None, password=None, sharefile_dir=None
    ) -> None:
        self.client_id = client_id or settings.SHAREFILE_CLIENT_ID
        self.client_secret = client_secret or settings.SHAREFILE_CLIENT_SECRET
        self.hostname = hostname or settings.SHAREFILE_HOSTNAME
        self.username = username or settings.SHAREFILE_USERNAME
        self.password = password or settings.SHAREFILE_PASSWORD
        self.sharefile_parentdir = sharefile_dir or settings.SHAREFILE_PARENTDIR
        self.access_token = None
        self.refresh_token = None
        self.authenticate()  # type: ignore[no-untyped-call]

    def authenticate(self):  # type: ignore[no-untyped-def]
        url_path = f"https://{self.hostname}/oauth/token"

        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        data = {
            "grant_type": "password",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "username": self.username,
            "password": self.password,
        }

        response = requests.post(url_path, headers=headers, data=data)
        token_data = response.json()
        if response.status_code == 200:
            self.access_token = token_data["access_token"]
            self.refresh_token = token_data["refresh_token"]
            logging.info(f"Received token info:{token_data}")
        else:
            logging.error(f"Failed to generate token info: {token_data} ")

    def refresh_auth_token(self):  # type: ignore[no-untyped-def]
        if not self.refresh_token:
            self.authenticate()  # type: ignore[no-untyped-call]
            return

        url = f"https://{self.hostname}/oauth/token"
        data = {
            "grant_type": "refresh_token",
            "refresh_token": self.refresh_token,
            "client_id": self.client_id,
            "client_secret": self.client_secret,
        }
        response = requests.post(url, data=data)
        if response.status_code == 200:
            token_data = response.json()
            self.access_token = token_data["access_token"]
            self.refresh_token = token_data["refresh_token"]
            logging.info("ShareFile token refreshed successfully")
        else:
            logging.error(f"ShareFile token refresh failed: {response.status_code} {response.text}")
            self.authenticate()

    def get_folder_id(self, folder_name, parent_id="allshared"):  # type: ignore[no-untyped-def]
        encoded_folder_name = urllib.parse.quote(folder_name)
        encoded_parent_id = urllib.parse.quote(parent_id)
        uri_path = f"/sf/v3/Items({encoded_parent_id})/Children?$filter=Name%20eq%20'{encoded_folder_name}'"

        conn = http.client.HTTPSConnection(self.hostname)
        headers = {"Authorization": f"Bearer {self.access_token}"}
        conn.request("GET", uri_path, headers=headers)
        response = conn.getresponse()

        if response.status == 200:
            data = json.loads(response.read().decode())
            if data["value"]:
                return data["value"][0]["Id"]
            else:
                logging.error(f"Folder '{folder_name}' not found in parent '{parent_id}'")
                raise Exception(f"Folder '{folder_name}' not found in parent '{parent_id}'")
        else:
            logging.error(f"Failed to get folder ID: {response.status} {response.reason}")
            raise Exception(f"Failed to get folder ID: {response.status} {response.reason}")

    def create_folder(self, parent_id, folder_name):  # type: ignore[no-untyped-def]
        uri_path = f"/sf/v3/Items({parent_id})/Folder"
        conn = http.client.HTTPSConnection(f"{self.hostname}")
        headers = {"Authorization": f"Bearer {self.access_token}", "Content-Type": "application/json"}
        data = json.dumps({"Name": folder_name})
        conn.request("POST", uri_path, body=data, headers=headers)
        response = conn.getresponse()

        if response.status == 200:
            data = json.loads(response.read().decode())
            return data["Id"]  # type: ignore[index]
        else:
            logging.error(f"Failed to create folder: {response.status} {response.reason}")
            raise Exception(f"Failed to create folder: {response.status} {response.reason}")

    def upload_file(self, folder_id, filename, content):  # type: ignore[no-untyped-def]
        uri_path = f"/sf/v3/Items({folder_id})/Upload"
        conn = http.client.HTTPSConnection(f"{self.hostname}")
        headers = {"Authorization": f"Bearer {self.access_token}"}
        conn.request("GET", uri_path, headers=headers)
        response = conn.getresponse()

        if response.status == 200:
            upload_config = json.loads(response.read().decode())
            if "ChunkUri" in upload_config:
                return self.multipart_form_post_upload(upload_config["ChunkUri"], filename, content)  # type: ignore[no-untyped-call]
            else:
                logging.error("ChunkUri not found in upload configuration")
                raise Exception("ChunkUri not found in upload configuration")
        else:
            logging.error(f"Failed to get upload URL: {response.status} {response.reason}")
            raise Exception(f"Failed to get upload URL: {response.status} {response.reason}")

    def multipart_form_post_upload(self, url, filename, content):  # type: ignore[no-untyped-def]
        boundary = f"----------{int(time.time())}"
        headers = {
            "Content-Type": f"multipart/form-data; boundary={boundary}",
        }

        body = (
            f"--{boundary}\r\n"
            f'Content-Disposition: form-data; name="File1"; filename="{filename}"\r\n'
            f"Content-Type: text/plain\r\n\r\n"
            f"{content}\r\n"
            f"--{boundary}--\r\n"
        )

        parsed_url = urllib.parse.urlparse(url)
        conn = http.client.HTTPSConnection(parsed_url.netloc)
        conn.request("POST", f"{parsed_url.path}?{parsed_url.query}", body.encode(), headers)
        response = conn.getresponse()

        if response.status == 200:
            data = response.read().decode()
            if "OK" in data:
                return True  # File uploaded successfully
            else:
                logging.error(f"Failed to upload file: {data}")
                raise Exception(f"Failed to upload file: {data}")
        else:
            logging.error(f"Failed to upload file: {response.status} {response.reason}")
            raise Exception(f"Failed to upload file: {response.status} {response.reason}")

    def add_interaction_with_client(self, note: Note):  # type: ignore[no-untyped-def]
        try:
            if not self.access_token:
                self.refresh_auth_token()  # type: ignore[no-untyped-call]

            parent_dir = self.sharefile_parentdir
            note_owner = note.note_owner.name  # type: ignore[union-attr]
            meeting_name = note.metadata.get("meeting_name", "unnamed_meeting")  # type: ignore[union-attr]
            current_time = note.created.strftime("%Y-%m-%d-%H-%M")
            filename = f"{current_time}_{meeting_name}.txt"

            # Create the content for the text file
            content = f"Title: {note.title()}\n\n"
            content += f"Summary: {note.get_summary_for_crm(use_html_formatting=False)}\n\n"
            content += f"Clients: {', '.join(note.get_attendees())}"

            # Check if parent directory exists and create note owner folder if needed
            parent_folder_id = self.get_folder_id(folder_name=parent_dir)  # type: ignore[no-untyped-call]
            if not parent_folder_id:
                logging.error(f"Parent directory '{parent_dir}' not found")
                raise Exception(f"Parent directory '{parent_dir}' not found")

            try:
                owner_folder_id = self.get_folder_id(folder_name=note_owner, parent_id=parent_folder_id)  # type: ignore[no-untyped-call]
            except Exception as e:
                if str(e) == f"Folder '{note_owner}' not found in parent '{parent_folder_id}'":
                    owner_folder_id = self.create_folder(parent_folder_id, note_owner)  # type: ignore[no-untyped-call]
                else:
                    raise

            # Upload the file
            file_uploaded = self.upload_file(owner_folder_id, filename, content)  # type: ignore[no-untyped-call]

            if file_uploaded:
                logging.info(f"File uploaded to ShareFile successfully: {filename}")
            else:
                logging.error(f"Failed to upload file {filename} to ShareFile")
                raise Exception(f"Failed to upload file {filename} to ShareFile")

        except Exception as e:
            logging.error(f"Error in add_interaction_with_client: {str(e)}")
            logging.error(f"Traceback: {traceback.format_exc()}")
            raise

    def get_accounts_by_owner_email_and_name(self, owner_email: str, account_name_filter=None) -> list[CRMAccount]:  # type: ignore[no-untyped-def]
        # This method is not applicable for ShareFile integration
        # You may want to implement this differently or leave it as a placeholder
        logging.warning("get_accounts_by_owner_email_and_name is not implemented for ShareFile")
        return []

    def preview_before_syncing_with_crm(self, note: Note) -> dict:  # type: ignore[type-arg]
        # This method is not applicable for ShareFile integration
        # You may want to implement this differently or leave it as a placeholder
        logging.warning("preview_before_syncing_with_crm is not implemented for ShareFile")
        return {}

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        logging.warning("get_client_basic_info not implemented for sharefile")
        return None

    def fetch_events(self, user: User, interval: datetime.timedelta) -> list[CalendarEvent]:
        logging.warning("fetch_crm_events not implemented for Sharefile")
        return []

    def fetch_notes_for_client(
        self, user: User, client: Client, lookback_interval: datetime.timedelta
    ) -> list[CRMNote]:
        logging.warning("fetch_notes_for_client not implemented for ShareFile")
        return []
