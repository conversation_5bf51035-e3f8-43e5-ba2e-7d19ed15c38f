import datetime
import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, ClassVar
from urllib.parse import urljoin

import requests
from django.db.models import QuerySet

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import CRMAccount, CRMNote
from deepinsights.core.integrations.oauth.microsoft_dynamics import MicrosoftDynamicsOAuth
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User


class TamaracMicrosoftDynamics(CrmBase):
    """Microsoft Dynamics CRM integration class for Tamarac Overlay"""

    # Class constants
    API_VERSION: ClassVar[str] = "v9.2"

    def __init__(self, *, user: User) -> None:
        """Initialize the Microsoft Dynamics CRM integration."""
        resource_url = user.get_crm_configuration().dynamics.dynamics_resource_url
        self.oauth = MicrosoftDynamicsOAuth(resource_url)
        self.user = user

    @classmethod
    def _get_headers(cls, access_token: str) -> dict[str, str]:
        """Get complete headers for all request types."""
        return {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json",
            "Content-Type": "application/json",
            "OData-MaxVersion": "4.0",
            "OData-Version": "4.0",
        }

    def _get_api_url(self, user: User) -> tuple[str, str]:
        access_token = self.oauth.get_access_token(user)
        if not access_token:
            raise Exception(f"Could not get access token for user {user.uuid}")

        crm_config = user.get_crm_configuration()
        dynamics_resource_url = crm_config.dynamics.dynamics_resource_url

        api_url = urljoin(dynamics_resource_url, f"api/data/{self.API_VERSION}/")

        return api_url, access_token

    def _get_household_account_type_ids(self, api_url: str, headers: dict[str, str]) -> list[str]:
        """
        Fetch household account type IDs dynamically from tam_accounttypes lookup table.

        Args:
            api_url: The Dynamics API base URL
            headers: Request headers

        Returns:
            List of household account type IDs
        """
        try:
            account_types_url = urljoin(api_url, "tam_accounttypes")

            # Query for active household account types
            params = {
                "$select": "tam_accounttypeid,tam_name",
                "$filter": "statecode eq 0 and (contains(tam_name, 'Household') or contains(tam_name, 'household'))",
            }

            response = requests.get(account_types_url, headers=headers, params=params)

            if response.status_code != 200:
                logging.error("Error fetching account types: %s, %s", response.status_code, response.text)
                return []

            account_types_data = response.json().get("value", [])
            household_type_ids = [
                account_type.get("tam_accounttypeid")
                for account_type in account_types_data
                if account_type.get("tam_accounttypeid")
            ]

            logging.info(
                "Found %d household account types: %s",
                len(household_type_ids),
                [f"{at.get('tam_name')} ({at.get('tam_accounttypeid')})" for at in account_types_data],
            )

            return household_type_ids

        except Exception as e:
            logging.error("Error fetching household account type IDs: %s", e, exc_info=True)
            return []

    def get_accounts_by_owner_email_and_name(
        self, owner_email: str, account_name_filter: str | None = None
    ) -> list[CRMAccount]:
        """
        Fetch household accounts from Tamarac (Microsoft Dynamics overlay)
        This method fetches only household-type accounts that are active.
        Args:
            owner_email: Email of the account owner
            account_name_filter: Optional filter for account name
        Returns:
            List of CRMAccount objects
        """
        try:
            user = User.objects.get(email=owner_email)
            api_url, access_token = self._get_api_url(user)

            headers = self._get_headers(access_token)

            # Dynamically fetch household account type IDs
            household_type_ids = self._get_household_account_type_ids(api_url, headers)

            if not household_type_ids:
                logging.warning("No household account types found, returning empty list")
                return []

            entities = []

            # Fetch household accounts using dynamically retrieved account type IDs
            accounts_url = urljoin(api_url, "accounts")

            # Build filter conditions for household accounts
            if len(household_type_ids) == 1:
                account_type_filter = f"_tam_accounttypeid_value eq '{household_type_ids[0]}'"
            else:
                type_conditions = [f"_tam_accounttypeid_value eq '{type_id}'" for type_id in household_type_ids]
                account_type_filter = f"({' or '.join(type_conditions)})"

            filter_conditions = [
                account_type_filter,  # Household account types
                "statecode eq 0",  # Only active accounts
            ]

            # Add account name filter if provided
            if account_name_filter:
                filter_conditions.append(f"contains(name, '{account_name_filter}')")

            params = {
                "$select": "name,accountid,telephone1,emailaddress1,tam_uploadid,_tam_accounttypeid_value,statecode",
                "$filter": " and ".join(filter_conditions),
            }

            response = requests.get(accounts_url, headers=headers, params=params)

            if response.status_code != 200:
                logging.error("Error fetching household accounts: %s, %s", response.status_code, response.text)
            else:
                accounts_data = response.json().get("value", [])
                logging.info("Found %d household accounts for user %s", len(accounts_data), owner_email)

                for account in accounts_data:
                    try:
                        entities.append(
                            CRMAccount(
                                email=account.get("emailaddress1"),
                                phone_number=account.get("telephone1", None),
                                crm_id=account.get("accountid"),
                                client_type="Account",
                                crm_system="microsoft_dynamics",
                                name=account.get("name", "Unknown Account"),
                            )
                        )
                    except Exception as e:
                        logging.error("Error creating CRMAccount object for household account: %s", account, exc_info=e)

                logging.info("Successfully retrieved %d household accounts", len(entities))
            return entities

        except Exception as e:
            logging.error("Error getting Tamarac household accounts: %s", e, exc_info=True)
            return []

    def preview_before_syncing_with_crm(self, note: Note) -> dict[str, Any]:
        """
        Generate a preview of what will be synced to Dynamics CRM.

        Args:
            note: The note to preview

        Returns:
            Dictionary with preview data including note, tasks, and client information
        """
        try:
            if not note.note_owner:
                raise Exception("Note owner not found")

            client = None
            if note.client and note.client.get("uuid"):
                try:
                    client = Client.objects.get(uuid=note.client.get("uuid"))
                except Client.DoesNotExist:
                    logging.error("Client with UUID %s not found", note.client.get("uuid"))

            if not client:
                raise Exception("No clients associated with the note")

            # Determine activity type based on meeting category
            meeting_category = getattr(note.meeting_type, "category", None) if note.meeting_type else None

            # NOTE: This logic was specifically built out for Tamarac's Dynamics overlay for TFC,
            # they wanted client meetring notes to be synced in activity type appointments,
            # and debriefs to be synced as phone calls.
            if meeting_category == "debrief":
                activity_type = "Phone Call"
            else:
                activity_type = "Appointment"

            # Prepare note data for preview
            note_data = {
                "activity_type": activity_type,
                "title": note.title(),
                "content": note.get_summary_for_crm(use_html_formatting=False),
                "regarding": client.name if client else "No client specified",
                "scheduled_start": note.created.isoformat() if note.created else None,
                "scheduled_end": (note.created + timedelta(hours=1)).isoformat() if note.created else None,
            }

            # Prepare tasks data for preview
            tasks_data = []
            tasks = list(note.task_set.all())

            if tasks:
                for task in tasks:
                    task_data = {
                        "subject": task.task_title,
                        "description": task.task_desc,
                        "due_date": task.due_date.isoformat() if task.due_date else "Unset",
                        "status": "Completed" if task.completed else "Open",
                        "assignee": task.assignee.email if task.assignee else note.note_owner.email,
                    }
                    tasks_data.append(task_data)

                meeting_name = note.title()
                consolidated_subject = f"{meeting_name} - Follow Up"

                task_descriptions = []
                earliest_due_date = None

                # counter starts with 1 because it's the task index we assign in the description at the time of syncing
                for i, task in enumerate(tasks, 1):
                    task_desc = f"{i}. {task.task_title}"
                    if task.task_desc:
                        task_desc += f": {task.task_desc}"
                    if task.assignee:
                        task_desc += f" (Assigned to: {task.assignee.email})"
                    if task.due_date:
                        task_desc += f" (Due: {task.due_date.strftime('%Y-%m-%d')})"
                        if not earliest_due_date or task.due_date < earliest_due_date:
                            earliest_due_date = task.due_date

                    task_descriptions.append(task_desc)

                consolidated_description = f"Follow-up tasks from meeting: {meeting_name}\n\n" + "\n".join(
                    task_descriptions
                )
                due_date = earliest_due_date or datetime.datetime.now(datetime.timezone.utc) + timedelta(days=7)

                consolidated_task = {
                    "subject": consolidated_subject,
                    "description": consolidated_description,
                    "due_date": due_date.isoformat(),
                    "status": "Open",
                    "priority": "Normal",
                    "note": f"This task consolidates {len(tasks)} individual tasks",
                }
            else:
                consolidated_task = None

            return {
                "note": note_data,
                "individual_tasks": tasks_data,
                "consolidated_task": consolidated_task,
                "client": client.to_dict() if client else None,
                "sync_summary": {
                    "will_create_activity": True,
                    "activity_type": activity_type,
                    "will_create_tasks": len(tasks_data) > 0,
                    "tasks_count": len(tasks_data),
                    "will_consolidate_tasks": len(tasks_data) > 0,
                },
            }

        except Exception as e:
            logging.error("Error previewing interaction with client for note %s: %s", note.uuid, e, exc_info=True)
            raise

    def add_interaction_with_client(self, note: Note) -> None:
        """
        Add a note and associated tasks to Microsoft Dynamics as activities.
        Maps different meeting types to appropriate Dynamics activities:
        - Client meetings -> Appointment activities
        - Debriefs -> Phone Call activities
        - Tasks -> Task activities (consolidated into one follow-up task)

        Args:
            note: The note to add as an interaction

        Raises:
            Exception: If the note cannot be added
        """
        try:
            if not note.note_owner:
                raise Exception("Note owner not found")

            api_url, access_token = self._get_api_url(note.note_owner)

            # If we already have an interaction ID, log and return
            if note.metadata and note.metadata.get("interactionId"):
                logging.info("Note %s already has an interaction ID: %s", note.uuid, note.metadata["interactionId"])
                return

            if not note.client or not note.client.get("uuid"):
                raise Exception("No primary client specified in the note")

            try:
                client = Client.objects.get(uuid=note.client.get("uuid"))
                if not client.crm_id:
                    raise Exception(f"Client {client.uuid} does not have a CRM ID")

                client_type = client.client_type.lower() if client.client_type else "account"
                client_info = {"id": client.crm_id, "type": client_type}

            except Client.DoesNotExist:
                raise Exception(f"Client with UUID {note.client.get('uuid')} not found")

            headers = self._get_headers(access_token)

            meeting_category = getattr(note.meeting_type, "category", None) if note.meeting_type else None

            # NOTE: This logic was specifically built out for Tamarac's Dynamics overlay for TFC,
            # they wanted client meetring notes to be synced in activity type appointments,
            # and debriefs to be synced as phone calls.
            if meeting_category == "debrief":
                # Debriefs -> Phone Call
                activity_type = "phonecall"
                activity_entity = "phonecalls"
            else:
                # Default to appointment for client meetings, internal or unknown types
                activity_type = "appointment"
                activity_entity = "appointments"

            if scheduled_event := note.scheduled_event:
                meeting_start_time = scheduled_event.start_time
                meeting_end_time = scheduled_event.end_time
            else:
                # Fallback to creation time if no scheduled event exists
                meeting_start_time = note.created  # Assuming created time is the start time
                meeting_end_time = meeting_start_time + timedelta(hours=1)

            activity_data = {
                "subject": note.title(),
                "description": note.get_summary_for_crm(use_html_formatting=False),
                f"regardingobjectid_{client_info['type']}@odata.bind": f"/{client_info['type']}s({client_info['id']})",
            }

            activity_data["scheduledstart"] = meeting_start_time.isoformat()
            activity_data["scheduledend"] = meeting_end_time.isoformat()

            activity_url = urljoin(api_url, activity_entity)
            response = requests.post(activity_url, headers=headers, json=activity_data)

            main_activity_id = None
            if response.status_code in (200, 201, 204):
                # Extract activity ID from response headers
                if "OData-EntityId" in response.headers:
                    entity_id = response.headers["OData-EntityId"]
                    main_activity_id = entity_id.split("(")[1].split(")")[0]

                if main_activity_id:
                    if not note.metadata:
                        note.metadata = {}
                    note.metadata["interactionId"] = main_activity_id
                    note.metadata["activityType"] = activity_type
                    note.save()
                    logging.info("Successfully created %s in Dynamics with ID %s", activity_type, main_activity_id)
                else:
                    logging.warning(
                        "%s created but couldn't extract ID from response headers", activity_type.capitalize()
                    )
            else:
                logging.error(
                    "Error creating %s in Dynamics: %s, %s", activity_type, response.status_code, response.text
                )

            # Handle tasks - consolidate into a single follow-up task
            tasks = note.task_set.all()
            if tasks:
                self._create_consolidated_follow_up_task(api_url, headers, note, tasks, client_info)

        except Exception as e:
            logging.error("Error adding interaction with client for note %s: %s", note.uuid, e, exc_info=True)
            raise

    def _create_consolidated_follow_up_task(
        self, api_url: str, headers: dict[str, str], note: Note, tasks: QuerySet[Task], client: dict[str, Any]
    ) -> None:
        """
        Create a consolidated follow-up task in Dynamics that contains all individual tasks.

        Args:
            api_url: The API URL
            headers: Request headers
            note: The original note
            tasks: List of tasks to consolidate
            client: Client dictionary with id and type
        """
        try:
            # Create consolidated task subject
            meeting_name = note.title()
            consolidated_subject = f"{meeting_name} - Follow Up"

            # Build consolidated description with all tasks
            task_descriptions = []
            earliest_due_date = None

            for i, task in enumerate(tasks, 1):
                task_desc = f"{i}. {task.task_title}"
                if task.task_desc:
                    task_desc += f": {task.task_desc}"
                if task.assignee:
                    task_desc += f" (Assigned to: {task.assignee.email})"
                if task.due_date:
                    task_desc += f" (Due: {task.due_date.strftime('%Y-%m-%d')})"
                    if not earliest_due_date or task.due_date < earliest_due_date:
                        earliest_due_date = task.due_date

                task_descriptions.append(task_desc)

            consolidated_description = f"Follow-up tasks from meeting: {meeting_name}\n\n" + "\n".join(
                task_descriptions
            )

            # Use earliest due date or default to 7 days from now
            due_date = earliest_due_date or datetime.datetime.now(datetime.timezone.utc) + timedelta(days=7)

            # Prepare consolidated task data
            consolidated_task_data = {
                "subject": consolidated_subject,
                "description": consolidated_description,
                "scheduledend": due_date.isoformat(),
                "statecode": 0,  # Open
                "prioritycode": 1,  # Normal priority
                f"regardingobjectid_{client['type']}@odata.bind": f"/{client['type']}s({client['id']})",
            }

            # Create the consolidated task
            tasks_url = urljoin(api_url, "tasks")
            response = requests.post(tasks_url, headers=headers, json=consolidated_task_data)

            if response.status_code in (200, 201, 204):
                # Extract task ID from response headers
                consolidated_task_id = None
                if "OData-EntityId" in response.headers:
                    entity_id = response.headers["OData-EntityId"]
                    consolidated_task_id = entity_id.split("(")[1].split(")")[0]

                if consolidated_task_id:
                    # Update all original tasks with the consolidated task ID
                    for task in tasks:
                        if not task.metadata:
                            task.metadata = {}
                        task.metadata["consolidatedTaskId"] = consolidated_task_id
                        task.save()

                    logging.info(
                        "Successfully created consolidated follow-up task in Dynamics with ID %s", consolidated_task_id
                    )
                    logging.info("Consolidated %d individual tasks into single follow-up task", len(tasks))
                else:
                    logging.warning("Consolidated task created but couldn't extract ID from response headers")
            else:
                logging.error(
                    "Error creating consolidated task in Dynamics: %s, %s", response.status_code, response.text
                )

        except Exception as e:
            logging.error("Error creating consolidated follow-up task: %s", e, exc_info=True)
            raise

    def fetch_notes_for_client(self, user: User, client: Client, lookback_interval: timedelta) -> list[CRMNote]:
        """
        Fetch notes for a client from Tamarac (Microsoft Dynamics overlay).
        Notes are stored as activities (appointments or phone calls) rather than annotations.

        Args:
            user: The user requesting the notes
            client: The client to fetch notes for
            lookback_interval: How far back to look for notes

        Returns:
            List of CRMNote objects
        """
        try:
            if not client.crm_id:
                logging.error("Client %s does not have a CRM ID", client.uuid)
                return []

            api_url, access_token = self._get_api_url(user)
            headers = self._get_headers(access_token)

            not_before_date = datetime.datetime.now(datetime.timezone.utc) - lookback_interval
            formatted_date = not_before_date.strftime("%Y-%m-%dT%H:%M:%SZ")

            activity_types = [
                {"endpoint": "appointments", "label": "Appointment", "entity": "appointment"},
                {"endpoint": "phonecalls", "label": "Phone Call", "entity": "phonecall"},
            ]

            all_notes = []
            for activity_config in activity_types:
                notes = self._fetch_activities_for_client(
                    api_url=api_url,
                    headers=headers,
                    client_crm_id=client.crm_id,
                    formatted_date=formatted_date,
                    user=user,
                    **activity_config,
                )
                all_notes.extend(notes)

            # Sort all notes by creation date (newest first)
            all_notes.sort(
                key=lambda x: x.created_at or datetime.datetime.min.replace(tzinfo=datetime.timezone.utc), reverse=True
            )

            logging.info(
                "Successfully fetched and processed %d total notes for client %s from Tamarac Dynamics",
                len(all_notes),
                client.uuid,
            )
            return all_notes

        except Exception as e:
            logging.error("Error fetching notes from Tamarac Dynamics for client %s: %s", client.uuid, e, exc_info=True)
            return []

    def _fetch_activities_for_client(
        self,
        api_url: str,
        headers: dict[str, str],
        client_crm_id: str,
        formatted_date: str,
        user: User,
        endpoint: str,
        label: str,
        entity: str,
    ) -> list[CRMNote]:
        """
        Helper method to fetch activities of a specific type for a client.

        Args:
            api_url: The Dynamics API base URL
            headers: Request headers
            client_crm_id: The CRM ID of the client
            formatted_date: Formatted date string for filtering
            user: The user making the request
            endpoint: The API endpoint (e.g., "appointments", "phonecalls")
            label: Display label for the activity type (e.g., "Appointment", "Phone Call")
            entity: Entity type name for web links (e.g., "appointment", "phonecall")

        Returns:
            List of CRMNote objects for this activity type
        """
        try:
            activities_url = urljoin(api_url, endpoint)
            filter_query = f"_regardingobjectid_value eq '{client_crm_id}' and createdon ge {formatted_date}"

            params = {
                "$filter": filter_query,
                "$orderby": "createdon desc",
                "$select": "activityid,subject,description,createdon,scheduledstart,scheduledend",
            }

            response = requests.get(activities_url, headers=headers, params=params)

            if response.status_code != 200:
                logging.error(
                    "Error fetching %s from Tamarac Dynamics: %s, %s", endpoint, response.status_code, response.text
                )
                return []

            activities_data = response.json().get("value", [])
            logging.info("Found %d %s for client", len(activities_data), endpoint)

            notes = []
            dynamics_url = user.get_crm_configuration().dynamics.dynamics_resource_url
            if dynamics_url.endswith("/"):
                dynamics_url = dynamics_url[:-1]

            for activity in activities_data:
                try:
                    activity_id = activity.get("activityid")
                    web_link = f"{dynamics_url}/main.aspx?pagetype=entityrecord&etn={entity}&id={activity_id}"

                    created_at = None
                    if created_date_str := activity.get("createdon"):
                        try:
                            created_at = datetime.datetime.fromisoformat(created_date_str.replace("Z", "+00:00"))
                        except Exception as e:
                            logging.error("Error parsing date for %s %s: %s", entity, activity_id, e)

                    subject = activity.get("subject", "")
                    description = activity.get("description", "")
                    content = f"[{label}] {subject}\n{description}" if subject else f"[{label}] {description}"

                    notes.append(
                        CRMNote(
                            crm_id=activity_id,
                            crm_system="microsoft_dynamics",
                            content=content,
                            created_at=created_at,
                            web_link=web_link,
                        )
                    )
                except Exception as e:
                    logging.error("Error creating CRMNote object for %s %s: %s", entity, activity.get("activityid"), e)
                    continue

            return notes

        except Exception as e:
            logging.error("Error fetching %s activities: %s", endpoint, e, exc_info=True)
            return []

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        logging.warning("fetch_events not implemented for TamaracMicrosoftDynamics")
        return {}

    def fetch_events(self, user: User, interval: timedelta) -> list[CalendarEvent]:
        logging.warning("fetch_events not implemented for TamaracMicrosoftDynamics")
        return []
