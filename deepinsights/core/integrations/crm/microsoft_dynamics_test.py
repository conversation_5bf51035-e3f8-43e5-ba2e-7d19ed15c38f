import datetime
from unittest.mock import ANY, MagicMock, call, patch

import pytest
from django.utils import timezone as django_timezone

from deepinsights.core.integrations.crm.microsoft_dynamics import MicrosoftDynamics
from deepinsights.core.preferences.preferences import CrmConfiguration, DynamicsConfiguration
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User

pytestmark = [pytest.mark.django_db]


@pytest.fixture
def test_organization() -> Organization:
    return Organization.objects.create(name="Test Organization")


@pytest.fixture
def test_user(test_organization: Organization) -> User:
    user = User.objects.create(email="<EMAIL>", organization=test_organization)

    dynamics_config = DynamicsConfiguration(dynamics_resource_url="https://org.example.com")

    crm_config = CrmConfiguration(crm_system="microsoft_dynamics", dynamics=dynamics_config)

    # Create a custom get_crm_configuration method
    def get_crm_configuration() -> CrmConfiguration:
        return crm_config

    # Attach it to the user
    user.get_crm_configuration = get_crm_configuration  # type: ignore[method-assign]

    return user


@pytest.fixture
def test_client(test_user: User, test_organization: Organization) -> Client:
    client = Client.objects.create(
        name="Test Client", organization=test_organization, crm_id="123", client_type="Contact"
    )
    client.authorized_users.add(test_user)
    client.save()
    return client


@pytest.fixture
def test_note(test_user: User, test_client: Client) -> Note:
    return Note.objects.create(note_owner=test_user, client={"uuid": str(test_client.uuid)}, summary="Test Note")


@pytest.fixture
def test_task(test_note: Note) -> Task:
    return Task.objects.create(note=test_note, task_title="Test Task", due_date=django_timezone.now())


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_accounts_by_owner_email_and_name(mock_oauth: MagicMock, test_user: User) -> None:
    # Setup mock OAuth
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    # Setup mock responses
    accounts_response = {
        "value": [
            {
                "name": "Test Account",
                "accountid": "acc123",
                "telephone1": "**********",
                "emailaddress1": "<EMAIL>",
            }
        ]
    }

    contacts_response = {
        "value": [
            {
                "fullname": "John Doe",
                "firstname": "John",
                "lastname": "Doe",
                "contactid": "con123",
                "telephone1": "**********",
                "emailaddress1": "<EMAIL>",
            }
        ]
    }

    leads_response = {
        "value": [
            {
                "fullname": "Jane Smith",
                "firstname": "Jane",
                "lastname": "Smith",
                "leadid": "lead123",
                "telephone1": "**********",
                "emailaddress1": "<EMAIL>",
                "companyname": "Prospect Corp",
                "subject": "New Lead",
            }
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: accounts_response),
            MagicMock(status_code=200, json=lambda: contacts_response),
            MagicMock(status_code=200, json=lambda: leads_response),
        ]

        dynamics = MicrosoftDynamics(user=test_user)
        accounts = dynamics.get_accounts_by_owner_email_and_name("<EMAIL>")

        assert len(accounts) == 3

        entity_types = [account.client_type for account in accounts]
        assert "Account" in entity_types
        assert "Contact" in entity_types
        assert "Lead" in entity_types

        assert mock_requests_get.call_count == 3
        mock_requests_get.assert_has_calls(
            [
                call(
                    "api/data/v9.2/accounts",
                    headers=ANY,
                    params={"$select": "name,accountid,telephone1,emailaddress1"},
                ),
                call(
                    "api/data/v9.2/contacts",
                    headers=ANY,
                    params={"$select": "fullname,firstname,lastname,contactid,telephone1,emailaddress1"},
                ),
                call(
                    "api/data/v9.2/leads",
                    headers=ANY,
                    params={
                        "$select": "fullname,firstname,lastname,leadid,telephone1,emailaddress1,subject,companyname"
                    },
                ),
            ]
        )


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_accounts_by_owner_email_and_name_with_filter(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.side_effect = [
            MagicMock(status_code=200, json=lambda: {"value": []}),
            MagicMock(status_code=200, json=lambda: {"value": []}),
        ]

        dynamics = MicrosoftDynamics(user=test_user)
        dynamics.get_accounts_by_owner_email_and_name("<EMAIL>", "Test")

        mock_requests_get.assert_has_calls(
            [
                call(
                    "api/data/v9.2/accounts",
                    headers=ANY,
                    params={"$select": "name,accountid,telephone1,emailaddress1", "$filter": "contains(name, 'Test')"},
                ),
                call(
                    "api/data/v9.2/contacts",
                    headers=ANY,
                    params={
                        "$select": "fullname,firstname,lastname,contactid,telephone1,emailaddress1",
                        "$filter": "contains(fullname, 'Test')",
                    },
                ),
            ]
        )


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_get_accounts_by_owner_email_and_name_error(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.return_value = MagicMock(status_code=500, text="Server error")

        dynamics = MicrosoftDynamics(user=test_user)
        accounts = dynamics.get_accounts_by_owner_email_and_name("<EMAIL>")

        assert accounts == []


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_preview_before_syncing_with_crm(mock_oauth: MagicMock, test_note: Note, test_task: Task) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    dynamics = MicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]

    with patch.object(Note, "get_summary_for_crm") as mock_summary:
        mock_summary.return_value = "Test summary"
        preview = dynamics.preview_before_syncing_with_crm(test_note)

    assert preview["note"]["content"] == "Test summary"
    assert len(preview["tasks"]) == 1
    assert preview["tasks"][0]["subject"] == "Test Task"
    assert preview["client"]["name"] == "Test Client"


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_with_client(mock_oauth: MagicMock, test_note: Note, test_task: Task) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.post") as mock_requests_post:
        note_response = MagicMock(status_code=204)
        note_response.headers = {"OData-EntityId": "https://org.example.com/api/data/v9.2/annotations(abc123)"}

        task_response = MagicMock(status_code=204)
        task_response.headers = {"OData-EntityId": "https://org.example.com/api/data/v9.2/tasks(def456)"}

        mock_requests_post.side_effect = [note_response, task_response]

        with patch.object(Note, "get_summary_for_crm") as mock_summary, patch.object(Note, "title") as mock_title:
            mock_summary.return_value = "Test summary"
            mock_title.return_value = "Test Title"

            dynamics = MicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
            dynamics.add_interaction_with_client(test_note)

        assert mock_requests_post.call_count == 2

        updated_note = Note.objects.get(pk=test_note.pk)
        updated_task = Task.objects.get(pk=test_task.pk)

        assert updated_note.metadata["interactionId"] == "abc123"  # type: ignore[index]
        assert "taskId" in updated_task.metadata  # type: ignore[operator]
        assert updated_task.metadata["taskId"] == "def456"  # type: ignore[index]


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_with_client_already_exists(mock_oauth: MagicMock, test_note: Note) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    test_note.metadata = {"interactionId": "existing123"}
    test_note.save()

    with patch("requests.post") as mock_requests_post:
        dynamics = MicrosoftDynamics(user=test_note.note_owner)  # type: ignore[arg-type]
        dynamics.add_interaction_with_client(test_note)

        mock_requests_post.assert_not_called()


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_add_interaction_with_client_no_clients(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    note = Note.objects.create(note_owner=test_user, summary="No client note")

    with patch("requests.post") as mock_requests_post:
        dynamics = MicrosoftDynamics(user=test_user)

        with pytest.raises(Exception) as exc_info:
            dynamics.add_interaction_with_client(note)

        assert "No primary client specified in the note" in str(exc_info.value)
        mock_requests_post.assert_not_called()


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_create_task_success(mock_oauth: MagicMock, test_user: User, test_task: Task) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.post") as mock_requests_post:
        mock_response = MagicMock(status_code=204)
        mock_response.headers = {"OData-EntityId": "https://org.example.com/api/data/v9.2/tasks(task123)"}
        mock_requests_post.return_value = mock_response

        dynamics = MicrosoftDynamics(user=test_user)
        api_url = "https://org.example.com/api/data/v9.2/"
        headers = dynamics._get_headers("mock_token")
        client = {"id": "123", "type": "contact"}

        dynamics._create_task(api_url, headers, test_task, client)

        updated_task = Task.objects.get(pk=test_task.pk)

        assert "taskId" in updated_task.metadata  # type: ignore[operator]
        assert updated_task.metadata["taskId"] == "task123"  # type: ignore[index]

        mock_requests_post.assert_called_once_with(
            "https://org.example.com/api/data/v9.2/tasks",
            headers=headers,
            json={
                "subject": "Test Task",
                "description": "",
                "scheduledend": ANY,
                "statecode": 0,
                "prioritycode": 1,
                "<EMAIL>": "/contacts(123)",
            },
        )


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_create_task_error(mock_oauth: MagicMock, test_user: User, test_task: Task) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.post") as mock_requests_post, patch("logging.error") as mock_error:
        mock_response = MagicMock(status_code=500, text="Server error")
        mock_requests_post.return_value = mock_response

        dynamics = MicrosoftDynamics(user=test_user)
        dynamics._create_task("https://example.com/api/", {}, test_task, {"id": "123", "type": "contact"})

        mock_error.assert_called_once()


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_events_not_implemented(mock_oauth: MagicMock, test_user: User) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("logging.warning") as mock_warning:
        dynamics = MicrosoftDynamics(user=test_user)
        events = dynamics.fetch_events(test_user, datetime.timedelta(days=30))

        assert events == []
        mock_warning.assert_called_once()


@pytest.mark.parametrize("completed,expected_state", [(True, 1), (False, 0)])
@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_create_task_state(
    mock_oauth: MagicMock, test_user: User, test_note: Note, completed: bool, expected_state: int
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    task = Task.objects.create(note=test_note, task_title="Test Task", completed=completed)

    with patch("requests.post") as mock_requests_post:
        mock_response = MagicMock(status_code=204)
        mock_response.headers = {"OData-EntityId": "https://org.example.com/api/data/v9.2/tasks(task123)"}
        mock_requests_post.return_value = mock_response

        dynamics = MicrosoftDynamics(user=test_user)
        api_url = "https://org.example.com/api/data/v9.2/"
        headers = dynamics._get_headers("mock_token")
        client = {"id": "123", "type": "contact"}

        dynamics._create_task(api_url, headers, task, client)

        mock_requests_post.assert_called_once()
        json_data = mock_requests_post.call_args[1]["json"]
        assert json_data["statecode"] == expected_state


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_success(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    notes_response = {
        "value": [
            {
                "annotationid": "note123",
                "subject": "Test Subject",
                "notetext": "Test note content",
                "createdon": "2023-01-01T12:00:00Z",
            },
            {
                "annotationid": "note456",
                "subject": "Another Subject",
                "notetext": "Another test note",
                "createdon": "2023-01-02T14:30:00Z",
            },
        ]
    }

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: notes_response)

        dynamics = MicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, datetime.timedelta(days=30))

        assert mock_requests_get.call_count == 1

        call_args = mock_requests_get.call_args[1]
        assert "_objectid_value eq" in call_args["params"]["$filter"]
        assert test_client.crm_id in call_args["params"]["$filter"]
        assert "createdon ge" in call_args["params"]["$filter"]

        assert len(notes) == 2
        assert notes[0].crm_id == "note123"
        assert notes[0].crm_system == "microsoft_dynamics"
        assert notes[0].content == "Test Subject\nTest note content"
        assert isinstance(notes[0].created_at, datetime.datetime)
        assert notes[1].crm_id == "note456"
        assert "entityrecord&etn=annotation&id=" in str(notes[0].web_link)


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_with_trailing_slash_url(
    mock_oauth: MagicMock, test_user: User, test_client: Client
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    notes_response = {
        "value": [
            {
                "annotationid": "note123",
                "subject": "Test Subject",
                "notetext": "Test note content",
                "createdon": "2023-01-01T12:00:00Z",
            }
        ]
    }

    def get_crm_configuration_with_trailing_slash() -> CrmConfiguration:
        dynamics_config = DynamicsConfiguration(dynamics_resource_url="https://org.example.com/")
        return CrmConfiguration(crm_system="microsoft_dynamics", dynamics=dynamics_config)

    original_get_crm_configuration = test_user.get_crm_configuration

    test_user.get_crm_configuration = get_crm_configuration_with_trailing_slash  # type: ignore[method-assign]

    try:
        with patch("requests.get") as mock_requests_get:
            mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: notes_response)

            dynamics = MicrosoftDynamics(user=test_user)
            notes = dynamics.fetch_notes_for_client(test_user, test_client, datetime.timedelta(days=30))

            assert len(notes) == 1

            web_link_str = str(notes[0].web_link)

            assert "https://org.example.com/main.aspx" in web_link_str
            assert "//main.aspx" not in web_link_str

            expected_url_pattern = (
                f"https://org.example.com/main.aspx?pagetype=entityrecord&etn=annotation&id={notes[0].crm_id}"
            )
            assert expected_url_pattern in web_link_str
    finally:
        # Restore the original method
        test_user.get_crm_configuration = original_get_crm_configuration  # type: ignore[method-assign]


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_no_crm_id(
    mock_oauth: MagicMock, test_user: User, test_organization: Organization
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    organization = test_user.organization
    assert organization is not None

    client = Client.objects.create(
        name="Test Client Without CRM ID", organization=test_organization, client_type="Contact"
    )
    client.authorized_users.add(test_user)

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        dynamics = MicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, client, datetime.timedelta(days=30))

        mock_requests_get.assert_not_called()

        assert notes == []
        mock_error.assert_called_once()
        assert "does not have a CRM ID" in mock_error.call_args[0][0]


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_api_error(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        mock_requests_get.return_value = MagicMock(
            status_code=401, text="Unauthorized", json=lambda: {"error": "Unauthorized"}
        )

        dynamics = MicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, datetime.timedelta(days=30))

        assert notes == []
        mock_error.assert_called_once()
        assert "Error fetching notes from Dynamics" in mock_error.call_args[0][0]


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_empty_result(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    with patch("requests.get") as mock_requests_get:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: {"value": []})

        dynamics = MicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, datetime.timedelta(days=30))

        assert notes == []
        mock_requests_get.assert_called_once()


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_parse_error(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    notes_response = {
        "value": [
            {
                "annotationid": "note123",
                "subject": "Test Subject",
                "notetext": "Test note content",
                "createdon": "Invalid date format",
            }
        ]
    }

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: notes_response)

        dynamics = MicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, datetime.timedelta(days=30))

        assert len(notes) == 1
        assert notes[0].crm_id == "note123"
        assert notes[0].created_at is None

        mock_error.assert_called_once()
        assert "Error parsing date" in mock_error.call_args[0][0]


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_oauth_error(mock_oauth: MagicMock, test_user: User, test_client: Client) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.side_effect = Exception("OAuth error")

    with patch("logging.error") as mock_error:
        dynamics = MicrosoftDynamics(user=test_user)
        notes = dynamics.fetch_notes_for_client(test_user, test_client, datetime.timedelta(days=30))

        # Verify empty result and error logging
        assert notes == []
        mock_error.assert_called_once()
        assert "Error fetching notes from Dynamics" in mock_error.call_args[0][0]


@patch("deepinsights.core.integrations.crm.microsoft_dynamics.MicrosoftDynamicsOAuth")
def test_fetch_notes_for_client_crmnote_creation_error(
    mock_oauth: MagicMock, test_user: User, test_client: Client
) -> None:
    mock_oauth_instance = mock_oauth.return_value
    mock_oauth_instance.get_access_token.return_value = "mock_access_token"

    notes_response = {
        "value": [
            {
                "annotationid": "note123",
                "subject": "Test Subject",
                "notetext": "Test note content",
                "createdon": "2023-01-01T12:00:00Z",
            },
            {
                "subject": "Missing ID",
                "notetext": "This note will cause an error",
                "createdon": "2023-01-02T12:00:00Z",
            },
            {
                "annotationid": "note456",
                "subject": "Valid Note",
                "notetext": "This note should be processed correctly",
                "createdon": "2023-01-03T12:00:00Z",
            },
        ]
    }

    with patch("requests.get") as mock_requests_get, patch("logging.error") as mock_error:
        mock_requests_get.return_value = MagicMock(status_code=200, json=lambda: notes_response)

        with patch("deepinsights.core.integrations.crm.microsoft_dynamics.CRMNote") as mock_crm_note:

            def side_effect(*args, **kwargs):  # type: ignore[no-untyped-def]
                if mock_crm_note.call_count == 2:  # Second call (0-indexed)
                    raise ValueError("Error creating CRMNote")
                return MagicMock()

            mock_crm_note.side_effect = side_effect

            dynamics = MicrosoftDynamics(user=test_user)
            notes = dynamics.fetch_notes_for_client(test_user, test_client, datetime.timedelta(days=30))

            assert len(notes) == 2

            assert mock_error.call_count >= 1

            error_message_found = False
            for call_args in mock_error.call_args_list:
                if "Error creating CRMNote object for note" in call_args[0][0]:
                    error_message_found = True
                    break

            assert error_message_found, "Error message about CRMNote creation not found in logs"
