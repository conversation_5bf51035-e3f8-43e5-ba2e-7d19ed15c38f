import datetime
import logging
from typing import Any

import pytz
import requests
from django.conf import settings
from django.db.models import QuerySet
from simple_salesforce.api import Salesforce

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm.crm_base import CrmBase
from deepinsights.core.integrations.crm.crm_models import CRMAccount, CRMNote
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.meeting_bot import MeetingBot
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.structured_meeting_data import StructuredMeetingDataTemplate
from deepinsights.meetingsapp.models.task import Task
from deepinsights.users.models.user import User


def extract_meeting_url_from_string(string: str) -> str | None:
    url_format = "https://seqfg.webex.com/seqfg/j.php?MTID="
    if url_format in string:
        print(string)
        return url_format + string.split(url_format)[1]
    return None


class SequoiaSalesforce(CrmBase):
    CASE_FIELDS = [
        "Id",
        "AccountId",
        "Account.Name",
        "Account.Service_Team_User_Ids__c",
        "Attendees__c",
        "CaseNumber",
        "Client_Review_Start_Time__c",
        "Subject",
        "Zeplyn_Id__c",
        "Client_Review_Location__c",
        "Meeting_Link__c",
        "IsClosed",
    ]
    CASE_SELECT = f"SELECT {', '.join(CASE_FIELDS)} FROM Case"

    def __init__(self, salesforce: Salesforce | None = None) -> None:
        if salesforce:
            self.sf = salesforce
            return
        oauth_url = settings.SEQUOIA_SALESFORCE_OAUTH_URL
        # Salesforce client credentials
        client_id = settings.SEQUOIA_SALESFORCE_CLIENT_ID
        client_secret = settings.SEQUOIA_SALESFORCE_CLIENT_SECRET
        # Define payload for authentication
        payload = {
            "grant_type": "client_credentials",
            "client_id": client_id,
            "client_secret": client_secret,
        }
        # Request OAuth token
        response = requests.post(oauth_url, data=payload)
        auth_data = response.json()
        # Check if authentication is successful
        if "access_token" in auth_data:
            access_token = auth_data["access_token"]

            # Authenticate with Salesforce using access token
            self.sf = Salesforce(instance_url=auth_data["instance_url"], session_id=access_token)
        else:
            logging.error("Authentication failed: %d, %s", response.status_code, auth_data)

    def _get_title_from_case(self, case: dict[str, Any]) -> str:
        return f"Meeting with {case['Account']['Name']} - {case['CaseNumber']}"

    def get_registed_users_from_user_ids(self, user_ids: list[str]) -> QuerySet[User]:
        user_ids_non_empty = [user_id for user_id in user_ids if user_id]
        quote_escaped_user_id_string = ",".join([f"'{user_id}'" for user_id in user_ids_non_empty])
        query = f"SELECT Id, Email from User WHERE Id IN ({quote_escaped_user_id_string})"
        results = self.sf.query_all(query)
        emails = []
        for record in results["records"]:
            emails.append(record["Email"])
        return User.objects.filter(email__in=emails)

    def _get_user_id_by_email(self, user_email: str) -> str | None:
        """
        Get the Salesforce User ID by email address.
        :param user_email: Email address of the Salesforce user
        :return: Salesforce User ID or None if not found
        """
        # Query for the user ID based on email
        query = f"SELECT Id FROM User WHERE Email = '{user_email}' LIMIT 1"
        result = self.sf.query_all(query)

        # Extract user ID if found
        if result["totalSize"] == 1:
            user_id = result["records"][0]["Id"]
            return user_id  # type: ignore[no-any-return]
        else:
            return None

    def get_sf_cases(self) -> dict[str, Any]:
        query = f"{self.CASE_SELECT} WHERE ((Client_Review_Start_Time__c <= NEXT_N_DAYS:2 OR Zeplyn_Id__c != null) AND Client_Review_Start_Time__c >= YESTERDAY)"
        results = self.sf.query_all(query)
        return results

    def _create_new_note(self, case: dict[str, Any]) -> Note | None:
        authorized_users = self.get_registed_users_from_user_ids(case["Account"]["Service_Team_User_Ids__c"].split(","))
        if authorized_users.count() == 0:
            logging.warning("No registered users found for case '%s', skipping", case["CaseNumber"])
            return None

        note_owner = authorized_users[0] if authorized_users else User.objects.get(email="<EMAIL>")
        meeting_name = self._get_title_from_case(case)

        # Ensure that the there is a client that matches this case.
        client, _ = Client.objects.update_or_create(
            crm_id=case["Id"],
            organization=note_owner.organization,
            defaults={
                "client_type": "case",
                "crm_system": "sequoia_sf",
                "name": meeting_name,
            },
        )
        client.authorized_users.add(*authorized_users)
        client.save()

        metadata = {}
        metadata["meeting_name"] = meeting_name
        metadata["scheduled_at"] = self._parse_isolike_time(case["Client_Review_Start_Time__c"]).isoformat()
        new_note = Note.objects.create(
            note_owner=note_owner,
            metadata=metadata,
            status=Note.PROCESSING_STATUS.scheduled,
            note_type=Note.NOTE_TYPE.meeting_recording,
            client={
                "uuid": str(client.uuid),
                "name": meeting_name,
            },
            meeting_type=MeetingType.objects.get(key="client"),
        )
        new_note.save()
        new_note.authorized_users.add(*authorized_users)
        new_note.save()

        if case["Attendees__c"]:
            attendees = Attendee.parse_attendees(case["Attendees__c"])  # type: ignore[no-untyped-call]
            Attendee.reconcile_attendees(note=new_note, attendees=attendees)

        return new_note

    def _create_bot(self, case, note):  # type: ignore[no-untyped-def]
        if case["Meeting_Link__c"] is not None:
            bot = MeetingBot.objects.create(
                note=note,
                bot_owner=note.note_owner,
                meeting_link=extract_meeting_url_from_string(case["Meeting_Link__c"]),
            )
            bot.save()
        else:
            logging.info("Note with Zeplyn Id '%s' not getting new bot", note.uuid)

    def update_existing_note(self, note: Note, case):  # type: ignore[no-untyped-def]
        logging.info(
            "Note with Salesforce id %s already exists with uuid %s, checking for new details",
            case["Id"],
            note.uuid,
        )
        # If the case is closed and there is no Zeplyn data associated with the
        # note, then delete it.
        if case["IsClosed"] and note.status == "scheduled":
            logging.info("Removing empty Zeplyn Note for closed Salesforce case %s", case["Id"])
            note.is_deleted = True
            note.save()
            return
        if note.metadata["scheduled_at"] != self._parse_isolike_time(case["Client_Review_Start_Time__c"]).isoformat():  # type: ignore[index]
            logging.info("Note '%s' has been rescheduled, updating details", note.uuid)
            note.metadata["scheduled_at"] = self._parse_isolike_time(case["Client_Review_Start_Time__c"]).isoformat()  # type: ignore[index]
            note.save()
        else:
            logging.info("Note with Zeplyn Id '%s' already has the right start time, skipping", note.uuid)
        if case["Meeting_Link__c"] is not None:
            new_link = extract_meeting_url_from_string(case["Meeting_Link__c"])
            if note.bot_uuid:
                logging.info("Note with Zeplyn Id '%s' already has a bot, checking start times", note.uuid)
                bot = MeetingBot.objects.get(uuid=note.bot_uuid)
                if bot.meeting_link != new_link:
                    logging.info("Note with Zeplyn Id '%s' has new meeting link, updating bot", note.uuid)
                    bot.meeting_link = new_link
                    bot.save()
                else:
                    logging.info(
                        "Note with Zeplyn Id '%s' already has the right meeting url, skipping",
                        note.uuid,
                    )
            else:
                bot = MeetingBot.objects.create(
                    note=note,
                    bot_owner=note.note_owner,
                    meeting_link=extract_meeting_url_from_string(case["Meeting_Link__c"]),
                )
                bot.save()
        else:
            logging.info("Note with Zeplyn Id '%s' not getting new bot", note.uuid)

    def generate_notes_from_cases(self, cases: list[dict[str, Any]]) -> list[Note]:
        notes: list[Note] = []
        for case in cases:
            try:
                client = Client.objects.get(crm_id=case["Id"])
            except Client.DoesNotExist:
                client = None
            if client and Note.objects.filter(client__uuid=str(client.uuid)).exists():
                for n in Note.objects.filter(client__uuid=str(client.uuid), status=Note.PROCESSING_STATUS.scheduled):
                    self.update_existing_note(n, case)
                continue
            if Note.objects.filter(salesforce_case_id=case["Id"]).exists():
                existing_note = Note.objects.get(salesforce_case_id=case["Id"])
                self.update_existing_note(existing_note, case)
                continue
            if case["IsClosed"]:
                continue
            try:
                note = self._create_new_note(case)
            except Exception as e:
                logging.error("Failed to create Case with for Salesforce case ID '%s'", case["Id"], exc_info=e)
                continue
            if not note:
                continue
            notes.append(note)
            self._create_bot(case, note)  # type: ignore[no-untyped-call]
            try:
                self.sf.Case.update(case["Id"], {"Zeplyn_Id__c": str(note.uuid)})  # type: ignore[operator]
            except Exception:
                logging.error(
                    "Failed to update Case with Zeplyn Id '%s', sf case id '%s'", note.uuid, case["Id"], exc_info=True
                )
        return notes

    # Given a time string in the ISO8601 format, or a similar format with the "T" separator,
    # extract a datetime.
    def _parse_isolike_time(self, time_str: str) -> datetime.datetime:
        formats = [
            "%Y-%m-%dT%H:%M:%S.%f%z",  # With fractional seconds
            "%Y-%m-%d %H:%M:%S.%f%z",  # Space separated with fractional seconds
            "%Y-%m-%dT%H:%M:%S%z",  # Without fractional seconds
            "%Y-%m-%d %H:%M:%S%z",  # Space separated without fractional seconds
        ]

        for fmt in formats:
            try:
                return datetime.datetime.strptime(time_str, fmt)
            except ValueError:
                continue
        raise ValueError(f"time data '{time_str}' does not match any expected formats")

    def get_accounts_by_owner_email_and_name(
        self, owner_email: str, account_name_filter: str | None = None
    ) -> list[CRMAccount]:
        sf_user_id = self._get_user_id_by_email(owner_email)
        if not sf_user_id:
            logging.warn("No SF user found with email '%s'", owner_email)
            sf_user_id = "0054P00000Bv3ZzQAJ"
        # Build the query based on the provided parameters
        query = f"{self.CASE_SELECT} WHERE Account.Service_Team_User_Ids__c like '%{sf_user_id}%'"
        logging.info(query)
        # Execute the query using the global Salesforce connection
        result = self.sf.query_all(query)

        # Extracting data from the result
        cases = result["records"]

        # Create a list to store dictionaries with account data
        accounts_list = []

        # Populate the list with dictionaries containing account ID, name, owner ID, and full name
        for case in cases:
            if case["IsClosed"]:
                continue
            account = CRMAccount(
                crm_id=case["Id"],
                name=self._get_title_from_case(case),
                client_type="case",
                crm_system="sequoia_sf",
            )
            accounts_list.append(account)

        return accounts_list

    # Given a note, return the case ID that it corresponds to.
    #
    # This method is complex, to handle older patterns of how cases were associated with Zeplyn
    # notes. Originally, Sequoia cases were associated with notes via `salesforce_case_id`; later,
    # we added support for other CRMs syncing via `note.client`, but did not fully migrate the Sequoia
    # integration to use this until much later.
    def _case_id_for_note(self, note: Note) -> str | None:
        if client_uuid_or_crm_id := (note.client or {}).get("uuid"):
            try:
                return Client.objects.get(uuid=client_uuid_or_crm_id).crm_id
            except Exception:
                # There is no client that matches the UUID. The UUID could be a Salesforce case ID,
                # but we know that Salesforce Case ID is a case ID, so return that if it's present.
                if salesforce_case_id := note.salesforce_case_id:
                    return salesforce_case_id

                # In the absence of a Salesforce Case ID, assume the client UUID is the Salesforce
                # CRM ID, if it's not a valid client ID. The exception caught above is generic
                # because, if client_uuid_or_crm_id is not a valid UUID, the Client.objects.get()
                # call will raise a different exception than "Client.DoesNotExist".
                return client_uuid_or_crm_id  # type: ignore[no-any-return]

        # If there is no client UUID, then the note is not associated with a client, so use the
        # (deprecated) salesforce_case_id.
        return note.salesforce_case_id

    def preview_before_syncing_with_crm(self, note: Note) -> dict[str, Any]:
        try:

            def case_to_create(task: Task):  # type: ignore[no-untyped-def]
                return task.task_title or "No title"

            data = {
                "Zeplyn Meeting Tags": ";".join(note.metadata.get("tags", [])),  # type: ignore[union-attr]
                "Attendee Speaker Allocations": ", ".join(note.get_attendees()),
                "Cases To Create": "<br>\n".join([case_to_create(t) for t in note.task_set.all()]),
                "Meeting Summary": note.get_summary_for_crm(use_html_formatting=True),
                "Meeting Duration": int(note.metadata.get("meeting_duration", 0)) / 3600,  # type: ignore[union-attr]
            }
            self._update_case_with_compliance_fields(note, data)

            if not (case_id := self._case_id_for_note(note)):
                logging.error("Note has neither case ID nor valid client UUID. Not updating in Salesforce")
                return {}

            data["Case ID"] = case_id
            return data

        except Exception as e:
            logging.error(
                f"Error previewing interaction with client {note.note_owner.uuid} for note {note.uuid}: {e}",  # type: ignore[union-attr]
                exc_info=True,
            )
            raise

    # Adds information from a compliance template (if it exists) to the case data.
    def _update_case_with_compliance_fields(self, note: Note, case_data: dict[str, Any]) -> None:
        if not (
            compliance_template := note.structuredmeetingdata_set.filter(
                kind=StructuredMeetingDataTemplate.Kind.SEQUOIA_COMPLIANCE_CHECKLIST
            ).first()
        ):
            logging.info("Note does not have a compliance template. Skipping compliance fields.")
            return

        metadata = note.metadata or {}
        meeting_timestamp = metadata.get("scheduled_at") or note.created.isoformat()

        # TODO: This should select the timezone based on the caller's timezone.
        meeting_date = (
            self._parse_isolike_time(meeting_timestamp).astimezone(pytz.timezone("US/Eastern")).strftime("%Y-%m-%d")
        )

        topic_id_to_case_field = {
            "financial_status": "Zep_Current_Fin_Status_Discussed_Date__c",
            "allocations_holdings": "Zep_Asset_Alloc_Holdings_Discussed_Date__c",
            "investment_performance": "Zep_Inv_Performance_Discussed_Date__c",
            "insurance_needs": "Zep_Insurance_Planning_Discussed_Date__c",
        }

        if not (entries := compliance_template.data.get("review_entries")):
            logging.error("Compliance template has invalid format. Skipping compliance fields.")
            return

        for entry in entries:
            if not (topic_id := entry.get("id")):
                logging.error("Compliance template entry has no topic ID. Skipping compliance field.")
                continue
            if not (field_name := topic_id_to_case_field.get(topic_id)):
                logging.info(
                    "Compliance template entry has no mapping for topic ID '%s'. Note that not all fields have mappings.",
                    topic_id,
                )
                continue
            discussed = entry.get("discussed")
            if discussed is None:
                logging.error(
                    "Compliance template entry for topic ID '%s' has no 'discussed' field or it is None. Skipping compliance field.",
                    topic_id,
                )
                continue
            if not discussed:
                continue
            case_data[field_name] = meeting_date

    def add_interaction_with_client(self, note: Note) -> None:
        """
        This is the mapping between note and case:
        Zeplyn Field	Case Field Label	Case Field API Name	Data Type (Data Format)
        Meeting Date/Time	Meeting Start Date/Time	Client_Review_Start_Time__c	Date/Time
        Key words Tagged	Zeplyn Meeting Tags	Zeplyn_Meeting_Tags__c	Multi-Select Picklist (Semicolon seperated)
        Meeting Attendees with % of time speaking	Attendee Speaker Allocations	Attendee_Speaker_Allocations__c	Text (255 character limit)
        Action Items	Cases to Create	Cases_To_Create__c	Rich Text (32768 character limit)
        Key Takeaways	Meeting Summary	Meeting_Summary__c	Rich Text (32768 character limit)
        Advisor Notes	Meeting Summary	Meeting_Summary__c	Rich Text (32768 character limit)
        Meeting Duration	Meeting Duration	Meeting_Duration__c	Number (1 decimal)
        Meeting Summary	Meeting Summary	Meeting_Summary__c	Rich Text (32768 character limit)
        [Unique Id]	Zeplyn Id	Zeplyn_Id__c	Text (50 character limit; case-sensitive)

        This also maps fields between the compliance follow-up template and the case, setting dates
        on fields in the case if the template indicates that the related topic was discussed.

        """

        def case_to_create(task: Task):  # type: ignore[no-untyped-def]
            return task.task_title or "No title"

        case_data = {
            "Zeplyn_Meeting_Tags__c": ";".join(note.metadata.get("tags", [])),  # type: ignore[union-attr]
            "Attendee_Speaker_Allocations_2__c": ", ".join(note.get_attendees()),
            "Cases_To_Create__c": "<br>\n".join([case_to_create(t) for t in note.task_set.all()]),
            "Meeting_Summary__c": note.get_summary_for_crm(use_html_formatting=True),
            "Meeting_Duration__c": int(note.metadata.get("meeting_duration", 0)) / 3600,  # type: ignore[union-attr]
        }
        self._update_case_with_compliance_fields(note, case_data)
        if not (case_id := self._case_id_for_note(note)):
            logging.error(
                "Note has neither case ID nor valid client UUID nor valid client case ID in CRM ID. Not updating in Salesforce"
            )
            return
        logging.info("Updating case '%s' with data: %s", case_id, case_data)
        self.sf.Case.update(case_id, case_data)  # type: ignore[operator]

    def get_client_basic_info(
        self, client: Client, user: User, include_household: bool = False
    ) -> dict[str, Any] | None:
        logging.warning("get_client_basic_info not implemented for Seqouia Salesforce")
        return None

    def fetch_events(self, user: User, interval: datetime.timedelta) -> list[CalendarEvent]:
        logging.warning("fetch_events not implemented for Sequoia Salesforce")
        return []

    def fetch_notes_for_client(self, user: User, client: Client, interval: datetime.timedelta) -> list[CRMNote]:
        logging.warning("fetch_notes_for_client not implemented for Sequoia Salesforce")
        return []
