import re
from typing import TypedDict
from urllib.parse import urlparse


def _is_valid_url(url: str) -> bool:
    """ "Whether or not the provided string is a valid URL."""
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


# Meeting domains that we are interested in.
_meeting_domains = [
    "meet.google.com",
    "teams.live.com",
    "teams.microsoft.com",
    "webex.com",
    "zoom.us",
]

# The match group that attempts to find the beginning of the URL: a non-whitespace character that is
# not a quote or a bracketing character. The expectation is that the URL is a contiguous blob of
# non-whitespaced text in a raw text input, that the URL is contained between double quotes in an
# HTML anchor tag, or that the URL is contained in angle brackets (which seems to be common for
# Outlook meetings with Teams URLs).
_opening_match_group = r"[^\s\"\'\<\>]"

# The match group that attempts to find the end of the URL: a non-whitespace character that is not a
# quote or an angle bracket character.
_closing_match_group = r"[^\s\"\'\>\<]"

# The regular expression that attempts to match URLs that look like they might be video meeting
# URLs.
_regex = (
    # Match characters before the meeting domain. See the comment above _opening_match_group for
    # an explanation of the openning match group.
    rf"({_opening_match_group}+|^{_opening_match_group}*)"
    # Match a meeting domain URL.
    rf"({'|'.join(_meeting_domains)})"
    # Match characters that indicate the end of the URL. See the comment above _closing_match_group
    # for more information.
    rf"({_closing_match_group}+|{_closing_match_group}*$)"
)


def find_urls(text: str) -> list[str]:
    """
    Given a text, find all URLs in the text that look like they might be video meeting URLs.

    URLs will be returned in the order found in the text.
    """

    urls: list[str] = []
    for match in re.finditer(_regex, text):
        url = match.group(0)
        if _is_valid_url(url):
            urls.append(url)
    return urls


class CRMLinkedEntityInfo(TypedDict):
    id: str
    name: str


def extract_crm_linked_entity_info(text: str) -> CRMLinkedEntityInfo | None:
    """
    Extracts information from the text that references an entity in a CRM system.
    """
    salesforce_match = re.search(r"\[ZDATA\]<salesforce_case_id>([^<]*)</salesforce_case_id>\[/ZDATA\]", text)
    if salesforce_match:
        # TODO: get the real Salesforce case name.
        return CRMLinkedEntityInfo(id=salesforce_match.group(1), name="")
    return None
