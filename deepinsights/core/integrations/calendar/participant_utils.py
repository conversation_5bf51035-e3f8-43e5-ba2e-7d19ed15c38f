from typing import Any, Protocol, Self, Sequence, TypeVar

from deepinsights.core.integrations.calendar.calendar_models import EventParticipant
from deepinsights.meetingsapp.models.client import Client
from deepinsights.users.models.user import User


class _HasParticipants(Protocol):
    participants: list[EventParticipant]

    # This is the model_copy method implemented on BaseModel.
    def model_copy(self, *, update: dict[str, Any] | None = None, deep: bool = False) -> Self:
        ...


T = TypeVar("T", bound=_HasParticipants)


# Attemps to map attendees in the provided calendar events to Zeplyn clients and users.
#
# Returns a list with the same events as the input, but with attendees mapped to Zeplyn clients and
# users (when possible). The base case for this (i.e., no users and clients) is to return a
# list equivalent to the input.
def events_with_zeplyn_attendees(events: Sequence[T], user: User) -> list[T]:
    events_with_zeplyn_attendees: list[T] = []
    for event in events:
        zeplyn_event = event.model_copy(deep=True)
        for participant in zeplyn_event.participants:
            if user_for_participant := User.objects.filter(
                organization=user.organization, email__iexact=participant.email_address
            ).first():
                if user_name := user_for_participant.name:
                    participant.name = user_name
                participant.zeplyn_uuid = user_for_participant.uuid
                participant.zeplyn_kind = EventParticipant.ZeplynKind.USER
            elif client := Client.objects.filter(
                authorized_users__isnull=False,
                authorized_users=user,
                email__iexact=participant.email_address,
                crm_system=user.get_crm_configuration().crm_system,
            ).first():
                if client_name := client.name:
                    participant.name = client_name
                participant.zeplyn_uuid = client.uuid
                participant.zeplyn_kind = EventParticipant.ZeplynKind.CLIENT
        events_with_zeplyn_attendees.append(zeplyn_event)
    return events_with_zeplyn_attendees
