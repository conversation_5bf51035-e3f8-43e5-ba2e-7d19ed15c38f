from unittest.mock import MagicMock, patch

import pytest

from deepinsights.core.integrations.calendar.auto_join import (
    update_recall_auto_join_integration,
)
from deepinsights.users.models.user import User

pytestmark = [pytest.mark.django_db]


@pytest.fixture
def user_without_recall_calendar_id(django_user_model: User) -> User:
    return django_user_model.objects.create(email="<EMAIL>")


@pytest.fixture
def user_with_recall_calendar_info(user_without_recall_calendar_id: User) -> User:
    user_without_recall_calendar_id.recall_calendar_id = "test_calendar_id"
    user_without_recall_calendar_id.recall_calendar_platform = "google"
    user_without_recall_calendar_id.save()
    return user_without_recall_calendar_id


@patch("deepinsights.core.integrations.calendar.auto_join.RecallBotController")
def test_update_recall_auto_join_integration_disable_no_changes(
    mock_recall_bot_controller: MagicMock, user_without_recall_calendar_id: User
) -> None:
    user = user_without_recall_calendar_id
    oauth_mock = MagicMock()
    calendar_link_function_mock = MagicMock()

    update_recall_auto_join_integration(user, False, oauth_mock, calendar_link_function_mock)

    user.refresh_from_db()
    oauth_mock.assert_not_called()
    mock_recall_bot_controller.return_value.unlink_calendar.assert_not_called()
    mock_recall_bot_controller.return_value.link_calendar.assert_not_called()
    mock_recall_bot_controller.connected_calendar_platform.assert_not_called()
    calendar_link_function_mock.assert_not_called()
    assert user.recall_calendar_id is None
    assert user.recall_calendar_platform is None


@patch("deepinsights.core.integrations.calendar.auto_join.RecallBotController")
def test_update_recall_auto_join_integration_disable(
    mock_recall_bot_controller: MagicMock, user_with_recall_calendar_info: User
) -> None:
    user = user_with_recall_calendar_info
    mock_recall_bot_controller.return_value.unlink_calendar.return_value = True

    oauth_mock = MagicMock()
    calendar_link_function_mock = MagicMock()

    update_recall_auto_join_integration(user, False, oauth_mock, calendar_link_function_mock)

    user.refresh_from_db()
    oauth_mock.assert_not_called()
    mock_recall_bot_controller.return_value.unlink_calendar.assert_called_once_with("test_calendar_id")
    mock_recall_bot_controller.return_value.link_calendar.assert_not_called()
    calendar_link_function_mock.assert_not_called()
    mock_recall_bot_controller.connected_calendar_platform.assert_not_called()
    assert user.recall_calendar_id is None
    assert user.recall_calendar_platform is None


@patch("deepinsights.core.integrations.calendar.auto_join.RecallBotController")
def test_update_recall_auto_join_integration_disable_unlink_fails(
    mock_recall_bot_controller: MagicMock, user_with_recall_calendar_info: User
) -> None:
    user = user_with_recall_calendar_info
    mock_recall_bot_controller.return_value.unlink_calendar.return_value = False
    oauth_mock = MagicMock()
    calendar_link_function_mock = MagicMock()

    with pytest.raises(Exception, match="Failed to unlink calendar from Recall"):
        update_recall_auto_join_integration(user, False, oauth_mock, calendar_link_function_mock)

    user.refresh_from_db()
    mock_recall_bot_controller.return_value.unlink_calendar.assert_called_once_with("test_calendar_id")
    oauth_mock.assert_not_called()
    mock_recall_bot_controller.connected_calendar_platform.assert_not_called()
    calendar_link_function_mock.assert_not_called()
    assert user.recall_calendar_id == "test_calendar_id"
    assert user.recall_calendar_platform == "google"


@patch("deepinsights.core.integrations.calendar.auto_join.RecallBotController")
def test_update_recall_auto_join_integration_enable_already_linked(
    mock_recall_bot_controller: MagicMock, user_with_recall_calendar_info: User
) -> None:
    user = user_with_recall_calendar_info
    oauth_mock = MagicMock()
    calendar_link_function_mock = MagicMock()

    update_recall_auto_join_integration(user, True, oauth_mock, calendar_link_function_mock)

    user.refresh_from_db()
    mock_recall_bot_controller.return_value.unlink_calendar.assert_not_called()
    oauth_mock.assert_not_called()
    calendar_link_function_mock.assert_not_called()
    mock_recall_bot_controller.connected_calendar_platform.assert_not_called()
    assert user.recall_calendar_id == "test_calendar_id"
    assert user.recall_calendar_platform == "google"


@patch("deepinsights.core.integrations.calendar.auto_join.RecallBotController")
def test_update_recall_auto_join_integration_enable_no_refresh_token(
    mock_recall_bot_controller: MagicMock, user_without_recall_calendar_id: User
) -> None:
    user = user_without_recall_calendar_id
    oauth_mock = MagicMock()
    oauth_mock.get_refresh_token.return_value = None
    calendar_link_function_mock = MagicMock()

    with pytest.raises(Exception, match="No refresh token found for user"):
        update_recall_auto_join_integration(user, True, oauth_mock, calendar_link_function_mock)

    user.refresh_from_db()
    calendar_link_function_mock.assert_not_called()
    mock_recall_bot_controller.connected_calendar_platform.assert_not_called()
    assert not user.recall_calendar_id
    assert not user.recall_calendar_platform


def test_update_recall_auto_join_integration_enable_fails(user_without_recall_calendar_id: User) -> None:
    user = user_without_recall_calendar_id
    oauth_mock = MagicMock()
    oauth_mock.get_refresh_token.return_value = "test_refresh_token"
    oauth_mock.client_id.return_value = "test_client_id"
    oauth_mock.client_secret.return_value = "test_client_secret"

    with pytest.raises(Exception, match="Failed to link calendar to Recall"):
        update_recall_auto_join_integration(user, True, oauth_mock, lambda *_: (False, ""))

    user.refresh_from_db()
    assert user.recall_calendar_id is None
    assert user.recall_calendar_platform is None


def test_update_recall_auto_join_integration_enable_raises(user_without_recall_calendar_id: User) -> None:
    user = user_without_recall_calendar_id
    oauth_mock = MagicMock()
    oauth_mock.get_refresh_token.return_value = "test_refresh_token"
    oauth_mock.client_id.return_value = "test_client_id"
    oauth_mock.client_secret.return_value = "test_client_secret"
    calendar_link_function_mock = MagicMock()
    calendar_link_function_mock.side_effect = Exception("Test exception")

    with pytest.raises(Exception, match="Test exception"):
        update_recall_auto_join_integration(user, True, oauth_mock, calendar_link_function_mock)

    user.refresh_from_db()
    assert user.recall_calendar_id is None
    assert user.recall_calendar_platform is None


@patch("deepinsights.core.integrations.calendar.auto_join.RecallBotController")
def test_update_recall_auto_join_integration_enable(
    mock_recall_bot_controller: MagicMock, user_without_recall_calendar_id: User
) -> None:
    user = user_without_recall_calendar_id
    mock_recall_bot_controller.connected_calendar_platform.return_value = "google"

    oauth_mock = MagicMock()
    oauth_mock.get_refresh_token.return_value = "test_refresh_token"
    oauth_mock.client_id.return_value = "test_client_id"
    oauth_mock.client_secret.return_value = "test_client_secret"

    update_recall_auto_join_integration(user, True, oauth_mock, lambda *_: (True, "new_calendar_id"))

    user.refresh_from_db()
    assert user.recall_calendar_id == "new_calendar_id"
    assert user.recall_calendar_platform == "google"
