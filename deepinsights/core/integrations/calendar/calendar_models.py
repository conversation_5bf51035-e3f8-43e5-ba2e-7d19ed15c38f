from datetime import datetime
from enum import Enum

import pydantic


# A participant in a calendar event
class EventParticipant(pydantic.BaseModel):
    class ZeplynKind(Enum):
        USER = "user"
        CLIENT = "client"

    # An identifier for the participant, generated by the calendar service. These will not be
    # comparable across calendar services. Not all calendar services will provide this.
    id: str | None = None

    # An identifier for this participant in Zeplyn. This is used to match the participant to a
    # Zeplyn user or client.
    zeplyn_uuid: pydantic.UUID4 | None = None

    # If zeplyn_uuid is not empty, this field will indicate whether the zeplyn_uuid is for a
    # Zeplyn user or client.
    zeplyn_kind: ZeplynKind | None = None

    # The name of the user, as provided by the calendar service.
    name: str | None = None

    # The email address, as it is on the calendar event (i.e., not normalized for casing).
    email_address: str


# A calendar event
class CalendarEvent(pydantic.BaseModel):
    # A string that indicates what calendar service this event is from (e.g., "google", "microsoft")
    provider: str

    # A unique identifier for the event, generated by the calendar service. This will not be
    # comparable across different calendar services.
    id: str

    # An identifier for this event, specific to the calendar being used. This may be the same as the
    # ID, but it will not necessarily be if the calendar provider uses different user-specific
    # identifiers for shared calendar events.
    user_specific_id: str

    # The title/name/brief summary of the event.
    title: str

    # The body/description of the event (if any).
    #
    # If the original body/description was an HTML string, this may contain escaped HTML.
    body: str | None

    # Start time
    #
    # If this is an all-day event (i.e., all_day is True), this will be set to midnight UTC of the
    # start date of the event. If this is not an all-day event (i.e. all_day is False), even if the
    # start and end time are separated by one day, then this is an event that starts and ends at the
    # exact timezone-aware times specified.
    start_time: datetime

    # End time
    #
    # If this is an all-day event (i.e., all_day is True), this will be set to midnight UTC of the
    # day after the event ends (i.e., the event ends at 11:59:59... the date before). If this is not
    # an all-day event (i.e. all_day is False), even if the start and end time are separated by one
    # day, then this is an event that starts and ends at the exact timezone-aware times specified.
    end_time: datetime

    # Whether this is an all-day event.
    all_day: bool

    # The participants in the event
    participants: list[EventParticipant]

    # URLs that can be used to join the meeting.
    meeting_urls: list[pydantic.OnErrorOmit[pydantic.HttpUrl]]
