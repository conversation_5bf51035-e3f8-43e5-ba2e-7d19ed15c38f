import pytest

from deepinsights.core.integrations.calendar.calendar_data_parser import extract_crm_linked_entity_info, find_urls


def test_find_urls_with_valid_meeting_urls() -> None:
    text = """
    Here are some meeting links:
    https://meet.google.com/abc-defg-hij
    https://teams.microsoft.com/l/meetup-join/19%3ameeting_NmE2YzY1YzgtYzY2Zi00YzY2LWE2YzYtYzY2YzY2YzY2YzY2%40thread.v2/0?context=%7b%22Tid%22%3a%22abc%22%2c%22Oid%22%3a%22def%22%7d
    https://zoom.us/j/1234567890
    https://foo.zoom.us/j/123-4567890
    https://foo.webex.com/abc-defg-hij
    """
    expected_urls = [
        "https://meet.google.com/abc-defg-hij",
        "https://teams.microsoft.com/l/meetup-join/19%3ameeting_NmE2YzY1YzgtYzY2Zi00YzY2LWE2YzYtYzY2YzY2YzY2YzY2%40thread.v2/0?context=%7b%22Tid%22%3a%22abc%22%2c%22Oid%22%3a%22def%22%7d",
        "https://zoom.us/j/1234567890",
        "https://foo.zoom.us/j/123-4567890",
        "https://foo.webex.com/abc-defg-hij",
    ]
    assert find_urls(text) == expected_urls


def test_handling_of_whitespace() -> None:
    text = "\thttps://meet.google.com/abc-defg-hij\r\nhttps://teams.microsoft.com/test https://zoom.us/foo\r"
    expected_urls = [
        "https://meet.google.com/abc-defg-hij",
        "https://teams.microsoft.com/test",
        "https://zoom.us/foo",
    ]
    assert find_urls(text) == expected_urls


def test_find_urls_with_no_meeting_urls() -> None:
    text = """
    Here are some random links:
    https://example.com
    https://anotherexample.com
    """
    assert find_urls(text) == []


def test_find_urls_with_mixed_content() -> None:
    text = """
    Here are some links:
    https://example.com
    https://meet.google.com/abc-defg-hij
    Some text in between
    https://zoom.us/j/1234567890
    """
    expected_urls = ["https://meet.google.com/abc-defg-hij", "https://zoom.us/j/1234567890"]
    assert find_urls(text) == expected_urls


def test_find_urls_with_invalid_urls() -> None:
    text = """
    Here are some invalid meeting links:
    meet.google.com/abc-defg-hij
    teams.microsoft.com/l/meetup-join/19%3ameeting_NmE2YzY1YzgtYzY2Zi00YzY2LWE2YzYtYzY2YzY2YzY2YzY2%40thread.v2/0?context=%7b%22Tid%22%3a%22abc%22%2c%22Oid%22%3a%22def%22%7d
    zoom.us/j/1234567890
    """
    assert find_urls(text) == []


def test_find_urls_without_meeting_info() -> None:
    text = """
    Edge cases:
    https://meet.google.com
    https://zoom.us
    """
    assert find_urls(text) == []


def test_html() -> None:
    text = """
    <table><tbody><tr><td><br>
    <a href="https://foo.zoom.us/j/123-4567890" target="_blank">URL</a>
    <a href="https://meet.google.com/test/meeting" target="_blank">URL</a>
    <a href='https://test.webex.com/test/meeting' target="_blank">URL</a>
    >https://meet.google.com/test/meeting<https://meet.google.com/test/meeting2<
    <https://meet.google.com/test/meeting3>
    <https://teams.microsoft.com/l/meetup-join/19%3ameeting_ZGI2NzY2MWQtM2E0Zi00MzU1LTliN2EtZGVhMTYzYjMzNzlh%40thread.v2/0?context=%7b%22Tid%22%3a%2209fd564e-bf42-4321-8f2d-b8e482f8635c%22%2c%22Oid%22%3a%22e8d89c93-338e-4191-a6eb-daae2bce18b6%22%7d>
    </td></tr></tbody></table>
    """
    assert find_urls(text) == [
        "https://foo.zoom.us/j/123-4567890",
        "https://meet.google.com/test/meeting",
        "https://test.webex.com/test/meeting",
        "https://meet.google.com/test/meeting",
        "https://meet.google.com/test/meeting2",
        "https://meet.google.com/test/meeting3",
        "https://teams.microsoft.com/l/meetup-join/19%3ameeting_ZGI2NzY2MWQtM2E0Zi00MzU1LTliN2EtZGVhMTYzYjMzNzlh%40thread.v2/0?context=%7b%22Tid%22%3a%2209fd564e-bf42-4321-8f2d-b8e482f8635c%22%2c%22Oid%22%3a%22e8d89c93-338e-4191-a6eb-daae2bce18b6%22%7d",
    ]


@pytest.mark.parametrize(
    "input_text,expected_result",
    [
        # Valid Salesforce case ID
        (
            "This text contains a [ZDATA]<salesforce_case_id>SF12345</salesforce_case_id>[/ZDATA] reference",
            {"id": "SF12345", "name": ""},
        ),
        # Valid Salesforce case ID with surrounding text
        (
            "Before [ZDATA]<salesforce_case_id>SF67890</salesforce_case_id>[/ZDATA] After",
            {"id": "SF67890", "name": ""},
        ),
        # Valid Salesforce case ID with special characters
        (
            "[ZDATA]<salesforce_case_id>SF-123_456.789</salesforce_case_id>[/ZDATA]",
            {"id": "SF-123_456.789", "name": ""},
        ),
        # No Salesforce case ID
        (
            "This text contains no CRM reference",
            None,
        ),
        # Invalid format - missing opening tag
        (
            "[ZDATA]salesforce_case_id>ABC123</salesforce_case_id>[/ZDATA]",
            None,
        ),
        # Invalid format - missing closing tag
        (
            "[ZDATA]<salesforce_case_id>ABC123[/ZDATA]",
            None,
        ),
        # Empty text
        (
            "",
            None,
        ),
        # Empty case ID
        (
            "[ZDATA]<salesforce_case_id></salesforce_case_id>[/ZDATA]",
            {"id": "", "name": ""},
        ),
    ],
)
def test_extract_crm_linked_entity_info(input_text: str, expected_result: dict[str, str] | None) -> None:
    result = extract_crm_linked_entity_info(input_text)
    assert result == expected_result
