import copy
import datetime
import json
import logging
import uuid
from typing import Any, Generator
from unittest.mock import AN<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ock, PropertyMock, call, patch

import pydantic
import pytest
import requests
from pytest_django.fixtures import SettingsWrapper

from deepinsights.core.integrations.calendar.calendar_models import Calendar<PERSON>vent
from deepinsights.core.integrations.meetingbot import recall_b64data
from deepinsights.core.integrations.meetingbot.bot_controller import BotMeetingType, BotStatus
from deepinsights.core.integrations.meetingbot.recall_ai import (
    RecallBotController,
    RecallCalendarStatus,
    _log_payload,
)
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.users.models.user import User


@pytest.fixture()
def organization() -> Organization:
    return Organization.objects.create(name="Test")


@pytest.fixture()
def test_user(django_user_model: User, organization: Organization) -> User:
    return django_user_model.objects.create_user(username="test_user", organization=organization)


@pytest.fixture()
def test_user_with_bot_preferences(test_user: User) -> User:
    preferences = test_user.get_preferences()
    preferences.bot_preferences.recording_image_b64 = "image"
    preferences.bot_preferences.not_recording_image_b64 = "not_recording_image"
    preferences.bot_preferences.recording_message_b64 = "recording message"
    test_user.preferences = preferences.to_dict()
    test_user.save()
    return test_user


@pytest.fixture()
def mocked_timezone_now_time() -> Generator[datetime.datetime, None, None]:
    with patch("deepinsights.core.integrations.meetingbot.recall_ai.timezone") as mock_timezone:
        now = datetime.datetime(2023, 10, 10, 0, 0, 0, tzinfo=datetime.timezone.utc)
        mock_timezone.now.return_value = now
        yield now


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_failed_request(mock_requests: MagicMock, test_user: User) -> None:
    mock_requests.get.return_value.status_code = 500
    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        False,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert not fully_processed
    assert failed_event_ids == []


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_last_updated_timestamp(mock_requests: MagicMock, test_user: User) -> None:
    last_updated_time = datetime.datetime(2023, 10, 10, 0, 0, 0, tzinfo=datetime.timezone.utc)
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [],
    }

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        last_updated_time.isoformat(),
        test_user,
        True,
        False,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert not failed_event_ids
    mock_requests.get.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/",
        params={
            "calendar_id": "test-calendar",
            "start_time__gte": ANY,
            "updated_at__gte": last_updated_time.isoformat(),
        },
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [],
            },
            {
                "id": "event2",
                "meeting_url": "http://example.com/meeting2",
                "start_time": "2023-10-10T11:00:00Z",
                "end_time": "2023-10-10T11:30:00Z",
                "is_deleted": True,
                "bots": [
                    {
                        "id": "bot1",
                        "start_time": "2023-10-10T11:00:00Z",
                        "end_time": "2023-10-10T11:15:00Z",
                        "meeting_url": "http://example.com/meeting2",
                    }
                ],
            },
            {
                "id": "event3",
                "meeting_url": "http://example.com/meeting3",
                "start_time": "2023-10-10T12:00:00Z",
                "end_time": "2023-10-10T12:30:00Z",
                "is_deleted": False,
                "bots": [
                    {
                        "id": "bot2",
                        "start_time": "2023-10-10T12:00:00Z",
                        "end_time": "2023-10-10T12:30:00Z",
                        "meeting_url": "http://example.com/meeting3_old",
                    }
                ],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204
    mock_requests.delete.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        False,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == []
    mock_requests.get.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/",
        params={"calendar_id": "test-calendar", "start_time__gte": mocked_timezone_now_time.isoformat()},
        headers=ANY,
    )
    assert mock_requests.post.call_count == 2
    mock_requests.post.assert_has_calls(
        [
            call(
                "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/",
                json={
                    "deduplication_key": "event1-http://example.com/meeting1-2023-10-10T10:00:00Z",
                    "bot_config": ANY,
                },
                headers=ANY,
            ),
            call(
                "https://us-east-1.recall.ai/api/v2/calendar-events/event3/bot/",
                json={
                    "deduplication_key": "event3-http://example.com/meeting3-2023-10-10T12:00:00Z",
                    "bot_config": ANY,
                },
                headers=ANY,
            ),
        ]
    )
    assert mock_requests.delete.call_count == 2
    mock_requests.delete.assert_has_calls(
        [
            call("https://us-east-1.recall.ai/api/v2/calendar-events/event2/bot/", headers=ANY),
            call("https://us-east-1.recall.ai/api/v2/calendar-events/event3/bot/", headers=ANY),
        ]
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_force_update(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event2",
                "meeting_url": "http://example.com/meeting2",
                "start_time": "2023-10-10T11:00:00Z",
                "end_time": "2023-10-10T11:30:00Z",
                "is_deleted": False,
                "bots": [
                    {
                        "id": "bot1",
                        "start_time": "2023-10-10T11:00:00Z",
                        "end_time": "2023-10-10T11:30:00Z",
                        "meeting_url": "http://example.com/meeting2",
                    }
                ],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204
    mock_requests.delete.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        False,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=True,
    )

    assert fully_processed
    assert failed_event_ids == []
    mock_requests.post.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event2/bot/",
        json={
            "deduplication_key": "event2-http://example.com/meeting2-2023-10-10T11:00:00Z",
            "bot_config": ANY,
        },
        headers=ANY,
    )
    assert mock_requests.delete.call_count == 1
    mock_requests.delete.assert_has_calls(
        [
            call("https://us-east-1.recall.ai/api/v2/calendar-events/event2/bot/", headers=ANY),
        ]
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_event_ids_to_process(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [],
            },
            {
                "id": "event2",
                "meeting_url": "http://example.com/meeting2",
                "start_time": "2023-10-10T11:00:00Z",
                "end_time": "2023-10-10T11:30:00Z",
                "is_deleted": False,
                "bots": [],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204
    mock_requests.delete.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        False,
        event_ids_to_process=["event2"],
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == []
    mock_requests.post.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event2/bot/",
        json={
            "deduplication_key": "event2-http://example.com/meeting2-2023-10-10T11:00:00Z",
            "bot_config": ANY,
        },
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_calendar_platform_ids_to_process(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "platform_id": "platformid1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [],
            },
            {
                "id": "event2",
                "platform_id": "platformid2",
                "meeting_url": "http://example.com/meeting2",
                "start_time": "2023-10-10T11:00:00Z",
                "end_time": "2023-10-10T11:30:00Z",
                "is_deleted": False,
                "bots": [],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204
    mock_requests.delete.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        False,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=["platformid2"],
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == []
    mock_requests.post.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event2/bot/",
        json={
            "deduplication_key": "event2-http://example.com/meeting2-2023-10-10T11:00:00Z",
            "bot_config": ANY,
        },
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_disabled_scheduled_events(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "platform_id": "uevent1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [],
            },
            {
                "id": "event2",
                "platform_id": "uevent2",
                "meeting_url": "http://example.com/meeting2",
                "start_time": "2023-10-10T11:00:00Z",
                "end_time": "2023-10-10T11:30:00Z",
                "is_deleted": True,
                "bots": [
                    {
                        "id": "bot1",
                        "start_time": "2023-10-10T11:00:00Z",
                        "end_time": "2023-10-10T11:15:00Z",
                        "meeting_url": "http://example.com/meeting2",
                    }
                ],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204
    mock_requests.delete.return_value.status_code = 204

    event_one = CalendarEvent(
        provider="google",
        id="event1",
        user_specific_id="uevent1",
        title="Test meeting",
        body="Test body",
        start_time=datetime.datetime(2023, 10, 10, 10, 0, 0, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2023, 10, 10, 10, 30, 0, tzinfo=datetime.timezone.utc),
        all_day=False,
        participants=[],
        meeting_urls=[pydantic.HttpUrl("http://example.com/meeting1")],
    )

    ScheduledEvent.objects.create(
        user=test_user,
        start_time=datetime.datetime(2023, 10, 10, 10, 0, 0, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2023, 10, 10, 10, 30, 0, tzinfo=datetime.timezone.utc),
        shared_source_id="event1",
        user_specific_source_id="uevent1",
        source_data=event_one.model_dump(mode="json"),
        autojoin_behavior=ScheduledEvent.AutoJoinOverride.DISABLED,
    )

    event_two = CalendarEvent(
        provider="google",
        id="event2",
        user_specific_id="uevent2",
        title="Test meeting",
        body="Test body two",
        start_time=datetime.datetime(2023, 10, 10, 11, 0, 0, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2023, 10, 10, 11, 30, 0, tzinfo=datetime.timezone.utc),
        all_day=False,
        participants=[],
        meeting_urls=[pydantic.HttpUrl("http://example.com/meeting2")],
    )
    ScheduledEvent.objects.create(
        user=test_user,
        start_time=datetime.datetime(2023, 10, 10, 11, 0, 0, tzinfo=datetime.timezone.utc),
        end_time=datetime.datetime(2023, 10, 10, 11, 30, 0, tzinfo=datetime.timezone.utc),
        shared_source_id="event2",
        user_specific_source_id="uevent2",
        source_data=event_two.model_dump(mode="json"),
        autojoin_behavior=ScheduledEvent.AutoJoinOverride.DISABLED,
    )

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        False,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == []
    mock_requests.get.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/",
        params={"calendar_id": "test-calendar", "start_time__gte": mocked_timezone_now_time.isoformat()},
        headers=ANY,
    )
    assert mock_requests.post.call_count == 0
    mock_requests.delete.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event2/bot/",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_creation_failure(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 500
    mock_requests.post.return_value.json.side_effect = json.JSONDecodeError("error", "", 0)
    mock_requests.post.return_value.text = "error"

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        False,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == ["event1"]
    mock_requests.post.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/",
        json={
            "deduplication_key": "event1-http://example.com/meeting1-2023-10-10T10:00:00Z",
            "bot_config": ANY,
        },
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_deletion_failure(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": True,
                "bots": [
                    {
                        "id": "bot1",
                        "start_time": "2023-10-10T10:00:00Z",
                        "end_time": "2023-10-10T10:30:00Z",
                        "meeting_url": "http://example.com/meeting1",
                    }
                ],
            },
        ],
    }
    mock_requests.delete.return_value.status_code = 500
    mock_requests.delete.return_value.json.side_effect = json.JSONDecodeError("error", "", 0)
    mock_requests.delete.return_value.text = "error"

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        False,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == ["event1"]
    mock_requests.delete.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_partial_failure(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [],
            },
            {
                "id": "event2",
                "meeting_url": "http://example.com/meeting2",
                "start_time": "2023-10-10T11:00:00Z",
                "is_deleted": True,
                "bots": [
                    {
                        "id": "bot1",
                        "start_time": "2023-10-10T11:00:00Z",
                        "end_time": "2023-10-10T11:15:00Z",
                        "meeting_url": "http://example.com/meeting2",
                    }
                ],
            },
            {
                "id": "event3",
                "meeting_url": "http://example.com/meeting3",
                "start_time": "2023-10-10T12:00:00Z",
                "is_deleted": False,
                "bots": [
                    {
                        "id": "bot2",
                        "start_time": "2023-10-10T12:00:00Z",
                        "end_time": "2023-10-10T12:30:00Z",
                        "meeting_url": "http://example.com/meeting3_old",
                    }
                ],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204
    mock_requests.delete.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        False,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == []
    mock_requests.get.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/",
        params={"calendar_id": "test-calendar", "start_time__gte": ANY},
        headers=ANY,
    )
    assert mock_requests.post.call_count == 2
    mock_requests.post.assert_has_calls(
        [
            call(
                "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/",
                json={
                    "deduplication_key": "event1-http://example.com/meeting1-2023-10-10T10:00:00Z",
                    "bot_config": ANY,
                },
                headers=ANY,
            ),
            call(
                "https://us-east-1.recall.ai/api/v2/calendar-events/event3/bot/",
                json={
                    "deduplication_key": "event3-http://example.com/meeting3-2023-10-10T12:00:00Z",
                    "bot_config": ANY,
                },
                headers=ANY,
            ),
        ]
    )
    assert mock_requests.delete.call_count == 2
    mock_requests.delete.assert_has_calls(
        [
            call("https://us-east-1.recall.ai/api/v2/calendar-events/event2/bot/", headers=ANY),
            call("https://us-east-1.recall.ai/api/v2/calendar-events/event3/bot/", headers=ANY),
        ]
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_no_updates_to_bots_that_match_event(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            # Unchanged
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [
                    {
                        "id": "bot1",
                        "start_time": "2023-10-10T10:00:00Z",
                        "end_time": "2023-10-10T10:30:00Z",
                        "meeting_url": "http://example.com/meeting1",
                    },
                ],
            },
            # Changed
            {
                "id": "event2",
                "meeting_url": "http://example.com/meeting2",
                "start_time": "2023-10-10T11:00:00Z",
                "end_time": "2023-10-10T11:30:00Z",
                "is_deleted": False,
                "bots": [
                    {
                        "id": "bot2",
                        "start_time": "2023-10-10T11:00:00Z",
                        "end_time": "2023-10-10T11:30:00Z",
                        "meeting_url": "http://example.com/meeting2_old",
                    }
                ],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204
    mock_requests.delete.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        False,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == []
    mock_requests.post.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event2/bot/",
        json={
            "deduplication_key": "event2-http://example.com/meeting2-2023-10-10T11:00:00Z",
            "bot_config": ANY,
        },
        headers=ANY,
    )
    mock_requests.delete.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event2/bot/", headers=ANY
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_only_past_bots(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [
                    {
                        "id": "bot1",
                        "start_time": "2023-10-09T10:00:00Z",
                        "end_time": "2023-10-09T10:30:00Z",
                        "meeting_url": "http://example.com/meeting1",
                    },
                    {
                        "id": "bot2",
                        "start_time": "2023-10-08T10:00:00Z",
                        "end_time": "2023-10-08T10:30:00Z",
                        "meeting_url": "http://example.com/meeting1",
                    },
                ],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204
    mock_requests.delete.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        False,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == []
    mock_requests.post.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/",
        json={
            "deduplication_key": "event1-http://example.com/meeting1-2023-10-10T10:00:00Z",
            "bot_config": ANY,
        },
        headers=ANY,
    )
    mock_requests.delete.assert_not_called()


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_past_and_future_bots(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [
                    {
                        "id": "bot1",
                        "start_time": "2023-10-09T10:00:00Z",
                        "end_time": "2023-10-09T10:30:00Z",
                        "meeting_url": "http://example.com/meeting1",
                    },
                    {
                        "id": "bot2",
                        "start_time": "2023-10-11T10:00:00Z",
                        "end_time": "2023-10-11T10:30:00Z",
                        "meeting_url": "http://example.com/meeting1",
                    },
                ],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204
    mock_requests.delete.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        False,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == []
    mock_requests.post.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/",
        json={
            "deduplication_key": "event1-http://example.com/meeting1-2023-10-10T10:00:00Z",
            "bot_config": ANY,
        },
        headers=ANY,
    )
    mock_requests.delete.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/", headers=ANY
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_update_deletion_failure(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [
                    {
                        "id": "bot2",
                        "start_time": "2023-10-11T10:00:00Z",
                        "end_time": "2023-10-11T10:30:00Z",
                        "meeting_url": "http://example.com/meeting1",
                    },
                ],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204
    mock_requests.delete.return_value.status_code = 500

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        False,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == ["event1"]
    mock_requests.post.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/",
        json={
            "deduplication_key": "event1-http://example.com/meeting1-2023-10-10T10:00:00Z",
            "bot_config": ANY,
        },
        headers=ANY,
    )
    mock_requests.delete.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/", headers=ANY
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_update_recreation_failure(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [
                    {
                        "id": "bot2",
                        "start_time": "2023-10-11T10:00:00Z",
                        "end_time": "2023-10-11T10:30:00Z",
                        "meeting_url": "http://example.com/meeting1",
                    },
                ],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 500
    mock_requests.delete.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        False,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == ["event1"]
    mock_requests.post.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/",
        json={
            "deduplication_key": "event1-http://example.com/meeting1-2023-10-10T10:00:00Z",
            "bot_config": ANY,
        },
        headers=ANY,
    )
    mock_requests.delete.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/", headers=ANY
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_later_page_failure_with_partial_failures(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    type(mock_requests.get.return_value).status_code = PropertyMock(side_effect=[200, 200, 200, 500])
    mock_requests.get.return_value.json.side_effect = [
        {
            "next": "https://us-east-1.recall.ai/api/v2/calendar-events/?cursor=2",
            "results": [
                {
                    "id": "event1",
                    "meeting_url": "http://example.com/meeting1",
                    "start_time": "2023-10-10T10:00:00Z",
                    "end_time": "2023-10-10T10:30:00Z",
                    "is_deleted": False,
                    "bots": [],
                },
            ],
        },
        {
            "next": "https://us-east-1.recall.ai/api/v2/calendar-events/?cursor=3",
            "results": [
                {
                    "id": "event2",
                    "meeting_url": "http://example.com/meeting2",
                    "start_time": "2023-10-10T10:00:00Z",
                    "end_time": "2023-10-10T10:30:00Z",
                    "is_deleted": False,
                    "bots": [],
                },
            ],
        },
        {
            "next": "https://us-east-1.recall.ai/api/v2/calendar-events/?cursor=4",
            "results": [
                {
                    "id": "event3",
                    "meeting_url": "http://example.com/meeting3",
                    "start_time": "2023-10-10T10:00:00Z",
                    "end_time": "2023-10-10T10:30:00Z",
                    "is_deleted": False,
                    "bots": [],
                },
            ],
        },
        json.JSONDecodeError("Error", "", 0),
    ]
    mock_requests.get.return_value.text = "Error"

    type(mock_requests.post.return_value).status_code = PropertyMock(side_effect=[204, 500, 204])

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        False,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert not fully_processed
    assert failed_event_ids == ["event2"]
    assert mock_requests.get.call_count == 4
    assert mock_requests.get.call_args_list == [
        call(
            "https://us-east-1.recall.ai/api/v2/calendar-events/",
            params=ANY,
            headers=ANY,
        ),
        call(
            "https://us-east-1.recall.ai/api/v2/calendar-events/?cursor=2",
            params=ANY,
            headers=ANY,
        ),
        call(
            "https://us-east-1.recall.ai/api/v2/calendar-events/?cursor=3",
            params=ANY,
            headers=ANY,
        ),
        call(
            "https://us-east-1.recall.ai/api/v2/calendar-events/?cursor=4",
            params=ANY,
            headers=ANY,
        ),
    ]
    assert mock_requests.post.call_count == 3
    assert mock_requests.post.call_args_list == [
        call("https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/", json=ANY, headers=ANY),
        call("https://us-east-1.recall.ai/api/v2/calendar-events/event2/bot/", json=ANY, headers=ANY),
        call("https://us-east-1.recall.ai/api/v2/calendar-events/event3/bot/", json=ANY, headers=ANY),
    ]


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_zoom_native(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        True,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == []
    mock_requests.post.assert_called_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/",
        json={
            "deduplication_key": ANY,
            "bot_config": {
                "variant": {"zoom": "native"},
                "transcription_options": ANY,
                "automatic_audio_output": ANY,
                "recording_mode_options": ANY,
                "recording_mode": ANY,
                "bot_name": ANY,
                "metadata": ANY,
                "automatic_video_output": ANY,
            },
        },
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
@patch("deepinsights.core.integrations.meetingbot.recall_ai.Flags")
def test_update_bots_for_calendar_auto_join_normal_deduplication(
    mock_flags: MagicMock,
    mock_requests: MagicMock,
    test_user: User,
    mocked_timezone_now_time: datetime.datetime,
) -> None:
    mock_flags.EnableOrgLevelBotDeduplication.is_active_for_user.return_value = False
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        True,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == []
    key = "event1" "-http://example.com/meeting1" "-2023-10-10T10:00:00Z"
    mock_requests.post.assert_called_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/",
        json={
            "deduplication_key": key,
            "bot_config": {
                "variant": {"zoom": "native"},
                "transcription_options": ANY,
                "automatic_audio_output": ANY,
                "recording_mode_options": ANY,
                "recording_mode": ANY,
                "bot_name": ANY,
                "metadata": ANY,
                "automatic_video_output": ANY,
            },
        },
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
@patch("deepinsights.core.integrations.meetingbot.recall_ai.Flags")
def test_update_bots_for_calendar_auto_join_org_level_deduplication(
    mock_flags: MagicMock,
    mock_requests: MagicMock,
    test_user: User,
    organization: Organization,
    mocked_timezone_now_time: datetime.datetime,
) -> None:
    mock_flags.EnableOrgLevelBotDeduplication.is_active_for_user.return_value = True
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        True,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == []
    key = (
        f"{organization.id}"
        "-532eaabd9574880dbf76b9b8cc00832c20a6ec113d682299550d7a6e0f345e25"
        "-http://example.com/meeting1"
        "-2023-10-10T10:00:00Z"
    )
    mock_requests.post.assert_called_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/",
        json={
            "deduplication_key": key,
            "bot_config": {
                "variant": {"zoom": "native"},
                "transcription_options": ANY,
                "automatic_audio_output": ANY,
                "recording_mode_options": ANY,
                "recording_mode": ANY,
                "bot_name": ANY,
                "metadata": ANY,
                "automatic_video_output": ANY,
            },
        },
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_calendar_event_no_calendar_event_id(mock_requests: MagicMock) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {"metadata": {}}

    result = RecallBotController("test-bot-id").calendar_event()

    assert not result
    mock_requests.get.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/test-bot-id",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_calendar_event_failure(mock_requests: MagicMock) -> None:
    def raise_exception() -> None:
        raise json.JSONDecodeError("error", "", 0)

    mock_requests.get.return_value = MagicMock(status_code=500, json=raise_exception, text="error")

    result = RecallBotController("test-bot-id").calendar_event()

    assert not result
    mock_requests.get.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/test-bot-id",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_calendar_event_with_calendar_event_id(mock_requests: MagicMock) -> None:
    mock_requests.get.side_effect = [
        MagicMock(status_code=200, json=lambda: {"metadata": {"calendar_event_id": "event-id"}}),
        MagicMock(status_code=200, json=lambda: {"event": "details"}),
    ]

    result = RecallBotController("test-bot-id").calendar_event()

    assert result == {"event": "details"}
    mock_requests.get.assert_has_calls(
        [
            call(
                "https://api.recall.ai/api/v1/bot/test-bot-id",
                headers=ANY,
            ),
            call(
                "https://us-east-1.recall.ai/api/v2/calendar-events/event-id/",
                headers=ANY,
            ),
        ]
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_calendar_event_with_calendar_event_id_failure(mock_requests: MagicMock) -> None:
    def raise_exception() -> None:
        raise json.JSONDecodeError("error", "", 0)

    mock_requests.get.side_effect = [
        MagicMock(status_code=200, json=lambda: {"metadata": {"calendar_event_id": "event-id"}}),
        MagicMock(status_code=500, json=raise_exception, text="error"),
    ]

    controller = RecallBotController(bot_id="test-bot-id")
    result = controller.calendar_event()

    assert result is None
    mock_requests.get.assert_has_calls(
        [
            call(
                "https://api.recall.ai/api/v1/bot/test-bot-id",
                headers=ANY,
            ),
            call(
                "https://us-east-1.recall.ai/api/v2/calendar-events/event-id/",
                headers=ANY,
            ),
        ]
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_calendar_event_with_calendar_event_id_not_found(mock_requests: MagicMock) -> None:
    mock_requests.get.side_effect = [
        MagicMock(status_code=200, json=lambda: {"metadata": {"calendar_event_id": "event-id"}}),
        MagicMock(status_code=404),
    ]

    controller = RecallBotController(bot_id="test-bot-id")
    result = controller.calendar_event()

    assert result is None
    mock_requests.get.assert_has_calls(
        [
            call(
                "https://api.recall.ai/api/v1/bot/test-bot-id",
                headers=ANY,
            ),
            call(
                "https://us-east-1.recall.ai/api/v2/calendar-events/event-id/",
                headers=ANY,
            ),
        ]
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_info_for_calendar_success(mock_requests: MagicMock) -> None:
    mock_requests.get.return_value = MagicMock(
        status_code=200, json=lambda: {"status": "active", "platform": "google_calendar"}
    )

    result = RecallBotController.info_for_calendar("test-calendar-id")

    assert result == {"status": "active", "platform": "google_calendar"}
    mock_requests.get.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendars/test-calendar-id/",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_status_for_calendar_failure(mock_requests: MagicMock) -> None:
    def raise_exception() -> None:
        raise json.JSONDecodeError("error", "", 0)

    mock_requests.get.return_value = MagicMock(status_code=500, json=raise_exception, text="error")

    result = RecallBotController.info_for_calendar("test-calendar-id")

    assert not result
    mock_requests.get.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendars/test-calendar-id/",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_status_for_calendar_no_status_key(mock_requests: MagicMock) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {"platform": "google_calendar"}

    result = RecallBotController.info_for_calendar("test-calendar-id")

    assert not result
    mock_requests.get.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendars/test-calendar-id/",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_status_for_calendar_no_platform_key(mock_requests: MagicMock) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {"status": "active"}

    result = RecallBotController.info_for_calendar("test-calendar-id")

    assert not result
    mock_requests.get.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendars/test-calendar-id/",
        headers=ANY,
    )


@pytest.fixture()
def test_user_with_no_audio(django_user_model: User, organization: Organization) -> User:
    """User with bot preferences where enable_audio_output is False"""
    user = django_user_model.objects.create_user(username="test_user_no_audio")
    user.organization = organization
    preferences = user.get_preferences()
    preferences.bot_preferences.recording_image_b64 = "image"
    preferences.bot_preferences.not_recording_image_b64 = "not_recording_image"
    preferences.bot_preferences.recording_message_b64 = "recording message"
    user.preferences = preferences.to_dict()
    user.preferences["bot_preferences"]["enable_audio_output"] = False  # type: ignore[call-overload,index]
    user.save()
    return user


@pytest.fixture()
def test_user_with_org_no_preferences(django_user_model: User, organization: Organization) -> User:
    """User with no specific preferences, should inherit from org defaults"""
    user = django_user_model.objects.create_user(username="test_user_no_prefs")
    user.organization = organization
    user.save()
    return user


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_bot_payload_with_audio_disabled(mock_requests: MagicMock, test_user_with_no_audio: User) -> None:
    """Test that bot payload doesn't include audio output when disabled"""
    bot_preferences = test_user_with_no_audio.get_preferences().bot_preferences

    payload = RecallBotController._RecallBotController__bot_payload(  # type: ignore[attr-defined]
        bot_preferences=bot_preferences,
        recording_trigger="call_join",
        enable_live_transcription=True,
        enable_native_zoom_bot=True,
        asr_language_code="en",
    )

    # automatic_audio_output should not be present when disabled
    assert "automatic_audio_output" not in payload


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_bot_payload_with_audio_enabled(mock_requests: MagicMock, test_user_with_bot_preferences: User) -> None:
    bot_preferences = test_user_with_bot_preferences.get_preferences().bot_preferences

    payload = RecallBotController._RecallBotController__bot_payload(  # type: ignore[attr-defined]
        bot_preferences=bot_preferences,
        recording_trigger="call_join",
        enable_live_transcription=True,
        enable_native_zoom_bot=True,
        asr_language_code="en",
    )

    # Verify audio output configuration when enabled
    assert "automatic_audio_output" in payload
    assert payload["automatic_audio_output"] == {
        "in_call_recording": {"data": {"kind": "mp3", "b64_data": "recording message"}},
    }


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_with_audio_disabled(
    mock_requests: MagicMock, test_user_with_no_audio: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user_with_no_audio,
        True,
        True,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == []

    # Verify the bot configuration excludes audio output
    mock_requests.post.assert_called_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/",
        json={
            "deduplication_key": ANY,
            "bot_config": {
                "variant": {"zoom": "native"},
                "transcription_options": ANY,
                "recording_mode_options": ANY,
                "recording_mode": ANY,
                "bot_name": ANY,
                "metadata": ANY,
                "automatic_video_output": ANY,
                # automatic_audio_output should not be present
            },
        },
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_with_recording_free_zoom(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    preferences = test_user.get_preferences()
    preferences.bot_preferences.enable_recording_free_zoom = True
    test_user.preferences = preferences.to_dict()
    test_user.save()

    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        True,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == []

    actual_call = mock_requests.post.call_args
    assert actual_call is not None

    assert actual_call.args[0] == "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/"

    actual_config = actual_call.kwargs["json"]["bot_config"]

    assert "zoom" in actual_config
    assert actual_config["zoom"]["require_recording_permission"] is False
    assert actual_config["zoom"]["request_recording_permission_on_host_join"] is False


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_bot_payload_with_custom_base_name(mock_requests: MagicMock, test_user: User) -> None:
    """Test that bot payload uses custom bot_base_name correctly"""
    bot_preferences = test_user.get_preferences().bot_preferences

    payload = RecallBotController._RecallBotController__bot_payload(  # type: ignore[attr-defined]
        bot_preferences=bot_preferences,
        recording_trigger="call_join",
        enable_live_transcription=True,
        enable_native_zoom_bot=True,
        bot_base_name="CustomBot",
        asr_language_code="en",
    )

    assert payload["bot_name"] == "CustomBot's notetaker"


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_bot_payload_with_default_base_name(mock_requests: MagicMock, test_user: User) -> None:
    """Test that bot payload uses default bot_base_name (Zeplyn) when none provided"""
    bot_preferences = test_user.get_preferences().bot_preferences

    payload = RecallBotController._RecallBotController__bot_payload(  # type: ignore[attr-defined]
        bot_preferences=bot_preferences,
        recording_trigger="call_join",
        enable_live_transcription=True,
        enable_native_zoom_bot=True,
        asr_language_code="en",
    )

    assert payload["bot_name"] == "Zeplyn's notetaker"


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_bot_payload_with_custom_notetaker_name(mock_requests: MagicMock, test_user: User) -> None:
    """Test that bot payload uses custom notetaker_name from preferences over bot_base_name"""
    bot_preferences = test_user.get_preferences().bot_preferences
    bot_preferences.notetaker_name = "Custom Notetaker"

    payload = RecallBotController._RecallBotController__bot_payload(  # type: ignore[attr-defined]
        bot_preferences=bot_preferences,
        recording_trigger="call_join",
        enable_live_transcription=True,
        enable_native_zoom_bot=True,
        asr_language_code="en",
        bot_base_name="CustomBot",
    )

    assert payload["bot_name"] == "Custom Notetaker"


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_bot_payload_with_recording_free_zoom_enabled(mock_requests: MagicMock, test_user: User) -> None:
    preferences = test_user.get_preferences()
    preferences.bot_preferences.enable_recording_free_zoom = True

    payload = RecallBotController._RecallBotController__bot_payload(  # type: ignore[attr-defined]
        bot_preferences=preferences.bot_preferences,
        recording_trigger="call_join",
        enable_live_transcription=True,
        enable_native_zoom_bot=True,
        asr_language_code="en",
    )

    assert "zoom" in payload
    assert payload["zoom"]["require_recording_permission"] is False
    assert payload["zoom"]["request_recording_permission_on_host_join"] is False


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_bot_payload_with_recording_free_zoom_disabled(mock_requests: MagicMock, test_user: User) -> None:
    preferences = test_user.get_preferences()
    preferences.bot_preferences.enable_recording_free_zoom = False

    payload = RecallBotController._RecallBotController__bot_payload(  # type: ignore[attr-defined]
        bot_preferences=preferences.bot_preferences,
        recording_trigger="call_join",
        enable_live_transcription=True,
        enable_native_zoom_bot=True,
        asr_language_code="en",
    )

    if "zoom" in payload:
        assert "require_recording_permission" not in payload["zoom"]
        assert "request_recording_permission_on_host_join" not in payload["zoom"]


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_bot_payload_with_notetaker_images(mock_requests: MagicMock, test_user: User) -> None:
    bot_preferences = test_user.get_preferences().bot_preferences
    bot_preferences.recording_image_b64 = "image_data"
    bot_preferences.not_recording_image_b64 = "not_recording_image_data"

    payload = RecallBotController._RecallBotController__bot_payload(  # type: ignore[attr-defined]
        bot_preferences=bot_preferences,
        recording_trigger="call_join",
        enable_live_transcription=True,
        enable_native_zoom_bot=True,
        asr_language_code="en",
    )

    assert payload["automatic_video_output"]["in_call_recording"]["b64_data"] == "image_data"
    assert payload["automatic_video_output"]["in_call_not_recording"]["b64_data"] == "not_recording_image_data"


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_bot_payload_with_default_images(mock_requests: MagicMock, test_user: User) -> None:
    payload = RecallBotController._RecallBotController__bot_payload(  # type: ignore[attr-defined]
        bot_preferences=test_user.get_preferences().bot_preferences,
        recording_trigger="call_join",
        enable_live_transcription=True,
        enable_native_zoom_bot=True,
        asr_language_code="en",
    )

    assert payload["automatic_video_output"]["in_call_recording"]["b64_data"] == recall_b64data.recording_default_jpeg
    assert (
        payload["automatic_video_output"]["in_call_not_recording"]["b64_data"]
        == recall_b64data.not_recording_default_jpeg
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_create_bot_uses_first_name_as_base_name(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    """Test that create_bot uses user's first_name as bot_base_name"""
    test_user.first_name = "TestFirst"
    test_user.save()

    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        True,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == []

    mock_requests.post.assert_called_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/",
        json={
            "deduplication_key": ANY,
            "bot_config": {
                "variant": {"zoom": "native"},
                "transcription_options": ANY,
                "recording_mode_options": ANY,
                "recording_mode": ANY,
                "bot_name": "TestFirst's notetaker",  # Check for first_name based bot name
                "metadata": ANY,
                "automatic_video_output": ANY,
                "automatic_audio_output": ANY,
            },
        },
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_update_bots_for_calendar_uses_first_name_as_base_name(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    test_user.first_name = ""
    test_user.name = "TestName"
    test_user.save()

    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        True,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == []

    mock_requests.post.assert_called_with(
        "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/",
        json={
            "deduplication_key": ANY,
            "bot_config": {
                "variant": {"zoom": "native"},
                "transcription_options": ANY,
                "recording_mode_options": ANY,
                "recording_mode": ANY,
                "bot_name": "TestName's notetaker",  # Check for name based bot name
                "metadata": ANY,
                "automatic_video_output": ANY,
                "automatic_audio_output": ANY,
            },
        },
        headers=ANY,
    )


def test_meeting_type() -> None:
    assert RecallBotController().meeting_type == BotMeetingType.VIDEO_CALL


def test_supports_pause_resume() -> None:
    assert RecallBotController().supports_pause_resume


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_bot_payload_includes_asr_language_code(mock_requests: MagicMock, test_user: User) -> None:
    """Test that bot payload includes the correct ASR language code"""
    bot_preferences = test_user.get_preferences().bot_preferences
    test_language = "es"

    payload = RecallBotController._RecallBotController__bot_payload(  # type: ignore[attr-defined]
        bot_preferences=bot_preferences,
        recording_trigger="call_join",
        enable_live_transcription=True,
        enable_native_zoom_bot=True,
        asr_language_code=test_language,
    )

    assert payload["transcription_options"]["deepgram"]["language"] == test_language


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_create_bot_uses_user_language_preference(
    mock_requests: MagicMock, test_user: User, mocked_timezone_now_time: datetime.datetime
) -> None:
    test_language = "fr"
    preferences = test_user.get_preferences()
    preferences.asr_language_code = test_language
    test_user.preferences = preferences.to_dict()
    test_user.save()

    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "next": None,
        "results": [
            {
                "id": "event1",
                "meeting_url": "http://example.com/meeting1",
                "start_time": "2023-10-10T10:00:00Z",
                "end_time": "2023-10-10T10:30:00Z",
                "is_deleted": False,
                "bots": [],
            },
        ],
    }
    mock_requests.post.return_value.status_code = 204

    fully_processed, failed_event_ids = RecallBotController.update_bots_for_calendar(
        "test-calendar",
        None,
        test_user,
        True,
        True,
        event_ids_to_process=None,
        calendar_platform_ids_to_process=None,
        force_update=False,
    )

    assert fully_processed
    assert failed_event_ids == []

    # Get the actual call args
    actual_call = mock_requests.post.call_args
    assert actual_call is not None

    # Verify URL
    assert actual_call.args[0] == "https://us-east-1.recall.ai/api/v2/calendar-events/event1/bot/"

    # Get the bot_config from the actual call
    actual_config = actual_call.kwargs["json"]["bot_config"]

    # Verify the transcription options specifically
    assert actual_config["transcription_options"] == {
        "provider": "deepgram",
        "deepgram": {
            "language": test_language,
            "smart_format": True,
            "model": "nova-2",
            "redact": ["pci", "ssn"],
            "numerals": True,
            "utterances": True,
            "keywords": [],
            "replace": [
                "zeplin:Zeplyn",
                "zeplins:Zeplyns",
                "zeplin's:Zeplyn's",
                "zeppelin:Zeplyn",
                "zeppelins:Zeplyns",
                "zeppelin's:Zeplyn's",
                "zeplon:Zeplyn",
                "zeplons:Zeplyns",
                "zeplon's:Zeplyn's",
            ],
            "log_data": False,
            "mip_opt_out": True,
        },
    }

    # Verify other required fields exist
    assert "variant" in actual_config
    assert actual_config["variant"] == {"zoom": "native"}
    assert "recording_mode_options" in actual_config
    assert "recording_mode" in actual_config
    assert "bot_name" in actual_config
    assert "metadata" in actual_config
    assert "automatic_video_output" in actual_config
    assert "automatic_audio_output" in actual_config


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_bot_payload_transcription_disabled(mock_requests: MagicMock, test_user: User) -> None:
    bot_preferences = test_user.get_preferences().bot_preferences

    payload = RecallBotController._RecallBotController__bot_payload(  # type: ignore[attr-defined]
        bot_preferences=bot_preferences,
        recording_trigger="call_join",
        enable_live_transcription=False,
        enable_native_zoom_bot=True,
        asr_language_code="en",
    )

    assert "transcription_options" not in payload


@patch("deepinsights.core.integrations.meetingbot.recall_ai.logging")
def test_log_payload_with_video_output(mock_logging: MagicMock) -> None:
    payload = {
        "automatic_video_output": {
            "in_call_recording": {
                "kind": "jpeg",
                "b64_data": "abcdefghijklmnopqrstuvwxyz",
            },
            "in_call_not_recording": {
                "kind": "jpeg",
                "b64_data": "********************1234",
            },
        },
    }
    payload_copy = copy.deepcopy(payload)

    _log_payload(payload_copy)

    assert payload == {
        "automatic_video_output": {
            "in_call_recording": {
                "kind": "jpeg",
                "b64_data": "abcdefghijklmnopqrstuvwxyz",
            },
            "in_call_not_recording": {
                "kind": "jpeg",
                "b64_data": "********************1234",
            },
        },
    }

    # Verify logged payload has truncated base64 data
    mock_logging.info.assert_called_once()
    logged_payload = mock_logging.info.call_args[0][1]
    assert logged_payload["automatic_video_output"]["in_call_recording"]["b64_data"] == "abcdefghij...qrstuvwxyz"
    assert logged_payload["automatic_video_output"]["in_call_not_recording"]["b64_data"] == "**********...**********"


@patch("deepinsights.core.integrations.meetingbot.recall_ai.logging")
def test_log_payload_with_audio_output(mock_logging: MagicMock) -> None:
    """Test _log_payload properly truncates audio output base64 data"""
    payload = {
        "automatic_audio_output": {
            "in_call_recording": {
                "data": {
                    "kind": "mp3",
                    "b64_data": "abcdefghijklmnopqrstuvwxyz",
                },
            },
        },
    }
    payload_copy = copy.deepcopy(payload)

    _log_payload(payload_copy)

    # Verify original payload wasn't modified
    assert payload == {
        "automatic_audio_output": {
            "in_call_recording": {
                "data": {
                    "kind": "mp3",
                    "b64_data": "abcdefghijklmnopqrstuvwxyz",
                },
            },
        },
    }

    # Verify logged payload has truncated base64 data
    mock_logging.info.assert_called_once()
    logged_payload = mock_logging.info.call_args[0][1]
    assert (
        logged_payload["automatic_audio_output"]["in_call_recording"]["data"]["b64_data"] == "abcdefghij...qrstuvwxyz"
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.logging")
def test_log_payload_with_both_outputs(mock_logging: MagicMock) -> None:
    """Test _log_payload handles both video and audio outputs correctly"""
    payload = {
        "automatic_video_output": {
            "in_call_recording": {
                "kind": "jpeg",
                "b64_data": "abcdefghijklmnopqrstuvwxyz",
            },
        },
        "automatic_audio_output": {
            "in_call_recording": {
                "data": {
                    "kind": "mp3",
                    "b64_data": "********************1234",
                },
            },
        },
    }
    payload_copy = copy.deepcopy(payload)

    _log_payload(payload_copy)

    assert payload == {
        "automatic_video_output": {
            "in_call_recording": {
                "kind": "jpeg",
                "b64_data": "abcdefghijklmnopqrstuvwxyz",
            },
        },
        "automatic_audio_output": {
            "in_call_recording": {
                "data": {
                    "kind": "mp3",
                    "b64_data": "********************1234",
                },
            },
        },
    }

    mock_logging.info.assert_called_once()
    logged_payload = mock_logging.info.call_args[0][1]
    assert logged_payload["automatic_video_output"]["in_call_recording"]["b64_data"] == "abcdefghij...qrstuvwxyz"
    assert (
        logged_payload["automatic_audio_output"]["in_call_recording"]["data"]["b64_data"] == "**********...**********"
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.logging")
def test_log_payload_with_no_base64_data(mock_logging: MagicMock) -> None:
    """Test _log_payload handles payloads without any base64 data"""
    payload = {
        "transcription_options": {
            "provider": "deepgram",
            "deepgram": {
                "language": "en",
                "smart_format": True,
            },
        },
        "recording_mode": "audio_only",
    }
    payload_copy = copy.deepcopy(payload)

    _log_payload(payload_copy)

    assert payload == {
        "transcription_options": {
            "provider": "deepgram",
            "deepgram": {
                "language": "en",
                "smart_format": True,
            },
        },
        "recording_mode": "audio_only",
    }

    # Verify logged payload is identical to original
    mock_logging.info.assert_called_once()
    logged_payload = mock_logging.info.call_args[0][1]
    assert logged_payload == payload


@patch("deepinsights.core.integrations.meetingbot.recall_ai.logging")
def test_log_payload_with_empty_payload(mock_logging: MagicMock) -> None:
    """Test _log_payload handles empty payloads"""
    payload: dict[str, Any] = {}
    payload_copy = copy.deepcopy(payload)

    _log_payload(payload_copy)

    # Verify logging was called with empty dict
    mock_logging.info.assert_called_once_with("Bot payload formed: %s", {})


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_get_bot_status_not_created(mock_requests: MagicMock) -> None:
    controller = RecallBotController(bot_id="")
    status = controller.get_bot_status()
    assert status == BotStatus.NOT_CREATED
    mock_requests.get.assert_not_called()


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_get_bot_status_success(mock_requests: MagicMock) -> None:
    response = requests.Response()
    response.status_code = 200
    response._content = json.dumps(
        {
            "status_changes": [
                {"code": "in_waiting_room"},
                {"code": "in_call_not_recording"},
                {"code": "in_call_recording"},
            ]
        }
    ).encode("utf-8")
    mock_requests.get.return_value = response

    controller = RecallBotController(bot_id="test-bot-id")
    status = controller.get_bot_status()

    assert status == BotStatus.IN_CALL_RECORDING
    mock_requests.get.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/test-bot-id/",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_get_bot_status_failure(mock_requests: MagicMock) -> None:
    mock_requests.get.return_value.status_code = 500
    mock_requests.get.return_value.json.side_effect = json.JSONDecodeError("error", "", 0)
    mock_requests.get.return_value.text = "error"

    controller = RecallBotController(bot_id="test-bot-id")
    status = controller.get_bot_status()

    assert status == BotStatus.UNKNOWN
    mock_requests.get.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/test-bot-id/",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_get_bot_status_exception(mock_requests: MagicMock) -> None:
    mock_requests.get.side_effect = Exception("Network error")

    controller = RecallBotController(bot_id="test-bot-id")
    status = controller.get_bot_status()

    assert status == BotStatus.UNKNOWN
    mock_requests.get.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/test-bot-id/",
        headers=ANY,
    )


@pytest.mark.asyncio
@patch("deepinsights.core.integrations.meetingbot.recall_ai.aiohttp.ClientSession")
async def test_aget_bot_status_not_created(mock_session: AsyncMock) -> None:
    controller = RecallBotController(bot_id="")
    status = await controller.aget_bot_status()
    assert status == BotStatus.NOT_CREATED
    mock_session.assert_not_called()


@pytest.mark.asyncio
@patch("deepinsights.core.integrations.meetingbot.recall_ai.aiohttp.ClientSession")
async def test_aget_bot_status_success(mock_session: AsyncMock) -> None:
    mock_response = AsyncMock()
    mock_response.status = 200
    mock_response.json.return_value = {
        "status_changes": [
            {"code": "in_waiting_room"},
            {"code": "in_call_not_recording"},
            {"code": "in_call_recording"},
        ]
    }
    mock_response.__int__ = lambda: "not an integer"
    mock_session.return_value.__aenter__.return_value.get.return_value = mock_response

    controller = RecallBotController(bot_id="test-bot-id")
    status = await controller.aget_bot_status()

    assert status == BotStatus.IN_CALL_RECORDING
    mock_session.return_value.__aenter__.return_value.get.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/test-bot-id/",
        headers=ANY,
    )


@pytest.mark.asyncio
@patch("deepinsights.core.integrations.meetingbot.recall_ai.aiohttp.ClientSession")
async def test_aget_bot_status_failure(mock_session: AsyncMock) -> None:
    mock_response = AsyncMock()
    mock_response.status = 500
    mock_response.json.side_effect = Exception("JSON decode error")
    mock_session.return_value.__aenter__.return_value.get.return_value = mock_response

    controller = RecallBotController(bot_id="test-bot-id")
    status = await controller.aget_bot_status()

    assert status == BotStatus.UNKNOWN
    mock_session.return_value.__aenter__.return_value.get.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/test-bot-id/",
        headers=ANY,
    )


@pytest.mark.asyncio
@patch("deepinsights.core.integrations.meetingbot.recall_ai.aiohttp.ClientSession")
async def test_aget_bot_status_exception(mock_session: AsyncMock) -> None:
    mock_session.return_value.__aenter__.return_value.get.side_effect = Exception("Network error")

    controller = RecallBotController(bot_id="test-bot-id")
    status = await controller.aget_bot_status()

    assert status == BotStatus.UNKNOWN
    mock_session.return_value.__aenter__.return_value.get.assert_called_once_with(
        "https://api.recall.ai/api/v1/bot/test-bot-id/",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_try_to_relink_calendar_success(mock_requests: MagicMock) -> None:
    mock_requests.patch.return_value.status_code = 200

    result = RecallBotController.try_to_relink_calendar("test-calendar-id")

    assert result is True
    mock_requests.patch.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendars/test-calendar-id",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_try_to_relink_calendar_failure(mock_requests: MagicMock) -> None:
    mock_requests.patch.return_value.status_code = 500
    mock_requests.patch.return_value.text = "error"

    result = RecallBotController.try_to_relink_calendar("test-calendar-id")

    assert result is False
    mock_requests.patch.assert_called_once_with(
        "https://us-east-1.recall.ai/api/v2/calendars/test-calendar-id",
        headers=ANY,
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
class TestCreateBotAndStartRecording:
    def test_create_bot_and_start_recording(
        self, mock_requests: MagicMock, test_user_with_bot_preferences: User, settings: SettingsWrapper
    ) -> None:
        settings.RECALL_API_TOKEN = "recall_api_token"

        internal_id = uuid.uuid4()
        expected_bot_id = "new-bot-id"

        mock_requests.post.return_value = MagicMock(status_code=201, json=lambda: {"id": expected_bot_id})

        response = RecallBotController().create_bot_and_start_recording(
            meeting_url="http://example.com/meeting",
            bot_preferences=test_user_with_bot_preferences.get_preferences().bot_preferences,
            enable_live_transcription=True,
            enable_native_zoom_bot=False,
            bot_base_name="TestBot",
            asr_language_code="es-MX",
            internal_id=internal_id,
            user_phone_number=None,
        )

        expected_payload = {
            "transcription_options": {
                "provider": "deepgram",
                "deepgram": ANY,
            },
            "recording_mode_options": {"start_recording_on": "call_join"},
            "recording_mode": "audio_only",
            "bot_name": "TestBot's notetaker",
            "metadata": {"zeplyn_bot_uuid": str(internal_id)},
            "automatic_video_output": {
                "in_call_recording": {"kind": "jpeg", "b64_data": "image"},
                "in_call_not_recording": {"kind": "jpeg", "b64_data": "not_recording_image"},
            },
            "automatic_audio_output": {"in_call_recording": {"data": {"kind": "mp3", "b64_data": "recording message"}}},
            "meeting_url": "http://example.com/meeting",
        }
        mock_requests.post.assert_called_once_with(
            "https://api.recall.ai/api/v1/bot/",
            headers={
                "Authorization": "Token recall_api_token",
                "accept": "application/json",
                "content-type": "application/json",
            },
            json=expected_payload,
        )

        assert response.status == 201
        assert response.details == {"id": expected_bot_id}

    def test_create_bot_and_start_recording_failure(
        self, mock_requests: MagicMock, test_user: User, settings: SettingsWrapper
    ) -> None:
        settings.RECALL_API_TOKEN = "recall_api_token"

        mock_requests.post.return_value = MagicMock(
            status_code=400,
            json=lambda: {"error": "Bad Request"},
            text='{"error": "Bad Request"}',
        )

        response = RecallBotController().create_bot_and_start_recording(
            meeting_url="http://example.com/meeting",
            bot_preferences=test_user.get_preferences().bot_preferences,
            enable_live_transcription=False,
            enable_native_zoom_bot=False,
            bot_base_name="Bot",
            asr_language_code="en-US",
            internal_id=uuid.uuid4(),
            user_phone_number=None,
        )

        mock_requests.post.assert_called_once()
        assert response.status == 400
        assert response.details == {"error": "Bad Request"}

    def test_create_bot_and_start_recording_existing_bot(
        self, mock_requests: MagicMock, test_user: User, settings: SettingsWrapper
    ) -> None:
        settings.RECALL_API_TOKEN = "recall_api_token"

        response = RecallBotController("existing_bot_id").create_bot_and_start_recording(
            meeting_url="http://example.com/meeting",
            bot_preferences=test_user.get_preferences().bot_preferences,
            enable_live_transcription=False,
            enable_native_zoom_bot=False,
            bot_base_name="Bot",
            asr_language_code="en-US",
            internal_id=uuid.uuid4(),
            user_phone_number=None,
        )

        mock_requests.post.assert_not_called()
        assert response.status == 400
        assert response.details == {"error": "Bot ID already set"}


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
class TestCreateOrUpdateScheduledBot:
    def test_create_or_update_scheduled_bot_create(
        self, mock_requests: MagicMock, test_user_with_bot_preferences: User, settings: SettingsWrapper
    ) -> None:
        settings.RECALL_API_TOKEN = "recall_api_token"

        internal_id = uuid.uuid4()
        expected_bot_id = "new-bot-id"
        join_time = datetime.datetime(2024, 1, 1, 12, 0, 0, tzinfo=datetime.timezone.utc)

        mock_requests.post.return_value = MagicMock(status_code=201, json=lambda: {"id": expected_bot_id})

        response = RecallBotController().create_or_update_scheduled_bot(
            meeting_url="http://example.com/meeting_join",
            join_time=join_time,
            bot_preferences=test_user_with_bot_preferences.get_preferences().bot_preferences,
            enable_live_transcription=True,
            enable_native_zoom_bot=False,
            bot_base_name="TestBotJoin",
            asr_language_code="en-US",
            internal_id=internal_id,
            user_phone_number=None,
        )

        expected_payload = {
            "transcription_options": {
                "provider": "deepgram",
                "deepgram": ANY,
            },
            "recording_mode_options": {"start_recording_on": "call_join"},
            "recording_mode": "audio_only",
            "bot_name": "TestBotJoin's notetaker",
            "metadata": {"zeplyn_bot_uuid": str(internal_id)},
            "automatic_video_output": {
                "in_call_recording": {"kind": "jpeg", "b64_data": "image"},
                "in_call_not_recording": {"kind": "jpeg", "b64_data": "not_recording_image"},
            },
            "automatic_audio_output": {"in_call_recording": {"data": {"kind": "mp3", "b64_data": "recording message"}}},
            "meeting_url": "http://example.com/meeting_join",
            "join_at": join_time.isoformat(),
        }
        mock_requests.post.assert_called_once_with(
            "https://api.recall.ai/api/v1/bot/",
            headers={
                "Authorization": "Token recall_api_token",
                "accept": "application/json",
                "content-type": "application/json",
            },
            json=expected_payload,
        )

        assert response.status == 201
        assert response.details == {"id": expected_bot_id}

    def test_create_or_update_scheduled_bot_update(
        self, mock_requests: MagicMock, test_user_with_bot_preferences: User, settings: SettingsWrapper
    ) -> None:
        settings.RECALL_API_TOKEN = "recall_api_token"

        existing_bot_id = "existing-bot-id"
        internal_id = uuid.uuid4()
        join_time = datetime.datetime(2024, 1, 1, 13, 0, 0, tzinfo=datetime.timezone.utc)

        mock_requests.patch.return_value = MagicMock(status_code=200, json=lambda: {"id": existing_bot_id})

        controller = RecallBotController(existing_bot_id)
        response = controller.create_or_update_scheduled_bot(
            meeting_url="http://example.com/meeting_update",
            join_time=join_time,
            bot_preferences=test_user_with_bot_preferences.get_preferences().bot_preferences,
            enable_live_transcription=False,
            enable_native_zoom_bot=True,
            bot_base_name="UpdatedBot",
            asr_language_code="en-GB",
            internal_id=internal_id,
            user_phone_number=None,
        )

        expected_payload = {
            "recording_mode_options": {"start_recording_on": "call_join"},
            "recording_mode": "audio_only",
            "bot_name": "UpdatedBot's notetaker",
            "metadata": {"zeplyn_bot_uuid": str(internal_id)},
            "automatic_video_output": {
                "in_call_recording": {"kind": "jpeg", "b64_data": "image"},
                "in_call_not_recording": {"kind": "jpeg", "b64_data": "not_recording_image"},
            },
            "automatic_audio_output": {"in_call_recording": {"data": {"kind": "mp3", "b64_data": "recording message"}}},
            "meeting_url": "http://example.com/meeting_update",
            "join_at": join_time.isoformat(),
            "variant": {"zoom": "native"},  # Added due to enable_native_zoom_bot=True
        }
        mock_requests.patch.assert_called_once_with(
            f"https://api.recall.ai/api/v1/bot/{existing_bot_id}/",
            headers={
                "Authorization": "Token recall_api_token",
                "accept": "application/json",
                "content-type": "application/json",
            },
            json=expected_payload,
        )
        mock_requests.post.assert_not_called()

        assert response.status == 200
        assert response.details == {"id": existing_bot_id}
        assert controller.bot_id == existing_bot_id

    def test_create_or_update_scheduled_bot_update_changing_bot_id(
        self,
        mock_requests: MagicMock,
        test_user_with_bot_preferences: User,
        settings: SettingsWrapper,
        caplog: pytest.LogCaptureFixture,
    ) -> None:
        settings.RECALL_API_TOKEN = "recall_api_token"

        existing_bot_id = "existing-bot-id"
        updated_bot_id = "updated-bot-id"
        internal_id = uuid.uuid4()
        join_time = datetime.datetime(2024, 1, 1, 13, 0, 0, tzinfo=datetime.timezone.utc)

        mock_requests.patch.return_value = MagicMock(status_code=200, json=lambda: {"id": updated_bot_id})

        with caplog.at_level(logging.WARNING):
            controller = RecallBotController(existing_bot_id)
            response = controller.create_or_update_scheduled_bot(
                meeting_url="http://example.com/meeting_update",
                join_time=join_time,
                bot_preferences=test_user_with_bot_preferences.get_preferences().bot_preferences,
                enable_live_transcription=False,
                enable_native_zoom_bot=True,
                bot_base_name="UpdatedBot",
                asr_language_code="en-GB",
                internal_id=internal_id,
                user_phone_number=None,
            )
            assert len(caplog.records) == 1
            assert (
                caplog.messages[0]
                == "Bot ID changed from existing-bot-id to updated-bot-id after bot update. This is unexpected."
            )

        expected_payload = {
            "recording_mode_options": {"start_recording_on": "call_join"},
            "recording_mode": "audio_only",
            "bot_name": "UpdatedBot's notetaker",
            "metadata": {"zeplyn_bot_uuid": str(internal_id)},
            "automatic_video_output": {
                "in_call_recording": {"kind": "jpeg", "b64_data": "image"},
                "in_call_not_recording": {"kind": "jpeg", "b64_data": "not_recording_image"},
            },
            "automatic_audio_output": {"in_call_recording": {"data": {"kind": "mp3", "b64_data": "recording message"}}},
            "meeting_url": "http://example.com/meeting_update",
            "join_at": join_time.isoformat(),
            "variant": {"zoom": "native"},  # Added due to enable_native_zoom_bot=True
        }
        mock_requests.patch.assert_called_once_with(
            f"https://api.recall.ai/api/v1/bot/{existing_bot_id}/",
            headers={
                "Authorization": "Token recall_api_token",
                "accept": "application/json",
                "content-type": "application/json",
            },
            json=expected_payload,
        )
        mock_requests.post.assert_not_called()

        assert response.status == 200
        assert response.details == {"id": updated_bot_id}
        assert controller.bot_id == updated_bot_id

    def test_create_or_update_scheduled_bot_update_failed(
        self, mock_requests: MagicMock, test_user: User, settings: SettingsWrapper
    ) -> None:
        settings.RECALL_API_TOKEN = "recall_api_token"
        existing_bot_id = "existing-bot-id-fail"

        mock_requests.patch.return_value = MagicMock(
            status_code=500,
            json=lambda: {"error": "Server Error"},
            text='{"error": "Server Error"}',
        )

        controller = RecallBotController(bot_id=existing_bot_id)
        response = controller.create_or_update_scheduled_bot(
            meeting_url="http://example.com/meeting_update_fail",
            join_time=datetime.datetime(2024, 1, 1, 13, 0, 0, tzinfo=datetime.timezone.utc),
            bot_preferences=test_user.get_preferences().bot_preferences,
            enable_live_transcription=True,
            enable_native_zoom_bot=False,
            bot_base_name="UpdateFailBot",
            asr_language_code="en-US",
            internal_id=None,
            user_phone_number=None,
        )

        mock_requests.patch.assert_called_once()
        assert response.status == 500
        assert response.details == {"error": "Server Error"}
        assert controller.bot_id == existing_bot_id


class TestRecallCalendarStatus:
    def test_recall_calendar_status_missing_lowercase(self) -> None:
        assert RecallCalendarStatus("connected") == RecallCalendarStatus.CONNECTED

    def test_recall_calendar_status_missing_uppercase(self) -> None:
        assert RecallCalendarStatus("CONNECTED") == RecallCalendarStatus.CONNECTED

    def test_recall_calendar_status_missing_mixed_case(self) -> None:
        assert RecallCalendarStatus("CoNnEcTeD") == RecallCalendarStatus.CONNECTED

    def test_recall_calendar_status_missing_invalid_string(self) -> None:
        assert RecallCalendarStatus("invalid_status") == RecallCalendarStatus.UNKNOWN

    def test_recall_calendar_status_missing_non_string(self) -> None:
        assert RecallCalendarStatus(123) == RecallCalendarStatus.UNKNOWN  # type: ignore[arg-type]
        assert RecallCalendarStatus(None) == RecallCalendarStatus.UNKNOWN  # type: ignore[arg-type]
        assert RecallCalendarStatus(True) == RecallCalendarStatus.UNKNOWN  # type: ignore[arg-type]

    def test_recall_calendar_status_direct_access(self) -> None:
        assert RecallCalendarStatus.CONNECTED == "connected"  # type: ignore[comparison-overlap]
        assert RecallCalendarStatus.DISCONNECTED == "disconnected"  # type: ignore[comparison-overlap]
        assert RecallCalendarStatus.UNKNOWN == "unknown"  # type: ignore[comparison-overlap]


@pytest.mark.parametrize(
    "recall_calendar_info, platform",
    [
        ({}, None),
        ({"platform": "microsoft_outlook"}, None),
        ({"platform": "google_calendar"}, None),
        ({"status": "disconnected", "platform": "microsoft_outlook"}, None),
        ({"status": "connected", "platform": "microsoft"}, None),
        ({"status": "connected", "platform": "google"}, None),
        ({"status": "connected", "platform": "other"}, None),
        ({"status": "connected", "platform": "microsoft_outlook"}, "microsoft"),
        ({"status": "connected", "platform": "google_calendar"}, "google"),
    ],
)
@patch("deepinsights.core.integrations.meetingbot.recall_ai.RecallBotController.info_for_calendar")
def test_connected_calendar_platform(
    mock_info_for_calendar: MagicMock, recall_calendar_info: dict[str, Any], platform: str | None
) -> None:
    mock_info_for_calendar.return_value = recall_calendar_info
    assert RecallBotController.connected_calendar_platform("123") == platform


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_send_chat_message_success_host_only(mock_requests: MagicMock) -> None:
    mock_requests.post.return_value.status_code = 200
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "meeting_url": {"platform": "zoom"},
        "meeting_participants": [{"id": 100, "is_host": True}, {"id": 200, "is_host": False}],
    }
    # also returns other fields not relevant here

    result = RecallBotController("test-bot-id").send_chat_message("host test", "everyone test")
    mock_requests.post.assert_has_calls(
        [
            call(
                "https://api.recall.ai/api/v1/bot/test-bot-id/send_chat_message/",
                headers=ANY,
                json={"to": "100", "message": "host test"},
            ),
        ]
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_send_chat_message_success_fallback_everyone(mock_requests: MagicMock) -> None:
    mock_requests.post.return_value.status_code = 200
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "meeting_url": {"platform": "google_meet"},
        "meeting_participants": [{"id": 100, "is_host": True}, {"id": 200, "is_host": False}],
    }
    # no zoom field

    result = RecallBotController("test-bot-id").send_chat_message("host test", "everyone test")
    assert result
    mock_requests.post.assert_has_calls(
        [
            call(
                "https://api.recall.ai/api/v1/bot/test-bot-id/send_chat_message/",
                headers=ANY,
                json={"to": "everyone", "message": "everyone test"},
            ),
        ]
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_send_chat_message_success_everyone(mock_requests: MagicMock) -> None:
    mock_requests.post.return_value.status_code = 200
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {
        "meeting_url": {"platform": "zoom"},
        "meeting_participants": [{"id": 100, "is_host": True}, {"id": 200, "is_host": False}],
    }

    result = RecallBotController("test-bot-id").send_chat_message(None, "everyone test")
    assert result
    mock_requests.post.assert_has_calls(
        [
            call(
                "https://api.recall.ai/api/v1/bot/test-bot-id/send_chat_message/",
                headers=ANY,
                json={"to": "everyone", "message": "everyone test"},
            ),
        ]
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_send_chat_message_failure_get_platform(mock_requests: MagicMock) -> None:
    bot = RecallBotController("test-bot-id")
    mock_requests.get.return_value = MagicMock(status_code=500)
    mock_requests.post.return_value = MagicMock(status_code=200)

    response = bot.send_chat_message("host test", "everyone test")
    assert response
    mock_requests.get.assert_called_once()
    mock_requests.post.assert_called_once()
    # Falls back to everyone if we can't get that it's a zoom call
    mock_requests.post.assert_has_calls(
        [
            call(
                "https://api.recall.ai/api/v1/bot/test-bot-id/send_chat_message/",
                headers=ANY,
                json={"to": "everyone", "message": "everyone test"},
            ),
        ]
    )


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_send_chat_message_failure(mock_requests: MagicMock) -> None:
    mock_requests.get.return_value.status_code = 200
    mock_requests.post.return_value.status_code = 500

    result = RecallBotController("test-bot-id").send_chat_message("host test", "everyone test")
    assert not result


@patch("deepinsights.core.integrations.meetingbot.recall_ai.requests")
def test_send_chat_message_success_localhost_replacement(mock_requests: MagicMock) -> None:
    mock_requests.post.return_value.status_code = 200
    mock_requests.get.return_value.status_code = 200
    mock_requests.get.return_value.json.return_value = {"meeting_url": {"platform": "zoom"}}

    result = RecallBotController("test-bot-id").send_chat_message(None, "localhost test")
    assert result
    mock_requests.post.assert_has_calls(
        [
            call(
                "https://api.recall.ai/api/v1/bot/test-bot-id/send_chat_message/",
                headers=ANY,
                json={"to": "everyone", "message": "example.com test"},
            ),
        ]
    )
