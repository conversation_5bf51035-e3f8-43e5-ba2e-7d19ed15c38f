import datetime
import logging
from typing import Any
from unittest.mock import MagicMock, PropertyMock, patch

import pytest
from pytest_django.fixtures import SettingsWrapper

from deepinsights.core.integrations.oauth.microsoft_dynamics import MicrosoftDynamicsOAuth
from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.users.models.user import User


@patch("deepinsights.core.integrations.oauth.microsoft_dynamics.msal.ConfidentialClientApplication")
def test_initialization(mock_app: MagicMock, settings: SettingsWrapper) -> None:
    settings.MSAL_CLIENT_ID = "client_id"
    settings.MSAL_CLIENT_SECRET = "client_secret"

    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    mock_app.assert_called_with(client_id="client_id", client_credential="client_secret")
    assert oauth.resource_url == resource_url
    assert oauth.scopes == [f"{resource_url}/user_impersonation"]


@patch("deepinsights.core.integrations.oauth.microsoft_dynamics.msal.ConfidentialClientApplication")
def test_initialization_fallback_on_exception(mock_app: MagicMock, settings: SettingsWrapper) -> None:
    """Test that initialization falls back to MICROSOFT_DYNAMICS_* settings when MSAL_* settings cause an exception."""
    settings.MSAL_CLIENT_ID = "main_client_id"
    settings.MSAL_CLIENT_SECRET = "main_client_secret"
    settings.MICROSOFT_DYNAMICS_CLIENT_ID = "fallback_client_id"
    settings.MICROSOFT_DYNAMICS_CLIENT_SECRET = "fallback_client_secret"

    # Make the first call (with MSAL_* settings) raise an exception
    # The second call (with fallback settings) should succeed
    mock_app.side_effect = [Exception("MSAL initialization failed"), MagicMock()]

    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    assert mock_app.call_count == 2

    first_call = mock_app.call_args_list[0]
    assert first_call[1]["client_id"] == "main_client_id"
    assert first_call[1]["client_credential"] == "main_client_secret"

    second_call = mock_app.call_args_list[1]
    assert second_call[1]["client_id"] == "fallback_client_id"
    assert second_call[1]["client_credential"] == "fallback_client_secret"

    assert oauth.resource_url == resource_url
    assert oauth.scopes == [f"{resource_url}/user_impersonation"]


def test_client_settings(settings: SettingsWrapper) -> None:
    settings.MICROSOFT_DYNAMICS_CLIENT_ID = "client_id"
    settings.MICROSOFT_DYNAMICS_CLIENT_SECRET = "client_secret"

    assert MicrosoftDynamicsOAuth.client_id() == "client_id"
    assert MicrosoftDynamicsOAuth.client_secret() == "client_secret"


@patch("deepinsights.core.integrations.oauth.microsoft_dynamics.msal.ConfidentialClientApplication")
def test_exchange_tokens_error(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    mock_app.return_value.acquire_token_by_authorization_code.side_effect = Exception("Error")

    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    with pytest.raises(Exception, match="Error exchanging tokens for Microsoft Dynamics"):
        oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")


@patch("deepinsights.core.integrations.oauth.microsoft_dynamics.msal.ConfidentialClientApplication")
def test_exchange_tokens_response_error(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    mock_app.return_value.acquire_token_by_authorization_code.return_value = {"error": "Error"}

    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    with pytest.raises(Exception, match="Error exchanging tokens"):
        oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")


@pytest.mark.parametrize(
    "response_data,error_match",
    [
        (
            {"refresh_token": "refresh_token", "expires_in": 1},
            None,  # Missing access_token
        ),
        (
            {"access_token": "access_token", "expires_in": 1},
            "Error exchanging tokens for Microsoft Dynamics",  # Missing refresh_token
        ),
        (
            {"access_token": "access_token", "refresh_token": "refresh_token"},
            None,  # Missing expires_in
        ),
    ],
)
@patch("deepinsights.core.integrations.oauth.microsoft_dynamics.msal.ConfidentialClientApplication")
def test_exchange_tokens_invalid_responses(
    mock_app: MagicMock, django_user_model: User, response_data: dict[str, Any], error_match: str
) -> None:
    """Test that exchange_tokens raises appropriate exceptions for invalid responses."""
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    mock_app.return_value.acquire_token_by_authorization_code.return_value = response_data

    # If error_match is provided, test for that specific error message
    # Otherwise just test that some exception is raised
    if error_match:
        with pytest.raises(Exception, match=error_match):
            oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")
    else:
        with pytest.raises(Exception):
            oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")


@patch("deepinsights.core.integrations.oauth.microsoft_dynamics.msal.ConfidentialClientApplication")
def test_exchange_tokens(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)
    expiry = datetime.datetime.now(datetime.timezone.utc)

    mock_acquire = mock_app.return_value.acquire_token_by_authorization_code
    mock_acquire.return_value = {
        "access_token": "access_token",
        "refresh_token": "refresh_token",
        "expires_in": 100,
    }

    oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")

    mock_acquire.assert_called_once_with(
        "auth_code",
        scopes=[f"{resource_url}/user_impersonation"],
        redirect_uri="http://localhost/redirect",
    )

    credentials = OAuthCredentials.objects.get(user=user, integration="microsoft_dynamics")
    assert credentials.access_token == "access_token"
    assert credentials.refresh_token == "refresh_token"
    assert credentials.scope == f"{resource_url}/user_impersonation"
    assert abs(credentials.expires_in - (expiry + datetime.timedelta(seconds=100))) < datetime.timedelta(seconds=2)
    assert credentials.refresh_token_expires_in > expiry


@patch("deepinsights.core.integrations.oauth.microsoft_dynamics.msal.ConfidentialClientApplication")
def test_exchange_tokens_with_scopes(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    mock_acquire = mock_app.return_value.acquire_token_by_authorization_code
    mock_acquire.return_value = {
        "access_token": "access_token",
        "refresh_token": "refresh_token",
        "expires_in": 100,
        "scope": "custom_scope",
    }

    oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")

    credentials = OAuthCredentials.objects.get(user=user, integration="microsoft_dynamics")
    assert credentials.scope == "custom_scope"


@patch("deepinsights.core.integrations.oauth.microsoft_dynamics.msal.ConfidentialClientApplication")
def test_exchange_tokens_with_resource(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    mock_acquire = mock_app.return_value.acquire_token_by_authorization_code
    mock_acquire.return_value = {
        "access_token": "access_token",
        "refresh_token": "refresh_token",
        "expires_in": 100,
        "resource": "https://custom.dynamics.com",
    }

    oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")


@patch("deepinsights.core.integrations.oauth.microsoft_dynamics.msal.ConfidentialClientApplication")
def test_exchange_tokens_updating_existing_token(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    OAuthCredentials.objects.create(
        user=user,
        integration="microsoft_dynamics",
        access_token="old_access_token",
        refresh_token="old_refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    expiry = datetime.datetime.now(datetime.timezone.utc)

    mock_acquire = mock_app.return_value.acquire_token_by_authorization_code
    mock_acquire.return_value = {
        "access_token": "access_token",
        "refresh_token": "refresh_token",
        "expires_in": 100,
        "scope": "user_impersonation",
        "resource": "https://different-dynamics.example.com",
    }

    oauth.exchange_tokens("auth_code", user, "http://localhost/redirect")

    credentials = OAuthCredentials.objects.get(user=user, integration="microsoft_dynamics")
    assert credentials.access_token == "access_token"
    assert credentials.refresh_token == "refresh_token"
    assert credentials.scope == "user_impersonation"
    assert abs(credentials.expires_in - (expiry + datetime.timedelta(seconds=100))) < datetime.timedelta(seconds=2)
    assert credentials.refresh_token_expires_in > expiry


def test_get_access_token_does_not_exist(django_user_model: User, caplog: pytest.LogCaptureFixture) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    caplog.set_level(logging.INFO)
    assert not oauth.get_access_token(user)
    assert "No Microsoft Dynamics credentials found for user" in caplog.text


@patch("deepinsights.core.integrations.oauth.microsoft_dynamics.OAuthCredentials")
def test_get_access_token_error(
    mock_oauth_credentials: MagicMock, django_user_model: User, caplog: pytest.LogCaptureFixture
) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    # Setup the mock to raise an Exception when accessing expires_in
    mock_credentials = MagicMock()
    mock_credentials.expires_in = PropertyMock(side_effect=Exception("Error"))
    mock_oauth_credentials.objects.get.return_value = mock_credentials

    # Set up DoesNotExist as a proper exception
    mock_oauth_credentials.DoesNotExist = OAuthCredentials.DoesNotExist

    caplog.set_level(logging.ERROR)
    with pytest.raises(Exception, match="Error getting access token"):
        oauth.get_access_token(user)
    assert "Error getting Microsoft Dynamics access token" in caplog.text


def test_get_access_token(django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    OAuthCredentials.objects.create(
        user=user,
        integration="microsoft_dynamics",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=1),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    assert oauth.get_access_token(user) == "access_token"


@patch("deepinsights.core.integrations.oauth.microsoft_dynamics.msal.ConfidentialClientApplication")
def test_get_access_token_refresh_exception(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    OAuthCredentials.objects.create(
        user=user,
        integration="microsoft_dynamics",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    mock_app.return_value.acquire_token_by_refresh_token.side_effect = Exception("Error")

    with pytest.raises(Exception, match="Error getting access token"):
        oauth.get_access_token(user)


@patch("deepinsights.core.integrations.oauth.microsoft_dynamics.msal.ConfidentialClientApplication")
def test_get_access_token_refresh_response_error(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    OAuthCredentials.objects.create(
        user=user,
        integration="microsoft_dynamics",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    mock_app.return_value.acquire_token_by_refresh_token.return_value = {"error": "Error"}

    with pytest.raises(Exception, match="Error getting access token"):
        oauth.get_access_token(user)


@patch("deepinsights.core.integrations.oauth.microsoft_dynamics.msal.ConfidentialClientApplication")
def test_get_access_token_refresh(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    OAuthCredentials.objects.create(
        user=user,
        integration="microsoft_dynamics",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    mock_app.return_value.acquire_token_by_refresh_token.return_value = {
        "access_token": "new_access_token",
        "expires_in": 100,
    }

    assert oauth.get_access_token(user) == "new_access_token"
    credentials = OAuthCredentials.objects.get(user=user, integration="microsoft_dynamics")
    assert credentials.access_token == "new_access_token"
    assert credentials.refresh_token == "refresh_token"  # Should remain unchanged
    assert credentials.scope == ""  # Should remain unchanged
    assert abs(
        credentials.expires_in - (datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(seconds=100))
    ) < datetime.timedelta(seconds=2)


@patch("deepinsights.core.integrations.oauth.microsoft_dynamics.msal.ConfidentialClientApplication")
def test_get_access_token_refresh_update_refresh_and_scopes(mock_app: MagicMock, django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    OAuthCredentials.objects.create(
        user=user,
        integration="microsoft_dynamics",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc),
    )

    mock_app.return_value.acquire_token_by_refresh_token.return_value = {
        "access_token": "new_access_token",
        "expires_in": 100,
        "refresh_token": "new_refresh_token",
        "scope": "new_scope",
    }

    assert oauth.get_access_token(user) == "new_access_token"
    credentials = OAuthCredentials.objects.get(user=user, integration="microsoft_dynamics")
    assert credentials.refresh_token == "new_refresh_token"
    assert credentials.scope == "new_scope"


def test_check_integration_status_does_not_exist(django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    assert not oauth.check_integration_status(user)


def test_check_integration_status_expired(django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    OAuthCredentials.objects.create(
        user=user,
        integration="microsoft_dynamics",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=1),
    )

    assert not oauth.check_integration_status(user)


def test_check_integration_status_valid(django_user_model: User) -> None:
    user = django_user_model.objects.create_user(username="<EMAIL>", email="<EMAIL>")
    resource_url = "https://dynamics.example.com"
    oauth = MicrosoftDynamicsOAuth(resource_url=resource_url)

    OAuthCredentials.objects.create(
        user=user,
        integration="microsoft_dynamics",
        access_token="access_token",
        refresh_token="refresh_token",
        scope="",
        expires_in=datetime.datetime.now(datetime.timezone.utc),
        refresh_token_expires_in=datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=30),
    )

    assert oauth.check_integration_status(user)
