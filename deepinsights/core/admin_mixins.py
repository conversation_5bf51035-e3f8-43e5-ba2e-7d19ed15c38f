import base64
import logging

from django import forms
from django.contrib import admin, messages
from django.core.files.uploadedfile import UploadedFile
from django.db.models.query import QuerySet
from django.http import HttpRequest
from django.shortcuts import redirect, render

from deepinsights.core.preferences.preferences import Preferences
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.tasks import update_bots_for_calendar_events
from deepinsights.users.models.user import User


def _validate_is_jpeg(value):  # type: ignore[no-untyped-def]
    if value.content_type != "image/jpeg":
        raise forms.ValidationError("Only JPEG image files are allowed.")


logger = logging.getLogger(__name__)


class BotPreferencesHandler:
    """Utility class to handle bot preferences operations."""

    @staticmethod
    def process_images(
        not_recording_image: UploadedFile | None = None, recording_image: UploadedFile | None = None
    ) -> tuple[str | None, str | None]:
        """
        Convert uploaded images to base64 strings.

        Args:
            not_recording_image: UploadedFile instance for `not recording` image
            recording_image: UploadedFile instance for `recording` image

        Returns:
            tuple[str | None, str | None]: Tuple of base64 encoded strings

        Note: This method assumes that the images are in JPEG format and the code
        calling this method does the check for the same.

        """
        base64_not_recording = None
        base64_recording = None

        try:
            if not_recording_image:
                not_recording_image_data = not_recording_image.read()
                base64_not_recording = base64.b64encode(not_recording_image_data).decode("utf-8")

            if recording_image:
                recording_image_data = recording_image.read()
                base64_recording = base64.b64encode(recording_image_data).decode("utf-8")

        except Exception as e:
            logger.error("Error processing images", exc_info=e)
            raise

        return base64_not_recording, base64_recording

    @staticmethod
    def update_preferences(
        obj: User | Organization,
        base64_not_recording: str | None = None,
        base64_recording: str | None = None,
        notetaker_name: str | None = None,
    ) -> tuple[bool, str | None]:
        """
        Update bot preferences for an object.

        Args:
            obj: User or Organization Object with preferences attribute
            base64_not_recording: Base64 encoded not recording image
            base64_recording: Base64 encoded recording image
            notetaker_name: Name for the notetaker

        Returns:
            tuple[bool, str | None]: (success, error_message)
        """
        try:
            preferences = Preferences.from_dict(obj.preferences or {})

            # Update images if provided
            if base64_not_recording:
                preferences.bot_preferences.not_recording_image_b64 = base64_not_recording
                logger.info("Updated not_recording_image_b64 in bot_preferences")
            if base64_recording:
                preferences.bot_preferences.recording_image_b64 = base64_recording
                logger.info("Updated recording_image_b64 in bot_preferences")

            # Update notetaker name if provided
            if notetaker_name:
                preferences.bot_preferences.notetaker_name = notetaker_name
                logger.info("Updated notetaker_name in bot_preferences")

            obj.preferences = preferences.to_dict()
            obj.save()

            return True, None

        except Exception as e:
            error_msg = "Error updating preferences: %s" % str(e)
            logger.error(error_msg)
            return False, error_msg

    @classmethod
    def process_and_update(
        cls,
        obj: User | Organization,
        not_recording_image: UploadedFile | None = None,
        recording_image: UploadedFile | None = None,
        notetaker_name: str | None = None,
    ) -> tuple[bool, str | None]:
        """
        Process images and update preferences in one operation.

        Args:
            obj: Object with preferences attribute
            not_recording_image: UploadedFile instance for not recording image
            recording_image: UploadedFile instance for recording image
            notetaker_name: Name for the notetaker

        Returns:
            tuple[bool, str | None]: (success, error_message)
        """
        try:
            # Process images
            base64_not_recording, base64_recording = cls.process_images(not_recording_image, recording_image)

            # Update preferences
            return cls.update_preferences(obj, base64_not_recording, base64_recording, notetaker_name)

        except Exception as e:
            error_msg = "Error processing and updating preferences: %s" % str(e)
            logger.error(error_msg)
            return False, error_msg


class UpdateBotPreferencesForm(forms.Form):
    not_recording_image = forms.ImageField(
        label="Not Recording Image",
        required=False,
        help_text="Allowed file types: .jpg, .jpeg",
        validators=[_validate_is_jpeg],
    )
    recording_image = forms.ImageField(
        label="Recording Image",
        required=False,
        help_text="Allowed file types: .jpg, .jpeg",
        validators=[_validate_is_jpeg],
    )
    notetaker_name = forms.CharField(label="Notetaker Name", required=False)


class BotPreferencesMixin(admin.ModelAdmin):  # type: ignore[type-arg]
    def __update_auto_join_bots(self, user: User) -> bool:
        """Refresh all of the bots"""
        if not (recall_calendar_id := user.recall_calendar_id):
            logger.info(
                "Attempted to update auto-join bots for user without recall_calendar_id: user %s", str(user.uuid)
            )
            return False
        update_bots_for_calendar_events.delay(recall_calendar_id, None, user.uuid, force_update=True)
        return True

    @admin.action(description="Update auto-join calendar events (including bot image)")
    def update_auto_join_calendar_events(self, request: HttpRequest, queryset: QuerySet[Organization] | QuerySet[User]):  # type: ignore[no-untyped-def]
        any_updated = False
        for obj in queryset:
            for user in obj.users.all() if isinstance(obj, Organization) else [obj]:
                updated = self.__update_auto_join_bots(user)
                any_updated = any_updated or updated

        message = (
            "No calendar event bots needed to be updated: auto-join not enabled for any relevant entities."
            if not any_updated
            else (
                "Successfully triggered scheduled calendar event bot updates for auto-join meetings. "
                "The changes may take a few minutes to reflect (the updates are happening in a "
                "Celery task)."
            )
        )
        self.message_user(request, message)

    @admin.action(description="Update bot preferences")
    def update_bot_preferences(self, request: HttpRequest, queryset: QuerySet[Organization] | QuerySet[User]):  # type: ignore[no-untyped-def]
        logger.info("update_bot_preferences called with request method: %s", request.method)

        if "apply" in request.POST:
            logger.info("Processing form submission")
            form = UpdateBotPreferencesForm(request.POST, request.FILES)
            if form.is_valid():
                logger.info("Form is valid, processing data")

                success_count = 0
                error_count = 0

                for obj in queryset:
                    logger.info("Processing %s: %s", self.model.__name__, obj)
                    success, error = BotPreferencesHandler.process_and_update(
                        obj,
                        not_recording_image=form.cleaned_data.get("not_recording_image"),
                        recording_image=form.cleaned_data.get("recording_image"),
                        notetaker_name=form.cleaned_data.get("notetaker_name"),
                    )

                    if success:
                        success_count += 1
                    else:
                        error_count += 1
                        messages.error(request, "Error updating %s: %s" % (obj, error))

                if success_count > 0:
                    self.message_user(
                        request,
                        "Successfully updated bot preferences for %s %s."
                        % (success_count, self.model._meta.verbose_name_plural),
                    )
                if error_count > 0:
                    messages.warning(
                        request, "Failed to update %s %s." % (error_count, self.model._meta.verbose_name_plural)
                    )

                return redirect(request.get_full_path())
            else:
                logger.warning("Form is invalid. Errors: %s", form.errors)
                messages.error(request, "Please correct the errors below.")
        else:
            logger.info("Displaying initial form")
            form = UpdateBotPreferencesForm()

        context = {
            "opts": self.model._meta,
            "form": form,
            "action": "update_bot_preferences",
            "title": f"Update Bot Preferences for {self.model._meta.verbose_name}",
            "objects": queryset,
        }
        logger.debug("Rendering template with context: %s", context)
        return render(request, "admin/update_bot_preferences.html", context)
