from unittest.mock import MagicMock, call, patch

from django.http import HttpRequest

from deepinsights.core.admin_mixins import BotPreferencesMixin
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.users.models.user import User


@patch("deepinsights.core.admin_mixins.update_bots_for_calendar_events.delay")
@patch.object(BotPreferencesMixin, "message_user")
def test_update_auto_join_calendar_events_user_no_update(mock_message_user, mock_delay, django_user_model: User):  # type: ignore[no-untyped-def]
    admin_mixin = BotPreferencesMixin(User, admin_site=MagicMock())
    request = HttpRequest()
    django_user_model.objects.create(username="testUser")

    admin_mixin.update_auto_join_calendar_events(request, django_user_model.objects.all())

    mock_message_user.assert_called_once_with(
        request, "No calendar event bots needed to be updated: auto-join not enabled for any relevant entities."
    )
    mock_delay.assert_not_called()


@patch("deepinsights.core.admin_mixins.update_bots_for_calendar_events.delay")
@patch.object(BotPreferencesMixin, "message_user")
def test_update_auto_join_calendar_events_user_update(mock_message_user, mock_delay, django_user_model: User):  # type: ignore[no-untyped-def]
    admin_mixin = BotPreferencesMixin(User, admin_site=MagicMock())
    request = HttpRequest()
    user = django_user_model.objects.create(username="testUser")
    user.recall_calendar_id = "calendar_id"
    user.save()

    user_two = django_user_model.objects.create(username="testUser2")
    user_two.recall_calendar_id = "calendar_id_two"
    user_two.save()

    django_user_model.objects.create(username="testUser3")

    admin_mixin.update_auto_join_calendar_events(request, django_user_model.objects.all().order_by("pk"))

    mock_message_user.assert_called_once_with(
        request,
        "Successfully triggered scheduled calendar event bot updates for auto-join meetings. "
        "The changes may take a few minutes to reflect (the updates are happening in a "
        "Celery task).",
    )
    assert mock_delay.call_count == 2
    mock_delay.assert_has_calls(
        [
            call("calendar_id", None, user.uuid, force_update=True),
            call("calendar_id_two", None, user_two.uuid, force_update=True),
        ]
    )


@patch("deepinsights.core.admin_mixins.update_bots_for_calendar_events.delay")
@patch.object(BotPreferencesMixin, "message_user")
def test_update_auto_join_calendar_events_org_no_update(mock_message_user, mock_delay, django_user_model: User):  # type: ignore[no-untyped-def]
    admin_mixin = BotPreferencesMixin(User, admin_site=MagicMock())
    request = HttpRequest()
    org = Organization.objects.create(name="testOrg")
    django_user_model.objects.create(username="testUser", organization=org)
    django_user_model.objects.create(username="testUser2", organization=org)

    admin_mixin.update_auto_join_calendar_events(request, Organization.objects.all())

    mock_message_user.assert_called_once_with(
        request, "No calendar event bots needed to be updated: auto-join not enabled for any relevant entities."
    )
    mock_delay.assert_not_called()


@patch("deepinsights.core.admin_mixins.update_bots_for_calendar_events.delay")
@patch.object(BotPreferencesMixin, "message_user")
def test_update_auto_join_calendar_events_org_update(mock_message_user, mock_delay, django_user_model: User):  # type: ignore[no-untyped-def]
    admin_mixin = BotPreferencesMixin(User, admin_site=MagicMock())
    request = HttpRequest()
    org = Organization.objects.create(name="testOrg")
    org_user_with_calendar = django_user_model.objects.create(username="testUser", organization=org)
    org_user_with_calendar.recall_calendar_id = "calendar_id"
    org_user_with_calendar.save()

    django_user_model.objects.create(username="testUser2", organization=org)

    org_two = Organization.objects.create(name="testOrg2")
    org_user_two = django_user_model.objects.create(username="testUser3", organization=org_two)
    org_user_two.recall_calendar_id = "calendar_id_two"
    org_user_two.save()

    admin_mixin.update_auto_join_calendar_events(request, Organization.objects.filter(pk=org.pk))

    mock_message_user.assert_called_once_with(
        request,
        "Successfully triggered scheduled calendar event bot updates for auto-join meetings. "
        "The changes may take a few minutes to reflect (the updates are happening in a "
        "Celery task).",
    )
    mock_delay.assert_called_once_with("calendar_id", None, org_user_with_calendar.uuid, force_update=True)
    mock_message_user.reset_mock()
    mock_delay.reset_mock()

    admin_mixin.update_auto_join_calendar_events(request, Organization.objects.all().order_by("pk"))
    mock_message_user.assert_called_once_with(
        request,
        "Successfully triggered scheduled calendar event bot updates for auto-join meetings. "
        "The changes may take a few minutes to reflect (the updates are happening in a "
        "Celery task).",
    )
    assert mock_delay.call_count == 2
    mock_delay.assert_has_calls(
        [
            call("calendar_id", None, org_user_with_calendar.uuid, force_update=True),
            call("calendar_id_two", None, org_user_two.uuid, force_update=True),
        ]
    )
