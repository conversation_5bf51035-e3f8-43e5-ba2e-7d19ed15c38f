import datetime
import json
import logging
import os
import uuid
from datetime import timed<PERSON><PERSON>
from typing import Any
from unittest.mock import ANY, As<PERSON><PERSON>ock, MagicMock, call, patch

import pytest
import redis
from deepgram._types import PrerecordedTranscriptionResponse
from django.test import TestCase, TransactionTestCase, override_settings
from django.utils import timezone
from pytest_django.fixtures import SettingsWrapper

from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.core.integrations.crm.crm_models import CRMAccount
from deepinsights.core.ml.models import Summary, TasksAndTakeaways
from deepinsights.core.preferences.preferences import NotificationPreferences, Preferences, get_default_preferences
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.audio_buffer import AudioBuffer
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.client_interaction import ClientInteraction
from deepinsights.meetingsapp.models.client_recap import C<PERSON><PERSON><PERSON>p
from deepinsights.meetingsapp.models.meeting_bot import MeetingBot
from deepinsights.meetingsapp.models.meeting_summary_email_template import MeetingSummaryEmailTemplate
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.mid_meeting_nudge import MidMeetingNudge, MidMeetingNudgeStatus
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.oauth_credentials import OAuthCredentials
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.prompt import Prompt
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.meetingsapp.models.search_query import SearchQuery
from deepinsights.meetingsapp.models.structured_meeting_data import (
    StructuredMeetingData,
    StructuredMeetingDataTemplate,
    StructuredMeetingDataTemplateRule,
)
from deepinsights.meetingsapp.models.structured_meeting_data_schema import StructuredMeetingDataSchema
from deepinsights.meetingsapp.models.task import Task
from deepinsights.meetingsapp.tasks import (
    _generate_client_intelligence,
    _process_note_common,
    create_advisor_notes_and_fill_with_client_intelligence,
    delete_old_transcripts,
    delete_organization_data_task,
    email_waitlisted_user_report,
    export_org_metrics,
    generate_agenda,
    generate_template_data,
    locked_task,
    process_bot_recording,
    process_client_recap,
    process_note_recording,
    process_phone_call_recording,
    process_structured_data_templates,
    reconcile_calendar_events,
    reprocess_note,
    reprocess_note_after_swapping,
    send_agenda_nudges,
    sync_crm_clients,
    update_autojoin_bots,
    update_bots_for_calendar_events,
)
from deepinsights.users.models.user import User


class SendAgendaNudgeTests(TestCase):
    def setUp(self) -> None:
        self.org1 = Organization.objects.create(name="Organization 1", transcript_ttl=30)
        self.notification_preferences = NotificationPreferences(mid_meeting_nudge_minutes_before_end=5)
        self.preferences = Preferences(notification_preferences=self.notification_preferences)
        self.user1 = User.objects.create(
            username="user1", organization=self.org1, preferences=self.preferences.to_json()
        )
        client_meeting_type = MeetingType.objects.create(name="Client Meeting", category="client")
        self.note1 = Note.objects.create(
            note_owner=self.user1,
            created=datetime.datetime(2025, 5, 1, 14, 0),
            metadata={"meeting_type": "client"},
            meeting_type=client_meeting_type,
            status=Note.PROCESSING_STATUS.scheduled,
        )

        self.bot = MeetingBot.objects.create(recall_bot_id="test-meeting-bot", bot_owner=self.user1, note=self.note1)

        self.event1 = ScheduledEvent.objects.create(
            user=self.user1,
            shared_source_id="id_one",
            user_specific_source_id="user_specific_id_one",
            start_time=timezone.make_aware(datetime.datetime(2025, 5, 1, 14, 0)),
            end_time=timezone.make_aware(datetime.datetime(2025, 5, 1, 15, 0)),
            source_data={"meeting_urls": ["test", "test2"]},
            note=self.note1,
        )

        self.not_processed_nudge = MidMeetingNudge.objects.create(
            scheduled_time=timezone.make_aware(datetime.datetime(2025, 5, 1, 14, 57)),
            status=MidMeetingNudgeStatus.NOT_PROCESSED,
            user=self.user1,
            note=self.note1,
        )

    @patch("deepinsights.meetingsapp.tasks._send_agenda_nudge_to_chat")
    @patch("deepinsights.meetingsapp.tasks.Flags")
    def test_send_nudge_no_send_if_user_not_enabled(self, flag_mock: MagicMock, nudge_mock: MagicMock) -> None:
        flag_mock.EnableMidMeetingNudge.is_active_for_user.return_value = False
        send_agenda_nudges()
        nudge_mock.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._get_interim_transcript_for_note")
    @patch("deepinsights.meetingsapp.tasks._get_outstanding_topics_from_agenda")
    @patch("deepinsights.meetingsapp.tasks._send_agenda_nudge_to_chat")
    @patch("deepinsights.meetingsapp.tasks.timezone")
    @patch("deepinsights.meetingsapp.tasks.Flags")
    def test_send_nudge_no_send_if_note_complete(
        self,
        flag_mock: MagicMock,
        tz_mock: MagicMock,
        nudge_mock: MagicMock,
        topics_mock: MagicMock,
        transcript_mock: MagicMock,
    ) -> None:
        flag_mock.EnableMidMeetingNudge.is_active_for_user.return_value = True
        self.note1.status = Note.PROCESSING_STATUS.finalized
        self.note1.save()
        tz_mock.now.return_value = timezone.make_aware(datetime.datetime(2025, 5, 1, 14, 56))
        send_agenda_nudges()
        nudge_mock.assert_not_called()
        topics_mock.assert_not_called()
        transcript_mock.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._get_interim_transcript_for_note")
    @patch("deepinsights.meetingsapp.tasks._get_outstanding_topics_from_agenda")
    @patch("deepinsights.meetingsapp.tasks._send_agenda_nudge_to_chat")
    @patch("deepinsights.meetingsapp.tasks.timezone")
    @patch("deepinsights.meetingsapp.tasks.Flags")
    def test_send_nudge_no_send_if_before_meeting_start(
        self,
        flag_mock: MagicMock,
        tz_mock: MagicMock,
        nudge_mock: MagicMock,
        topics_mock: MagicMock,
        transcript_mock: MagicMock,
    ) -> None:
        flag_mock.EnableMidMeetingNudge.is_active_for_user.return_value = True
        tz_mock.now.return_value = timezone.make_aware(datetime.datetime(2025, 5, 1, 12, 0))
        send_agenda_nudges()
        nudge_mock.assert_not_called()
        topics_mock.assert_not_called()
        transcript_mock.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._get_interim_transcript_for_note")
    @patch("deepinsights.meetingsapp.tasks._get_outstanding_topics_from_agenda")
    @patch("deepinsights.meetingsapp.tasks._send_agenda_nudge_to_chat")
    @patch("deepinsights.meetingsapp.tasks.timezone")
    @patch("deepinsights.meetingsapp.tasks.Flags")
    def test_send_nudge_no_send_if_after_meeting_end_schedule(
        self,
        flag_mock: MagicMock,
        tz_mock: MagicMock,
        nudge_mock: MagicMock,
        topics_mock: MagicMock,
        transcript_mock: MagicMock,
    ) -> None:
        flag_mock.EnableMidMeetingNudge.is_active_for_user.return_value = True
        tz_mock.now.return_value = timezone.make_aware(datetime.datetime(2025, 5, 3, 16, 0))
        send_agenda_nudges()
        nudge_mock.assert_not_called()
        topics_mock.assert_not_called()
        transcript_mock.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._get_interim_transcript_for_note")
    @patch("deepinsights.meetingsapp.tasks._get_outstanding_topics_from_agenda")
    @patch("deepinsights.meetingsapp.tasks._send_agenda_nudge_to_chat")
    @patch("deepinsights.meetingsapp.tasks.timezone")
    @patch("deepinsights.meetingsapp.tasks.Flags")
    def test_send_nudge_no_send_if_before_nudge_time(
        self,
        flag_mock: MagicMock,
        tz_mock: MagicMock,
        nudge_mock: MagicMock,
        topics_mock: MagicMock,
        transcript_mock: MagicMock,
    ) -> None:
        flag_mock.EnableMidMeetingNudge.is_active_for_user.return_value = True
        tz_mock.now.return_value = timezone.make_aware(datetime.datetime(2025, 5, 1, 14, 30))
        send_agenda_nudges()
        nudge_mock.assert_not_called()
        topics_mock.assert_not_called()
        transcript_mock.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._get_interim_transcript_for_note")
    @patch("deepinsights.meetingsapp.tasks._get_outstanding_topics_from_agenda")
    @patch("deepinsights.meetingsapp.tasks._send_agenda_nudge_to_chat")
    @patch("deepinsights.meetingsapp.tasks.timezone")
    @patch("deepinsights.meetingsapp.tasks.Flags")
    def test_send_nudge_no_send_if_no_bot(
        self,
        flag_mock: MagicMock,
        tz_mock: MagicMock,
        nudge_mock: MagicMock,
        topics_mock: MagicMock,
        transcript_mock: MagicMock,
    ) -> None:
        flag_mock.EnableMidMeetingNudge.is_active_for_user.return_value = True
        tz_mock.now.return_value = timezone.make_aware(datetime.datetime(2025, 5, 1, 14, 57))
        transcript_mock.return_value = None
        self.bot.delete()
        send_agenda_nudges()
        nudge_mock.assert_not_called()
        transcript_mock.assert_called_once_with(self.note1, self.user1)
        topics_mock.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._get_interim_transcript_for_note")
    @patch("deepinsights.meetingsapp.tasks._get_outstanding_topics_from_agenda")
    @patch("deepinsights.meetingsapp.tasks._send_agenda_nudge_to_chat")
    @patch("deepinsights.meetingsapp.tasks.timezone")
    @patch("deepinsights.meetingsapp.tasks.Flags")
    def test_send_nudge_send_if_criteria_met(
        self,
        flag_mock: MagicMock,
        tz_mock: MagicMock,
        nudge_mock: MagicMock,
        topics_mock: MagicMock,
        transcript_mock: MagicMock,
    ) -> None:
        transcript = [{"test": "test2"}]
        transcript_mock.return_value = str(transcript)
        long_topic_string = (
            "test length here greater than 500 but less than 4096 characters, to test message limitations"
            + "test length here greater than 500 but less than 4096 characters, to test message limitations"
            + "test length here greater than 500 but less than 4096 characters, to test message limitations"
            + "test length here greater than 500 but less than 4096 characters, to test message limitations"
            + "test length here greater than 500 but less than 4096 characters, to test message limitations"
        )

        base_link = os.environ.get("APP_DOMAIN")
        link = f"{base_link}/notes/create/{str(self.note1.uuid)}?noteID={str(self.note1.uuid)}&tab=prep"
        base_message = "Your meeting is nearing the end and not all agenda topics have been covered."
        full_message = f"{base_message}\nStill need to cover: {long_topic_string}\nFull agenda: {link}"

        topics_mock.return_value = long_topic_string
        flag_mock.EnableMidMeetingNudge.is_active_for_user.return_value = True
        tz_mock.now.return_value = timezone.make_aware(datetime.datetime(2025, 5, 1, 14, 57))
        send_agenda_nudges()
        nudge_mock.assert_called_once_with(self.bot, full_message, base_message)
        transcript_mock.assert_called_once_with(self.note1, self.user1)
        topics_mock.assert_called_once_with(self.note1, str(transcript))

    @patch("deepinsights.meetingsapp.models.MeetingBot.get_transcript")
    @patch("deepinsights.meetingsapp.tasks._send_agenda_nudge_to_chat")
    @patch("deepinsights.meetingsapp.tasks.process_transcript")
    @patch("deepinsights.meetingsapp.tasks.get_agenda_completion_info")
    @patch("deepinsights.meetingsapp.tasks.timezone")
    @patch("deepinsights.meetingsapp.tasks.Flags")
    def test_send_nudge_send_if_criteria_met_collaborators(
        self,
        flag_mock: MagicMock,
        tz_mock: MagicMock,
        agenda_info_mock: MagicMock,
        process_transcript_mock: MagicMock,
        nudge_send_mock: MagicMock,
        get_transcript_mock: MagicMock,
    ) -> None:
        long_topic_string = (
            "test length here greater than 500 but less than 4096 characters, to test message limitations"
            + "test length here greater than 500 but less than 4096 characters, to test message limitations"
            + "test length here greater than 500 but less than 4096 characters, to test message limitations"
            + "test length here greater than 500 but less than 4096 characters, to test message limitations"
            + "test length here greater than 500 but less than 4096 characters, to test message limitations"
        )
        get_transcript_mock.return_value = [{"speaker": {"placeholder"}}]
        process_transcript_mock.return_value = {"transcript": "processed"}
        agenda_info_mock.return_value = long_topic_string

        base_link = os.environ.get("APP_DOMAIN")
        link = f"{base_link}/notes/create/{str(self.note1.uuid)}?noteID={str(self.note1.uuid)}&tab=prep"
        base_message = "Your meeting is nearing the end and not all agenda topics have been covered."
        full_message = f"{base_message}\nStill need to cover: {long_topic_string}\nFull agenda: {link}"

        flag_mock.EnableMidMeetingNudge.is_active_for_user.return_value = True
        tz_mock.now.return_value = timezone.make_aware(datetime.datetime(2025, 5, 1, 14, 57))
        send_agenda_nudges()
        process_transcript_mock.assert_called_once()
        agenda_info_mock.assert_called_once()
        nudge_send_mock.assert_called_once_with(ANY, full_message, base_message)

    @patch("deepinsights.meetingsapp.tasks._get_interim_transcript_for_note")
    @patch("deepinsights.meetingsapp.tasks._get_outstanding_topics_from_agenda")
    @patch("deepinsights.meetingsapp.tasks._send_agenda_nudge_to_chat")
    @patch("deepinsights.meetingsapp.tasks.timezone")
    @patch("deepinsights.meetingsapp.tasks.Flags")
    def test_send_nudge_no_send_if_transcript_empty(
        self,
        flag_mock: MagicMock,
        tz_mock: MagicMock,
        nudge_mock: MagicMock,
        topics_mock: MagicMock,
        transcript_mock: MagicMock,
    ) -> None:
        transcript_mock.return_value = ""
        flag_mock.EnableMidMeetingNudge.is_active_for_user.return_value = True
        tz_mock.now.return_value = timezone.make_aware(datetime.datetime(2025, 5, 1, 14, 57))
        send_agenda_nudges()
        nudge_mock.assert_not_called()
        transcript_mock.assert_called_once_with(self.note1, self.user1)
        topics_mock.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._get_interim_transcript_for_note")
    @patch("deepinsights.meetingsapp.tasks._get_outstanding_topics_from_agenda")
    @patch("deepinsights.meetingsapp.tasks._send_agenda_nudge_to_chat")
    @patch("deepinsights.meetingsapp.tasks.timezone")
    @patch("deepinsights.meetingsapp.tasks.Flags")
    def test_send_nudge_no_send_if_all_topics_covered(
        self,
        flag_mock: MagicMock,
        tz_mock: MagicMock,
        nudge_mock: MagicMock,
        topics_mock: MagicMock,
        transcript_mock: MagicMock,
    ) -> None:
        transcript = [{"test": "test2"}]
        transcript_mock.return_value = str(transcript)
        topics_mock.return_value = ""
        flag_mock.EnableMidMeetingNudge.is_active_for_user.return_value = True
        tz_mock.now.return_value = timezone.make_aware(datetime.datetime(2025, 5, 1, 14, 57))
        send_agenda_nudges()
        nudge_mock.assert_not_called()
        transcript_mock.assert_called_once_with(self.note1, self.user1)
        topics_mock.assert_called_once_with(self.note1, str(transcript))
        # test we updated the DB nudge to status PROCESSED
        self.not_processed_nudge.refresh_from_db()
        assert self.not_processed_nudge.status == MidMeetingNudgeStatus.PROCESSED

    @patch("deepinsights.meetingsapp.tasks._get_interim_transcript_for_note")
    @patch("deepinsights.meetingsapp.tasks._get_outstanding_topics_from_agenda")
    @patch("deepinsights.meetingsapp.tasks._send_agenda_nudge_to_chat")
    @patch("deepinsights.meetingsapp.tasks.timezone")
    @patch("deepinsights.meetingsapp.tasks.Flags")
    def test_send_nudge_no_send_if_celery_already_sent(
        self,
        flag_mock: MagicMock,
        tz_mock: MagicMock,
        nudge_mock: MagicMock,
        topics_mock: MagicMock,
        transcript_mock: MagicMock,
    ) -> None:
        flag_mock.EnableMidMeetingNudge.is_active_for_user.return_value = True
        tz_mock.now.return_value = timezone.make_aware(datetime.datetime(2025, 5, 1, 14, 57))
        self.not_processed_nudge.status = MidMeetingNudgeStatus.PROCESSING
        self.not_processed_nudge.save()
        send_agenda_nudges()
        nudge_mock.assert_not_called()
        topics_mock.assert_not_called()
        transcript_mock.assert_not_called()


class DeleteOldTranscriptsTests(TestCase):
    def setUp(self) -> None:
        # Create test organizations
        self.org1 = Organization.objects.create(name="Organization 1", transcript_ttl=30)
        self.org2 = Organization.objects.create(name="Organization 2", transcript_ttl=30)
        self.org3 = Organization.objects.create(name="Organization 3", transcript_ttl=None)  # No TTL set
        self.org4 = Organization.objects.create(name="Organization 4", transcript_ttl=7)  # Short TTL
        self.org5 = Organization.objects.create(name="Organization 5", transcript_ttl=0)

        # Set up mock data for organizations
        self.user1 = User.objects.create(username="user1", organization=self.org1)
        self.user2 = User.objects.create(username="user2", organization=self.org2)
        self.user3 = User.objects.create(username="user3", organization=self.org3)
        self.user4 = User.objects.create(username="user4", organization=self.org4)
        self.user5 = User.objects.create(username="user5", organization=self.org5)

        # Create notes for Organization 1
        self.org1note1 = self.create_note(self.user1, 31)
        self.org1note2 = self.create_note(self.user1, 29)

        # Create notes for Organization 2
        self.org2note1 = self.create_note(self.user2, 31)
        self.org2note2 = self.create_note(self.user2, 29)

        # Create notes for Organization 3 (no TTL)
        self.org3note1 = self.create_note(self.user3, 100)

        # Create notes for Organization 4 (7-day TTL)
        self.org4note1 = self.create_note(self.user4, 8)
        self.org4note2 = self.create_note(self.user4, 6)

        # Create notes for Organization 5 (0-day TTL)
        self.org5note1 = self.create_note(self.user5, 1)  # 1 day old
        self.org5note2 = self.create_note(self.user5, 0)  # Created today

    def create_note(self, user: User, days_ago: float, meeting_type: MeetingType | None = None) -> Note:
        return Note.objects.create(
            note_owner=user,
            created=timezone.now() - timedelta(days=days_ago),
            metadata={"meeting_type": "client"},
            diarized_trans_with_names=f"Transcript for {user.username}",
            raw_transcript=f"Raw transcript for {user.username}",
            raw_asr_response={"text": f"ASR response for {user.username}"},
            meeting_type=meeting_type,
        )

    def test_delete_old_transcripts(self) -> None:
        # Run the function to delete old transcripts
        delete_old_transcripts()

        # Refresh all notes from the database
        for note in [
            self.org1note1,
            self.org1note2,
            self.org2note1,
            self.org2note2,
            self.org3note1,
            self.org4note1,
            self.org4note2,
            self.org4note2,
            self.org5note1,
            self.org5note2,
        ]:
            note.refresh_from_db()

        # Check Organization 1 (30-day TTL)
        self.assertIsNone(self.org1note1.diarized_trans_with_names)  # 31 days old, should be deleted
        self.assertIsNotNone(self.org1note2.diarized_trans_with_names)  # 29 days old, should not be deleted

        # Check Organization 2 (30-day TTL)
        self.assertIsNone(self.org2note1.diarized_trans_with_names)  # 31 days old, should be deleted
        self.assertIsNotNone(self.org2note2.diarized_trans_with_names)  # 29 days old, should not be deleted

        # Check Organization 3 (No TTL)
        self.assertIsNotNone(self.org3note1.diarized_trans_with_names)  # Should not be deleted regardless of age

        # Check Organization 4 (7-day TTL)
        self.assertIsNone(self.org4note1.diarized_trans_with_names)  # 8 days old, should be deleted
        self.assertIsNotNone(self.org4note2.diarized_trans_with_names)  # 6 days old, should not be deleted

        # Check Organization 5 (0-day TTL) [None of the transcripts should be deleted for org 5]
        self.assertIsNotNone(self.org5note1.diarized_trans_with_names)
        self.assertIsNotNone(self.org5note2.diarized_trans_with_names)

    def test_meeting_type_model_handling(self) -> None:
        client_meeting_type = MeetingType.objects.create(name="Client Meeting", category="client")
        internal_meeting_type = MeetingType.objects.create(name="Internal Meeting", category="internal")
        # The metadata meeting type is "internal" but the meeting type is set to a meeting with the
        # "client" category. This is intentional, to test the fallback behavior of meeting category
        # handling.
        note_with_meeting_type_and_metadata = Note.objects.create(
            note_owner=self.user1,
            created=timezone.now() - timedelta(days=30),
            metadata={"meeting_type": "internal"},
            diarized_trans_with_names="Transcript",
            meeting_type=client_meeting_type,
        )
        note_with_meeting_type = Note.objects.create(
            note_owner=self.user1,
            created=timezone.now() - timedelta(days=30),
            diarized_trans_with_names="Transcript",
            meeting_type=client_meeting_type,
        )
        note_with_metadata = Note.objects.create(
            note_owner=self.user1,
            created=timezone.now() - timedelta(days=30),
            metadata={"meeting_type": "client"},
            diarized_trans_with_names="Transcript",
            meeting_type=None,
        )
        # The metadata meeting type is "client" but the meeting type is set to a meeting with the
        # "internal" category. This is intentional, to test the fallback behavior of meeting
        # category handling.
        note_with_internal_meeting_type_and_metadata = Note.objects.create(
            note_owner=self.user1,
            created=timezone.now() - timedelta(days=30),
            metadata={"meeting_type": "client"},
            diarized_trans_with_names="Transcript",
            meeting_type=internal_meeting_type,
        )

        # Run the function to delete old transcripts
        delete_old_transcripts()

        # Refresh the note from the database
        note_with_meeting_type.refresh_from_db()
        note_with_meeting_type_and_metadata.refresh_from_db()
        note_with_metadata.refresh_from_db()
        note_with_internal_meeting_type_and_metadata.refresh_from_db()

        # Check that the note with the meeting type is not deleted if within TTL
        self.assertIsNone(note_with_meeting_type.diarized_trans_with_names)
        self.assertIsNone(note_with_meeting_type_and_metadata.diarized_trans_with_names)
        self.assertIsNone(note_with_metadata.diarized_trans_with_names)
        self.assertIsNotNone(note_with_internal_meeting_type_and_metadata.diarized_trans_with_names)


class TestProcessNoteCommon:
    def _assert_email_sent(
        self, mock_email_service: MagicMock, email_address: str, meeting_name: str = "Test Meeting"
    ) -> None:
        mock_email_service.send_email.assert_called_once_with(
            subject=f"Your Zeplyn notes for {meeting_name} are ready for review",
            body=f"<a href='https://example.com/notes/{self.note.uuid}'>Click here</a> to review your notes.",
            recipients=email_address,
            sender="Zeplyn <<EMAIL>>",
            attachments=[],
            is_html=True,
        )

    @pytest.fixture(autouse=True)
    def setUp(self, settings: SettingsWrapper) -> None:
        settings.APP_DOMAIN = "https://example.com"
        # Create Test Org
        self.org = Organization.objects.create(name="Test organization")

        # Create user
        self.user = User.objects.create(
            username="Test User",
            first_name="Test",
            last_name="User",
            email="<EMAIL>",
            password="testpass",
            organization=self.org,
        )
        self.user.user_context = "Test user context"
        user_preferences = self.user.get_preferences()
        user_preferences.notification_preferences.meeting_processed_notification_enabled = True
        self.user.preferences = user_preferences.to_dict()
        self.user.save()

        # Create meeting follow-up
        self.meeting_type = MeetingType.objects.create(name="Client Meeting", category="client")
        schema_definition = StructuredMeetingDataSchema.objects.create(name="test", schema={"type": "object"})
        self.structured_meeting_data_template = StructuredMeetingDataTemplate.objects.create(
            title="Follow-up", schema_definition=schema_definition
        )
        rule = StructuredMeetingDataTemplateRule.objects.create(everyone=True)
        rule.meeting_types.add(self.meeting_type)
        rule.follow_up_templates.add(self.structured_meeting_data_template)
        rule.save()

        # Create a note
        self.note = Note.objects.create(
            note_owner=self.user,
            status=Note.PROCESSING_STATUS.uploaded,
            metadata={
                "user_context": "Test user context",
                "meeting_name": "Test Meeting",
            },
            diarized_trans_with_names="",
            raw_transcript="",
            raw_asr_response={},
            meeting_type=self.meeting_type,
        )
        self.note.authorized_users.add(self.user)
        self.note.save()

        self.attendee1 = Attendee.objects.create(attendee_name="Jon Doe", note=self.note)
        self.attendee2 = Attendee.objects.create(attendee_name="Jane Doe", note=self.note)
        self.attendee3 = Attendee.objects.create(attendee_name="Bob Doe", note=self.note)

        # Mock raw_asr_response for "deepgram" ASR process
        self.mock_raw_asr_response_deepgram = PrerecordedTranscriptionResponse(
            request_id="request",
            metadata={
                "duration": 30.5  # 30.5 seconds duration
            },
            results={
                "utterances": [
                    {"speaker": 0, "start": 0.0, "end": 5.2, "transcript": "Hello, this is Jon Doe speaking."},
                    {
                        "speaker": 1,
                        "start": 5.5,
                        "end": 10.8,
                        "transcript": "Hi Jon, Jane Doe here. How are you today?",
                    },
                    {
                        "speaker": 0,
                        "start": 11.2,
                        "end": 15.7,
                        "transcript": "I'm doing well, thanks for asking. How about you?",
                    },
                    {
                        "speaker": 2,
                        "start": 16.0,
                        "end": 22.5,
                        "transcript": "Hello everyone, Bob Doe joining in. Sorry I'm a bit late.",
                    },
                    {
                        "speaker": 1,
                        "start": 23.0,
                        "end": 30.0,
                        "transcript": "No problem, Bob. We're just getting started. Shall we begin the meeting?",
                    },
                ]
            },
        )

    @patch("deepinsights.meetingsapp.tasks.process_transcript")
    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.compute_task_due_date")
    @patch("deepinsights.meetingsapp.tasks.process_structured_data_templates")
    @patch("deepinsights.meetingsapp.tasks.email_service")
    def test_success(
        self,
        mock_email_service: MagicMock,
        mock_process_structured_data_templates: MagicMock,
        mock_compute_task_due_date: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
        mock_process_transcript: MagicMock,
    ) -> None:
        mock_process_transcript.return_value = {
            "transcript": 'speaker0\t"0.0-->5.2\nHello, this is Jon Doe speaking.\n\nspeaker1\t"5.5-->10.8\nHi Jon, Jane Doe here. How are you today?\n\nspeaker0\t"11.2-->15.7\nI\'m doing well, thanks for asking. How about you?\n\nspeaker2\t"16.0-->22.5\nHello everyone, Bob Doe joining in. Sorry I\'m a bit late.\n\nspeaker1\t"23.0-->30.0\nNo problem, Bob. We\'re just getting started. Shall we begin the meeting?\n\n',
            "raw_transcript": [
                {
                    "speaker": "speaker0",
                    "start": "0:00:00",
                    "end": "0:00:05.200000",
                    "utt": "Hello, this is Jon Doe speaking.",
                },
                {
                    "speaker": "speaker1",
                    "start": "0:00:05.500000",
                    "end": "0:00:10.800000",
                    "utt": "Hi Jon, Jane Doe here. How are you today?",
                },
                {
                    "speaker": "speaker0",
                    "start": "0:00:11.200000",
                    "end": "0:00:15.700000",
                    "utt": "I'm doing well, thanks for asking. How about you?",
                },
                {
                    "speaker": "speaker2",
                    "start": "0:00:16",
                    "end": "0:00:22.500000",
                    "utt": "Hello everyone, Bob Doe joining in. Sorry I'm a bit late.",
                },
                {
                    "speaker": "speaker1",
                    "start": "0:00:23",
                    "end": "0:00:30",
                    "utt": "No problem, Bob. We're just getting started. Shall we begin the meeting?",
                },
            ],
            "meeting_duration": 30,
            "speaker_mapping": {"speaker0": "Jon Doe", "speaker1": "Jane Doe", "speaker2": "Bob Doe"},
            "speaker_percentage": {"speaker0": 32.1, "speaker1": 42.6, "speaker2": 25.3},
        }
        tasks_and_takeaways = TasksAndTakeaways.model_validate(
            {
                "key_takeaways": ["All attendees joined the call", "Meeting about to begin"],
                "keywords": ["greeting", "late", "start", "meeting"],
                "action_items": [
                    {"description": "Begin the meeting", "assignee": "Jon Doe", "due_date": "2023-10-30"},
                ],
                "advisor_notes": ["3 peoples in the meeting"],
            }
        )
        mock_get_tasks_and_takeaways.return_value = (tasks_and_takeaways, True)
        summary = Summary.model_validate(
            {
                "sections": [
                    {
                        "topic": "Meeting Summary",
                        "bullets": ["Greetings exchanged", "Bob joined late", "Meeting ready to start"],
                    }
                ]
            }
        )
        mock_get_summary.return_value = (summary, True)
        mock_compute_task_due_date.return_value = timezone.now() + timedelta(days=7)

        creation_time = self.note.created

        _process_note_common(self.note, self.user, self.mock_raw_asr_response_deepgram, "deepgram")

        # Assert that the note was updated correctly
        assert self.note.created == creation_time
        assert self.note.diarized_trans_with_names == mock_process_transcript.return_value["transcript"]
        assert self.note.raw_transcript == mock_process_transcript.return_value["raw_transcript"]
        assert self.note.key_takeaways == tasks_and_takeaways.key_takeaways
        assert self.note.advisor_notes == tasks_and_takeaways.advisor_notes
        assert self.note.summary == summary.model_dump()
        assert self.note.metadata == {
            "user_context": "Test user context",
            "meeting_name": "Test Meeting",
            "meeting_duration": 30,
            "tags": ["greeting", "late", "start", "meeting"],
        }

        assert self.note.metadata["meeting_duration"] == mock_process_transcript.return_value["meeting_duration"]
        assert self.note.status == Note.PROCESSING_STATUS.processed
        assert not self.note.data_source

        # Assert that Tasks were created
        tasks = Task.objects.filter(note=self.note)
        assert tasks.count() == len(tasks_and_takeaways.action_items)
        for task, expected_title in zip(tasks, [a.description for a in tasks_and_takeaways.action_items]):
            assert task.task_title == expected_title
            assert task.task_owner == self.user

        # Assert that follow-ups were created and deleted
        mock_process_structured_data_templates.delay_on_commit.assert_called_once_with(
            self.note.uuid,
            self.user.uuid,
            'Jon Doe\t"0.0-->5.2\nHello, this is Jon Doe speaking.\n\nJane Doe\t"5.5-->10.8\nHi Jon, Jane Doe here. How are you today?\n\nJon Doe\t"11.2-->15.7\nI\'m doing well, thanks for asking. How about you?\n\nBob Doe\t"16.0-->22.5\nHello everyone, Bob Doe joining in. Sorry I\'m a bit late.\n\nJane Doe\t"23.0-->30.0\nNo problem, Bob. We\'re just getting started. Shall we begin the meeting?\n\n',
        )

        self._assert_email_sent(mock_email_service, self.user.email)

    @pytest.mark.parametrize(
        "has_tasks_and_takeaways_content, has_summary_content",
        [
            (True, True),
            (True, False),
            (False, True),
        ],
        ids=["both", "only_tasks_and_takeaways", "only_summary"],
    )
    @patch("deepinsights.meetingsapp.tasks.process_transcript")
    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.process_structured_data_templates")
    @patch("deepinsights.meetingsapp.tasks.email_service")
    def test_summary_and_tasks_and_takeaways_not_both_empty(
        self,
        mock_email_service: MagicMock,
        mock_process_structured_data_templates: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
        mock_process_transcript: MagicMock,
        has_tasks_and_takeaways_content: bool,
        has_summary_content: bool,
    ) -> None:
        mock_process_transcript.return_value = {
            "transcript": "Empty transcript",
            "raw_transcript": "Empty transcript",
            "meeting_duration": 0,
        }
        mock_get_tasks_and_takeaways.return_value = (
            TasksAndTakeaways.model_validate(
                {
                    "key_takeaways": [],
                    "action_items": [],
                    "advisor_notes": [],
                    "keywords": [],
                }
            ),
            has_tasks_and_takeaways_content,
        )
        mock_get_summary.return_value = (Summary.model_validate({}), has_summary_content)

        creation_time = self.note.created

        _process_note_common(self.note, self.user, self.mock_raw_asr_response_deepgram, "deepgram")

        assert self.note.created == creation_time
        assert self.note.diarized_trans_with_names == mock_process_transcript.return_value["transcript"]
        assert self.note.raw_transcript == mock_process_transcript.return_value["raw_transcript"]
        assert self.note.metadata == {
            "user_context": "Test user context",
            "meeting_name": "Test Meeting",
            "meeting_duration": 0,
            "tags": [],
        }
        assert self.note.status == Note.PROCESSING_STATUS.processed
        assert not Task.objects.filter(note=self.note).exists()

        mock_process_structured_data_templates.delay_on_commit.assert_called_once()
        self._assert_email_sent(mock_email_service, self.user.email)

    @patch("deepinsights.meetingsapp.tasks.process_transcript")
    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.process_structured_data_templates")
    @patch("deepinsights.meetingsapp.tasks.email_service")
    def test_empty_llm_generated_summary_and_tasks_and_takeaways(
        self,
        mock_email_service: MagicMock,
        mock_process_structured_data_templates: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
        mock_process_transcript: MagicMock,
    ) -> None:
        mock_process_transcript.return_value = {
            "transcript": "Empty transcript",
            "raw_transcript": "Empty transcript",
            "meeting_duration": 0,
        }
        mock_get_tasks_and_takeaways.return_value = (
            TasksAndTakeaways.model_validate(
                {
                    "key_takeaways": [],
                    "action_items": [],
                    "advisor_notes": [],
                    "keywords": [],
                }
            ),
            True,
        )
        mock_get_summary.return_value = (Summary.model_validate({"sections": []}), True)

        creation_time = self.note.created

        _process_note_common(self.note, self.user, self.mock_raw_asr_response_deepgram, "deepgram")

        assert self.note.created == creation_time
        assert self.note.diarized_trans_with_names == mock_process_transcript.return_value["transcript"]
        assert self.note.raw_transcript == mock_process_transcript.return_value["raw_transcript"]
        assert self.note.metadata == {
            "user_context": "Test user context",
            "meeting_name": "Test Meeting",
            "meeting_duration": 0,
            "tags": [],
        }
        assert self.note.status == Note.PROCESSING_STATUS.processed
        assert not Task.objects.filter(note=self.note).exists()

        mock_process_structured_data_templates.delay_on_commit.assert_called_once()
        self._assert_email_sent(mock_email_service, self.user.email)

    @patch("deepinsights.meetingsapp.tasks.process_transcript")
    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.process_structured_data_templates")
    @patch("deepinsights.meetingsapp.tasks.email_service")
    def test_no_response(
        self,
        mock_email_service: MagicMock,
        mock_process_structured_data_templates: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
        mock_process_transcript: MagicMock,
    ) -> None:
        mock_process_transcript.return_value = {
            "transcript": "Empty transcript",
            "raw_transcript": "Empty transcript",
            "meeting_duration": 0,
        }
        mock_get_tasks_and_takeaways.return_value = (TasksAndTakeaways.model_validate({}), False)
        mock_get_summary.return_value = (Summary.model_validate({}), False)

        creation_time = self.note.created

        _process_note_common(self.note, self.user, self.mock_raw_asr_response_deepgram, "deepgram")

        assert self.note.created == creation_time
        assert self.note.status == Note.PROCESSING_STATUS.processed
        assert self.note.diarized_trans_with_names == mock_process_transcript.return_value["transcript"]
        assert self.note.raw_transcript == mock_process_transcript.return_value["raw_transcript"]
        assert self.note.metadata == {
            "user_context": "Test user context",
            "meeting_name": "Test Meeting",
            "meeting_duration": 0,
            "tags": ["Empty note"],
        }
        assert self.note.summary == {
            "sections": [
                {
                    "topic": "Empty meeting",
                    "bullets": ["Nothing was discussed in this meeting."],
                }
            ]
        }
        assert Task.objects.filter(note=self.note).count() == 0

        mock_process_structured_data_templates.delay_on_commit.assert_not_called()
        mock_email_service.send_email.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.process_transcript")
    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.process_structured_data_templates")
    @patch("deepinsights.meetingsapp.tasks.email_service")
    def test_email_empty_meeting_name(
        self,
        mock_email_service: MagicMock,
        mock_process_structured_data_templates: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
        mock_process_transcript: MagicMock,
    ) -> None:
        metadata = self.note.metadata or {}
        del metadata["meeting_name"]
        self.note.metadata = metadata
        self.note.save()

        mock_process_transcript.return_value = {
            "transcript": "Transcript",
            "raw_transcript": "Transcript",
            "meeting_duration": 30,
        }
        mock_get_tasks_and_takeaways.return_value = (
            TasksAndTakeaways.model_validate(
                {
                    "key_takeaways": [],
                    "action_items": [],
                    "advisor_notes": [],
                    "keywords": [],
                }
            ),
            True,
        )
        mock_get_summary.return_value = (Summary.model_validate({"sections": []}), True)

        _process_note_common(self.note, self.user, self.mock_raw_asr_response_deepgram, "deepgram")

        mock_process_structured_data_templates.delay_on_commit.assert_called_once()
        self._assert_email_sent(mock_email_service, self.user.email, meeting_name="your recent meeting")

    @patch("deepinsights.meetingsapp.tasks.process_transcript")
    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.process_structured_data_templates")
    @patch("deepinsights.meetingsapp.tasks.email_service")
    def test_no_email_for_no_authorized_users(
        self,
        mock_email_service: MagicMock,
        mock_process_structured_data_templates: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
        mock_process_transcript: MagicMock,
    ) -> None:
        self.note.authorized_users.clear()
        self.note.save()

        mock_process_transcript.return_value = {
            "transcript": "Transcript",
            "raw_transcript": "Transcript",
            "meeting_duration": 30,
        }
        mock_get_tasks_and_takeaways.return_value = (
            TasksAndTakeaways.model_validate(
                {
                    "key_takeaways": [],
                    "action_items": [],
                    "advisor_notes": [],
                    "keywords": [],
                }
            ),
            True,
        )
        mock_get_summary.return_value = (Summary.model_validate({"sections": []}), True)

        _process_note_common(self.note, self.user, self.mock_raw_asr_response_deepgram, "deepgram")

        mock_email_service.send_email.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.process_transcript")
    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.process_structured_data_templates")
    @patch("deepinsights.meetingsapp.tasks.email_service")
    def test_no_email_for_authorized_user_with_notification_disabled(
        self,
        mock_email_service: MagicMock,
        mock_process_structured_data_templates: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
        mock_process_transcript: MagicMock,
    ) -> None:
        self.user.preferences = get_default_preferences()
        self.user.save()

        mock_process_transcript.return_value = {
            "transcript": "Transcript",
            "raw_transcript": "Transcript",
            "meeting_duration": 30,
        }
        mock_get_tasks_and_takeaways.return_value = (
            TasksAndTakeaways.model_validate(
                {
                    "key_takeaways": [],
                    "action_items": [],
                    "advisor_notes": [],
                    "keywords": [],
                }
            ),
            True,
        )
        mock_get_summary.return_value = (Summary.model_validate({"sections": []}), True)

        _process_note_common(self.note, self.user, self.mock_raw_asr_response_deepgram, "deepgram")

        mock_email_service.send_email.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.process_transcript")
    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.process_structured_data_templates")
    @patch("deepinsights.meetingsapp.tasks.email_service")
    def test_process_structured_data_emails_for_multiple_users(
        self,
        mock_email_service: MagicMock,
        mock_process_structured_data_templates: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
        mock_process_transcript: MagicMock,
        django_user_model: User,
    ) -> None:
        user_two = django_user_model.objects.create(username="testusertwo", email="<EMAIL>")
        user_preferences = user_two.get_preferences()
        user_preferences.notification_preferences.meeting_processed_notification_enabled = True
        user_two.preferences = user_preferences.to_dict()
        user_two.save()
        user_three = User.objects.create(username="testuserthree", email="<EMAIL>")
        self.note.authorized_users.add(user_two, user_three)
        transcript = 'Jon Doe\t"0.0-->5.2\nHello, this is Jon Doe speaking.\n\nJane Doe\t"5.5-->10.8\nHi Jon, Jane Doe here. How are you today?'
        self.note.diarized_trans_with_names = transcript
        self.note.save()

        process_structured_data_templates(self.note.uuid, self.user.uuid, transcript)

        mock_process_transcript.return_value = {
            "transcript": "Transcript",
            "raw_transcript": "Transcript",
            "meeting_duration": 30,
        }
        mock_get_tasks_and_takeaways.return_value = (
            TasksAndTakeaways.model_validate(
                {
                    "key_takeaways": [],
                    "action_items": [],
                    "advisor_notes": [],
                    "keywords": [],
                }
            ),
            True,
        )
        mock_get_summary.return_value = (Summary.model_validate({"sections": []}), True)

        _process_note_common(self.note, self.user, self.mock_raw_asr_response_deepgram, "deepgram")

        assert mock_email_service.send_email.call_count == 2
        mock_email_service.send_email.assert_has_calls(
            [
                call(
                    subject="Your Zeplyn notes for Test Meeting are ready for review",
                    body=f"<a href='https://example.com/notes/{self.note.uuid}'>Click here</a> to review your notes.",
                    recipients=self.user.email,
                    sender="Zeplyn <<EMAIL>>",
                    attachments=[],
                    is_html=True,
                ),
                call(
                    subject="Your Zeplyn notes for Test Meeting are ready for review",
                    body=f"<a href='https://example.com/notes/{self.note.uuid}'>Click here</a> to review your notes.",
                    recipients=user_two.email,
                    sender="Zeplyn <<EMAIL>>",
                    attachments=[],
                    is_html=True,
                ),
            ],
            any_order=True,
        )


class UpdateBotsForCalendarEventsTests(TestCase):
    @patch("deepinsights.meetingsapp.tasks.RecallBotController")
    def test_update_bots_for_calendar_events_no_user(self, mock_bot_controller: MagicMock) -> None:
        mock_bot_controller.update_bots_for_calendar.return_value = (True, [])
        result = update_bots_for_calendar_events.apply(
            (
                "test_calendar_id",
                None,
                uuid.uuid4(),
            ),
            {
                "force_update": False,
            },
        )
        self.assertFalse(result.successful())
        mock_bot_controller.update_bots_for_calendar.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.RecallBotController")
    def test_update_bots_for_calendar_events_simple_success(self, mock_bot_controller: MagicMock) -> None:
        user = User.objects.create(username="testuser", email="<EMAIL>")
        calendar_id = "test_calendar_id"
        mock_bot_controller.update_bots_for_calendar.return_value = (True, [])

        result = update_bots_for_calendar_events.apply(
            (
                calendar_id,
                None,
                user.uuid,
            ),
            {
                "force_update": False,
            },
        )

        self.assertTrue(result.successful())
        mock_bot_controller.update_bots_for_calendar.assert_called_once_with(
            calendar_id=calendar_id,
            last_updated_ts=None,
            user=user,
            enable_live_transcription=True,
            enable_native_zoom_bot=False,
            event_ids_to_process=None,
            calendar_platform_ids_to_process=None,
            force_update=False,
        )

    @patch("deepinsights.meetingsapp.tasks.RecallBotController")
    def test_update_bots_for_calendar_events_success_after_incomplete(self, mock_bot_controller: MagicMock) -> None:
        user = User.objects.create(username="testuser", email="<EMAIL>")
        calendar_id = "test_calendar_id"
        mock_bot_controller.update_bots_for_calendar.side_effect = [(False, ["id"]), (True, [])]

        result = update_bots_for_calendar_events.apply(
            (
                calendar_id,
                None,
                user.uuid,
            ),
            {
                "force_update": False,
            },
        )

        self.assertTrue(result.successful())
        self.assertEqual(mock_bot_controller.update_bots_for_calendar.call_count, 2)
        mock_bot_controller.update_bots_for_calendar.assert_has_calls(
            [
                call(
                    calendar_id=calendar_id,
                    last_updated_ts=None,
                    user=user,
                    enable_live_transcription=True,
                    enable_native_zoom_bot=False,
                    event_ids_to_process=None,
                    calendar_platform_ids_to_process=None,
                    force_update=False,
                ),
                call(
                    calendar_id=calendar_id,
                    last_updated_ts=None,
                    user=user,
                    enable_live_transcription=True,
                    enable_native_zoom_bot=False,
                    event_ids_to_process=None,
                    calendar_platform_ids_to_process=None,
                    force_update=False,
                ),
            ],
        )

    @patch("deepinsights.meetingsapp.tasks.RecallBotController")
    def test_update_bots_for_calendar_events_success_after_partial_failure(
        self, mock_bot_controller: MagicMock
    ) -> None:
        user = User.objects.create(username="testuser", email="<EMAIL>")
        calendar_id = "test_calendar_id"
        mock_bot_controller.update_bots_for_calendar.side_effect = [(True, ["id"]), (True, [])]

        result = update_bots_for_calendar_events.apply(
            (
                calendar_id,
                None,
                user.uuid,
            ),
            {
                "force_update": False,
            },
        )

        self.assertTrue(result.successful())
        self.assertEqual(mock_bot_controller.update_bots_for_calendar.call_count, 2)
        mock_bot_controller.update_bots_for_calendar.assert_has_calls(
            [
                call(
                    calendar_id=calendar_id,
                    last_updated_ts=None,
                    user=user,
                    enable_live_transcription=True,
                    enable_native_zoom_bot=False,
                    event_ids_to_process=None,
                    calendar_platform_ids_to_process=None,
                    force_update=False,
                ),
                call(
                    calendar_id=calendar_id,
                    last_updated_ts=None,
                    user=user,
                    enable_live_transcription=True,
                    enable_native_zoom_bot=False,
                    event_ids_to_process=["id"],
                    calendar_platform_ids_to_process=None,
                    force_update=False,
                ),
            ],
        )

    @patch("deepinsights.meetingsapp.tasks.RecallBotController")
    def test_update_bots_for_calendar_events_failure(self, mock_bot_controller: MagicMock) -> None:
        user = User.objects.create(username="testuser", email="<EMAIL>")
        calendar_id = "test_calendar_id"
        mock_bot_controller.update_bots_for_calendar.side_effect = [
            (False, []),
            (False, ["id", "id2"]),
            (True, ["id", "id2"]),
            (True, ["id"]),
        ]

        with self.assertLogs(level="ERROR") as logs:
            result = update_bots_for_calendar_events.apply(
                (
                    calendar_id,
                    None,
                    user.uuid,
                ),
                {
                    "force_update": False,
                },
            )
            self.assertEqual(
                logs.output[:4],
                [
                    "ERROR:root:Failed to fully update bots for calendar test_calendar_id. Calendar was not fully processed. Failed event IDs: []",
                    "ERROR:root:Failed to fully update bots for calendar test_calendar_id. Calendar was not fully processed. Failed event IDs: ['id', 'id2']",
                    "ERROR:root:Failed to fully update bots for calendar test_calendar_id. Calendar was fully processed. Failed event IDs: ['id', 'id2']",
                    "ERROR:root:Failed to fully update bots for calendar test_calendar_id. Calendar was fully processed. Failed event IDs: ['id']",
                ],
            )

        self.assertFalse(result.successful())
        self.assertEqual(mock_bot_controller.update_bots_for_calendar.call_count, 4)
        mock_bot_controller.update_bots_for_calendar.assert_has_calls(
            [
                call(
                    calendar_id=calendar_id,
                    last_updated_ts=None,
                    user=user,
                    enable_live_transcription=True,
                    enable_native_zoom_bot=False,
                    event_ids_to_process=None,
                    calendar_platform_ids_to_process=None,
                    force_update=False,
                ),
                call(
                    calendar_id=calendar_id,
                    last_updated_ts=None,
                    user=user,
                    enable_live_transcription=True,
                    enable_native_zoom_bot=False,
                    event_ids_to_process=None,
                    calendar_platform_ids_to_process=None,
                    force_update=False,
                ),
                call(
                    calendar_id=calendar_id,
                    last_updated_ts=None,
                    user=user,
                    enable_live_transcription=True,
                    enable_native_zoom_bot=False,
                    event_ids_to_process=None,
                    calendar_platform_ids_to_process=None,
                    force_update=False,
                ),
                call(
                    calendar_id=calendar_id,
                    last_updated_ts=None,
                    user=user,
                    enable_live_transcription=True,
                    enable_native_zoom_bot=False,
                    event_ids_to_process=["id", "id2"],
                    calendar_platform_ids_to_process=None,
                    force_update=False,
                ),
            ],
        )

    @patch("deepinsights.meetingsapp.tasks.RecallBotController")
    def test_update_bots_for_calendar_events_force_update(self, mock_bot_controller: MagicMock) -> None:
        user = User.objects.create(username="testuser", email="<EMAIL>")
        calendar_id = "test_calendar_id"
        mock_bot_controller.update_bots_for_calendar.return_value = (True, [])

        result = update_bots_for_calendar_events.apply(
            (
                calendar_id,
                None,
                user.uuid,
            ),
            {
                "force_update": True,
            },
        )

        self.assertTrue(result.successful())
        mock_bot_controller.update_bots_for_calendar.assert_called_once_with(
            calendar_id=calendar_id,
            last_updated_ts=None,
            user=user,
            enable_live_transcription=True,
            enable_native_zoom_bot=False,
            event_ids_to_process=None,
            calendar_platform_ids_to_process=None,
            force_update=True,
        )

    @patch("deepinsights.meetingsapp.tasks.RecallBotController")
    def test_update_bots_for_calendar_events_passed_in_id_handling(self, mock_bot_controller: MagicMock) -> None:
        user = User.objects.create(username="testuser", email="<EMAIL>")
        calendar_id = "test_calendar_id"
        # First pass: full failure, so same sets of calendar platform and event IDs should be passed
        # Second pass: partial success, so only the failed event IDs should be passed and the calendar IDs should be dropped
        # Third pass: full success
        mock_bot_controller.update_bots_for_calendar.side_effect = [(False, []), (True, ["event_id_2"]), (True, [])]

        result = update_bots_for_calendar_events.apply(
            (
                calendar_id,
                None,
                user.uuid,
            ),
            {
                "force_update": False,
                "event_ids_to_process": ["event_id_1", "event_id_2"],
                "calendar_platform_ids_to_process": ["calendar_platform_id_1", "calendar_platform_id_2"],
            },
        )

        self.assertTrue(result.successful())
        mock_bot_controller.update_bots_for_calendar.assert_has_calls(
            [
                call(
                    calendar_id=calendar_id,
                    last_updated_ts=None,
                    user=user,
                    enable_live_transcription=True,
                    enable_native_zoom_bot=False,
                    event_ids_to_process=["event_id_1", "event_id_2"],
                    calendar_platform_ids_to_process=["calendar_platform_id_1", "calendar_platform_id_2"],
                    force_update=False,
                ),
                call(
                    calendar_id=calendar_id,
                    last_updated_ts=None,
                    user=user,
                    enable_live_transcription=True,
                    enable_native_zoom_bot=False,
                    event_ids_to_process=["event_id_1", "event_id_2"],
                    calendar_platform_ids_to_process=["calendar_platform_id_1", "calendar_platform_id_2"],
                    force_update=False,
                ),
                call(
                    calendar_id=calendar_id,
                    last_updated_ts=None,
                    user=user,
                    enable_live_transcription=True,
                    enable_native_zoom_bot=False,
                    event_ids_to_process=["event_id_2"],
                    calendar_platform_ids_to_process=None,
                    force_update=False,
                ),
            ]
        )

    @patch("deepinsights.meetingsapp.tasks.RecallBotController")
    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=True, CELERY_TASK_LOCK_TIMEOUT=1)
    def test_locking_on_failure(self, mock_cache: MagicMock, mock_bot_controller: MagicMock) -> None:
        calendar_id = "test_calendar_id"
        mock_bot_controller.update_bots_for_calendar.return_value = (True, [])
        test_uuid = uuid.uuid4()

        result = update_bots_for_calendar_events.apply(
            (
                calendar_id,
                None,
                test_uuid,
                None,
            ),
            {
                "force_update": False,
            },
        )

        self.assertFalse(result.successful())
        mock_cache.lock.assert_called_once_with(str(test_uuid), timeout=1)
        mock_bot_controller.update_bots_for_calendar.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.RecallBotController")
    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=True, CELERY_TASK_LOCK_TIMEOUT=1)
    def test_locking(self, mock_cache: MagicMock, mock_bot_controller: MagicMock) -> None:
        user = User.objects.create(username="testuser", email="<EMAIL>")
        calendar_id = "test_calendar_id"
        mock_bot_controller.update_bots_for_calendar.return_value = (True, [])

        result = update_bots_for_calendar_events.apply(
            (
                calendar_id,
                None,
                user.uuid,
            ),
            {
                "force_update": False,
            },
        )

        self.assertTrue(result.successful())
        mock_cache.lock.assert_called_once_with(str(user.uuid), timeout=1)

        mock_bot_controller.update_bots_for_calendar.assert_called_once_with(
            calendar_id=calendar_id,
            last_updated_ts=None,
            user=user,
            enable_live_transcription=True,
            enable_native_zoom_bot=False,
            event_ids_to_process=None,
            calendar_platform_ids_to_process=None,
            force_update=False,
        )

    @patch("deepinsights.meetingsapp.tasks.RecallBotController")
    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=False)
    def test_locking_disabled(self, mock_cache: MagicMock, mock_bot_controller: MagicMock) -> None:
        user = User.objects.create(username="testuser", email="<EMAIL>")
        calendar_id = "test_calendar_id"
        mock_bot_controller.update_bots_for_calendar.return_value = (True, [])

        result = update_bots_for_calendar_events.apply(
            (
                calendar_id,
                None,
                user.uuid,
            ),
            {
                "force_update": False,
            },
        )

        self.assertTrue(result.successful())

        mock_cache.lock.assert_not_called()
        mock_bot_controller.update_bots_for_calendar.assert_called_once_with(
            calendar_id=calendar_id,
            last_updated_ts=None,
            user=user,
            enable_live_transcription=True,
            enable_native_zoom_bot=False,
            event_ids_to_process=None,
            calendar_platform_ids_to_process=None,
            force_update=False,
        )


class ExportOrgMetricsTests(TestCase):
    """Test cases for organization metrics export functionality."""

    def setUp(self) -> None:
        self.mock_csv_files = ["metrics_1.csv", "metrics_2.csv"]
        self.env = "test_environment"

    @patch("deepinsights.meetingsapp.tasks.get_all_metrics")
    @patch("deepinsights.meetingsapp.tasks.send_email_with_attachments")
    @patch("os.environ.get")
    def test_export_org_metrics_success(
        self,
        mock_environ_get: MagicMock,
        mock_send_email: MagicMock,
        mock_get_metrics: MagicMock,
    ) -> None:
        """Test successful export of organization metrics."""
        # Set up mocks
        mock_environ_get.return_value = self.env
        mock_get_metrics.return_value = self.mock_csv_files

        # Run the export
        export_org_metrics()

        # Verify email was sent with correct parameters
        mock_send_email.assert_called_once_with(
            f"Weekly Org Metrics Reports for {self.env}",
            "Find the weekly metrics for the week in the attached CSV files:",
            [
                ("metrics_1.csv", f"{os.getcwd()}/metrics_1.csv"),
                ("metrics_2.csv", f"{os.getcwd()}/metrics_2.csv"),
            ],
        )

    @patch("deepinsights.meetingsapp.tasks.get_all_metrics")
    @patch("deepinsights.meetingsapp.tasks.send_email_with_attachments")
    @patch("os.environ.get")
    def test_export_org_metrics_no_files(
        self,
        mock_environ_get: MagicMock,
        mock_send_email: MagicMock,
        mock_get_metrics: MagicMock,
    ) -> None:
        """Test export when no metric files are generated."""
        # Set up mocks
        mock_environ_get.return_value = self.env
        mock_get_metrics.return_value = []

        # Run the export
        export_org_metrics()

        # Verify email was sent with empty attachments
        mock_send_email.assert_called_once_with(
            f"Weekly Org Metrics Reports for {self.env}",
            "Find the weekly metrics for the week in the attached CSV files:",
            [],
        )

    @patch("deepinsights.meetingsapp.tasks.get_all_metrics")
    @patch("deepinsights.meetingsapp.tasks.send_email_with_attachments")
    @patch("os.environ.get")
    def test_export_org_metrics_fallback_environment(
        self,
        mock_environ_get: MagicMock,
        mock_send_email: MagicMock,
        mock_get_metrics: MagicMock,
    ) -> None:
        """Test environment fallback when APP_DOMAIN is not set."""
        # Set up mocks
        mock_environ_get.side_effect = [
            None,
            "django.settings",
            None,
        ]  # Simulate APP_DOMAIN -> DJANGO_SETTINGS_MODULE -> None
        mock_get_metrics.return_value = self.mock_csv_files

        # Run the export
        export_org_metrics()

        # Verify email was sent with fallback environment
        mock_send_email.assert_called_once_with(
            "Weekly Org Metrics Reports for django.settings",
            "Find the weekly metrics for the week in the attached CSV files:",
            [
                ("metrics_1.csv", f"{os.getcwd()}/metrics_1.csv"),
                ("metrics_2.csv", f"{os.getcwd()}/metrics_2.csv"),
            ],
        )


class EmailWaitlistedUserReportTests(TestCase):
    """Test cases for waitlisted user report functionality."""

    def setUp(self) -> None:
        """Set up test data."""
        self.org1 = Organization.objects.create(name="Test Org 1")
        self.org2 = Organization.objects.create(name="Test Org 2")

        # Create test users with different statuses
        self.waitlisted_user1 = User.objects.create(
            username="wait1", email="<EMAIL>", status="waitlisted", organization=self.org1
        )
        self.waitlisted_user2 = User.objects.create(
            username="wait2", email="<EMAIL>", status="waitlisted", organization=self.org2
        )
        self.expired_user = User.objects.create(
            username="expired1", email="<EMAIL>", status="expired", organization=self.org1
        )
        self.active_user = User.objects.create(
            username="active1", email="<EMAIL>", status="active", organization=self.org1
        )

    @patch("deepinsights.meetingsapp.tasks.send_email_with_attachments")
    @patch("os.environ.get")
    def test_email_waitlisted_user_report_success(
        self,
        mock_environ_get: MagicMock,
        mock_send_email: MagicMock,
    ) -> None:
        """Test successful generation and sending of waitlisted user report."""
        mock_environ_get.return_value = "test_environment"

        # Run the report generation
        email_waitlisted_user_report()

        # Verify email was sent with correct parameters
        mock_send_email.assert_called_once()
        call_args = mock_send_email.call_args[1]

        self.assertEqual(call_args["subject"], "Waitlisted and Expired Users Report for test_environment")
        self.assertEqual(call_args["body"], "Please find attached the report of waitlisted and expired users.")
        self.assertEqual(len(call_args["attachments"]), 1)
        self.assertEqual(call_args["attachments"][0][0], "waitlisted_expired_users_report.csv")

    @patch("deepinsights.meetingsapp.tasks.send_email_with_attachments")
    @patch("os.environ.get")
    def test_email_waitlisted_user_report_no_users(
        self,
        mock_environ_get: MagicMock,
        mock_send_email: MagicMock,
    ) -> None:
        """Test report generation when no waitlisted/expired users exist."""
        # Delete all waitlisted and expired users
        User.objects.filter(status__in=["waitlisted", "expired"]).delete()

        mock_environ_get.return_value = "test_environment"

        # Run the report generation
        email_waitlisted_user_report()

        # Verify email was still sent (with empty report)
        mock_send_email.assert_called_once()

    @patch("deepinsights.meetingsapp.tasks.generate_csv_file")
    @patch("deepinsights.meetingsapp.tasks.send_email_with_attachments")
    def test_email_waitlisted_user_report_handles_file_generation_error(
        self,
        mock_send_email: MagicMock,
        mock_generate_csv: MagicMock,
    ) -> None:
        """Test error handling when CSV file generation fails."""
        mock_generate_csv.side_effect = Exception("Failed to generate CSV")

        # Run the report generation and expect it to raise the exception
        with self.assertRaises(Exception):
            email_waitlisted_user_report()

        # Verify email was not sent
        mock_send_email.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.send_email_with_attachments")
    def test_email_waitlisted_user_report_content(
        self,
        mock_send_email: MagicMock,
    ) -> None:
        """Test the content of the generated report."""
        email_waitlisted_user_report()

        # Get the generated CSV content
        mock_send_email.assert_called_once()
        call_args = mock_send_email.call_args[1]

        # Verify all waitlisted and expired users are included
        user_count = User.objects.filter(status__in=["waitlisted", "expired"]).count()
        self.assertEqual(user_count, 3)  # 2 waitlisted + 1 expired

        # Verify that active users are not included
        active_count = User.objects.filter(status="active").count()
        self.assertEqual(active_count, 1)


class TestReprocessNote(TestCase):
    def setUp(self) -> None:
        # Create Test Org
        self.org = Organization.objects.create(name="Test organization")

        # Create user
        self.user = User.objects.create(
            uuid=uuid.uuid4(),
            username="Test User",
            first_name="Test",
            last_name="User",
            email="<EMAIL>",
            password="testpass",
            metadata={"user_context": "Test user context"},
            organization=self.org,
        )

        # Use meeting type from conftest.py or create it if needed
        self.meeting_type, _ = MeetingType.objects.get_or_create(
            key="client", category="client", defaults={"name": "Client Meeting"}
        )
        schema_definition = StructuredMeetingDataSchema.objects.create(name="test", schema={"type": "object"})
        self.meeting_follow_up_template = StructuredMeetingDataTemplate.objects.create(
            title="Follow-up", schema_definition=schema_definition
        )
        rule = StructuredMeetingDataTemplateRule.objects.create(everyone=True)
        rule.meeting_types.add(self.meeting_type)
        rule.follow_up_templates.add(self.meeting_follow_up_template)
        rule.save()

        # Create a note
        self.note = Note.objects.create(
            uuid=uuid.uuid4(),
            note_owner=self.user,
            status=Note.PROCESSING_STATUS.uploaded,
            metadata={
                "user_context": "Test user context",
                "meeting_name": "Test Meeting",
                "tags": ["original", "tags"],
                "meeting_duration": 30,
            },
            diarized_trans_with_names='speaker0\t"0.0-->5.2\nHello, this is Jon speaking.\n\nspeaker1\t"5.5-->10.8\nHi Jon, Jane here. How are you today?',
            raw_transcript="Raw test transcript",
            advisor_notes=["Original advisor note"],
            key_takeaways=["Original takeaway"],
            summary={"summary": "original summary"},
            meeting_type=self.meeting_type,
        )

        Attendee.objects.create(attendee_name="Jon Doe", note=self.note, speaker_alias="speaker0")
        Attendee.objects.create(attendee_name="Jane Doe", note=self.note, speaker_alias="speaker1")

        self.task = Task.objects.create(note=self.note, task_title="Task 1", task_desc="Task 1 description")

        self.transcript_with_names = 'Jon Doe\t"0.0-->5.2\nHello, this is Jon speaking.\n\nJane Doe\t"5.5-->10.8\nHi Jon, Jane here. How are you today?'

    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.process_structured_data_templates")
    def test_reprocess_note_all_options(
        self,
        mock_process_structured_data_templates: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
    ) -> None:
        tasks_and_takeaways = TasksAndTakeaways.model_validate(
            {
                "advisor_notes": ["New advisor note"],
                "key_takeaways": ["New takeaway"],
                "keywords": ["new", "keywords"],
                "action_items": [
                    {"description": "New action 1", "assignee": "Jon Doe", "due_date": "2023-10-30"},
                    {"description": "New action 2", "assignee": "Jane Doe", "due_date": "2023-10-30"},
                ],
            }
        )
        mock_get_tasks_and_takeaways.return_value = (tasks_and_takeaways, True)
        summary = Summary.model_validate({"summary": [{"topic": "Topic", "bullets": ["New summary point"]}]})
        mock_get_summary.return_value = (summary, True)

        reprocess_note(self.note.uuid, tasks=True, summary=True, follow_ups=True)
        self.note.refresh_from_db()

        mock_get_tasks_and_takeaways.assert_called_once_with(self.note, self.transcript_with_names)
        mock_get_summary.assert_called_once_with(self.note, self.transcript_with_names)
        mock_process_structured_data_templates.delay_on_commit.assert_called_once_with(
            self.note.uuid, self.user.uuid, self.transcript_with_names
        )

        # Verify note was updated with new tasks data
        self.assertEqual(self.note.advisor_notes, ["New advisor note"])
        self.assertEqual(self.note.key_takeaways, ["New takeaway"])
        self.assertEqual((self.note.metadata or {})["tags"], ["new", "keywords"])

        # Verify summary was updated
        self.assertEqual(self.note.summary, summary.model_dump())

        # Verify tasks were updated
        tasks = Task.objects.filter(note=self.note)
        self.assertEqual(tasks.count(), 2)
        self.assertEqual(tasks[0].task_title, "New action 1")
        self.assertEqual(tasks[0].task_owner, self.user)
        self.assertEqual(tasks[1].task_title, "New action 2")
        self.assertEqual(tasks[1].task_owner, self.user)

        # Verify status was updated
        self.assertEqual(self.note.status, Note.PROCESSING_STATUS.processed)

        self.assertIsNone(self.note.data_source)

    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.process_structured_data_templates")
    def test_reprocess_note_empty_tasks_and_takeaways(
        self,
        mock_process_structured_data_templates: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
    ) -> None:
        # Set up task mock
        mock_get_tasks_and_takeaways.return_value = (TasksAndTakeaways(), False)

        # Store original values
        original_summary = self.note.summary

        reprocess_note(self.note.uuid, tasks=True, summary=False, follow_ups=False)
        self.note.refresh_from_db()

        # Verify mocks
        mock_get_tasks_and_takeaways.assert_called_once()
        mock_get_summary.assert_not_called()
        mock_process_structured_data_templates.delay_on_commit.assert_not_called()

        self.assertEqual(self.note.advisor_notes, [])
        self.assertEqual(self.note.key_takeaways, [])
        self.assertEqual((self.note.metadata or {})["tags"], [])

        # Verify summary was updated
        self.assertEqual(self.note.summary, original_summary)

        # Verify tasks were not created
        self.assertFalse(Task.objects.filter(note=self.note).exists())

        # Verify other fields were not updated
        self.assertEqual(self.note.summary, original_summary)
        self.assertEqual(StructuredMeetingData.objects.filter(note=self.note, is_deleted=True).count(), 0)

    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.process_structured_data_templates")
    def test_reprocess_note_only_tasks_and_takeaways(
        self,
        mock_process_structured_data_templates: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
    ) -> None:
        tasks_and_takeaways = TasksAndTakeaways.model_validate(
            {
                "advisor_notes": ["New advisor note"],
                "key_takeaways": ["New takeaway"],
                "keywords": ["new", "keywords"],
                "action_items": [
                    {"description": "New task 1", "assignee": "Jon Doe", "due_date": "2023-10-30"},
                ],
            }
        )

        mock_get_tasks_and_takeaways.return_value = (tasks_and_takeaways, True)

        original_summary = self.note.summary

        reprocess_note(self.note.uuid, tasks=True, summary=False, follow_ups=False)
        self.note.refresh_from_db()

        # Verify mocks
        mock_get_tasks_and_takeaways.assert_called_once()
        mock_get_summary.assert_not_called()
        mock_process_structured_data_templates.delay_on_commit.assert_not_called()

        # Verify only tasks-related fields were updated
        self.assertEqual(self.note.advisor_notes, ["New advisor note"])
        self.assertEqual(self.note.key_takeaways, ["New takeaway"])
        self.assertEqual((self.note.metadata or {})["tags"], ["new", "keywords"])

        # Verify task was created
        tasks = Task.objects.filter(note=self.note)
        self.assertEqual(tasks.count(), 1)
        self.assertEqual(tasks[0].task_title, "New task 1")

        # Verify other fields were not updated
        self.assertEqual(self.note.summary, original_summary)
        self.assertEqual(StructuredMeetingData.objects.filter(note=self.note, is_deleted=True).count(), 0)

    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.process_structured_data_templates")
    def test_reprocess_note_empty_summary(
        self,
        mock_process_structured_data_templates: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
    ) -> None:
        original_summary = self.note.summary
        new_summary = Summary.model_validate({"sections": []})
        mock_get_summary.return_value = (new_summary, False)

        # Store original values
        original_advisor_notes = self.note.advisor_notes
        original_key_takeaways = self.note.key_takeaways
        original_tags = (self.note.metadata or {})["tags"]

        reprocess_note(self.note.uuid, tasks=False, summary=True, follow_ups=False)
        self.note.refresh_from_db()

        # Verify mocks
        mock_get_summary.assert_called_once()
        mock_get_tasks_and_takeaways.assert_not_called()
        mock_process_structured_data_templates.delay_on_commit.assert_not_called()

        self.assertEqual(self.note.summary, new_summary.model_dump())
        self.assertEqual(self.note.advisor_notes, original_advisor_notes)
        self.assertEqual(self.note.key_takeaways, original_key_takeaways)
        self.assertEqual((self.note.metadata or {})["tags"], original_tags)
        self.assertEqual(list(Task.objects.filter(note=self.note)), [self.task])
        self.assertEqual(StructuredMeetingData.objects.filter(note=self.note, is_deleted=True).count(), 0)

    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.process_structured_data_templates")
    def test_reprocess_note_only_summary(
        self,
        mock_process_structured_data_templates: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
    ) -> None:
        # Set up summary mock
        new_summary = Summary.model_validate(
            {"sections": [{"topic": "New Topic", "bullets": ["New bullet point 1", "New bullet point 2"]}]}
        )
        mock_get_summary.return_value = (new_summary, True)

        # Store original values
        original_advisor_notes = self.note.advisor_notes
        original_key_takeaways = self.note.key_takeaways
        original_tags = (self.note.metadata or {})["tags"]

        reprocess_note(self.note.uuid, tasks=False, summary=True, follow_ups=False)
        self.note.refresh_from_db()

        # Verify mocks
        mock_get_summary.assert_called_once()
        mock_get_tasks_and_takeaways.assert_not_called()
        mock_process_structured_data_templates.delay_on_commit.assert_not_called()

        # Verify only summary was updated
        self.assertEqual(self.note.summary, new_summary.model_dump())

        # Verify other fields were not updated
        self.assertEqual(self.note.advisor_notes, original_advisor_notes)
        self.assertEqual(self.note.key_takeaways, original_key_takeaways)
        self.assertEqual((self.note.metadata or {})["tags"], original_tags)
        self.assertEqual(list(Task.objects.filter(note=self.note)), [self.task])
        self.assertEqual(StructuredMeetingData.objects.filter(note=self.note, is_deleted=True).count(), 0)

    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.process_structured_data_templates")
    def test_reprocess_note_only_follow_ups(
        self,
        mock_process_structured_data_templates: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
    ) -> None:
        # Create existing follow-up
        StructuredMeetingData.objects.create(title="Old Follow-up", schema={}, data={}, note=self.note)

        # Store original values
        original_summary = self.note.summary
        original_advisor_notes = self.note.advisor_notes
        original_key_takeaways = self.note.key_takeaways
        original_tags = (self.note.metadata or {})["tags"]

        reprocess_note(self.note.uuid, tasks=False, summary=False, follow_ups=True)

        # Verify mocks
        mock_process_structured_data_templates.delay_on_commit.assert_called_once_with(
            self.note.uuid, self.user.uuid, self.transcript_with_names
        )
        mock_get_summary.assert_not_called()
        mock_get_tasks_and_takeaways.assert_not_called()

        # Verify other fields were not updated
        self.assertEqual(self.note.summary, original_summary)
        self.assertEqual(self.note.advisor_notes, original_advisor_notes)
        self.assertEqual(self.note.key_takeaways, original_key_takeaways)
        self.assertEqual((self.note.metadata or {})["tags"], original_tags)
        self.assertEqual(list(Task.objects.filter(note=self.note)), [self.task])

    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=True, CELERY_TASK_LOCK_TIMEOUT=1)
    def test_reprocess_note_locking(self, mock_cache: MagicMock) -> None:
        reprocess_note.apply((self.note.uuid,), {"tasks": False, "summary": False, "follow_ups": False})

        mock_cache.lock.assert_called_once_with(str(self.note.uuid), timeout=1)
        self.note.refresh_from_db()
        self.assertEqual(self.note.status, Note.PROCESSING_STATUS.processed)

    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=False)
    def test_reprocess_note_locking_disabled(self, mock_cache: MagicMock) -> None:
        reprocess_note.apply((self.note.uuid,), {"tasks": False, "summary": False, "follow_ups": False})

        mock_cache.lock.assert_not_called()
        self.note.refresh_from_db()
        self.assertEqual(self.note.status, Note.PROCESSING_STATUS.processed)

    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=True, CELERY_TASK_LOCK_TIMEOUT=1)
    def test_reprocess_note_locking_failure(self, mock_cache: MagicMock) -> None:
        invalid_uuid = uuid.uuid4()
        reprocess_note.apply((invalid_uuid,), {"tasks": False, "summary": False, "follow_ups": False})

        mock_cache.lock.assert_called_once_with(str(invalid_uuid), timeout=1)
        self.note.refresh_from_db()
        self.assertEqual(self.note.status, Note.PROCESSING_STATUS.uploaded)


class SyncCRMClientsTests(TestCase):
    def setUp(self) -> None:
        self.maxDiff = None
        self.organization = Organization.objects.create(name="Test Organization")
        self.user = User.objects.create(
            username="testuser", email="<EMAIL>", organization=self.organization
        )

    @patch.object(User, "crm_handler")
    def test_sync_crm_clients_create_update(self, mock_crm_handler: MagicMock) -> None:
        second_user = User.objects.create(username="seconduser")
        client = Client.objects.create(
            crm_id="1",
            organization=self.organization,
            name="Old name",
            client_type="old type",
            crm_system="old system",
            first_name="Old first name",
            last_name="Old last name",
            email="<EMAIL>",
            phone_number="************",
        )
        client.authorized_users.add(second_user)
        client.save()
        mock_crm_handler.get_accounts_by_owner_email_and_name.return_value = [
            CRMAccount(
                crm_id="1",
                name="Client 1",
                first_name="Client",
                last_name="1",
                email="<EMAIL>",
                phone_number="************",
                client_type="client",
                crm_system="redtail",
            ),
            CRMAccount(crm_id="2", name="Client 2", client_type="prospect", crm_system="wealthbox"),
        ]

        sync_crm_clients(self.user.uuid)

        self.assertEqual(
            list(
                Client.objects.all().values(
                    "name",
                    "client_type",
                    "crm_system",
                    "first_name",
                    "last_name",
                    "email",
                    "phone_number",
                    "organization",
                )
            ),
            [
                {
                    "name": "Client 1",
                    "client_type": "client",
                    "crm_system": "redtail",
                    "first_name": "Client",
                    "last_name": "1",
                    "email": "<EMAIL>",
                    "phone_number": "+***********",
                    "organization": self.organization.pk,
                },
                {
                    "name": "Client 2",
                    "client_type": "prospect",
                    "crm_system": "wealthbox",
                    "first_name": None,
                    "last_name": None,
                    "email": None,
                    "phone_number": None,
                    "organization": self.organization.pk,
                },
            ],
        )
        client.refresh_from_db()
        self.assertEqual(set(client.authorized_users.all()), set([second_user, self.user]))
        self.assertEqual(list(Client.objects.exclude(pk=client.pk).get().authorized_users.all()), [self.user])
        mock_crm_handler.get_accounts_by_owner_email_and_name.assert_called_once_with(self.user.email)

    @patch.object(User, "crm_handler")
    def test_sync_crm_clients_no_crm_handler(self, mock_crm_handler: MagicMock) -> None:
        mock_crm_handler.return_value = None

        sync_crm_clients(self.user.uuid)

        self.assertEqual(Client.objects.all().count(), 0)

    @patch.object(User, "crm_handler")
    def test_sync_crm_clients_none_clients(self, mock_crm_handler: MagicMock) -> None:
        mock_crm_handler.get_accounts_by_owner_email_and_name.return_value = None

        sync_crm_clients(self.user.uuid)

        self.assertEqual(Client.objects.all().count(), 0)

    @patch.object(User, "crm_handler")
    def test_sync_crm_clients_no_clients(self, mock_crm_handler: MagicMock) -> None:
        mock_crm_handler.get_accounts_by_owner_email_and_name.return_value = []

        sync_crm_clients(self.user.uuid)

        self.assertEqual(Client.objects.all().count(), 0)

    @patch.object(User, "crm_handler")
    def test_sync_crm_clients_overlapping_clients(self, mock_crm_handler: MagicMock) -> None:
        client = Client.objects.create(
            crm_id="1",
            organization=self.organization,
            name="old name",
            client_type="old type",
            crm_system="old system",
            first_name="old first name",
            last_name="old last name",
            email="<EMAIL>",
            phone_number="************",
        )
        client.authorized_users.add(self.user)
        client.save()
        overlapping_client = Client.objects.create(
            crm_id="1",
            organization=self.organization,
            name="old name two",
            client_type="old type two",
            crm_system="old system two",
            first_name="old first name two",
            last_name="old last name two",
            email="<EMAIL>",
            phone_number="************",
        )
        overlapping_client.authorized_users.add(self.user)
        overlapping_client.save()

        mock_crm_handler.get_accounts_by_owner_email_and_name.return_value = [
            CRMAccount(
                crm_id="1",
                name="Client 1",
                first_name="Client",
                last_name="1",
                email="<EMAIL>",
                phone_number="************",
                client_type="client",
                crm_system="redtail",
            ),
            CRMAccount(crm_id="2", name="Client 2", client_type="prospect", crm_system="wealthbox"),
        ]

        with self.assertLogs(level="ERROR") as cm:
            sync_crm_clients(self.user.uuid)
            self.assertEqual(len(cm.records), 1)
            self.assertEqual(cm.records[0].getMessage(), "Multiple clients found for crm_id 1. Skipping update.")

        self.assertEqual(
            list(
                Client.objects.all()
                .order_by("pk")
                .values(
                    "name",
                    "client_type",
                    "crm_system",
                    "first_name",
                    "last_name",
                    "email",
                    "phone_number",
                    "organization",
                )
            ),
            [
                {
                    "name": "old name",
                    "client_type": "old type",
                    "crm_system": "old system",
                    "first_name": "old first name",
                    "last_name": "old last name",
                    "email": "<EMAIL>",
                    "phone_number": "+12125551234",
                    "organization": self.organization.pk,
                },
                {
                    "name": "old name two",
                    "client_type": "old type two",
                    "crm_system": "old system two",
                    "first_name": "old first name two",
                    "last_name": "old last name two",
                    "email": "<EMAIL>",
                    "phone_number": "+12125551235",
                    "organization": self.organization.pk,
                },
                {
                    "name": "Client 2",
                    "client_type": "prospect",
                    "crm_system": "wealthbox",
                    "first_name": None,
                    "last_name": None,
                    "email": None,
                    "phone_number": None,
                    "organization": self.organization.pk,
                },
            ],
        )


@override_settings(AWS_MEETING_BUCKET="testbucket", AWS_S3_FILE_FOLDER="testfolder")
class ProcessNoteRecordingTests(TestCase):
    def setUp(self) -> None:
        self.user = User.objects.create(username="test")
        self.user.preferences = get_default_preferences()
        self.user.save()
        self.note = Note.objects.create(note_owner=self.user, file_path="testfolder/testfile.mp3")
        self.deepgram_response = PrerecordedTranscriptionResponse(
            request_id="request",
            metadata={"duration": 1.0},
            results={
                "utterances": [
                    {"speaker": 0, "start": 0.0, "end": 1.0, "transcript": "Hello, this is Jon Doe speaking."}
                ]
            },
        )

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch("deepinsights.core.aws.boto3.Session")
    def test_process_note_recording_success(
        self,
        mock_session: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        AudioBuffer.objects.create(note=self.note, data=b"test audio", sequence=0, duration=10)
        AudioBuffer.objects.create(note=self.note, data=b"test audio two", sequence=1, duration=10)
        AudioBuffer.objects.create(data=b"test audio three", sequence=0, duration=10)

        mock_generate_presigned_url = mock_session.return_value.client.return_value.generate_presigned_url
        mock_generate_presigned_url.return_value = "http://example.com/media"

        mock_get_deepgram_transcript.return_value = self.deepgram_response
        self.user.biasing_words = ["biasing", "words"]
        self.user.save()

        process_note_recording(self.user.uuid, self.note.uuid)

        self.note.refresh_from_db()
        self.assertFalse(AudioBuffer.objects.filter(note=self.note).exists())
        self.assertTrue(AudioBuffer.objects.filter(note=None).exists())
        self.assertEqual(self.note.raw_asr_response, self.deepgram_response)
        self.assertEqual(self.note.data_source, Note.DataSource.AUDIO_FILE)
        mock_process_note_common.assert_called_once_with(self.note, self.user, self.deepgram_response, "deepgram")
        mock_generate_presigned_url.assert_called_once_with(
            ClientMethod=ANY, Params={"Bucket": "testbucket", "Key": "testfolder/testfile.mp3"}, ExpiresIn=ANY
        )
        mock_get_deepgram_transcript.assert_called_once_with({"url": "http://example.com/media"}, [], "en")
        mock_delete_object = mock_session.return_value.client.return_value.delete_object
        mock_delete_object.assert_called_once_with(Bucket="testbucket", Key="testfolder/testfile.mp3")

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch("deepinsights.core.aws.AWS.get_temp_url")
    def test_process_note_recording_already_processed(
        self,
        mock_get_temp_url: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        self.note.data_source = Note.DataSource.RECALL
        self.note.status = Note.PROCESSING_STATUS.processed
        self.note.save()

        process_note_recording(self.user.uuid, self.note.uuid)

        self.assertEqual(self.note.data_source, Note.DataSource.RECALL)
        mock_process_note_common.assert_not_called()
        mock_get_deepgram_transcript.assert_not_called()
        mock_get_temp_url.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch("deepinsights.core.aws.AWS.get_temp_url")
    def test_process_note_recording_no_media(
        self,
        mock_get_temp_url: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        self.note.data_source = Note.DataSource.RECALL
        mock_get_temp_url.return_value = None

        with self.assertRaises(Exception) as context:
            process_note_recording(self.user.uuid, self.note.uuid)

        self.assertEqual(self.note.data_source, Note.DataSource.RECALL)
        self.assertIn("No media found for note", str(context.exception))
        mock_process_note_common.assert_not_called()
        mock_get_deepgram_transcript.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch("deepinsights.core.aws.boto3.Session")
    def test_process_note_recording_no_asr_response(
        self,
        mock_session: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_generate_presigned_url = mock_session.return_value.client.return_value.generate_presigned_url
        mock_generate_presigned_url.return_value = "http://example.com/media"
        mock_get_deepgram_transcript.return_value = None

        with self.assertRaises(Exception) as context:
            process_note_recording(self.user.uuid, self.note.uuid)

        self.assertIn("No ASR response returned", str(context.exception))
        mock_process_note_common.assert_not_called()
        mock_generate_presigned_url.assert_called_once()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch("deepinsights.core.aws.boto3.Session")
    def test_process_note_recording_force_process_no_asr_response(
        self,
        mock_session: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_generate_presigned_url = mock_session.return_value.client.return_value.generate_presigned_url
        mock_generate_presigned_url.return_value = "http://example.com/media"
        mock_get_deepgram_transcript.return_value = self.deepgram_response
        self.note.status = Note.PROCESSING_STATUS.processed
        self.note.save()

        process_note_recording(self.user.uuid, self.note.uuid, force_process=True)

        self.note.refresh_from_db()
        self.assertEqual(self.note.status, Note.PROCESSING_STATUS.processed)
        mock_process_note_common.assert_called_once_with(self.note, self.user, self.deepgram_response, "deepgram")
        mock_get_deepgram_transcript.assert_called_once_with({"url": "http://example.com/media"}, [], "en")
        mock_generate_presigned_url.assert_called_once()
        mock_delete = mock_session.return_value.client.return_value.delete_object
        mock_process_note_common.assert_called_once()
        # If there is a file path on the note, it should still be deleted.
        mock_delete.assert_called_once_with(Bucket="testbucket", Key="testfolder/testfile.mp3")

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch("deepinsights.core.aws.boto3.Session")
    def test_process_note_recording_force_process_existing_asr_response(
        self,
        mock_session: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_generate_presigned_url = mock_session.return_value.client.return_value.generate_presigned_url
        mock_generate_presigned_url.return_value = "http://example.com/media"
        self.note.status = Note.PROCESSING_STATUS.processed
        self.note.raw_asr_response = self.deepgram_response
        self.note.save()

        process_note_recording(self.user.uuid, self.note.uuid, force_process=True)

        self.note.refresh_from_db()
        self.assertEqual(self.note.status, Note.PROCESSING_STATUS.processed)
        mock_process_note_common.assert_called_once_with(self.note, self.user, self.deepgram_response, "deepgram")
        mock_get_deepgram_transcript.assert_not_called()
        mock_generate_presigned_url.assert_not_called()
        mock_delete = mock_session.return_value.client.return_value.delete_object
        # If there is a file path on the note, it should still be deleted.
        mock_delete.assert_called_once_with(Bucket="testbucket", Key="testfolder/testfile.mp3")

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch("deepinsights.core.aws.boto3.Session")
    def test_process_note_recording_with_audio_chunks_no_chunks(
        self,
        mock_session: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_generate_presigned_url = mock_session.return_value.client.return_value.generate_presigned_url
        mock_get_deepgram_transcript.return_value = self.deepgram_response

        with self.assertRaises(Exception):
            process_note_recording(self.user.uuid, self.note.uuid, use_audio_buffers=True)

        self.assertIsNone(self.note.data_source)
        mock_process_note_common.assert_not_called()
        mock_get_deepgram_transcript.assert_not_called()
        mock_generate_presigned_url.assert_not_called()
        mock_delete = mock_session.return_value.client.return_value.delete_object
        # If there is a file path on the note, it should still be deleted.
        mock_delete.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch("deepinsights.core.aws.boto3.Session")
    def test_process_note_recording_with_audio_chunks(
        self,
        mock_session: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_generate_presigned_url = mock_session.return_value.client.return_value.generate_presigned_url
        mock_get_deepgram_transcript.return_value = self.deepgram_response

        AudioBuffer.objects.create(note=self.note, data=b"two", sequence=1, duration=10, mime_type="audio/webm")
        AudioBuffer.objects.create(note=self.note, data=b"one", sequence=0, duration=10, mime_type="audio/webm")
        AudioBuffer.objects.create(note=self.note, data=b"three", sequence=2, duration=10, mime_type="audio/webm")

        process_note_recording(self.user.uuid, self.note.uuid, use_audio_buffers=True)

        self.note.refresh_from_db()
        self.assertFalse(AudioBuffer.objects.exists())
        self.assertEqual(self.note.raw_asr_response, self.deepgram_response)
        self.assertEqual(self.note.data_source, Note.DataSource.AUDIO_BUFFERS)
        mock_process_note_common.assert_called_once_with(self.note, self.user, self.deepgram_response, "deepgram")
        mock_get_deepgram_transcript.assert_called_once_with(
            {
                "buffer": b"onetwothree",
                "mimetype": "audio/webm",
            },
            [],
            "en",
        )
        mock_generate_presigned_url.assert_not_called()
        mock_delete = mock_session.return_value.client.return_value.delete_object
        # If there is a file path on the note, it should still be deleted.
        mock_delete.assert_called_once_with(Bucket="testbucket", Key="testfolder/testfile.mp3")

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch("deepinsights.core.aws.boto3.Session")
    def test_process_note_recording_with_audio_chunks_multiple_nonces(
        self,
        mock_session: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_generate_presigned_url = mock_session.return_value.client.return_value.generate_presigned_url
        mock_get_deepgram_transcript.return_value = self.deepgram_response

        AudioBuffer.objects.create(
            note=self.note, data=b"two", sequence=1, duration=10, mime_type="audio/webm", nonce="123"
        )
        AudioBuffer.objects.create(
            note=self.note, data=b"one", sequence=0, duration=10, mime_type="audio/webm", nonce="123"
        )
        AudioBuffer.objects.create(
            note=self.note, data=b"three", sequence=2, duration=10, mime_type="audio/webm", nonce="123"
        )
        AudioBuffer.objects.create(
            note=self.note, data=b"b", sequence=1, duration=10, mime_type="audio/webm", nonce="456"
        )
        AudioBuffer.objects.create(
            note=self.note, data=b"a", sequence=0, duration=10, mime_type="audio/webm", nonce="456"
        )
        AudioBuffer.objects.create(
            note=self.note, data=b"c", sequence=2, duration=10, mime_type="audio/webm", nonce="456"
        )

        process_note_recording(self.user.uuid, self.note.uuid, use_audio_buffers=True)

        self.note.refresh_from_db()
        self.assertFalse(AudioBuffer.objects.exists())
        self.assertEqual(self.note.raw_asr_response, self.deepgram_response)
        mock_process_note_common.assert_called_once_with(self.note, self.user, self.deepgram_response, "deepgram")
        mock_get_deepgram_transcript.assert_called_once_with(
            {
                "buffer": b"abc",
                "mimetype": "audio/webm",
            },
            [],
            "en",
        )
        mock_generate_presigned_url.assert_not_called()
        mock_delete = mock_session.return_value.client.return_value.delete_object
        # If there is a file path on the note, it should still be deleted.
        mock_delete.assert_called_once_with(Bucket="testbucket", Key="testfolder/testfile.mp3")

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch("deepinsights.core.aws.boto3.Session")
    def test_process_note_recording_with_audio_chunks_multiple_mime_types(
        self,
        mock_session: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_generate_presigned_url = mock_session.return_value.client.return_value.generate_presigned_url
        mock_get_deepgram_transcript.return_value = self.deepgram_response

        AudioBuffer.objects.create(note=self.note, data=b"two", sequence=1, duration=10, mime_type="audio/webm")
        AudioBuffer.objects.create(note=self.note, data=b"one", sequence=0, duration=10, mime_type="audio/webm")
        AudioBuffer.objects.create(note=self.note, data=b"foo", sequence=0, duration=10, mime_type="audio/foo")
        AudioBuffer.objects.create(note=self.note, data=b"bar", sequence=1, duration=10, mime_type="audio/foo")
        AudioBuffer.objects.create(note=self.note, data=b"three", sequence=2, duration=10, mime_type="audio/webm")

        process_note_recording(self.user.uuid, self.note.uuid, use_audio_buffers=True)

        self.note.refresh_from_db()
        self.assertFalse(AudioBuffer.objects.exists())
        self.assertEqual(self.note.raw_asr_response, self.deepgram_response)
        mock_process_note_common.assert_called_once_with(self.note, self.user, self.deepgram_response, "deepgram")
        mock_get_deepgram_transcript.assert_called_once_with(
            {
                "buffer": b"onetwothree",
                "mimetype": "audio/webm",
            },
            [],
            "en",
        )
        mock_generate_presigned_url.assert_not_called()
        mock_delete = mock_session.return_value.client.return_value.delete_object
        # If there is a file path on the note, it should still be deleted.
        mock_delete.assert_called_once_with(Bucket="testbucket", Key="testfolder/testfile.mp3")

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch("deepinsights.core.aws.boto3.Session")
    def test_process_note_recording_with_audio_chunks_no_mime_type(
        self,
        mock_session: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_generate_presigned_url = mock_session.return_value.client.return_value.generate_presigned_url
        mock_get_deepgram_transcript.return_value = self.deepgram_response

        AudioBuffer.objects.create(note=self.note, data=b"one", sequence=0, duration=10)
        AudioBuffer.objects.create(note=self.note, data=b"two", sequence=1, duration=10)

        with self.assertRaises(Exception):
            process_note_recording(self.user.uuid, self.note.uuid, use_audio_buffers=True)

        self.note.refresh_from_db()
        self.assertTrue(AudioBuffer.objects.exists())
        self.assertIsNone(self.note.raw_asr_response)
        mock_process_note_common.assert_not_called()
        mock_get_deepgram_transcript.assert_not_called()
        mock_generate_presigned_url.assert_not_called()
        mock_delete = mock_session.return_value.client.return_value.delete_object
        mock_delete.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=True, CELERY_TASK_LOCK_TIMEOUT=1)
    def test_process_note_recording_locking(self, mock_cache: MagicMock) -> None:
        self.note.status = Note.PROCESSING_STATUS.processed
        self.note.save()

        with self.assertLogs(level="INFO") as cm:
            process_note_recording.apply((self.user.uuid, self.note.uuid))
            self.assertTrue(any([f"Note {self.note.uuid} already processed" in x for x in cm.output]))
        mock_cache.lock.assert_called_once_with(str(self.note.uuid), timeout=1)

    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=False)
    def test_process_note_recording_locking_disabled(self, mock_cache: MagicMock) -> None:
        self.note.status = Note.PROCESSING_STATUS.processed
        self.note.save()

        with self.assertLogs(level="INFO") as cm:
            process_note_recording.apply((self.user.uuid, self.note.uuid))
            self.assertTrue(any([f"Note {self.note.uuid} already processed" in x for x in cm.output]))
        mock_cache.lock.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=True, CELERY_TASK_LOCK_TIMEOUT=1)
    def test_process_note_recording_locking_failure(self, mock_cache: MagicMock) -> None:
        self.note.status = Note.PROCESSING_STATUS.processed
        self.note.save()

        invalid_uuid = uuid.uuid4()

        with self.assertLogs(level="INFO") as cm:
            process_note_recording.apply((invalid_uuid, self.note.uuid))
            self.assertFalse(any([f"Note {self.note.uuid} already processed" in x for x in cm.output]))
        mock_cache.lock.assert_has_calls(
            [
                call(str(self.note.uuid), timeout=1),
                call(str(self.note.uuid), timeout=1),
                call(str(self.note.uuid), timeout=1),
            ],
            any_order=True,  # There are __enter__ and __exit__calls interspersed.
        )


class ProcessBotRecordingTests(TestCase):
    def setUp(self) -> None:
        self.user = User.objects.create(username="testuser", email="<EMAIL>")
        self.note = Note.objects.create(note_owner=self.user, status="uploaded")
        self.bot = MeetingBot.objects.create(uuid=uuid.uuid4(), note=self.note, recall_bot_id="recall_bot_id")
        self.recall_transcript = [
            {
                "speaker": "First speaker",
                "start": datetime.timedelta(seconds=0),
                "end": datetime.timedelta(seconds=30),
                "utt": "Hello",
            },
            {
                "speaker": "Second speaker",
                "start": datetime.timedelta(seconds=31),
                "end": datetime.timedelta(seconds=60),
                "utt": "Hi",
            },
            {
                "speaker": "First speaker",
                "start": datetime.timedelta(seconds=61),
                "end": datetime.timedelta(seconds=90),
                "utt": "How are you?",
            },
        ]
        self.deepgram_response = PrerecordedTranscriptionResponse(
            request_id="request",
            metadata={"duration": 1.0},
            results={
                "utterances": [
                    {"speaker": 0, "start": 0.0, "end": 1.0, "transcript": "Hello, this is Jon Doe speaking."}
                ]
            },
        )
        self.empty_deepgram_response = PrerecordedTranscriptionResponse(
            request_id="request", metadata={"duration": 1.0}, results={"utterances": []}
        )

    def test_process_bot_recording_no_note(self) -> None:
        self.bot.note = None
        self.bot.save()
        result = process_bot_recording.apply((self.bot.uuid, False))
        self.assertFalse(result.successful())
        self.assertIn(f"MeetingBot {self.bot.uuid} does not have an associated note", str(result.result))

    def test_process_bot_recording_no_user(self) -> None:
        self.note.note_owner = None
        self.note.save()

        result = process_bot_recording.apply((self.bot.uuid, False))
        self.assertFalse(result.successful())
        self.assertIn(f"Note {self.note.uuid} does not have an associated user", str(result.result))

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch.object(MeetingBot, "get_transcript")
    @patch.object(MeetingBot, "delete_media")
    def test_process_bot_recording_already_processed(
        self,
        mock_delete_media: MagicMock,
        mock_get_transcript: MagicMock,
        mock_fetch_and_save_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        self.note.status = Note.PROCESSING_STATUS.processed
        self.note.data_source = Note.DataSource.AUDIO_FILE
        self.note.save()

        result = process_bot_recording.apply((self.bot.uuid, False))
        self.assertTrue(result.successful())
        self.note.refresh_from_db()
        self.assertEqual(self.note.data_source, Note.DataSource.AUDIO_FILE)
        mock_delete_media.assert_not_called()
        mock_get_transcript.assert_not_called()
        mock_fetch_and_save_deepgram_transcript.assert_not_called()
        mock_process_note_common.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch.object(MeetingBot, "get_transcript")
    @patch.object(MeetingBot, "delete_media")
    def test_process_bot_recording_recall_success(
        self,
        mock_delete_media: MagicMock,
        mock_get_transcript: MagicMock,
        mock_fetch_and_save_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_get_transcript.return_value = self.recall_transcript

        result = process_bot_recording.apply((self.bot.uuid, False))
        self.note.refresh_from_db()

        self.assertTrue(result.successful())
        self.assertIsNone(self.note.raw_asr_response)
        self.note.refresh_from_db()
        self.assertEqual(self.note.data_source, Note.DataSource.RECALL)
        mock_fetch_and_save_deepgram_transcript.assert_not_called()
        mock_process_note_common.assert_called_once_with(self.note, self.user, self.recall_transcript, "recall")
        mock_delete_media.assert_called_once()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch.object(MeetingBot, "get_transcript")
    @patch.object(MeetingBot, "delete_media")
    def test_process_bot_recording_force_process(
        self,
        mock_delete_media: MagicMock,
        mock_get_transcript: MagicMock,
        mock_fetch_and_save_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_get_transcript.return_value = self.recall_transcript
        self.note.status = Note.PROCESSING_STATUS.processed
        self.note.save()

        result = process_bot_recording.apply((self.bot.uuid, True))
        self.note.refresh_from_db()

        self.assertTrue(result.successful())
        self.note.refresh_from_db()
        self.assertEqual(self.note.data_source, Note.DataSource.RECALL)
        self.assertIsNone(self.note.raw_asr_response)
        mock_fetch_and_save_deepgram_transcript.assert_not_called()
        mock_process_note_common.assert_called_once_with(self.note, self.user, self.recall_transcript, "recall")
        mock_delete_media.assert_called_once()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch.object(MeetingBot, "get_transcript")
    @patch.object(MeetingBot, "delete_media")
    def test_process_bot_recording_recall_success_but_empty(
        self,
        mock_delete_media: MagicMock,
        mock_get_transcript: MagicMock,
        mock_fetch_and_save_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_get_transcript.return_value = []

        result = process_bot_recording.apply((self.bot.uuid, False))
        self.note.refresh_from_db()

        self.assertTrue(result.successful())
        self.note.refresh_from_db()
        self.assertEqual(self.note.data_source, Note.DataSource.RECALL)
        self.assertIsNone(self.note.raw_asr_response)
        mock_fetch_and_save_deepgram_transcript.assert_not_called()
        mock_process_note_common.assert_called_once_with(self.note, self.user, [], "recall")
        mock_delete_media.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch.object(MeetingBot, "get_transcript")
    @patch.object(MeetingBot, "delete_media")
    def test_process_bot_recording_recall_partial_failure(
        self,
        mock_delete_media: MagicMock,
        mock_get_transcript: MagicMock,
        mock_fetch_and_save_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_get_transcript.side_effect = [[], [], self.recall_transcript]

        result = process_bot_recording.apply((self.bot.uuid, False))
        self.note.refresh_from_db()

        self.assertTrue(result.successful())
        self.assertIsNone(self.note.raw_asr_response)
        mock_fetch_and_save_deepgram_transcript.assert_not_called()
        self.assertEqual(mock_get_transcript.call_count, 3)
        mock_process_note_common.assert_called_once_with(self.note, self.user, self.recall_transcript, "recall")
        mock_delete_media.assert_called_once()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch.object(MeetingBot, "get_transcript")
    @patch.object(MeetingBot, "delete_media")
    @patch.object(MeetingBot, "get_media_download_info")
    def test_process_bot_recording_recall_full_failure_deepgram_success(
        self,
        mock_get_media_download_info: MagicMock,
        mock_delete_media: MagicMock,
        mock_get_transcript: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_get_transcript.side_effect = [[], [], [], []]
        mock_get_media_download_info.return_value = ("http://example.com/media", None, None)
        mock_get_deepgram_transcript.return_value = self.deepgram_response

        result = process_bot_recording.apply((self.bot.uuid, False))
        self.note.refresh_from_db()

        self.assertTrue(result.successful())
        self.assertEqual(self.note.raw_asr_response, self.deepgram_response)
        self.assertEqual(mock_get_transcript.call_count, 4)
        mock_get_deepgram_transcript.assert_called_once_with({"url": "http://example.com/media"}, [], "en")
        mock_process_note_common.assert_called_once_with(self.note, self.user, self.deepgram_response, "deepgram")
        mock_delete_media.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch.object(MeetingBot, "get_transcript")
    @patch.object(MeetingBot, "delete_media")
    @patch.object(MeetingBot, "get_media_download_info")
    def test_process_bot_recording_recall_full_failure_deepgram_success_empty(
        self,
        mock_get_media_download_info: MagicMock,
        mock_delete_media: MagicMock,
        mock_get_transcript: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_get_transcript.side_effect = [[], [], [], []]
        mock_get_media_download_info.return_value = ("http://example.com/media", None, None)
        mock_get_deepgram_transcript.return_value = self.empty_deepgram_response

        result = process_bot_recording.apply((self.bot.uuid, False))
        self.note.refresh_from_db()

        self.assertTrue(result.successful())
        self.assertEqual(self.note.raw_asr_response, self.empty_deepgram_response)
        self.assertEqual(mock_get_transcript.call_count, 4)
        mock_get_deepgram_transcript.assert_called_once_with({"url": "http://example.com/media"}, [], "en")
        mock_process_note_common.assert_called_once_with(self.note, self.user, self.empty_deepgram_response, "deepgram")
        mock_delete_media.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch.object(MeetingBot, "get_transcript")
    @patch.object(MeetingBot, "delete_media")
    @patch.object(MeetingBot, "get_media_download_info")
    def test_process_bot_recording_recall_full_failure_deepgram_failure(
        self,
        mock_get_media_download_info: MagicMock,
        mock_delete_media: MagicMock,
        mock_get_transcript: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_get_transcript.side_effect = [[], [], [], []]
        mock_get_media_download_info.return_value = ("http://example.com/media", None, None)
        mock_get_deepgram_transcript.return_value = None

        result = process_bot_recording.apply((self.bot.uuid, False))
        self.note.refresh_from_db()

        self.assertTrue(result.successful())
        self.assertIsNone(self.note.raw_asr_response)
        self.assertEqual(mock_get_transcript.call_count, 4)
        mock_get_deepgram_transcript.assert_called_once_with({"url": "http://example.com/media"}, [], "en")
        mock_process_note_common.assert_called_once_with(self.note, self.user, [], "recall")
        mock_delete_media.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch(
        "deepinsights.meetingsapp.tasks.Flags.EnableProcessRecallBotTranscriptsWithZeplynDeepgramIntegration.is_active_for_user"
    )
    @patch.object(MeetingBot, "get_media_download_info")
    @patch.object(MeetingBot, "delete_media")
    def test_process_bot_recording_deepgram_success(
        self,
        mock_delete_media: MagicMock,
        mock_get_media_download_info: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_is_active_for_user.return_value = True
        mock_get_deepgram_transcript.return_value = self.deepgram_response
        mock_get_media_download_info.return_value = ("http://example.com/media", None, None)

        result = process_bot_recording.apply((self.bot.uuid, False))

        self.assertTrue(result.successful())

        self.note.refresh_from_db()
        self.assertEqual(self.note.raw_asr_response, self.deepgram_response)
        mock_get_deepgram_transcript.assert_called_once_with({"url": "http://example.com/media"}, [], "en")
        mock_process_note_common.assert_called_once_with(self.note, self.user, self.deepgram_response, "deepgram")
        mock_delete_media.assert_called_once()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch(
        "deepinsights.meetingsapp.tasks.Flags.EnableProcessRecallBotTranscriptsWithZeplynDeepgramIntegration.is_active_for_user"
    )
    @patch.object(MeetingBot, "get_media_download_info")
    @patch.object(MeetingBot, "delete_media")
    def test_process_bot_recording_deepgram_success_empty_transcript(
        self,
        mock_delete_media: MagicMock,
        mock_get_media_download_info: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_is_active_for_user.return_value = True
        mock_get_deepgram_transcript.return_value = self.empty_deepgram_response
        mock_get_media_download_info.return_value = ("http://example.com/media", None, None)

        result = process_bot_recording.apply((self.bot.uuid, False))

        self.assertTrue(result.successful())

        self.note.refresh_from_db()
        self.assertEqual(self.note.raw_asr_response, self.empty_deepgram_response)
        mock_get_deepgram_transcript.assert_called_once_with({"url": "http://example.com/media"}, [], "en")
        mock_process_note_common.assert_called_once_with(self.note, self.user, self.empty_deepgram_response, "deepgram")
        mock_delete_media.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch(
        "deepinsights.meetingsapp.tasks.Flags.EnableProcessRecallBotTranscriptsWithZeplynDeepgramIntegration.is_active_for_user"
    )
    @patch.object(MeetingBot, "get_media_download_info")
    @patch.object(MeetingBot, "delete_media")
    def test_process_bot_recording_deepgram_partial_failure(
        self,
        mock_delete_media: MagicMock,
        mock_get_media_download_info: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_is_active_for_user.return_value = True
        mock_get_media_download_info.side_effect = [
            None,
            ("http://example.com/media", None, None),
            ("http://example.com/media", None, None),
        ]
        mock_get_deepgram_transcript.side_effect = [None, self.deepgram_response]

        result = process_bot_recording.apply((self.bot.uuid, False))

        self.assertTrue(result.successful())

        self.note.refresh_from_db()
        self.assertEqual(self.note.raw_asr_response, self.deepgram_response)
        self.assertEqual(mock_get_deepgram_transcript.call_count, 2)
        self.assertEqual(mock_get_media_download_info.call_count, 3)
        mock_process_note_common.assert_called_once_with(self.note, self.user, self.deepgram_response, "deepgram")
        mock_delete_media.assert_called_once()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch(
        "deepinsights.meetingsapp.tasks.Flags.EnableProcessRecallBotTranscriptsWithZeplynDeepgramIntegration.is_active_for_user"
    )
    @patch.object(MeetingBot, "get_media_download_info")
    @patch.object(MeetingBot, "delete_media")
    def test_process_bot_recording_deepgram_full_failure(
        self,
        mock_delete_media: MagicMock,
        mock_get_media_download_info: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_is_active_for_user.return_value = True
        mock_get_media_download_info.return_value = None
        mock_get_deepgram_transcript.return_value = None

        result = process_bot_recording.apply((self.bot.uuid, False))

        self.assertTrue(result.successful())
        self.note.refresh_from_db()

        self.assertFalse(self.note.raw_asr_response)
        mock_get_deepgram_transcript.assert_not_called()
        mock_process_note_common.assert_called_once_with(self.note, self.user, [], "recall")
        mock_delete_media.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.core.ml.asr._get_deepgram_transcript")
    @patch(
        "deepinsights.meetingsapp.tasks.Flags.EnableProcessRecallBotTranscriptsWithZeplynDeepgramIntegration.is_active_for_user"
    )
    @patch.object(MeetingBot, "get_media_download_info")
    @patch.object(MeetingBot, "delete_media")
    def test_process_bot_recording_deepgram_existing_asr_response(
        self,
        mock_delete_media: MagicMock,
        mock_get_media_download_info: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_get_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_is_active_for_user.return_value = True
        self.note.raw_asr_response = self.deepgram_response
        self.note.save()

        result = process_bot_recording.apply((self.bot.uuid, False))

        self.assertTrue(result.successful())

        self.note.refresh_from_db()
        self.assertEqual(self.note.raw_asr_response, self.deepgram_response)
        mock_get_media_download_info.assert_not_called()
        mock_get_deepgram_transcript.assert_not_called()
        mock_process_note_common.assert_called_once_with(self.note, self.user, self.deepgram_response, "deepgram")
        mock_delete_media.assert_called_once()


class TestGenerateTemplateData(TestCase):
    def setUp(self) -> None:
        super().setUp()

        # Create schema definition
        self.schema_definition = StructuredMeetingDataSchema.objects.create(
            name="test_schema",
            description="Test schema",
            schema={"type": "object", "properties": {"field": {"type": "string"}}},
        )

        # Create fill template prompt
        self.fill_template_prompt = Prompt.objects.create(name="Fill Template Prompt", user_prompt="Fill this template")

        # Create template generator prompt
        self.template_generator_prompt = Prompt.objects.create(
            unique_name="template_generator_prompt",
            user_prompt="Template generator prompt text with schema: {schema} and questions: {questions}",
        )

        # Test response used in multiple tests
        self.test_response = {"initial_data": {"field": "initial value"}, "example": {"field": "example value"}}

    @patch("deepinsights.meetingsapp.tasks.call_gpt4o")
    @patch("deepinsights.meetingsapp.tasks.Prompt.objects.get")
    def test_successful_generation(self, mock_prompt_get: MagicMock, mock_gpt4o: MagicMock) -> None:
        """Test successful template generation on first try"""
        # Configure the mock to return our template generator prompt
        mock_prompt_get.return_value = self.template_generator_prompt

        # Update test response to include both initial_data and example
        test_response = {"initial_data": {"field": "initial value"}}
        mock_gpt4o.return_value = json.dumps(test_response)

        generate_template_data(
            questions_text="Test question?",
            schema_id=self.schema_definition.id,
            name="Test Template",
            internal_name="test_template",
            kind="test",
            prompt_id=self.fill_template_prompt.id,
        )

        created_template: StructuredMeetingDataTemplate | None = StructuredMeetingDataTemplate.objects.last()

        assert created_template is not None, "Template should not be None"

        self.assertEqual(created_template.title, "Test Template")
        self.assertEqual(created_template.internal_name, "test_template")
        self.assertEqual(created_template.kind, "test")
        self.assertEqual(created_template.initial_data, test_response["initial_data"])

    @patch("deepinsights.meetingsapp.tasks.call_gpt4o")
    @patch("deepinsights.meetingsapp.tasks.Prompt.objects.get")
    def test_failure_invalid_json(self, mock_prompt_get: MagicMock, mock_gpt4o: MagicMock) -> None:
        """Test handling of invalid JSON response"""
        # Configure the mock to return our template generator prompt
        mock_prompt_get.return_value = self.template_generator_prompt

        mock_gpt4o.return_value = "invalid json"

        with self.assertRaises(ValueError) as context:
            generate_template_data(
                questions_text="Test question?",
                schema_id=self.schema_definition.id,
                name="Test Template",
                internal_name="test_template",
                kind="test",
                prompt_id=self.fill_template_prompt.id,
            )
        self.assertIn("Failed to generate valid template data", str(context.exception))

    @patch("deepinsights.meetingsapp.tasks.Prompt.objects.get")
    def test_failure_empty_prompt(self, mock_prompt_get: MagicMock) -> None:
        # Configure the mock to return an empty prompt
        empty_prompt = Prompt.objects.create(unique_name="template_generator_prompt_v2", user_prompt="")
        mock_prompt_get.return_value = empty_prompt

        with self.assertRaises(ValueError) as context:
            generate_template_data(
                questions_text="Test question?",
                schema_id=self.schema_definition.id,
                name="Test Template",
                internal_name="test_template",
                kind="test",
                prompt_id=self.fill_template_prompt.id,
            )
        self.assertIn("Template generator prompt is empty", str(context.exception))

    @patch("deepinsights.meetingsapp.tasks.call_gpt4o")
    @patch("deepinsights.meetingsapp.tasks.Prompt.objects.get")
    def test_failure_missing_required_fields(self, mock_prompt_get: MagicMock, mock_gpt4o: MagicMock) -> None:
        """Test handling of response missing required fields"""
        # Configure the mock to return our template generator prompt
        mock_prompt_get.return_value = self.template_generator_prompt

        mock_gpt4o.return_value = json.dumps({"wrong": "structure"})

        with self.assertRaises(ValueError) as context:
            generate_template_data(
                questions_text="Test question?",
                schema_id=self.schema_definition.id,
                name="Test Template",
                internal_name="test_template",
                kind="test",
                prompt_id=self.fill_template_prompt.id,
            )
        self.assertIn("Failed to generate valid template data", str(context.exception))

    def test_failure_schema_not_found(self) -> None:
        """Test handling of non-existent schema"""
        with self.assertRaises(StructuredMeetingDataSchema.DoesNotExist):
            generate_template_data(
                questions_text="Test question?",
                schema_id=999999,
                name="Test Template",
                internal_name="test_template",
                kind="test",
                prompt_id=self.fill_template_prompt.id,
            )

    @patch("deepinsights.meetingsapp.tasks.Prompt.objects.get")
    def test_failure_prompt_not_found(self, mock_prompt_get: MagicMock) -> None:
        # Configure the mock to raise DoesNotExist
        mock_prompt_get.side_effect = Prompt.DoesNotExist()

        with self.assertRaises(Prompt.DoesNotExist):
            generate_template_data(
                questions_text="Test question?",
                schema_id=self.schema_definition.id,
                name="Test Template",
                internal_name="test_template",
                kind="test",
                prompt_id=999999,
            )


class TestDeleteOrganizationData(TestCase):
    def setUp(self) -> None:
        self.organization = Organization.objects.create(name="DUMMYY")

        self.user1 = User.objects.create(username="user1", email="<EMAIL>", organization=self.organization)
        self.user2 = User.objects.create(username="user2", email="<EMAIL>", organization=self.organization)

        self.client1 = Client.objects.create(name="dummy", organization=self.organization)
        self.client2 = Client.objects.create(name="dummy2", organization=self.organization)

        self.meeting_type = MeetingType.objects.create(name="Dummy Internal", internal_name="dummy_internal")
        self.meeting_type.organizations.add(self.organization)
        self.meeting_type.users.add(self.user1, self.user2)

        self.note = Note.objects.create(note_owner=self.user1, metadata={"meeting_name": "Dummy Meeting"})

        self.attendee1 = Attendee.objects.create(note=self.note, attendee_name="Jim Reeves")
        self.attendee2 = Attendee.objects.create(note=self.note, attendee_name="Frank Sinatra")

        self.bot = MeetingBot.objects.create(note=self.note, bot_owner=self.user1, meeting_link="Dummy Meeting")

        self.oauth = OAuthCredentials.objects.create(
            user=self.user1,
            access_token="dummy_token",
            refresh_token="dummy_refresh",
            expires_in=timezone.now(),
            refresh_token_expires_in=timezone.now(),
        )

        self.client_recap = ClientRecap.objects.create(user=self.user1, client=self.client2)

        self.task1 = Task.objects.create(task_title="Dummy1", task_owner=self.user1, note=self.note)
        self.task2 = Task.objects.create(task_title="Dummy2", task_owner=self.user2, note=self.note)

        self.email_template = MeetingSummaryEmailTemplate.objects.create(name="Dummy Template")
        self.email_template.organizations.add(self.organization)
        self.email_template.users.add(self.user1, self.user2)

        self.schema = StructuredMeetingDataSchema.objects.create(
            name="dummy structured meeting data schema", schema={"type": "object"}
        )

        self.data_template = StructuredMeetingDataTemplate.objects.create(
            title="dummy template", schema_definition=self.schema
        )

        self.template_rule = StructuredMeetingDataTemplateRule.objects.create(internal_name="Dummy data template rule")
        self.template_rule.organizations.add(self.organization)
        self.template_rule.users.add(self.user1, self.user2)
        self.template_rule.meeting_types.add(self.meeting_type)

        self.structured_data = StructuredMeetingData.objects.create(title="dummy", note=self.note, schema={}, data={})

        self.search_query = SearchQuery.objects.create(
            requestor=self.user1, query="dummy query", structured_response={"dummy": "dummy"}
        )
        self.search_query.notes.add(self.note)

    @patch("deepinsights.meetingsapp.tasks.email_service.send_email")
    def test_delete_organization_data(self, mock_send_email: MagicMock) -> None:
        mock_send_email.return_value = {"message_id": "django-email-123", "status": "Sent", "message_id": 1}

        delete_organization_data_task(str(self.organization.id))

        self.assertEqual(User.objects.filter(organization=self.organization).count(), 0)
        self.assertEqual(Client.objects.filter(organization=self.organization).count(), 0)
        self.assertEqual(Note.objects.filter(note_owner__organization=self.organization).count(), 0)
        self.assertEqual(Task.objects.filter(task_owner__organization=self.organization).count(), 0)
        self.assertEqual(MeetingBot.objects.filter(bot_owner__organization=self.organization).count(), 0)
        self.assertEqual(ClientRecap.objects.filter(user__organization=self.organization).count(), 0)
        self.assertEqual(Attendee.objects.filter(note__note_owner__organization=self.organization).count(), 0)
        self.assertEqual(OAuthCredentials.objects.filter(user__organization=self.organization).count(), 0)
        self.assertEqual(SearchQuery.objects.filter(requestor__organization=self.organization).count(), 0)
        self.assertEqual(
            StructuredMeetingData.objects.filter(note__note_owner__organization=self.organization).count(), 0
        )

        self.assertEqual(Organization.objects.filter(id=self.organization.id).count(), 0)

        self.meeting_type.refresh_from_db()
        self.assertNotIn(self.organization, self.meeting_type.organizations.all())
        self.assertFalse(self.meeting_type.users.filter(id__in=[self.user1.id, self.user2.id]).exists())

        self.email_template.refresh_from_db()
        self.assertNotIn(self.organization, self.email_template.organizations.all())
        self.assertFalse(self.email_template.users.filter(id__in=[self.user1.id, self.user2.id]).exists())

        self.template_rule.refresh_from_db()
        self.assertNotIn(self.organization, self.template_rule.organizations.all())
        self.assertFalse(self.template_rule.users.filter(id__in=[self.user1.id, self.user2.id]).exists())

        # Verify objects that should remain unchanged
        self.assertTrue(StructuredMeetingDataSchema.objects.filter(id=self.schema.id).exists())
        self.assertTrue(StructuredMeetingDataTemplate.objects.filter(id=self.data_template.id).exists())


class TestProcessStructuredDataTemplates(TransactionTestCase):
    def setUp(self) -> None:
        self.meeting_type = MeetingType.objects.create(name="Client Meeting", category="client")
        self.user = User.objects.create(username="testuser", email="<EMAIL>")
        self.diarized_trans_with_names = 'Jon Doe\t"0.0-->5.2\nHello, this is Jon Doe speaking.'
        self.note = Note.objects.create(
            note_owner=self.user,
            metadata={"meeting_name": "Dummy Meeting"},
            diarized_trans_with_names=self.diarized_trans_with_names,
            meeting_type=self.meeting_type,
        )
        self.note.authorized_users.add(self.user)
        self.note.save()
        self.schema = StructuredMeetingDataSchema.objects.create(name="test", schema={"type": "object"})
        self.template = StructuredMeetingDataTemplate.objects.create(
            schema_definition=self.schema,
            title="Test Template",
            internal_name="test_template",
            kind="follow_up",
            initial_data={},
            context="{}",
            prompt=Prompt.objects.create(unique_name="test_prompt", user_prompt="Test prompt"),
        )
        self.rule = StructuredMeetingDataTemplateRule.objects.create(everyone=True)
        self.rule.meeting_types.add(self.meeting_type)
        self.rule.follow_up_templates.add(self.template)
        self.rule.save()

    @patch("deepinsights.meetingsapp.tasks.get_follow_up_structured_data")
    def test_process_structured_data_templates_success(self, mock_get_follow_up_structured_data: MagicMock) -> None:
        mock_get_follow_up_structured_data.return_value = {"structured_data": "Follow-up structured data"}

        process_structured_data_templates(self.note.uuid, self.user.uuid, self.diarized_trans_with_names)

        self.assertEqual(StructuredMeetingData.objects.filter(note=self.note).count(), 1)
        follow_up = StructuredMeetingData.objects.filter(note=self.note)[0]
        self.assertEqual(follow_up.data, {"structured_data": "Follow-up structured data"})
        self.assertEqual(follow_up.status, StructuredMeetingData.Status.COMPLETED)

    @patch("deepinsights.meetingsapp.tasks.get_follow_up_structured_data")
    def test_process_structured_data_templates_failure(self, mock_get_follow_up_structured_data: MagicMock) -> None:
        mock_get_follow_up_structured_data.side_effect = Exception("Test exception")

        process_structured_data_templates(self.note.uuid, self.user.uuid, self.diarized_trans_with_names)

        self.assertEqual(StructuredMeetingData.objects.filter(note=self.note).count(), 1)
        follow_up = StructuredMeetingData.objects.filter(note=self.note)[0]
        self.assertEqual(follow_up.status, StructuredMeetingData.Status.FAILED)

    @patch("deepinsights.meetingsapp.tasks.get_follow_up_structured_data")
    def test_process_structured_data_templates_partial_failure(
        self, mock_get_follow_up_structured_data: MagicMock
    ) -> None:
        mock_get_follow_up_structured_data.side_effect = [
            Exception("Test exception"),
            {"structured_data": "Follow-up structured data"},
        ]
        second_template = StructuredMeetingDataTemplate.objects.create(
            schema_definition=self.schema,
            title="Test Template",
            internal_name="test_template",
            kind="follow_up",
            initial_data={},
            context="{}",
            prompt=Prompt.objects.create(unique_name="test_prompt", user_prompt="Test prompt"),
        )
        self.rule.follow_up_templates.add(second_template)
        self.rule.save()

        process_structured_data_templates(self.note.uuid, self.user.uuid, self.diarized_trans_with_names)

        follow_ups = StructuredMeetingData.objects.filter(note=self.note)
        self.assertEqual(follow_ups.count(), 2)
        failed_follow_up = follow_ups.filter(status=StructuredMeetingData.Status.FAILED)[0]
        successful_follow_up = follow_ups.filter(status=StructuredMeetingData.Status.COMPLETED)[0]
        self.assertEqual(failed_follow_up.status, StructuredMeetingData.Status.FAILED)
        self.assertEqual(successful_follow_up.data, {"structured_data": "Follow-up structured data"})
        self.assertEqual(successful_follow_up.status, StructuredMeetingData.Status.COMPLETED)

    @patch("deepinsights.meetingsapp.tasks.get_follow_up_structured_data")
    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=True, CELERY_TASK_LOCK_TIMEOUT=1)
    def test_process_structured_data_templates_locking(
        self, mock_cache: MagicMock, mock_get_follow_up_structured_data: MagicMock
    ) -> None:
        mock_get_follow_up_structured_data.return_value = {"structured_data": "Follow-up structured data"}

        process_structured_data_templates(self.note.uuid, self.user.uuid, self.diarized_trans_with_names)

        self.assertEqual(StructuredMeetingData.objects.filter(note=self.note).count(), 1)
        follow_up = StructuredMeetingData.objects.filter(note=self.note)[0]
        self.assertEqual(StructuredMeetingData.objects.filter(note=self.note).count(), 1)
        follow_up = StructuredMeetingData.objects.filter(note=self.note)[0]
        self.assertEqual(follow_up.data, {"structured_data": "Follow-up structured data"})
        self.assertEqual(follow_up.status, StructuredMeetingData.Status.COMPLETED)
        self.assertEqual(follow_up.data, {"structured_data": "Follow-up structured data"})
        self.assertEqual(follow_up.status, StructuredMeetingData.Status.COMPLETED)
        mock_cache.lock.assert_called_once_with(str(self.note.uuid), timeout=1)

    @patch("deepinsights.meetingsapp.tasks.get_follow_up_structured_data")
    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=False)
    def test_process_structured_data_templates_locking_disabled(
        self, mock_cache: MagicMock, mock_get_follow_up_structured_data: MagicMock
    ) -> None:
        mock_get_follow_up_structured_data.return_value = {"structured_data": "Follow-up structured data"}

        process_structured_data_templates(self.note.uuid, self.user.uuid, self.diarized_trans_with_names)

        self.assertEqual(StructuredMeetingData.objects.filter(note=self.note).count(), 1)
        follow_up = StructuredMeetingData.objects.filter(note=self.note)[0]
        self.assertEqual(follow_up.data, {"structured_data": "Follow-up structured data"})
        self.assertEqual(follow_up.status, StructuredMeetingData.Status.COMPLETED)
        mock_cache.lock.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.get_follow_up_structured_data")
    def test_process_structured_data_templates_with_personalized_summary(
        self, mock_get_follow_up_structured_data: MagicMock
    ) -> None:
        # Create a personalized summary template
        personalized_template = StructuredMeetingDataTemplate.objects.create(
            schema_definition=self.schema,
            title="Personalized Summary",
            internal_name="personalized_summary",
            kind="personalized_summary",
            initial_data={},
            context="{}",
            prompt=Prompt.objects.create(unique_name="test_prompt", user_prompt="Test prompt"),
        )
        self.rule.follow_up_templates.add(personalized_template)
        self.rule.save()

        # Verify we have 2 templates
        self.assertEqual(self.rule.follow_up_templates.count(), 2)

        mock_get_follow_up_structured_data.return_value = {"structured_data": "Follow-up structured data"}

        process_structured_data_templates(self.note.uuid, self.user.uuid, self.diarized_trans_with_names)

        # Verify that only the regular template was processed
        self.assertEqual(StructuredMeetingData.objects.filter(note=self.note).count(), 1)
        follow_up = StructuredMeetingData.objects.filter(note=self.note)[0]
        self.assertEqual(follow_up.data, {"structured_data": "Follow-up structured data"})
        self.assertEqual(follow_up.status, StructuredMeetingData.Status.COMPLETED)
        # Verify get_follow_up_structured_data was only called once for the regular template
        mock_get_follow_up_structured_data.assert_called_once()
        # Verify the exact parameters passed to get_follow_up_structured_data
        mock_get_follow_up_structured_data.assert_called_once_with(
            self.template, self.diarized_trans_with_names, self.note
        )


@override_settings(AWS_MEETING_BUCKET="testbucket", AWS_S3_FILE_FOLDER="testfolder")
class ProcessPhoneCallRecordingTests(TestCase):
    def setUp(self) -> None:
        self.user = User.objects.create(username="testuser", email="<EMAIL>")
        self.organization = Organization.objects.create(name="Test Organization")
        self.user.organization = self.organization
        self.user.save()
        self.note = Note.objects.create(note_owner=self.user, status=Note.PROCESSING_STATUS.uploaded)
        self.bot = MeetingBot.objects.create(note=self.note, bot_owner=self.user)

    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch("deepinsights.meetingsapp.tasks._resolved_twilio_bot_media_url")
    def test_process_phone_call_recording_no_bot(
        self, mock_resolved_twilio_bot_media_url: MagicMock, mock_fetch: MagicMock
    ) -> None:
        self.bot.delete()

        with self.assertRaises(Exception) as context:
            process_phone_call_recording(self.bot.uuid, False)

        self.assertIn("MeetingBot", str(context.exception))
        self.assertIn("does not exist", str(context.exception))
        mock_resolved_twilio_bot_media_url.assert_not_called()
        mock_fetch.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch("deepinsights.meetingsapp.tasks._resolved_twilio_bot_media_url")
    def test_process_phone_call_recording_bot_has_no_note(
        self, mock_resolved_twilio_bot_media_url: MagicMock, mock_fetch: MagicMock
    ) -> None:
        self.bot.note = None
        self.bot.save()

        with self.assertRaises(Exception) as context:
            process_phone_call_recording(self.bot.uuid, False)

        self.assertIn("MeetingBot", str(context.exception))
        self.assertIn("does not have an associated note", str(context.exception))
        mock_resolved_twilio_bot_media_url.assert_not_called()
        mock_fetch.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch("deepinsights.meetingsapp.tasks._resolved_twilio_bot_media_url")
    def test_process_phone_call_recording_no_user(
        self, mock_resolved_twilio_bot_media_url: MagicMock, mock_fetch_and_save_deepgram_transcript: MagicMock
    ) -> None:
        self.note.note_owner = None
        self.note.save()

        with self.assertRaises(Exception) as context:
            process_phone_call_recording(self.bot.uuid, False)

        self.assertIn("Note", str(context.exception))
        self.assertIn("does not have an associated user", str(context.exception))
        mock_resolved_twilio_bot_media_url.assert_not_called()
        mock_fetch_and_save_deepgram_transcript.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch("deepinsights.meetingsapp.tasks._resolved_twilio_bot_media_url")
    def test_process_phone_call_recording_already_processed(
        self, mock_resolved_twilio_bot_media_url: MagicMock, mock_fetch_and_save_deepgram_transcript: MagicMock
    ) -> None:
        self.note.data_source = Note.DataSource.RECALL
        self.note.status = Note.PROCESSING_STATUS.processed
        self.note.save()

        process_phone_call_recording(self.bot.uuid, False)

        self.note.refresh_from_db()
        self.assertEqual(self.note.data_source, Note.DataSource.RECALL)
        mock_resolved_twilio_bot_media_url.assert_not_called()
        mock_fetch_and_save_deepgram_transcript.assert_not_called()

    @patch("deepinsights.core.aws.boto3.Session")
    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch("deepinsights.meetingsapp.tasks._resolved_twilio_bot_media_url")
    def test_process_phone_call_recording_has_asr_response(
        self,
        mock_resolved_twilio_bot_media_url: MagicMock,
        mock_fetch_and_save_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
        mock_session: MagicMock,
    ) -> None:
        self.note.raw_asr_response = "ASR response"
        self.note.file_path = "test.mp3"
        self.note.save()

        with patch.object(MeetingBot, "delete_media") as mock_delete_media:
            process_phone_call_recording(self.bot.uuid, False)
            mock_delete_media.assert_called_once()

        self.note.refresh_from_db()
        self.assertIsNone(self.note.file_path)
        self.assertEqual(self.note.data_source, Note.DataSource.TWILIO)
        mock_resolved_twilio_bot_media_url.assert_not_called()
        mock_fetch_and_save_deepgram_transcript.assert_not_called()
        mock_process_note_common.assert_called_once_with(self.note, self.user, "ASR response", "deepgram")
        mock_delete = mock_session.return_value.client.return_value.delete_object
        mock_delete.assert_called_once_with(Bucket="testbucket", Key="test.mp3")

    @patch("deepinsights.core.aws.boto3.Session")
    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch("deepinsights.meetingsapp.tasks._resolved_twilio_bot_media_url")
    def test_process_phone_call_recording_success(
        self,
        mock_resolved_twilio_bot_media_url: MagicMock,
        mock_fetch_and_save_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
        mock_session: MagicMock,
    ) -> None:
        mock_resolved_twilio_bot_media_url.return_value = "http://example.com/media.mp3"
        mock_fetch_and_save_deepgram_transcript.return_value = {"results": {"utterances": []}}

        self.note.file_path = "test.mp3"
        self.note.save()

        process_phone_call_recording(self.bot.uuid, False)

        self.note.refresh_from_db()
        self.assertEqual(self.note.data_source, Note.DataSource.TWILIO)
        mock_process_note_common.assert_called_once_with(
            self.note, self.user, {"results": {"utterances": []}}, "deepgram"
        )
        mock_resolved_twilio_bot_media_url.assert_called_once_with(self.bot, self.note, self.user)
        mock_fetch_and_save_deepgram_transcript.assert_called_once_with(
            {"url": "http://example.com/media.mp3"}, self.note, self.user
        )
        mock_delete = mock_session.return_value.client.return_value.delete_object
        mock_delete.assert_called_once_with(Bucket="testbucket", Key="test.mp3")

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch("deepinsights.meetingsapp.tasks._resolved_twilio_bot_media_url")
    def test_process_phone_call_recording_no_media_url(
        self,
        mock_resolved_twilio_bot_media_url: MagicMock,
        mock_fetch_and_save_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_resolved_twilio_bot_media_url.return_value = None

        with patch.object(MeetingBot, "delete_media") as mock_delete_media, self.assertRaises(Exception) as context:
            process_phone_call_recording(self.bot.uuid, False)
            mock_delete_media.assert_not_called()

        self.assertIn("No media URL available for bot", str(context.exception))
        self.note.refresh_from_db()
        self.assertIsNone(self.note.data_source)
        mock_resolved_twilio_bot_media_url.assert_called_once_with(self.bot, self.note, self.user)
        mock_fetch_and_save_deepgram_transcript.assert_not_called()
        mock_process_note_common.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch("deepinsights.meetingsapp.tasks._resolved_twilio_bot_media_url")
    def test_process_phone_call_recording_no_asr_response(
        self,
        mock_resolved_twilio_bot_media_url: MagicMock,
        mock_fetch_and_save_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_resolved_twilio_bot_media_url.return_value = "http://example.com/media.mp3"
        mock_fetch_and_save_deepgram_transcript.return_value = None

        with self.assertRaises(Exception) as context:
            process_phone_call_recording(self.bot.uuid, False)

        self.note.refresh_from_db()
        self.assertIsNone(self.note.data_source)
        self.assertIn("No ASR response returned", str(context.exception))
        mock_resolved_twilio_bot_media_url.assert_called_once_with(self.bot, self.note, self.user)
        mock_fetch_and_save_deepgram_transcript.assert_called_once_with(
            {"url": "http://example.com/media.mp3"}, self.note, self.user
        )
        mock_process_note_common.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch("deepinsights.meetingsapp.tasks._resolved_twilio_bot_media_url")
    def test_process_phone_call_recording_final_retry(
        self,
        mock_resolved_twilio_bot_media_url: MagicMock,
        mock_fetch_and_save_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        mock_resolved_twilio_bot_media_url.return_value = "http://example.com/media.mp3"
        mock_fetch_and_save_deepgram_transcript.return_value = None

        max_retries = process_phone_call_recording.max_retries
        process_phone_call_recording.max_retries = 0
        process_phone_call_recording(self.bot.uuid, False)
        process_phone_call_recording.max_retries = max_retries

        mock_resolved_twilio_bot_media_url.assert_called_once_with(self.bot, self.note, self.user)
        mock_fetch_and_save_deepgram_transcript.assert_called_once_with(
            {"url": "http://example.com/media.mp3"}, self.note, self.user
        )
        mock_process_note_common.assert_called_once_with(
            self.note, self.user, {"metadata": {"duration": 0}, "results": {"utterances": []}}, "deepgram"
        )

    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch("deepinsights.meetingsapp.tasks._resolved_twilio_bot_media_url")
    def test_process_phone_call_recording_force_update(
        self,
        mock_resolved_twilio_bot_media_url: MagicMock,
        mock_fetch_and_save_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
    ) -> None:
        self.note.data_source = Note.DataSource.RECALL
        self.note.status = Note.PROCESSING_STATUS.processed
        self.note.save()
        mock_resolved_twilio_bot_media_url.return_value = "http://example.com/media.mp3"
        mock_fetch_and_save_deepgram_transcript.return_value = {"results": {"utterances": []}}

        process_phone_call_recording(self.bot.uuid, True)

        self.note.refresh_from_db()
        self.assertEqual(self.note.status, Note.PROCESSING_STATUS.processed)
        self.assertEqual(self.note.data_source, Note.DataSource.TWILIO)
        mock_resolved_twilio_bot_media_url.assert_called_once_with(self.bot, self.note, self.user)
        mock_fetch_and_save_deepgram_transcript.assert_called_once_with(
            {"url": "http://example.com/media.mp3"}, self.note, self.user
        )
        mock_process_note_common.assert_called_once_with(
            self.note, self.user, {"results": {"utterances": []}}, "deepgram"
        )

    @patch("deepinsights.core.aws.boto3.Session")
    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch("deepinsights.meetingsapp.tasks._resolved_twilio_bot_media_url")
    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=True, CELERY_TASK_LOCK_TIMEOUT=1)
    def test_process_phone_call_recording_locking(
        self,
        mock_cache: MagicMock,
        mock_resolved_twilio_bot_media_url: MagicMock,
        mock_fetch_and_save_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
        mock_session: MagicMock,
    ) -> None:
        mock_resolved_twilio_bot_media_url.return_value = "http://example.com/media.mp3"
        mock_fetch_and_save_deepgram_transcript.return_value = {"results": {"utterances": []}}

        self.note.file_path = "test.mp3"
        self.note.save()

        result = process_phone_call_recording.apply((self.bot.uuid, False))
        self.assertTrue(result.successful())

        mock_cache.lock.assert_called_once_with(str(self.bot.uuid), timeout=1)
        mock_process_note_common.assert_called_once_with(
            self.note, self.user, {"results": {"utterances": []}}, "deepgram"
        )
        mock_resolved_twilio_bot_media_url.assert_called_once_with(self.bot, self.note, self.user)
        mock_fetch_and_save_deepgram_transcript.assert_called_once_with(
            {"url": "http://example.com/media.mp3"}, self.note, self.user
        )
        mock_delete = mock_session.return_value.client.return_value.delete_object
        mock_delete.assert_called_once_with(Bucket="testbucket", Key="test.mp3")

    @patch("deepinsights.core.aws.boto3.Session")
    @patch("deepinsights.meetingsapp.tasks._process_note_common")
    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch("deepinsights.meetingsapp.tasks._resolved_twilio_bot_media_url")
    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=False)
    def test_process_phone_call_recording_locking_disabled(
        self,
        mock_cache: MagicMock,
        mock_resolved_twilio_bot_media_url: MagicMock,
        mock_fetch_and_save_deepgram_transcript: MagicMock,
        mock_process_note_common: MagicMock,
        mock_session: MagicMock,
    ) -> None:
        mock_resolved_twilio_bot_media_url.return_value = "http://example.com/media.mp3"
        mock_fetch_and_save_deepgram_transcript.return_value = {"results": {"utterances": []}}

        self.note.file_path = "test.mp3"
        self.note.save()

        result = process_phone_call_recording.apply((self.bot.uuid, False))

        self.assertTrue(result.successful())
        mock_cache.lock.assert_not_called()
        mock_process_note_common.assert_called_once_with(
            self.note, self.user, {"results": {"utterances": []}}, "deepgram"
        )
        mock_resolved_twilio_bot_media_url.assert_called_once_with(self.bot, self.note, self.user)
        mock_fetch_and_save_deepgram_transcript.assert_called_once_with(
            {"url": "http://example.com/media.mp3"}, self.note, self.user
        )
        mock_delete = mock_session.return_value.client.return_value.delete_object
        mock_delete.assert_called_once_with(Bucket="testbucket", Key="test.mp3")

    @patch("deepinsights.meetingsapp.tasks.fetch_and_save_deepgram_transcript")
    @patch("deepinsights.meetingsapp.tasks._resolved_twilio_bot_media_url")
    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=True, CELERY_TASK_LOCK_TIMEOUT=1)
    def test_process_phone_call_recording_locking_failure(
        self, mock_cache: MagicMock, mock_resolved_twilio_bot_media_url: MagicMock, mock_fetch: MagicMock
    ) -> None:
        self.bot.delete()

        result = process_phone_call_recording.apply((self.bot.uuid, False))
        self.assertFalse(result.successful())
        self.assertIn("MeetingBot", str(result.result))
        self.assertIn("does not exist", str(result.result))

        mock_resolved_twilio_bot_media_url.assert_not_called()
        mock_fetch.assert_not_called()
        mock_cache.lock.assert_has_calls(
            [
                call(str(self.bot.uuid), timeout=1),
                call(str(self.bot.uuid), timeout=1),
                call(str(self.bot.uuid), timeout=1),
            ],
            any_order=True,  # There are __enter__ and __exit__calls interspersed.
        )


class TestLockedTask:
    @patch("deepinsights.meetingsapp.tasks.cache")
    def test_locked_task_disabled(
        self, mock_cache: MagicMock, settings: SettingsWrapper, caplog: pytest.LogCaptureFixture
    ) -> None:
        settings.ENABLE_CELERY_TASK_LOCKING = False

        @locked_task(arg_index=0)
        def f(arg_zero: str, arg_one: int) -> int:
            return 1

        with caplog.at_level(logging.INFO):
            assert f("test", 1) == 1
            assert len(caplog.messages) == 1
            assert "Celery task locking disabled." in caplog.messages[0]
        mock_cache.lock.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.cache")
    def test_locked_task_not_supported(
        self, mock_cache: MagicMock, settings: SettingsWrapper, caplog: pytest.LogCaptureFixture
    ) -> None:
        settings.ENABLE_CELERY_TASK_LOCKING = True

        del mock_cache.lock

        @locked_task(arg_index=0)
        def f(arg_zero: str, arg_one: int) -> int:
            return 1

        with caplog.at_level(logging.WARNING):
            assert f("test", 1) == 1
            assert len(caplog.messages) == 1
            assert "Cache does not support locking." in caplog.messages[0]

    @patch("deepinsights.meetingsapp.tasks.cache")
    def test_locked_task_enabled(self, mock_cache: MagicMock, settings: SettingsWrapper) -> None:
        settings.ENABLE_CELERY_TASK_LOCKING = True
        settings.CELERY_TASK_LOCK_TIMEOUT = 1

        @locked_task(arg_index=0)
        def f(arg_zero: str, arg_one: int) -> int:
            return 1

        assert f("test", 1) == 1
        mock_cache.lock.assert_called_once_with("test", timeout=1)

    @patch("deepinsights.meetingsapp.tasks.cache")
    def test_locked_task_enabled_arg_index(self, mock_cache: MagicMock, settings: SettingsWrapper) -> None:
        settings.ENABLE_CELERY_TASK_LOCKING = True
        settings.CELERY_TASK_LOCK_TIMEOUT = 1

        @locked_task(arg_index=1)
        def f(arg_zero: str, arg_one: int) -> int:
            return 1

        assert f("test", 1) == 1
        mock_cache.lock.assert_called_once_with("1", timeout=1)

    @locked_task(arg_index=2)
    def _f(self, arg_zero: str, arg_one: int) -> str:
        return "hello"

    @patch("deepinsights.meetingsapp.tasks.cache")
    def test_locked_task_enabled_bound_method(self, mock_cache: MagicMock, settings: SettingsWrapper) -> None:
        settings.ENABLE_CELERY_TASK_LOCKING = True
        settings.CELERY_TASK_LOCK_TIMEOUT = 1

        assert self._f("test", 1) == "hello"
        mock_cache.lock.assert_called_once_with("1", timeout=1)

    @patch("deepinsights.meetingsapp.tasks.cache")
    @pytest.mark.parametrize("arg_index", [-1, 2, None, "invalid"])
    def test_locked_task_enabled_invalid_arg_index(
        self, mock_cache: MagicMock, settings: SettingsWrapper, arg_index: Any
    ) -> None:
        settings.ENABLE_CELERY_TASK_LOCKING = True
        settings.CELERY_TASK_LOCK_TIMEOUT = 1

        @locked_task(arg_index=arg_index)
        def f(arg_zero: str, arg_one: int) -> int:
            return 1

        with pytest.raises(ValueError) as context:
            assert f("test", 1) == 1
            mock_cache.lock.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.cache")
    def test_locked_task_lock_expired(
        self, mock_cache: MagicMock, settings: SettingsWrapper, caplog: pytest.LogCaptureFixture
    ) -> None:
        settings.ENABLE_CELERY_TASK_LOCKING = True
        settings.CELERY_TASK_LOCK_TIMEOUT = 1

        @locked_task(arg_index=0)
        def f(arg_zero: str, arg_one: int) -> int:
            return 1

        mock_cache.lock.return_value.__exit__.side_effect = redis.exceptions.LockNotOwnedError

        with caplog.at_level(logging.ERROR):
            assert f("test", 1) == 1
            assert len(caplog.messages) == 1
            assert "tried to release an un-owned lock" in caplog.messages[0]

    @patch("deepinsights.meetingsapp.tasks.cache")
    def test_locked_task_exception_result(self, mock_cache: MagicMock, settings: SettingsWrapper) -> None:
        settings.ENABLE_CELERY_TASK_LOCKING = True
        settings.CELERY_TASK_LOCK_TIMEOUT = 1

        @locked_task(arg_index=0)
        def f(arg_zero: str, arg_one: int) -> int:
            raise Exception("Test")

        with pytest.raises(Exception, match="Test"):
            assert not f("test", 1)
            mock_cache.lock.assert_called_once_with("test", timeout=1)


class TestReprocessNoteAfterSwapping:
    @pytest.fixture(autouse=True)
    def setUp(self) -> None:
        self.user = User.objects.create(username="testuser", email="<EMAIL>")
        self.note = Note.objects.create(
            note_owner=self.user,
            status=Note.PROCESSING_STATUS.uploaded,
            diarized_trans_with_names='speaker0\t"0.0-->5.2\nHello, this is Jon speaking.\n\nspeaker1\t"5.5-->10.8\nHi Jon, Jane here. How are you today?',
            metadata={"tags": ["original", "tags"]},
        )
        Attendee.objects.create(attendee_name="Jon Doe", note=self.note, speaker_alias="speaker0")
        Attendee.objects.create(attendee_name="Jane Doe", note=self.note, speaker_alias="speaker1")
        self.speaker_mapping = {"speaker0": "Jon Doe", "speaker1": "Jane Doe"}
        self.transcript_with_names = 'Jon Doe\t"0.0-->5.2\nHello, this is Jon speaking.\n\nJane Doe\t"5.5-->10.8\nHi Jon, Jane here. How are you today?'

    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.substitute_names_in_transcript")
    @patch("deepinsights.meetingsapp.tasks.process_structured_data_templates")
    def test_reprocess_note_after_swapping_success(
        self,
        mock_process_structured_data_templates: MagicMock,
        mock_substitute_names_in_transcript: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
    ) -> None:
        mock_substitute_names_in_transcript.return_value = self.transcript_with_names
        summary = Summary.model_validate({"summary": [{"topic": "new summary", "bullets": ["new summary"]}]})
        mock_get_summary.return_value = (summary, True)
        tasks_and_takeaways = TasksAndTakeaways.model_validate(
            {
                "advisor_notes": ["new advisor note"],
                "key_takeaways": ["new takeaway"],
                "keywords": ["new", "tags"],
                "action_items": [
                    {"description": "new task", "assignee": "Jon Doe", "due_date": "2023-10-01"},
                ],
            }
        )
        mock_get_tasks_and_takeaways.return_value = (tasks_and_takeaways, True)

        reprocess_note_after_swapping.apply((self.note.uuid, self.speaker_mapping))

        self.note.refresh_from_db()
        assert self.note.summary == summary.model_dump()
        assert self.note.advisor_notes == ["new advisor note"]
        assert self.note.key_takeaways == ["new takeaway"]
        assert self.note.metadata
        assert self.note.metadata["tags"] == ["new", "tags"]
        assert self.note.status == Note.PROCESSING_STATUS.processed
        assert Task.objects.filter(note=self.note).count() == 1
        assert list(Task.objects.filter(note=self.note).values_list("task_title", flat=True)) == ["new task"]

        mock_process_structured_data_templates.delay_on_commit.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.substitute_names_in_transcript")
    def test_reprocess_note_after_swapping_no_summary_no_tasks(
        self,
        mock_substitute_names_in_transcript: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
        caplog: pytest.LogCaptureFixture,
    ) -> None:
        mock_substitute_names_in_transcript.return_value = self.transcript_with_names
        mock_get_summary.return_value = (Summary(), False)
        mock_get_tasks_and_takeaways.return_value = (TasksAndTakeaways(), False)

        original_summary = self.note.summary
        original_advisor_notes = self.note.advisor_notes
        original_key_takeaways = self.note.key_takeaways
        original_tags = (self.note.metadata or {})["tags"]

        with caplog.at_level(level="ERROR"):
            reprocess_note_after_swapping.apply((self.note.uuid, self.speaker_mapping))
            assert any("No summary and tasks and takeaways generated" in message for message in caplog.messages)

        self.note.refresh_from_db()
        assert self.note.summary == original_summary
        assert self.note.advisor_notes == original_advisor_notes
        assert self.note.key_takeaways == original_key_takeaways
        assert self.note.metadata
        assert self.note.metadata["tags"] == original_tags
        assert self.note.status != Note.PROCESSING_STATUS.processed

    @pytest.mark.parametrize(
        "has_tasks_and_takeaways_content, has_summary_content",
        [
            (True, True),
            (True, False),
            (False, True),
        ],
        ids=["both", "only_tasks_and_takeaways", "only_summary"],
    )
    @patch("deepinsights.meetingsapp.tasks.substitute_names_in_transcript")
    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    def test_process_note_common_summary_and_tasks_and_takeaways_not_both_empty(
        self,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
        mock_substitute_names_in_transcript: MagicMock,
        caplog: pytest.LogCaptureFixture,
        has_tasks_and_takeaways_content: bool,
        has_summary_content: bool,
    ) -> None:
        mock_substitute_names_in_transcript.return_value = self.transcript_with_names
        mock_get_tasks_and_takeaways.return_value = (
            TasksAndTakeaways.model_validate(
                {
                    "key_takeaways": [],
                    "action_items": [],
                    "advisor_notes": [],
                    "keywords": [],
                }
            ),
            has_tasks_and_takeaways_content,
        )
        summary = Summary.model_validate({"sections": []})
        mock_get_summary.return_value = (summary, has_summary_content)
        with caplog.at_level(logging.ERROR):
            reprocess_note_after_swapping.apply((self.note.uuid, self.speaker_mapping))
            assert not any("No summary and tasks and takeaways generated" in message for message in caplog.messages)

        old_summary = self.note.summary
        reprocess_note_after_swapping.apply((self.note.uuid, self.speaker_mapping))

        self.note.refresh_from_db()

        assert self.note.summary == summary.model_dump()
        assert self.note.advisor_notes == []
        assert self.note.key_takeaways == []
        assert self.note.metadata
        assert self.note.metadata["tags"] == []
        assert len(Task.objects.filter(note=self.note)) == 0
        assert self.note.status == Note.PROCESSING_STATUS.processed

    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.substitute_names_in_transcript")
    def test_reprocess_note_after_swapping_exception(
        self,
        mock_substitute_names_in_transcript: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
    ) -> None:
        error = Exception("Test exception")
        mock_substitute_names_in_transcript.side_effect = error

        result = reprocess_note_after_swapping.apply((self.note.uuid, self.speaker_mapping))

        assert not result.successful()
        assert result.result == error
        self.note.refresh_from_db()
        assert self.note.status != Note.PROCESSING_STATUS.processed
        mock_get_summary.assert_not_called()
        mock_get_tasks_and_takeaways.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.substitute_names_in_transcript")
    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=True, CELERY_TASK_LOCK_TIMEOUT=1)
    def test_reprocess_note_after_swapping_locking(
        self,
        mock_cache: MagicMock,
        mock_substitute_names_in_transcript: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
    ) -> None:
        mock_substitute_names_in_transcript.return_value = self.transcript_with_names
        summary = Summary.model_validate(
            {
                "summary": [
                    {"topic": "new summary", "bullets": ["new summary"]},
                ]
            }
        )
        mock_get_summary.return_value = (summary, True)
        mock_get_tasks_and_takeaways.return_value = (
            TasksAndTakeaways.model_validate(
                {
                    "advisor_notes": ["new advisor note"],
                    "key_takeaways": ["new takeaway"],
                    "keywords": ["new", "tags"],
                    "action_items": [
                        {"description": "new task", "assignee": "Jon Doe", "due_date": "2023-10-01"},
                    ],
                }
            ),
            True,
        )

        result = reprocess_note_after_swapping.apply((self.note.uuid, self.speaker_mapping))

        assert result.successful()
        self.note.refresh_from_db()
        assert self.note.summary == summary.model_dump()
        assert self.note.advisor_notes == ["new advisor note"]
        assert self.note.key_takeaways == ["new takeaway"]
        assert self.note.metadata
        assert self.note.metadata["tags"] == ["new", "tags"]
        assert self.note.status == Note.PROCESSING_STATUS.processed
        assert Task.objects.filter(note=self.note).count() == 1
        assert list(Task.objects.filter(note=self.note).values_list("task_title", flat=True)) == ["new task"]

        mock_cache.lock.assert_called_once_with(str(self.note.uuid), timeout=1)

    @patch("deepinsights.meetingsapp.tasks.get_tasks_and_takeaways")
    @patch("deepinsights.meetingsapp.tasks.get_summary")
    @patch("deepinsights.meetingsapp.tasks.substitute_names_in_transcript")
    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=False)
    def test_reprocess_note_after_swapping_locking_disabled(
        self,
        mock_cache: MagicMock,
        mock_substitute_names_in_transcript: MagicMock,
        mock_get_summary: MagicMock,
        mock_get_tasks_and_takeaways: MagicMock,
    ) -> None:
        mock_substitute_names_in_transcript.return_value = self.transcript_with_names
        summary = Summary.model_validate({"summary": [{"topic": "new summary", "bullets": ["new summary"]}]})
        mock_get_summary.return_value = (summary, True)
        mock_get_tasks_and_takeaways.return_value = (
            TasksAndTakeaways.model_validate(
                {
                    "advisor_notes": ["new advisor note"],
                    "key_takeaways": ["new takeaway"],
                    "keywords": ["new", "tags"],
                    "action_items": [
                        {"description": "new task", "assignee": "Jon Doe", "due_date": "2023-10-01"},
                    ],
                }
            ),
            True,
        )

        result = reprocess_note_after_swapping.apply((self.note.uuid, self.speaker_mapping))

        assert result.successful()
        self.note.refresh_from_db()
        assert self.note.summary == summary.model_dump()
        assert self.note.advisor_notes == ["new advisor note"]
        assert self.note.key_takeaways == ["new takeaway"]
        assert self.note.metadata
        assert self.note.metadata["tags"] == ["new", "tags"]
        assert self.note.status == Note.PROCESSING_STATUS.processed
        assert Task.objects.filter(note=self.note).count() == 1
        assert list(Task.objects.filter(note=self.note).values_list("task_title", flat=True)) == ["new task"]

        mock_cache.lock.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=True, CELERY_TASK_LOCK_TIMEOUT=1)
    def test_reprocess_note_after_swapping_locking_failure(self, mock_cache: MagicMock) -> None:
        invalid_uuid = uuid.uuid4()
        result = reprocess_note_after_swapping.apply((invalid_uuid, self.speaker_mapping))

        assert not result.successful()
        mock_cache.lock.assert_called_once_with(str(invalid_uuid), timeout=1)


class TestGenerateAgenda(TestCase):
    def setUp(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        self.user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)
        self.test_client = Client.objects.create(name="Test Client", organization=org)
        self.meeting_type = MeetingType.objects.create(name="Test Meeting Type")
        self.other_meeting_type = MeetingType.objects.create(name="Other Meeting Type")
        self.agenda_template = StructuredMeetingDataTemplate.objects.create(
            schema_definition=StructuredMeetingDataSchema.objects.create(name="agenda", schema={}),
            title="Test Template",
            internal_name="test_template",
            kind="agenda",
            initial_data={"content": "template content"},
            prompt=Prompt.objects.create(unique_name="test_prompt", user_prompt="Test prompt"),
        )
        self.note = Note.objects.create(note_owner=self.user, status=Note.PROCESSING_STATUS.processed)
        self.meeting_type.agenda_templates.add(self.agenda_template)
        self.meeting_type.save()
        self.interaction = ClientInteraction.objects.create(meeting_type=self.meeting_type, note=self.note)
        self.interaction.clients.add(self.test_client)
        self.interaction.save()

    @patch("deepinsights.meetingsapp.tasks.generate_filled_agenda")
    def test_generate_agenda_success(self, mock_generate_filled_agenda: MagicMock) -> None:
        # Create agenda for the interaction
        schema_unstructured = StructuredMeetingDataSchema.objects.get(name="unstructured_text")
        self.interaction.agenda = StructuredMeetingData.objects.create(
            title="Meeting Agenda",
            schema=schema_unstructured.schema,
            data={"content": "", "format": "markdown"},
            note=None,
        )
        self.interaction.save()

        mock_generate_filled_agenda.return_value = {"content": "filled agenda", "format": "markdown"}

        result = generate_agenda.apply(
            (self.user.uuid, self.test_client.uuid, self.interaction.uuid, "template content")
        )

        self.assertTrue(result.successful())
        self.interaction.refresh_from_db()
        self.assertIsNotNone(self.interaction.agenda)
        self.assertEqual(self.interaction.agenda.status, StructuredMeetingData.Status.COMPLETED)
        self.assertEqual(self.interaction.agenda.data, {"content": "filled agenda", "format": "markdown"})
        mock_generate_filled_agenda.assert_called_once_with(self.user, self.test_client, "template content")

    def test_generate_agenda_user_not_found(self) -> None:
        result = generate_agenda.apply((uuid.uuid4(), self.test_client.uuid, self.interaction.uuid, "template content"))
        self.assertFalse(result.successful())
        self.assertIn("User matching query does not exist", str(result.result))

    def test_generate_agenda_client_not_found(self) -> None:
        result = generate_agenda.apply((self.user.uuid, uuid.uuid4(), self.interaction.uuid, "template content"))
        self.assertFalse(result.successful())
        self.assertIn("Client matching query does not exist", str(result.result))

    def test_generate_agenda_client_interaction_not_found(self) -> None:
        result = generate_agenda.apply((self.user.uuid, self.test_client.uuid, uuid.uuid4(), "template content"))
        self.assertFalse(result.successful())
        self.assertIn("ClientInteraction matching query does not exist", str(result.result))

    def test_generate_agenda_no_agenda(self) -> None:
        result = generate_agenda.apply((self.user.uuid, self.test_client.uuid, self.interaction.uuid, ""))
        self.assertFalse(result.successful())
        self.assertIn("No agenda found for interaction", str(result.result))

    def test_generate_agenda_empty_template_content(self) -> None:
        schema_unstructured = StructuredMeetingDataSchema.objects.get(name="unstructured_text")
        self.interaction.agenda = StructuredMeetingData.objects.create(
            title="Meeting Agenda",
            schema=schema_unstructured.schema,
            data={"content": "", "format": "markdown"},
            note=None,
            status=StructuredMeetingData.Status.PROCESSING,
        )
        self.interaction.save()
        result = generate_agenda.apply((self.user.uuid, self.test_client.uuid, self.interaction.uuid, ""))
        self.assertTrue(result.successful())
        self.interaction.refresh_from_db()
        self.assertEqual(self.interaction.agenda.status, StructuredMeetingData.Status.FAILED)
        self.assertEqual(self.interaction.agenda.data, {"content": "", "format": "markdown"})

    @patch("deepinsights.meetingsapp.tasks.generate_filled_agenda")
    def test_generate_agenda_generation_error(self, mock_generate_filled_agenda: MagicMock) -> None:
        error = Exception("Test exception")
        mock_generate_filled_agenda.side_effect = error
        schema_unstructured = StructuredMeetingDataSchema.objects.get(name="unstructured_text")

        self.interaction.agenda = StructuredMeetingData.objects.create(
            title="Meeting Agenda",
            schema=schema_unstructured.schema,
            data={"content": "", "format": "markdown"},
            note=None,
        )
        self.interaction.save()

        result = generate_agenda.apply(
            (self.user.uuid, self.test_client.uuid, self.interaction.uuid, "template content")
        )

        self.assertFalse(result.successful())
        self.assertEqual(result.result, error)
        self.interaction.refresh_from_db()
        self.assertEqual(self.interaction.agenda.status, StructuredMeetingData.Status.FAILED)
        self.assertEqual(self.interaction.agenda.data["content"], "")

    @patch("deepinsights.meetingsapp.tasks.generate_filled_agenda")
    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=True, CELERY_TASK_LOCK_TIMEOUT=1)
    def test_generate_agenda_locking(self, mock_cache: MagicMock, mock_generate_filled_agenda: MagicMock) -> None:
        # Create agenda for the interaction
        schema_unstructured = StructuredMeetingDataSchema.objects.get(name="unstructured_text")
        self.interaction.agenda = StructuredMeetingData.objects.create(
            title="Meeting Agenda",
            schema=schema_unstructured.schema,
            data={"content": "", "format": "markdown"},
            note=None,
            status=StructuredMeetingData.Status.PROCESSING,
        )
        self.interaction.save()

        mock_generate_filled_agenda.return_value = {"content": "filled agenda", "format": "markdown"}

        result = generate_agenda.apply(
            (self.user.uuid, self.test_client.uuid, self.interaction.uuid, "template content")
        )

        self.assertTrue(result.successful())
        self.interaction.refresh_from_db()
        self.assertIsNotNone(self.interaction.agenda)
        self.assertEqual(self.interaction.agenda.data, {"content": "filled agenda", "format": "markdown"})
        self.assertEqual(self.interaction.agenda.title, "Meeting Agenda")

        mock_generate_filled_agenda.assert_called_once_with(self.user, self.test_client, "template content")
        mock_cache.lock.assert_called_once_with(str(self.interaction.uuid), timeout=1)

    @patch("deepinsights.meetingsapp.tasks.generate_filled_agenda")
    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=False)
    def test_generate_agenda_locking_disabled(
        self, mock_cache: MagicMock, mock_generate_filled_agenda: MagicMock
    ) -> None:
        # Create agenda for the interaction
        schema_unstructured = StructuredMeetingDataSchema.objects.get(name="unstructured_text")
        self.interaction.agenda = StructuredMeetingData.objects.create(
            title="Meeting Agenda",
            schema=schema_unstructured.schema,
            data={"content": "", "format": "markdown"},
            note=None,
        )
        self.interaction.save()

        mock_generate_filled_agenda.return_value = {"content": "filled agenda", "format": "markdown"}

        result = generate_agenda.apply(
            (self.user.uuid, self.test_client.uuid, self.interaction.uuid, "template content")
        )

        self.assertTrue(result.successful())
        self.interaction.refresh_from_db()
        self.assertIsNotNone(self.interaction.agenda)
        self.assertEqual(self.interaction.agenda.data, {"content": "filled agenda", "format": "markdown"})
        self.assertEqual(self.interaction.agenda.title, "Meeting Agenda")

        mock_generate_filled_agenda.assert_called_once_with(self.user, self.test_client, "template content")
        mock_cache.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.generate_filled_agenda")
    @patch("deepinsights.meetingsapp.tasks.cache")
    @override_settings(ENABLE_CELERY_TASK_LOCKING=True, CELERY_TASK_LOCK_TIMEOUT=1)
    def test_generate_agenda_locking_failure(
        self, mock_cache: MagicMock, mock_generate_filled_agenda: MagicMock
    ) -> None:
        # Create agenda for the interaction
        schema_unstructured = StructuredMeetingDataSchema.objects.get(name="unstructured_text")
        self.interaction.agenda = StructuredMeetingData.objects.create(
            title="Meeting Agenda",
            schema=schema_unstructured.schema,
            data={"content": "", "format": "markdown"},
            note=None,
            status=StructuredMeetingData.Status.PROCESSING,
        )
        self.interaction.save()

        error = Exception("Test exception")
        mock_generate_filled_agenda.side_effect = error

        result = generate_agenda.apply(
            (self.user.uuid, self.test_client.uuid, self.interaction.uuid, "template content")
        )

        self.assertFalse(result.successful())
        self.assertEqual(result.result, error)
        self.interaction.refresh_from_db()
        self.assertEqual(self.interaction.agenda.status, StructuredMeetingData.Status.FAILED)
        self.assertEqual(self.interaction.agenda.data["content"], "")

        mock_cache.lock.assert_has_calls(
            [
                call(str(self.interaction.uuid), timeout=1),
                call(str(self.interaction.uuid), timeout=1),
                call(str(self.interaction.uuid), timeout=1),
            ],
            any_order=True,  # There are __enter__ and __exit__calls interspersed.
        )


class TestClientIntelligence(TestCase):
    def setUp(self) -> None:
        org = Organization.objects.create(name="Test Organization")
        self.user = User.objects.create(username="testuser", email="<EMAIL>", organization=org)
        self.test_client = Client.objects.create(name="Test Client", organization=org)
        self.meeting_type = MeetingType.objects.create(name="Test Meeting Type")
        self.note = Note.objects.create(note_owner=self.user, status=Note.PROCESSING_STATUS.processed)
        self.interaction = ClientInteraction.objects.create(meeting_type=self.meeting_type, note=self.note)
        self.interaction.clients.add(self.test_client)
        self.interaction.save()

        # Create unstructured schema if it doesn't exist
        self.unstructured_schema = StructuredMeetingDataSchema.objects.get_or_create(
            name="unstructured_text",
            defaults={
                "schema": {
                    "type": "object",
                    "properties": {"content": {"type": "string"}, "format": {"type": "string"}},
                }
            },
        )[0]

        # Create advisor notes for the interaction
        self.interaction.advisor_notes = StructuredMeetingData.objects.create(
            title="Advisor Notes",
            kind="advisor_notes",
            schema=self.unstructured_schema.schema,
            status=StructuredMeetingData.Status.CREATED,
            data={
                "content": "",
                "format": "markdown",
            },
        )
        self.interaction.save()

    @patch("deepinsights.meetingsapp.tasks.build_client_recap_for_client")
    def test_generate_client_intelligence_success(self, mock_build_client_recap: MagicMock) -> None:
        mock_build_client_recap.return_value = {
            "recap": [{"topic": "Test Topic", "bullets": [{"text": "Test bullet"}]}]
        }

        result = _generate_client_intelligence(self.user.uuid, self.test_client.uuid, self.interaction.uuid)

        self.assertEqual(result, "## Test Topic\n- Test bullet")
        mock_build_client_recap.assert_called_once_with(self.user, self.test_client)

    @patch("deepinsights.meetingsapp.tasks.build_client_recap_for_client")
    def test_generate_client_intelligence_no_data(self, mock_build_client_recap: MagicMock) -> None:
        mock_build_client_recap.return_value = None

        result = _generate_client_intelligence(self.user.uuid, self.test_client.uuid, self.interaction.uuid)
        self.assertIsNone(result)

    def test_generate_client_intelligence_no_advisor_notes(self) -> None:
        self.interaction.advisor_notes = None
        self.interaction.save()
        result = create_advisor_notes_and_fill_with_client_intelligence.apply(
            (self.user.uuid, self.test_client.uuid, self.interaction.uuid), {}
        )
        self.assertFalse(result.successful())
        self.assertIn("No advisor notes found for interaction", str(result.result))

    @patch("deepinsights.meetingsapp.tasks._generate_client_intelligence")
    def test_create_advisor_notes_and_fill_with_client_intelligence_success(
        self, mock_generate_intelligence: MagicMock
    ) -> None:
        mock_generate_intelligence.return_value = "## Test Topic\n- Test bullet"

        create_advisor_notes_and_fill_with_client_intelligence.apply(
            (self.user.uuid, self.test_client.uuid, self.interaction.uuid)
        )

        self.interaction.refresh_from_db()
        self.assertIsNotNone(self.interaction.advisor_notes)
        assert self.interaction.advisor_notes
        self.assertEqual(self.interaction.advisor_notes.data["content"], "## Test Topic\n- Test bullet")
        self.assertEqual(self.interaction.advisor_notes.data["format"], "markdown")
        mock_generate_intelligence.assert_called_once_with(self.user.uuid, self.test_client.uuid, self.interaction.uuid)

    @patch("deepinsights.meetingsapp.tasks._generate_client_intelligence")
    def test_create_advisor_notes_and_fill_with_client_intelligence_generation_error(
        self, mock_generate_intelligence: MagicMock
    ) -> None:
        # Even if generate_client_intelligence fails, we should still create an empty advisor notes.
        mock_generate_intelligence.return_value = None

        result = create_advisor_notes_and_fill_with_client_intelligence.apply(
            (self.user.uuid, self.test_client.uuid, self.interaction.uuid)
        )
        self.assertTrue(result.successful())


class TestProcessClientRecap:
    @patch("deepinsights.meetingsapp.tasks.generate_client_recap")
    def test_process_client_recap_success(self, mock_generate_client_recap: MagicMock) -> None:
        client_recap_uuid = uuid.uuid4()
        user_uuid = uuid.uuid4()

        result = process_client_recap.apply((client_recap_uuid, user_uuid))

        assert result.successful()
        mock_generate_client_recap.assert_called_once_with(client_recap_uuid, user_uuid=user_uuid)

    @patch("deepinsights.meetingsapp.tasks.generate_client_recap")
    def test_process_client_recap_failure(
        self, mock_generate_client_recap: MagicMock, caplog: pytest.LogCaptureFixture
    ) -> None:
        client_recap_uuid = uuid.uuid4()
        user_uuid = uuid.uuid4()
        mock_generate_client_recap.side_effect = Exception("Test exception")

        with caplog.at_level(level="ERROR"):
            result = process_client_recap.apply((client_recap_uuid, user_uuid))
            assert not result.successful()
            # Our logged error, and the error logged by Celery for the job failure
            assert len(caplog.records) == 2
            assert caplog.messages[0] == "Error generating client recap"


@patch("deepinsights.meetingsapp.tasks.fetch_calendar_events", new_callable=AsyncMock)
@patch("deepinsights.meetingsapp.tasks.Flags.UseScheduledEventsForCalendarAPI.is_active_for_user", return_value=True)
@patch("deepinsights.meetingsapp.tasks.update_autojoin_bots")
class TestReconcileCalendarEvents:
    @pytest.fixture(autouse=True)
    def setUp(self, django_user_model: User) -> None:
        self.org = Organization.objects.create(name="Reconcile Test Org")
        self.user = django_user_model.objects.create(username="reconcile_user", organization=self.org)

    def test_flag_disabled(
        self,
        mock_update_autojoin_bots: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_fetch_calendar_events: AsyncMock,
    ) -> None:
        mock_is_active_for_user.return_value = False

        assert reconcile_calendar_events.apply((self.user.uuid,)).successful()

        mock_fetch_calendar_events.assert_not_called()
        mock_update_autojoin_bots.delay.assert_not_called()
        assert not ScheduledEvent.objects.exists()

    def test_cache_hit_skips_processing(
        self,
        mock_update_autojoin_bots: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_fetch_calendar_events: AsyncMock,
        settings: SettingsWrapper,
    ) -> None:
        settings.CALENDAR_EVENT_SYNC_DEFAULT_LOOKAHEAD_SECONDS = 100

        mock_fetch_calendar_events.return_value = ([], True)

        assert reconcile_calendar_events.apply((self.user.uuid,)).successful()

        mock_fetch_calendar_events.assert_called_once_with(self.user, timedelta(seconds=100))

        assert reconcile_calendar_events.apply((self.user.uuid,)).successful()

        mock_update_autojoin_bots.delay.assert_not_called()
        assert mock_fetch_calendar_events.call_count == 1

    def test_force_update_ignores_cache(
        self,
        mock_update_autojoin_bots: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_fetch_calendar_events: AsyncMock,
    ) -> None:
        event = CalendarEvent(
            id="id",
            provider="google",
            all_day=False,
            user_specific_id="user_specific_id",
            title="Event 1",
            body="Event body",
            start_time=datetime.datetime(2023, 10, 1, 10, 0),
            end_time=datetime.datetime(2023, 10, 1, 11, 0),
            participants=[],
            meeting_urls=[],
        )
        mock_fetch_calendar_events.return_value = ([], True)

        assert reconcile_calendar_events.apply((self.user.uuid,)).successful()

        mock_update_autojoin_bots.delay.assert_not_called()

        mock_fetch_calendar_events.return_value = ([event], True)

        assert reconcile_calendar_events.apply((self.user.uuid, True)).successful()

        assert ScheduledEvent.objects.exists()
        assert ScheduledEvent.objects.all()[0].source_data == event.model_dump(mode="json")
        mock_update_autojoin_bots.delay.assert_called_once_with(
            self.user.uuid,
            list(ScheduledEvent.objects.all().values_list("uuid", flat=True)),
            update_recall=False,
            force_update=True,
        )

    def test_fetch_failure_does_not_cache(
        self,
        mock_update_autojoin_bots: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_fetch_calendar_events: AsyncMock,
    ) -> None:
        event = CalendarEvent(
            id="id",
            provider="google",
            all_day=False,
            user_specific_id="user_specific_id",
            title="Event 1",
            body="Event body",
            start_time=datetime.datetime(2023, 10, 1, 10, 0),
            end_time=datetime.datetime(2023, 10, 1, 11, 0),
            participants=[],
            meeting_urls=[],
        )
        mock_fetch_calendar_events.return_value = ([], False)

        assert reconcile_calendar_events.apply((self.user.uuid,)).successful()

        mock_fetch_calendar_events.return_value = ([event], True)

        assert reconcile_calendar_events.apply((self.user.uuid,)).successful()

        assert ScheduledEvent.objects.exists()
        assert ScheduledEvent.objects.all()[0].source_data == event.model_dump(mode="json")
        mock_update_autojoin_bots.delay.assert_called_once_with(
            self.user.uuid,
            list(ScheduledEvent.objects.all().values_list("uuid", flat=True)),
            update_recall=False,
            force_update=False,
        )

    def test_creates_new_events(
        self,
        mock_update_autojoin_bots: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_fetch_calendar_events: AsyncMock,
        caplog: pytest.LogCaptureFixture,
    ) -> None:
        event = CalendarEvent(
            id="id",
            provider="google",
            all_day=False,
            user_specific_id="user_specific_id",
            title="Event",
            body="Event body",
            start_time=datetime.datetime(2023, 10, 1, 10, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2023, 10, 1, 11, 0, tzinfo=datetime.timezone.utc),
            participants=[],
            meeting_urls=[],
        )
        event_two = CalendarEvent(
            id="id_two",
            provider="microsoft",
            all_day=False,
            user_specific_id="user_specific_id_two",
            title="Event Two",
            body="Event body two",
            start_time=datetime.datetime(2023, 10, 1, 12, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2023, 10, 1, 13, 0, tzinfo=datetime.timezone.utc),
            participants=[],
            meeting_urls=[],
        )

        mock_fetch_calendar_events.return_value = ([event, event_two], True)

        with caplog.at_level(logging.INFO):
            assert reconcile_calendar_events.apply((self.user.uuid, False)).successful()
            assert caplog.messages
            assert (
                caplog.messages[0]
                == f"Reconciled calendar events for user {str(self.user.uuid)}. Created 2 events, updated (potentially with no changes) 0 events, deleted 0 events."
            )

        assert ScheduledEvent.objects.count() == 2
        db_event = ScheduledEvent.objects.get(shared_source_id="id")
        db_event_two = ScheduledEvent.objects.get(shared_source_id="id_two")

        assert db_event.user == self.user
        assert db_event.user_specific_source_id == "user_specific_id"
        assert db_event.start_time == event.start_time
        assert db_event.end_time == event.end_time
        assert db_event.source_data == event.model_dump(mode="json")

        assert db_event_two.user == self.user
        assert db_event_two.user_specific_source_id == "user_specific_id_two"
        assert db_event_two.start_time == event_two.start_time
        assert db_event_two.end_time == event_two.end_time
        assert db_event_two.source_data == event_two.model_dump(mode="json")

        mock_update_autojoin_bots.delay.assert_called_once_with(
            self.user.uuid,
            [db_event.uuid, db_event_two.uuid],
            update_recall=False,
            force_update=False,
        )

    def test_updates_existing_events(
        self,
        mock_update_autojoin_bots: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_fetch_calendar_events: AsyncMock,
        caplog: pytest.LogCaptureFixture,
    ) -> None:
        unchanged_event = CalendarEvent(
            id="unchanged_event_id",
            provider="google",
            all_day=False,
            user_specific_id="user_specific_unchanged_event_id",
            title="Unchanged",
            body="Body",
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=1),
            participants=[],
            meeting_urls=[],
        )
        changed_title_event = CalendarEvent(
            id="changed_title_event_id",
            provider="microsoft",
            all_day=False,
            user_specific_id="user_specific_changed_title_event_id",
            title="Changed Title",
            body="Body",
            start_time=timezone.now() + timedelta(hours=2),
            end_time=timezone.now() + timedelta(hours=3),
            participants=[],
            meeting_urls=[],
        )
        changed_body_event = CalendarEvent(
            id="changed_body_event_id",
            provider="microsoft",
            all_day=False,
            user_specific_id="user_specific_changed_body_event_id",
            title="Changed Body",
            body="Body",
            start_time=timezone.now() + timedelta(hours=2),
            end_time=timezone.now() + timedelta(hours=3),
            participants=[],
            meeting_urls=[],
        )
        changed_start_time_event = CalendarEvent(
            id="changed_start_time_event_id",
            provider="microsoft",
            all_day=False,
            user_specific_id="user_specific_changed_start_time_event_id",
            title="Changed Start Time",
            body="Body",
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=1),
            participants=[],
            meeting_urls=[],
        )
        changed_end_time_event = CalendarEvent(
            id="changed_end_time_event_id",
            provider="microsoft",
            all_day=False,
            user_specific_id="user_specific_changed_end_time_event_id",
            title="Changed End Time",
            body="Body",
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=1),
            participants=[],
            meeting_urls=[],
        )
        changed_meeting_urls_event = CalendarEvent(
            id="changed_meeting_urls_event_id",
            provider="microsoft",
            all_day=False,
            user_specific_id="user_specific_changed_meeting_urls_event_id",
            title="Changed Meeting URLs",
            body="Body",
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=1),
            participants=[],
            meeting_urls=[],
        )

        unchanged_scheduled_event, _ = ScheduledEvent.create_or_update_with_calendar_event(self.user, unchanged_event)
        changed_title_scheduled_event, _ = ScheduledEvent.create_or_update_with_calendar_event(
            self.user, changed_title_event
        )
        changed_body_scheduled_event, _ = ScheduledEvent.create_or_update_with_calendar_event(
            self.user, changed_body_event
        )
        changed_start_time_scheduled_event, _ = ScheduledEvent.create_or_update_with_calendar_event(
            self.user, changed_start_time_event
        )
        changed_end_time_scheduled_event, _ = ScheduledEvent.create_or_update_with_calendar_event(
            self.user, changed_end_time_event
        )
        changed_meeting_urls_scheduled_event, _ = ScheduledEvent.create_or_update_with_calendar_event(
            self.user, changed_meeting_urls_event
        )

        updated_changed_title_event = CalendarEvent.model_validate(
            {
                **changed_title_event.model_dump(),
                "title": "Updated Changed Title",
            }
        )
        updated_changed_start_time_event = CalendarEvent.model_validate(
            {
                **changed_start_time_event.model_dump(),
                "start_time": timezone.now() + timedelta(minutes=1),
            }
        )
        updated_changed_body_event = CalendarEvent.model_validate(
            {
                **changed_body_event.model_dump(),
                "body": "Updated Changed Body",
            }
        )
        updated_changed_end_time_event = CalendarEvent.model_validate(
            {
                **changed_end_time_event.model_dump(),
                "end_time": timezone.now() + timedelta(hours=2),
            }
        )
        updated_changed_meeting_urls_event = CalendarEvent.model_validate(
            {
                **changed_meeting_urls_event.model_dump(),
                "meeting_urls": ["https://example.com/updated"],
            }
        )

        mock_fetch_calendar_events.return_value = (
            [
                unchanged_event,
                updated_changed_title_event,
                updated_changed_body_event,
                updated_changed_start_time_event,
                updated_changed_end_time_event,
                updated_changed_meeting_urls_event,
            ],
            True,
        )

        with caplog.at_level(logging.INFO):
            assert reconcile_calendar_events.apply((self.user.uuid, False)).successful()
            assert caplog.messages
            assert (
                caplog.messages[0]
                == f"Reconciled calendar events for user {str(self.user.uuid)}. Created 0 events, updated (potentially with no changes) 6 events, deleted 0 events."
            )

        assert ScheduledEvent.objects.count() == 6
        unchanged_scheduled_event.refresh_from_db()
        changed_title_scheduled_event.refresh_from_db()
        changed_body_scheduled_event.refresh_from_db()
        changed_start_time_scheduled_event.refresh_from_db()
        changed_end_time_scheduled_event.refresh_from_db()
        changed_meeting_urls_scheduled_event.refresh_from_db()

        assert unchanged_scheduled_event.user == self.user
        assert unchanged_scheduled_event.shared_source_id == "unchanged_event_id"
        assert unchanged_scheduled_event.user_specific_source_id == "user_specific_unchanged_event_id"
        assert unchanged_scheduled_event.start_time == unchanged_event.start_time
        assert unchanged_scheduled_event.end_time == unchanged_event.end_time
        assert unchanged_scheduled_event.source_data == unchanged_event.model_dump(mode="json")

        assert changed_title_scheduled_event.user == self.user
        assert changed_title_scheduled_event.shared_source_id == "changed_title_event_id"
        assert changed_title_scheduled_event.user_specific_source_id == "user_specific_changed_title_event_id"
        assert changed_title_scheduled_event.start_time == changed_title_event.start_time
        assert changed_title_scheduled_event.end_time == changed_title_event.end_time
        assert changed_title_scheduled_event.source_data == updated_changed_title_event.model_dump(mode="json")

        assert changed_body_scheduled_event.user == self.user
        assert changed_body_scheduled_event.shared_source_id == "changed_body_event_id"
        assert changed_body_scheduled_event.user_specific_source_id == "user_specific_changed_body_event_id"
        assert changed_body_scheduled_event.start_time == changed_body_event.start_time
        assert changed_body_scheduled_event.end_time == changed_body_event.end_time
        assert changed_body_scheduled_event.source_data == updated_changed_body_event.model_dump(mode="json")

        assert changed_start_time_scheduled_event.user == self.user
        assert changed_start_time_scheduled_event.shared_source_id == "changed_start_time_event_id"
        assert changed_start_time_scheduled_event.user_specific_source_id == "user_specific_changed_start_time_event_id"
        assert changed_start_time_scheduled_event.start_time == updated_changed_start_time_event.start_time
        assert changed_start_time_scheduled_event.end_time == updated_changed_start_time_event.end_time
        assert changed_start_time_scheduled_event.source_data == updated_changed_start_time_event.model_dump(
            mode="json"
        )

        assert changed_end_time_scheduled_event.user == self.user
        assert changed_end_time_scheduled_event.shared_source_id == "changed_end_time_event_id"
        assert changed_end_time_scheduled_event.user_specific_source_id == "user_specific_changed_end_time_event_id"
        assert changed_end_time_scheduled_event.start_time == updated_changed_end_time_event.start_time
        assert changed_end_time_scheduled_event.end_time == updated_changed_end_time_event.end_time
        assert changed_end_time_scheduled_event.source_data == updated_changed_end_time_event.model_dump(mode="json")

        assert changed_meeting_urls_scheduled_event.user == self.user
        assert changed_meeting_urls_scheduled_event.shared_source_id == "changed_meeting_urls_event_id"
        assert (
            changed_meeting_urls_scheduled_event.user_specific_source_id
            == "user_specific_changed_meeting_urls_event_id"
        )
        assert changed_meeting_urls_scheduled_event.start_time == updated_changed_meeting_urls_event.start_time
        assert changed_meeting_urls_scheduled_event.end_time == updated_changed_meeting_urls_event.end_time
        assert changed_meeting_urls_scheduled_event.source_data == updated_changed_meeting_urls_event.model_dump(
            mode="json"
        )

        mock_update_autojoin_bots.delay.assert_called_once_with(
            self.user.uuid,
            [
                changed_start_time_scheduled_event.uuid,
                changed_end_time_scheduled_event.uuid,
                changed_meeting_urls_scheduled_event.uuid,
            ],
            update_recall=False,
            force_update=False,
        )

    def test_deletes_existing_events(
        self,
        mock_update_autojoin_bots: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_fetch_calendar_events: AsyncMock,
        caplog: pytest.LogCaptureFixture,
    ) -> None:
        date = timezone.now()
        event = CalendarEvent(
            id="id",
            provider="google",
            all_day=False,
            user_specific_id="user_specific_id",
            title="Event",
            body="Event body",
            start_time=date,
            end_time=date + timedelta(hours=1),
            participants=[],
            meeting_urls=[],
        )
        event_two = CalendarEvent(
            id="id_two",
            provider="microsoft",
            all_day=False,
            user_specific_id="user_specific_id_two",
            title="Event Two",
            body="Event body two",
            start_time=date + timedelta(hours=2),
            end_time=date + timedelta(hours=3),
            participants=[],
            meeting_urls=[],
        )

        scheduled_event, _ = ScheduledEvent.create_or_update_with_calendar_event(self.user, event)
        deleted_event, _ = ScheduledEvent.create_or_update_with_calendar_event(self.user, event_two)
        non_provider_event = ScheduledEvent.objects.create(
            user=self.user,
            start_time=date + timedelta(hours=4),
            end_time=date + timedelta(hours=5),
        )

        mock_fetch_calendar_events.return_value = ([event], True)

        with caplog.at_level(logging.INFO):
            assert reconcile_calendar_events.apply((self.user.uuid, True)).successful()
            assert caplog.messages
            assert (
                caplog.messages[0]
                == f"Reconciled calendar events for user {str(self.user.uuid)}. Created 0 events, updated (potentially with no changes) 1 events, deleted 1 events."
            )

        assert ScheduledEvent.objects.count() == 2
        assert set(ScheduledEvent.objects.all()) == {scheduled_event, non_provider_event}
        mock_update_autojoin_bots.delay.assert_called_once_with(
            self.user.uuid,
            [deleted_event.uuid],
            update_recall=False,
            force_update=True,
        )

    def test_delete_time_handling(
        self,
        mock_update_autojoin_bots: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_fetch_calendar_events: AsyncMock,
        caplog: pytest.LogCaptureFixture,
        settings: SettingsWrapper,
    ) -> None:
        settings.CALENDAR_EVENT_SYNC_DEFAULT_LOOKAHEAD_SECONDS = 60 * 60

        past_event = CalendarEvent(
            id="past_event_id",
            provider="google",
            all_day=False,
            user_specific_id="past_event_user_specific_id",
            title="Past Event",
            body="Body",
            start_time=timezone.now() - timedelta(days=1),
            end_time=timezone.now() - timedelta(days=1, hours=1),
            participants=[],
            meeting_urls=[],
        )
        ongoing_event = CalendarEvent(
            id="ongoing_event_id",
            provider="google",
            all_day=False,
            user_specific_id="ongoing_event_user_specific_id",
            title="Ongoing Event",
            body="Body",
            start_time=timezone.now() - timedelta(minutes=30),
            end_time=timezone.now() + timedelta(minutes=30),
            participants=[],
            meeting_urls=[],
        )
        event = CalendarEvent(
            id="id",
            provider="google",
            all_day=False,
            user_specific_id="user_specific_id",
            title="Event",
            body="Body",
            start_time=timezone.now() + timedelta(minutes=59),
            end_time=timezone.now() + timedelta(hours=1, minutes=59),
            participants=[],
            meeting_urls=[],
        )
        event_two = CalendarEvent(
            id="id_two",
            provider="microsoft",
            all_day=False,
            user_specific_id="user_specific_id_two",
            title="Event",
            body="Body",
            start_time=timezone.now() + timedelta(hours=1, minutes=1),
            end_time=timezone.now() + timedelta(hours=2, minutes=1),
            participants=[],
            meeting_urls=[],
        )

        past_scheduled_event, _ = ScheduledEvent.create_or_update_with_calendar_event(self.user, past_event)
        first_deleted_event, _ = ScheduledEvent.create_or_update_with_calendar_event(self.user, ongoing_event)
        second_deleted_event, _ = ScheduledEvent.create_or_update_with_calendar_event(self.user, event)
        scheduled_event, _ = ScheduledEvent.create_or_update_with_calendar_event(self.user, event_two)

        mock_fetch_calendar_events.return_value = ([], True)

        with caplog.at_level(logging.INFO):
            assert reconcile_calendar_events.apply((self.user.uuid, True)).successful()
            assert caplog.messages
            assert (
                caplog.messages[0]
                == f"Reconciled calendar events for user {str(self.user.uuid)}. Created 0 events, updated (potentially with no changes) 0 events, deleted 2 events."
            )

        assert ScheduledEvent.objects.count() == 2
        assert set(ScheduledEvent.objects.all()) == {scheduled_event, past_scheduled_event}

        mock_update_autojoin_bots.delay.assert_called_once_with(
            self.user.uuid,
            [first_deleted_event.uuid, second_deleted_event.uuid],
            update_recall=False,
            force_update=True,
        )

    def test_mixed_create_update_delete(
        self,
        mock_update_autojoin_bots: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_fetch_calendar_events: AsyncMock,
        caplog: pytest.LogCaptureFixture,
    ) -> None:
        event = CalendarEvent(
            id="id",
            provider="google",
            all_day=False,
            user_specific_id="user_specific_id",
            title="Event",
            body="Event body",
            start_time=datetime.datetime(2023, 10, 1, 10, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2023, 10, 1, 11, 0, tzinfo=datetime.timezone.utc),
            participants=[],
            meeting_urls=[],
        )
        event_two = CalendarEvent(
            id="id_two",
            provider="microsoft",
            all_day=False,
            user_specific_id="user_specific_id_two",
            title="Event Two",
            body="Event body two",
            start_time=datetime.datetime(2023, 10, 1, 12, 0, tzinfo=datetime.timezone.utc),
            end_time=datetime.datetime(2023, 10, 1, 13, 0, tzinfo=datetime.timezone.utc),
            participants=[],
            meeting_urls=[],
        )
        deleted_event = CalendarEvent(
            id="deleted_id",
            provider="redtail",
            all_day=False,
            user_specific_id="deleted_user_specific_id",
            title="Deleted Event",
            body="Deleted Event body",
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=1),
            participants=[],
            meeting_urls=[],
        )

        scheduled_event, _ = ScheduledEvent.create_or_update_with_calendar_event(self.user, event)
        deleted_scheduled_event, _ = ScheduledEvent.create_or_update_with_calendar_event(self.user, deleted_event)

        updated_event = CalendarEvent.model_validate(
            {
                **event.model_dump(),
                "title": "Updated Event",
                "start_time": event.start_time + timedelta(hours=1),
                "end_time": event.end_time + timedelta(minutes=30),
            }
        )

        mock_fetch_calendar_events.return_value = ([updated_event, event_two], True)

        with caplog.at_level(logging.INFO):
            assert reconcile_calendar_events.apply((self.user.uuid, True)).successful()
            assert caplog.messages
            assert (
                caplog.messages[0]
                == f"Reconciled calendar events for user {str(self.user.uuid)}. Created 1 events, updated (potentially with no changes) 1 events, deleted 1 events."
            )

        assert ScheduledEvent.objects.count() == 2
        scheduled_event.refresh_from_db()
        deleted_scheduled_event.refresh_from_db()
        created_scheduled_event = ScheduledEvent.objects.get(shared_source_id="id_two")

        assert scheduled_event.user == self.user
        assert scheduled_event.shared_source_id == "id"
        assert scheduled_event.user_specific_source_id == "user_specific_id"
        assert scheduled_event.start_time == updated_event.start_time
        assert scheduled_event.end_time == updated_event.end_time
        assert scheduled_event.source_data == updated_event.model_dump(mode="json")

        assert created_scheduled_event.user == self.user
        assert created_scheduled_event.shared_source_id == "id_two"
        assert created_scheduled_event.user_specific_source_id == "user_specific_id_two"
        assert created_scheduled_event.start_time == event_two.start_time
        assert created_scheduled_event.end_time == event_two.end_time
        assert created_scheduled_event.source_data == event_two.model_dump(mode="json")

        assert deleted_scheduled_event.is_deleted

        mock_update_autojoin_bots.delay.assert_called_once_with(
            self.user.uuid,
            [scheduled_event.uuid, created_scheduled_event.uuid, deleted_scheduled_event.uuid],
            update_recall=False,
            force_update=True,
        )

    def test_reconcile_with_different_users(
        self,
        mock_update_autojoin_bots: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_fetch_calendar_events: AsyncMock,
        django_user_model: User,
    ) -> None:
        event = CalendarEvent(
            id="id",
            provider="google",
            all_day=False,
            user_specific_id="user_specific_id",
            title="Event",
            body="Event body",
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(hours=1),
            participants=[],
            meeting_urls=[],
        )

        mock_fetch_calendar_events.return_value = ([event], True)

        other_user = django_user_model.objects.create(username="other_user", organization=self.org)

        assert reconcile_calendar_events.apply((self.user.uuid,)).successful()

        assert ScheduledEvent.objects.count() == 1
        assert ScheduledEvent.objects.filter(shared_source_id="id", user=self.user).exists()

        mock_update_autojoin_bots.delay.assert_called_once_with(
            self.user.uuid,
            [ScheduledEvent.objects.get(shared_source_id="id").uuid],
            update_recall=False,
            force_update=False,
        )

        assert reconcile_calendar_events.apply((other_user.uuid,)).successful()

        assert ScheduledEvent.objects.filter(shared_source_id="id").count() == 2
        assert ScheduledEvent.objects.filter(shared_source_id="id", user=other_user).exists()
        assert ScheduledEvent.objects.filter(shared_source_id="id", user=self.user).exists()
        assert ScheduledEvent.objects.get(user=self.user) != ScheduledEvent.objects.get(user=other_user)
        other_user_event = ScheduledEvent.objects.get(user=other_user)
        mock_update_autojoin_bots.delay.assert_called_with(
            other_user.uuid,
            [other_user_event.uuid],
            update_recall=False,
            force_update=False,
        )

        updated_event = CalendarEvent.model_validate(
            {
                **event.model_dump(),
                "title": "Updated Event",
                "start_time": event.start_time + timedelta(minutes=30),
                "end_time": event.end_time + timedelta(minutes=30),
            }
        )
        mock_fetch_calendar_events.return_value = ([updated_event], True)

        assert reconcile_calendar_events.apply((self.user.uuid, True)).successful()

        user_event = ScheduledEvent.objects.get(user=self.user)
        assert user_event.source_data == updated_event.model_dump(mode="json")
        assert other_user_event.source_data == event.model_dump(mode="json")

        mock_update_autojoin_bots.delay.assert_called_with(
            self.user.uuid,
            [user_event.uuid],
            update_recall=False,
            force_update=True,
        )

    @patch("deepinsights.meetingsapp.tasks.cache")
    def test_locking_on_failure(
        self,
        mock_cache: MagicMock,
        mock_update_autojoin_bots: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_fetch_calendar_events: AsyncMock,
        settings: SettingsWrapper,
    ) -> None:
        settings.ENABLE_CELERY_TASK_LOCKING = True
        settings.CELERY_TASK_LOCK_TIMEOUT = 1
        mock_fetch_calendar_events.side_effect = Exception("Test exception")

        assert not reconcile_calendar_events.apply((self.user.uuid, True)).successful()

        mock_cache.lock.assert_called_once_with(str(self.user.uuid), timeout=1)

    @patch("deepinsights.meetingsapp.tasks.cache")
    def test_locking(
        self,
        mock_cache: MagicMock,
        mock_update_autojoin_bots: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_fetch_calendar_events: AsyncMock,
        settings: SettingsWrapper,
    ) -> None:
        settings.ENABLE_CELERY_TASK_LOCKING = True
        settings.CELERY_TASK_LOCK_TIMEOUT = 1

        mock_fetch_calendar_events.return_value = ([], True)

        assert reconcile_calendar_events.apply((self.user.uuid, True)).successful()

        mock_cache.lock.assert_called_once_with(str(self.user.uuid), timeout=1)

    @patch("deepinsights.meetingsapp.tasks.cache")
    def test_locking_disabled(
        self,
        mock_cache: MagicMock,
        mock_update_autojoin_bots: MagicMock,
        mock_is_active_for_user: MagicMock,
        mock_fetch_calendar_events: AsyncMock,
        settings: SettingsWrapper,
    ) -> None:
        settings.ENABLE_CELERY_TASK_LOCKING = False

        mock_fetch_calendar_events.return_value = ([], True)

        assert reconcile_calendar_events.apply((self.user.uuid, True)).successful()

        mock_cache.lock.assert_not_called()


@patch("deepinsights.meetingsapp.tasks.update_bots_for_calendar_events")
class TestUpdateAutojoinBots:
    @pytest.fixture(autouse=True)
    def setUp(self, django_user_model: User) -> None:
        self.org = Organization.objects.create(name="Reconcile Test Org")
        self.user = django_user_model.objects.create(username="reconcile_user", organization=self.org)

    def test_no_recall_calendar_id(self, mock_update_bots_for_calendar_events: MagicMock) -> None:
        result = update_autojoin_bots.apply((self.user.uuid, None), {"update_recall": True})

        assert result.successful()
        mock_update_bots_for_calendar_events.delay.assert_not_called()

    def test_recall_update_disabled(self, mock_update_bots_for_calendar_events: MagicMock) -> None:
        self.user.recall_calendar_id = "test_calendar_id"
        self.user.save()

        result = update_autojoin_bots.apply((self.user.uuid, None), {"update_recall": False})

        assert result.successful()
        mock_update_bots_for_calendar_events.delay.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.RecallBotController.connected_calendar_platform")
    def test_recall_no_connected_calendar_platform(
        self, mock_connected_calendar_platform: MagicMock, mock_update_bots_for_calendar_events: MagicMock
    ) -> None:
        self.user.recall_calendar_id = "test_calendar_id"
        self.user.save()

        mock_connected_calendar_platform.return_value = None

        result = update_autojoin_bots.apply((self.user.uuid, None), {"update_recall": True})

        assert result.successful()
        mock_connected_calendar_platform.assert_called_once_with("test_calendar_id")
        mock_update_bots_for_calendar_events.delay.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.RecallBotController.connected_calendar_platform")
    def test_recall_connected_calendar_platform_no_events(
        self, mock_connected_calendar_platform: MagicMock, mock_update_bots_for_calendar_events: MagicMock
    ) -> None:
        self.user.recall_calendar_id = "test_calendar_id"
        self.user.save()

        mock_connected_calendar_platform.return_value = "google"

        result = update_autojoin_bots.apply((self.user.uuid, None), {"update_recall": True})

        assert result.successful()
        mock_connected_calendar_platform.assert_called_once_with("test_calendar_id")
        mock_update_bots_for_calendar_events.delay.assert_not_called()

    @patch("deepinsights.meetingsapp.tasks.RecallBotController.connected_calendar_platform")
    def test_recall_connected_calendar_platform_google_provider(
        self, mock_connected_calendar_platform: MagicMock, mock_update_bots_for_calendar_events: MagicMock
    ) -> None:
        self.user.recall_calendar_id = "test_calendar_id"
        self.user.save()

        mock_connected_calendar_platform.return_value = "google"

        event, _ = ScheduledEvent.create_or_update_with_calendar_event(
            self.user,
            CalendarEvent(
                id="id",
                provider="google",
                all_day=False,
                user_specific_id="user_specific_id",
                title="Event 1",
                body="Event 1 Body",
                start_time=datetime.datetime(2023, 10, 1, 10, 0),
                end_time=datetime.datetime(2023, 10, 1, 11, 0),
                participants=[],
                meeting_urls=[],
            ),
        )
        event_two, _ = ScheduledEvent.create_or_update_with_calendar_event(
            self.user,
            CalendarEvent(
                id="id_two",
                provider="microsoft",
                all_day=False,
                user_specific_id="user_specific_id_two",
                title="Event Two",
                body="Event Two Body",
                start_time=datetime.datetime(2023, 10, 1, 12, 0),
                end_time=datetime.datetime(2023, 10, 1, 13, 0),
                participants=[],
                meeting_urls=[],
            ),
        )

        result = update_autojoin_bots.apply((self.user.uuid, None), {"update_recall": True})

        assert result.successful()
        mock_connected_calendar_platform.assert_called_once_with("test_calendar_id")
        mock_update_bots_for_calendar_events.delay.assert_called_once_with(
            "test_calendar_id",
            None,
            self.user.uuid,
            event_ids_to_process=None,
            calendar_platform_ids_to_process=["user_specific_id"],
            force_update=False,
        )

    @patch("deepinsights.meetingsapp.tasks.RecallBotController.connected_calendar_platform")
    def test_recall_connected_calendar_platform_microsoft_provider(
        self, mock_connected_calendar_platform: MagicMock, mock_update_bots_for_calendar_events: MagicMock
    ) -> None:
        self.user.recall_calendar_id = "test_calendar_id"
        self.user.save()

        mock_connected_calendar_platform.return_value = "microsoft"

        event, _ = ScheduledEvent.create_or_update_with_calendar_event(
            self.user,
            CalendarEvent(
                id="id",
                provider="google",
                all_day=False,
                user_specific_id="user_specific_id",
                title="Event 1",
                body="Event 1 Body",
                start_time=datetime.datetime(2023, 10, 1, 10, 0),
                end_time=datetime.datetime(2023, 10, 1, 11, 0),
                participants=[],
                meeting_urls=[],
            ),
        )
        event_two, _ = ScheduledEvent.create_or_update_with_calendar_event(
            self.user,
            CalendarEvent(
                id="id_two",
                provider="microsoft",
                all_day=False,
                user_specific_id="user_specific_id_two",
                title="Event Two",
                body="Event Two Body",
                start_time=datetime.datetime(2023, 10, 1, 12, 0),
                end_time=datetime.datetime(2023, 10, 1, 13, 0),
                participants=[],
                meeting_urls=[],
            ),
        )

        result = update_autojoin_bots.apply((self.user.uuid, None), {"update_recall": True})

        assert result.successful()
        mock_connected_calendar_platform.assert_called_once_with("test_calendar_id")
        mock_update_bots_for_calendar_events.delay.assert_called_once_with(
            "test_calendar_id",
            None,
            self.user.uuid,
            event_ids_to_process=None,
            calendar_platform_ids_to_process=["user_specific_id_two"],
            force_update=False,
        )

    @patch("deepinsights.meetingsapp.tasks.RecallBotController.connected_calendar_platform")
    def test_recall_connected_calendar_platform_specific_ids(
        self, mock_connected_calendar_platform: MagicMock, mock_update_bots_for_calendar_events: MagicMock
    ) -> None:
        self.user.recall_calendar_id = "test_calendar_id"
        self.user.save()

        mock_connected_calendar_platform.return_value = "google"

        event, _ = ScheduledEvent.create_or_update_with_calendar_event(
            self.user,
            CalendarEvent(
                id="id",
                provider="google",
                all_day=False,
                user_specific_id="user_specific_id",
                title="Event 1",
                body="Event 1 Body",
                start_time=datetime.datetime(2023, 10, 1, 10, 0),
                end_time=datetime.datetime(2023, 10, 1, 11, 0),
                participants=[],
                meeting_urls=[],
            ),
        )
        event_two, _ = ScheduledEvent.create_or_update_with_calendar_event(
            self.user,
            CalendarEvent(
                id="id_two",
                provider="google",
                all_day=False,
                user_specific_id="user_specific_id_two",
                title="Event Two",
                body="Event Two Body",
                start_time=datetime.datetime(2023, 10, 1, 12, 0),
                end_time=datetime.datetime(2023, 10, 1, 13, 0),
                participants=[],
                meeting_urls=[],
            ),
        )

        result = update_autojoin_bots.apply((self.user.uuid, [event_two.uuid]), {"update_recall": True})

        assert result.successful()
        mock_connected_calendar_platform.assert_called_once_with("test_calendar_id")
        mock_update_bots_for_calendar_events.delay.assert_called_once_with(
            "test_calendar_id",
            None,
            self.user.uuid,
            event_ids_to_process=None,
            calendar_platform_ids_to_process=["user_specific_id_two"],
            force_update=False,
        )

    @patch("deepinsights.meetingsapp.tasks.RecallBotController.connected_calendar_platform")
    def test_recall_connected_calendar_platform_invalid(
        self, mock_connected_calendar_platform: MagicMock, mock_update_bots_for_calendar_events: MagicMock
    ) -> None:
        self.user.recall_calendar_id = "test_calendar_id"
        self.user.save()

        mock_connected_calendar_platform.return_value = "google"

        event, _ = ScheduledEvent.create_or_update_with_calendar_event(
            self.user,
            CalendarEvent(
                id="id",
                provider="google",
                all_day=False,
                user_specific_id="user_specific_id",
                title="Event 1",
                body="Event 1 Body",
                start_time=datetime.datetime(2023, 10, 1, 10, 0),
                end_time=datetime.datetime(2023, 10, 1, 11, 0),
                participants=[],
                meeting_urls=[],
            ),
        )
        event_two, _ = ScheduledEvent.create_or_update_with_calendar_event(
            self.user,
            CalendarEvent(
                id="id_two",
                provider="google",
                all_day=False,
                user_specific_id="user_specific_id_two",
                title="Event Two",
                body="Event Two Body",
                start_time=datetime.datetime(2023, 10, 1, 12, 0),
                end_time=datetime.datetime(2023, 10, 1, 13, 0),
                participants=[],
                meeting_urls=[],
            ),
        )
        ScheduledEvent.objects.create(
            user=self.user,
            shared_source_id="id_three",
            user_specific_source_id="user_specific_id_three",
            start_time=datetime.datetime(2023, 10, 1, 14, 0),
            end_time=datetime.datetime(2023, 10, 1, 15, 0),
            source_data={"invalid": "data"},
        )
        ScheduledEvent.objects.create(
            user=self.user,
            shared_source_id="id_four",
            user_specific_source_id="user_specific_id_four",
            start_time=datetime.datetime(2023, 10, 1, 16, 0),
            end_time=datetime.datetime(2023, 10, 1, 17, 0),
            source_data={},
        )

        result = update_autojoin_bots.apply((self.user.uuid, None), {"update_recall": True})

        assert result.successful()
        mock_connected_calendar_platform.assert_called_once_with("test_calendar_id")
        mock_update_bots_for_calendar_events.delay.assert_called_once_with(
            "test_calendar_id",
            None,
            self.user.uuid,
            event_ids_to_process=None,
            calendar_platform_ids_to_process=["user_specific_id", "user_specific_id_two"],
            force_update=False,
        )
