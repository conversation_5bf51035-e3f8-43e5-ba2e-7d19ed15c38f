import base64
import io
import logging
import os
import uuid
import zipfile
from typing import Any

from django import forms
from django.conf import settings
from django.contrib import admin, messages
from django.db.models import Count, JSONField
from django.db.models.query import Prefetch, QuerySet
from django.http import HttpRequest, HttpResponse
from django.shortcuts import redirect, render
from django.urls import path, reverse
from django.utils.html import format_html
from django.utils.safestring import SafeString
from django.utils.translation import ngettext
from django_celery_beat.models import PeriodicTask
from django_json_widget.widgets import JSONEditorWidget
from django_jsonform.widgets import JSONFormWidget
from m3u8_generator import PlaylistGenerator
from simple_history.admin import SimpleHistoryAdmin

from deepinsights.core.admin_mixins import BotPreferencesMixin
from deepinsights.meetingsapp.admin_utils import (
    CustomPeriodicTaskAdmin,
    delete_organization_and_associated_data,
    disable_organization,
    get_note_agenda_template_html,
    get_note_followup_templates_html,
    get_org_active_agendas_html,
    get_org_active_templates_html,
    process_users_from_csv,
)
from deepinsights.meetingsapp.management.commands.dumpnotes import notes_data_for_export
from deepinsights.meetingsapp.models.attendees import Attendee
from deepinsights.meetingsapp.models.audio_buffer import AudioBuffer
from deepinsights.meetingsapp.models.client import Client
from deepinsights.meetingsapp.models.client_interaction import ClientInteraction
from deepinsights.meetingsapp.models.client_recap import ClientRecap
from deepinsights.meetingsapp.models.meeting_bot import MeetingBot
from deepinsights.meetingsapp.models.meeting_summary_email_template import MeetingSummaryEmailTemplate
from deepinsights.meetingsapp.models.meeting_type import MeetingType
from deepinsights.meetingsapp.models.note import Note
from deepinsights.meetingsapp.models.oauth_credentials import OAuthClientCredentials, OAuthCredentials
from deepinsights.meetingsapp.models.organization import Organization
from deepinsights.meetingsapp.models.phone_number import PhoneNumber
from deepinsights.meetingsapp.models.prompt import Prompt
from deepinsights.meetingsapp.models.scheduled_event import ScheduledEvent
from deepinsights.meetingsapp.models.search_query import SearchQuery
from deepinsights.meetingsapp.models.structured_meeting_data import (
    StructuredMeetingData,
    StructuredMeetingDataTemplate,
    StructuredMeetingDataTemplateRule,
)
from deepinsights.meetingsapp.models.structured_meeting_data_schema import StructuredMeetingDataSchema
from deepinsights.meetingsapp.models.task import Task
from deepinsights.meetingsapp.models.user_impersonation import UserImpersonation
from deepinsights.meetingsapp.organization_import import OrganizationImportView
from deepinsights.meetingsapp.organization_wizard import OrganizationOnboardingWizard, UserImportForm
from deepinsights.meetingsapp.tasks import delete_transcripts_for_notes, process_note_recording, reprocess_note
from deepinsights.meetingsapp.template_generation import TemplateGeneratorView
from deepinsights.users.models.flags import Flag
from deepinsights.users.models.user import User

# Adjust the title and header to help ensure we're not accessing the wrong admin site.
environment = os.environ.get("APP_DOMAIN") or "Unknown environment"
admin.site.site_title = f"Zeplyn admin ({environment})"
admin.site.site_header = f"Zeplyn admin ({environment})"


logger = logging.getLogger(__name__)


@admin.register(MeetingBot)
class MeetingBotAdmin(SimpleHistoryAdmin):  # type: ignore[misc]
    list_display = ["note", "bot_owner", "created", "meeting_link", "recall_bot_id"]
    raw_id_fields = ["note", "bot_owner"]
    readonly_fields = ["uuid", "created", "modified"]
    search_fields = ["meeting_link", "note__metadata__meeting_name", "recall_bot_id", "uuid"]
    actions: list[str] = ["process_note_recording_action"]

    @admin.action(description="Force reprocess corresponding note recording")
    def process_note_recording_action(self, request: HttpRequest, queryset: QuerySet[MeetingBot]) -> None:
        for bot in queryset:
            bot.processing_task.delay_on_commit(bot.uuid, True)
        self.message_user(
            request,
            ngettext(
                "Reprocessing started for %d note. Check Celery job status for updates.",
                "Reprocessing started for %d notes. Check Celery job status for updates.",
                len(queryset),
            )
            % len(queryset),
            messages.SUCCESS,
        )


@admin.register(OAuthCredentials)
class OAuthCredentialsAdmin(SimpleHistoryAdmin):  # type: ignore[misc]
    list_display = [
        "user",
        "integration",
        "truncated_access_token",
        "truncated_refresh_token",
        "expires_in",
        "refresh_token_expires_in",
    ]

    def truncated_access_token(self, obj: OAuthCredentials) -> str:
        return f"{obj.access_token[:10]}..." if obj.access_token else "No token"

    def truncated_refresh_token(self, obj: OAuthCredentials) -> str:
        return f"{obj.refresh_token[:10]}..." if obj.refresh_token else "No token"


@admin.register(Task)
class TaskAdmin(SimpleHistoryAdmin):  # type: ignore[misc]
    list_display = [
        "get_truncated_title",
        "task_owner",
        "assignee",
        "get_completed_status",
        "due_date",
        "get_note_link",
        "created",
        "modified",
    ]
    list_filter = ["completed", "due_date"]
    search_fields = ["task_title", "task_desc", "=task_owner__email", "=assignee__email", "=note__uuid"]
    readonly_fields = ["uuid", "created", "modified"]
    raw_id_fields = ["task_owner", "note", "assignee"]

    def get_queryset(self, request: HttpRequest) -> QuerySet[Task]:
        return (  # type: ignore[no-any-return]
            super()
            .get_queryset(request)
            .select_related("task_owner", "assignee", "note")
            .defer(
                "metadata",
                "note__raw_asr_response",
                "note__raw_transcript",
                "note__metadata",
                "note__advisor_notes",
                "note__diarized_trans_with_names",
            )
        )

    def delete_queryset(self, request: HttpRequest, queryset: QuerySet[Task]) -> None:
        return super().delete_queryset(request, queryset.defer(None))  # type: ignore[no-any-return]

    @admin.display(description="Status", ordering="completed")
    def get_completed_status(self, obj: Task) -> str:
        return "Completed" if obj.completed else "Pending"

    @admin.display(description="Task Title", ordering="task_title")
    def get_truncated_title(self, obj: Task) -> str:
        return f"{obj.task_title[:50]}..." if len(obj.task_title) > 50 else obj.task_title

    def get_note_link(self, obj: Task) -> SafeString:
        if not obj.note:
            return format_html("<span>No note</span>")
        note_title = obj.note.metadata.get("meeting_name", "No title") if obj.note.metadata else "No title"
        note_url = reverse("admin:meetingsapp_note_change", args=[obj.note.id])
        return format_html('<a href="{}">{}</a>', note_url, note_title)

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "task_title",
                    "task_desc",
                    "task_owner",
                    "assignee",
                    "note",
                    "uuid",
                )
            },
        ),
        (
            "Status",
            {
                "fields": (
                    "completed",
                    "due_date",
                )
            },
        ),
        ("Dates", {"fields": ("created", "modified")}),
    )


@admin.register(ClientRecap)
class ClientRecapAdmin(SimpleHistoryAdmin):  # type: ignore[misc]
    list_display = ["user", "client", "summary", "status"]
    formfield_overrides = {JSONField: {"widget": JSONEditorWidget(mode="view")}}


@admin.register(Client)
class ClientAdmin(SimpleHistoryAdmin):  # type: ignore[misc]
    list_display = [
        "name",
        "email",
        "organization",
        "crm_id",
        "client_type",
        "crm_system",
    ]
    list_select_related = ["organization"]
    search_fields = ["name", "email", "uuid", "crm_id"]
    autocomplete_fields = ["organization"]
    readonly_fields = ["uuid", "created", "modified"]
    list_per_page = 20  # Limit to 20 records per page

    list_filter = ["organization", "client_type", "crm_system"]

    filter_horizontal = ["authorized_users"]

    def delete_queryset(self, request: HttpRequest, queryset: QuerySet[Client]) -> None:
        return super().delete_queryset(request, queryset.defer(None))  # type: ignore[no-any-return]

    def get_queryset(self, request: HttpRequest) -> QuerySet[Client]:
        queryset = super().get_queryset(request)
        return queryset.select_related("organization")  # type: ignore[no-any-return]


@admin.register(Note)
class NoteAdmin(SimpleHistoryAdmin):  # type: ignore[misc]
    # See https://github.com/jazzband/django-simple-history/issues/678
    def delete_queryset(self, request: HttpRequest, queryset: QuerySet[Note]) -> None:
        super().delete_queryset(request, queryset.defer(None))

    def get_queryset(self, request: HttpRequest) -> QuerySet[Note]:
        qs = (
            Note.objects_with_deleted.defer("raw_asr_response", "raw_transcript")
            .select_related("note_owner", "meeting_type")
            .prefetch_related(
                Prefetch("attendees", queryset=Attendee.objects.select_related("client", "user")),
                "structuredmeetingdata_set",
                "task_set",
            )
            .annotate(task_count=Count("task"))
        )
        ordering = self.get_ordering(request)
        if ordering:
            qs = qs.order_by(*ordering)
        return qs

    list_select_related = ["note_owner", "meeting_type"]

    list_display = [
        "id",
        "note_owner",
        "title",
        "created",
        "modified",
        "status",
        "is_deleted",
        "note_type",
        "note_url",
        "get_attendees",
    ]

    list_filter = ["status", "note_type", "is_deleted", "note_owner__organization"]

    # exact match for ID, UUID, note owner's email, and contains search for meeting name
    search_fields = [
        "=id",
        "=uuid",
        "=note_owner__email",
        "metadata__meeting_name",
    ]

    def get_search_results(
        self, request: HttpRequest, queryset: QuerySet[Note], search_term: str
    ) -> tuple[QuerySet, bool]:  # type: ignore[type-arg]
        """
        Override to optimize search behavior and prevent performance issues.
        Try exact matches first in order of specificity, then fall back to general search.
        """
        queryset = queryset.defer("raw_asr_response", "raw_transcript")

        if not search_term:
            return queryset, False

        # List of fields to try exact matches in order of specificity
        exact_match_attempts = [
            # (field_name, transform_function)
            ("id", lambda x: int(x) if x.isdigit() else None),
            ("uuid", lambda x: str(uuid.UUID(x)) if self._is_valid_uuid(x) else None),
            ("note_owner__email", lambda x: x),  # No transformation needed for email
        ]

        for field, transform in exact_match_attempts:
            try:
                value = transform(search_term)  # type: ignore[no-untyped-call]
                if value is not None:
                    result = queryset.filter(**{field: value})
                    if result.exists():
                        return result, False
            except (ValueError, TypeError):
                continue

        # If no exact matches found, fall back to default search behavior
        queryset, use_distinct = super().get_search_results(request, queryset, search_term)
        return queryset, use_distinct

    @staticmethod
    def _is_valid_uuid(value: str) -> bool:
        """Helper method to check if a string is a valid UUID"""
        try:
            uuid.UUID(value)
            return True
        except (ValueError, AttributeError, TypeError):
            return False

    actions = [
        "process_note_recording_action",
        "process_note_recording_from_audio_buffers_action",
        "process_bot_recording_action",
        "delete_transcript_action",
        "reprocess_tasks",
        "reprocess_summary",
        "reprocess_templates",
    ]
    filter_horizontal = ["authorized_users"]

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "note_owner",
                    "authorized_users",
                    "file_path",
                    "file_type",
                    "note_type",
                    "audio_source",
                    "uuid",
                    "s3_url",
                    "audio_buffers_url",
                    "provider_bot_urls",
                    "zeplyn_bot_urls",
                    "get_tasks_link",
                    "get_structured_meeting_data_link",
                    "clientinteraction",
                )
            },
        ),
        ("Status", {"fields": ("status", "is_deleted")}),
        (
            "Transcript",
            {
                "fields": ("diarized_transcript",),
                "classes": ("collapse",),
                "description": "Click to expand/collapse the transcript.",
            },
        ),
        (
            "Metadata",
            {
                "fields": (
                    "metadata",
                    "advisor_notes",
                    "key_takeaways",
                    "summary",
                    "client",
                    "meeting_type",
                    "category",
                    "associated_templates",
                    "agenda_template",
                )
            },
        ),
        ("Salesforce", {"fields": ("salesforce_case_id",)}),
        ("Note Dates", {"fields": ("scheduled_start_time", "scheduled_end_time")}),
        ("Model object dates", {"fields": ("created", "modified")}),
    )

    readonly_fields = [
        "scheduled_start_time",
        "scheduled_end_time",
        "title",
        "uuid",
        "created",
        "modified",
        "audio_source",
        "provider_bot_urls",
        "zeplyn_bot_urls",
        "s3_url",
        "audio_buffers_url",
        "category",
        "get_tasks_link",
        "get_structured_meeting_data_link",
        "clientinteraction",
        "diarized_transcript",
        "associated_templates",
        "agenda_template",
    ]
    formfield_overrides = {JSONField: {"widget": JSONEditorWidget(mode="view")}}

    @admin.display(description="Diarized Transcript")
    def diarized_transcript(self, obj: Note) -> str:
        """Display the diarized transcript with speaker names as readonly text."""
        return obj.diarized_trans_with_names or "No transcript available"

    @admin.display(description="Structured Meeting Data")
    def get_structured_meeting_data_link(self, obj: Note) -> SafeString:
        """Return a link to view structured meeting data associated with this note."""
        structured_data_url = (
            reverse("admin:meetingsapp_structuredmeetingdata_changelist") + f"?note_id__exact={obj.id}"
        )
        structured_data_count = obj.structuredmeetingdata_set.count()
        return format_html(
            '<a href="{}" target="_blank">View Structured Meeting Data ({})</a>',
            structured_data_url,
            structured_data_count,
        )

    @admin.display(description="Associated Templates")
    def associated_templates(self, obj: Note) -> SafeString:
        """Display templates that are associated with this note based on meeting type and rules."""
        return get_note_followup_templates_html(obj)

    @admin.display(description="Agenda Template")
    def agenda_template(self, obj: Note) -> SafeString:
        """Display the agenda template associated with this note's meeting type."""
        return get_note_agenda_template_html(obj)

    def title(self, obj: Note) -> str:
        if obj.metadata:
            return obj.metadata.get("meeting_name", "<No Title>")  # type: ignore[no-any-return]
        return "Err:Contact support:No Metadata"

    def note_url(self, obj: Note) -> SafeString:
        url = obj.public_url()
        return format_html(f'<a href="{url}" target="_blank">{url}</a>')

    @admin.display(description="Tasks")
    def get_tasks_link(self, obj: Note) -> SafeString:
        """Return a link to view tasks associated with this note."""
        tasks_url = reverse("admin:meetingsapp_task_changelist") + f"?note_id__exact={obj.id}"
        return format_html(
            '<a href="{}" target="_blank">View Tasks ({})</a>',
            tasks_url,
            obj.task_set.count(),
        )

    @admin.display(description="Audio Source")
    def audio_source(self, obj: Note) -> str:
        match obj.data_source:
            case Note.DataSource.AUDIO_BUFFERS | Note.DataSource.AUDIO_FILE:
                return "Mic"
            case Note.DataSource.RECALL:
                return "Notetaker"
            case Note.DataSource.TWILIO:
                return "Phone call"
            case _:
                return "Unknown"

    @admin.display(description="ID")
    def id(self, obj: Note) -> int:
        return obj.pk

    @admin.display(description="Provider Bot URL(s)")
    def provider_bot_urls(self, obj: Note) -> SafeString:
        """Return the Recall API console URLs for the bots associated with the note (if any)."""
        bot_urls = [bot.provider_bot_url for bot in MeetingBot.objects.filter(note=obj)]
        if not bot_urls:
            return format_html("N/A")
        return format_html("<br>".join([f'<a href="{url}" target="_blank">{url}</a>' for url in bot_urls]))

    @admin.display(description="Zeplyn MeetingBots")
    def zeplyn_bot_urls(self, obj: Note) -> SafeString:
        """Return the Zeplyn admin console URLs for the bots associated with the note (if any)."""
        bot_urls = [
            reverse("admin:meetingsapp_meetingbot_change", args=[bot.id]) for bot in MeetingBot.objects.filter(note=obj)
        ]
        if not bot_urls:
            return format_html("N/A")
        return format_html("<br>".join([f'<a href="{url}" target="_blank">{url}</a>' for url in bot_urls]))

    @admin.display(description="AWS S3 object URL")
    def s3_url(self, obj: Note) -> SafeString:
        """Return the S3 URL for the note's audio file (if any)."""
        if not obj.file_path or obj.file_path == "None":
            return format_html("N/A")
        url = f"https://{settings.AWS_S3_REGION_NAME}.console.aws.amazon.com/s3/buckets/{settings.AWS_MEETING_BUCKET}/{obj.file_path}"
        return format_html(f'<a href="{url}" target="_blank">{url}</a>')

    @admin.display(description="Audio buffers (if any; may be empty)")
    def audio_buffers_url(self, obj: Note) -> SafeString:
        """Return the Zeplyn admin console URL for the audio chunks associated with the note (if any)."""
        if not obj.audio_buffers.exists():
            return format_html("N/A (no audio buffers)")
        chunks_url = f'{reverse("admin:meetingsapp_audiobuffer_changelist")}?note_id__exact={obj.id}'
        return format_html(f'<a href="{chunks_url}" target="_blank">{chunks_url}</a>')

    @admin.display(description="Attendees")
    def get_attendees(self, obj) -> SafeString:  # type: ignore[no-untyped-def]
        attendees_info = []
        for attendee in obj.attendees.all():
            attendee_name = attendee.get_name()
            attendee_url = reverse("admin:meetingsapp_attendee_change", args=[attendee.id])
            attendee_link = f'<a href="{attendee_url}">{attendee_name}</a>'

            if attendee.client:
                client_url = reverse("admin:meetingsapp_client_change", args=[attendee.client.id])
                client_link = f' (<a href="{client_url}">Client</a>)'
                attendees_info.append(f"{attendee_link}{client_link}")
            elif attendee.user:
                user_url = reverse("admin:users_user_change", args=[attendee.user.id])
                user_link = f' (<a href="{user_url}">User</a>)'
                attendees_info.append(f"{attendee_link}{user_link}")
            else:
                attendees_info.append(attendee_link)

        return format_html("<br>".join(attendees_info))

    @admin.display(description="Scheduled start time")
    def scheduled_start_time(self, obj: Note) -> str | None:
        if scheduled_event := obj.scheduled_event:
            return scheduled_event.start_time.strftime("%Y-%m-%d %H:%M:%S %Z")
        return obj.scheduled_at

    @admin.display(description="Scheduled end time")
    def scheduled_end_time(self, obj: Note) -> str | None:
        if scheduled_event := obj.scheduled_event:
            return scheduled_event.end_time.strftime("%Y-%m-%d %H:%M:%S %Z")
        return None

    @admin.action(description="Force reprocess note recording (mic)")
    def process_note_recording_action(self, request: HttpRequest, queryset: QuerySet[Note]) -> None:
        for note in queryset:
            if not (user := note.note_owner):
                self.message_user(request, f"Note {note.uuid} has no owner", messages.ERROR)
                continue
            process_note_recording.delay(user.uuid, note.uuid, force_process=True)
        self.message_user(
            request,
            ngettext(
                "Reprocessing started for %d note. Check Celery job status for updates.",
                "Reprocessing started for %d notes. Check Celery job status for updates.",
                len(queryset),
            )
            % len(queryset),
            messages.SUCCESS,
        )

    @admin.action(description="Force reprocess note recording (mic) (audio buffers)")
    def process_note_recording_from_audio_buffers_action(self, request: HttpRequest, queryset: QuerySet[Note]) -> None:
        for note in queryset:
            if not (user := note.note_owner):
                self.message_user(request, f"Note {note.uuid} has no owner", messages.ERROR)
                continue
            process_note_recording.delay(user.uuid, note.uuid, force_process=True, use_audio_buffers=True)
        self.message_user(
            request,
            ngettext(
                "Reprocessing started for %d note. Check Celery job status for updates.",
                "Reprocessing started for %d notes. Check Celery job status for updates.",
                len(queryset),
            )
            % len(queryset),
            messages.SUCCESS,
        )

    @admin.action(description="Force reprocess note recording (bot)")
    def process_bot_recording_action(self, request: HttpRequest, queryset: QuerySet[Note]) -> None:
        processed_count = 0
        for note in queryset:
            if note.meetingbot_set.count() > 1:
                self.message_user(
                    request,
                    f"Note {note.uuid} has multiple meeting bots. Processing the newest bot.",
                    messages.WARNING,
                )
            bot: MeetingBot | None = note.meetingbot_set.order_by("-created").first()
            if bot:
                processed_count += 1
                bot.processing_task.delay_on_commit(bot.uuid, True)
        self.message_user(
            request,
            ngettext(
                "Reprocessing started for %d note. Check Celery job status for updates.",
                "Reprocessing started for %d notes. Check Celery job status for updates.",
                len(queryset),
            )
            % processed_count,
            messages.SUCCESS,
        )

    @admin.action(description="Delete transcript")
    def delete_transcript_action(self, request: HttpRequest, queryset: QuerySet[Note]) -> None:
        try:
            delete_transcripts_for_notes([note for note in queryset])
        except Exception as e:
            logger.error(f"Error deleting transcripts for selected notes: {str(e)}")
            self.message_user(request, f"Error deleting transcripts: {e}", messages.ERROR)
        else:
            self.message_user(request, "Successfully deleted transcripts", messages.SUCCESS)

    @admin.action(description="Reprocess tasks and takeaways only")
    def reprocess_tasks(self, request: HttpRequest, queryset: QuerySet[Note]) -> None:
        for note in queryset:
            reprocess_note.delay(note.uuid, tasks=True, summary=False, follow_ups=False)
        self.message_user(
            request,
            ngettext(
                "Tasks reprocessing started for %d note. Check Celery job status for updates.",
                "Tasks reprocessing started for %d notes. Check Celery job status for updates.",
                len(queryset),
            )
            % len(queryset),
            messages.SUCCESS,
        )

    @admin.action(description="Reprocess summary only")
    def reprocess_summary(self, request: HttpRequest, queryset: QuerySet[Note]) -> None:
        for note in queryset:
            reprocess_note.delay(note.uuid, tasks=False, summary=True, follow_ups=False)
        self.message_user(
            request,
            ngettext(
                "Summary reprocessing started for %d note. Check Celery job status for updates.",
                "Summary reprocessing started for %d notes. Check Celery job status for updates.",
                len(queryset),
            )
            % len(queryset),
            messages.SUCCESS,
        )

    @admin.action(description="Reprocess templates only")
    def reprocess_templates(self, request: HttpRequest, queryset: QuerySet[Note]) -> None:
        for note in queryset:
            reprocess_note.delay(note.uuid, tasks=False, summary=False, follow_ups=True)
        self.message_user(
            request,
            ngettext(
                "Templates reprocessing started for %d note. Check Celery job status for updates.",
                "Templates reprocessing started for %d notes. Check Celery job status for updates.",
                len(queryset),
            )
            % len(queryset),
            messages.SUCCESS,
        )


# An inline editor for `Flag.organizations`, displayed as a child of the organization admin page.
class EnabledFlagInline(admin.TabularInline):  # type: ignore[type-arg]
    verbose_name = "Organization enabled flag"
    model = Organization.flag_set.through
    extra = 0


# An inline editor for `Flag.disabled_organizations`, displayed as a child of the organization admin page.
class DisabledFlagInline(admin.TabularInline):  # type: ignore[type-arg]
    verbose_name = "Organization disabled flag"
    model = Organization.disabledflag_set.through
    extra = 0


# An inline editor for `StructuredMeetingDataTemplateRule.organizations`, displayed as a child of
# the organization admin page.
class TemplateRuleInline(admin.TabularInline):  # type: ignore[type-arg]
    verbose_name = "Organization enabled structured meeting data rule"
    model = Organization.structuredmeetingdatatemplaterule_set.through
    extra = 0


@admin.register(Organization)
class OrganizationAdmin(SimpleHistoryAdmin, BotPreferencesMixin):  # type: ignore[misc]
    list_display = ["name", "get_truncated_description", "transcript_ttl", "users_link", "notes_link"]
    search_fields = ["name", "description"]
    readonly_fields = [
        "users_link",
        "notes_link",
        "uuid",
        "recording_image",
        "not_recording_image",
        "flags_enabled_for_everyone",
        "active_templates",
        "active_agendas",
    ]
    inlines = [EnabledFlagInline, DisabledFlagInline, TemplateRuleInline]

    @admin.display(description="Active Templates")
    def active_templates(self, obj: Organization) -> SafeString:
        """Display all active templates for this organization."""
        return get_org_active_templates_html(obj)

    @admin.display(description="Active Agenda Templates")
    def active_agendas(self, obj: Organization) -> SafeString:
        """Display all active agenda templates for this organization."""
        return get_org_active_agendas_html(obj)

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "name",
                    "uuid",
                    "description",
                    "transcript_ttl",
                    "phone_number",
                    "users_link",
                    "notes_link",
                )
            },
        ),
        (
            "Advanced options",
            {
                "classes": ("collapse",),
                "fields": ("crm_configuration", "preferences", "recording_image", "not_recording_image"),
            },
        ),
        (
            "Flags",
            {
                "fields": ("flags_enabled_for_everyone",),
            },
        ),
        (
            "Templates",
            {
                "fields": ("active_templates", "active_agendas"),
            },
        ),
    )
    formfield_overrides = {JSONField: {"widget": JSONEditorWidget(mode="view")}}

    def delete_queryset(self, request: HttpRequest, queryset: QuerySet[Organization]) -> None:
        super().delete_queryset(request, queryset.defer(None))

    def get_queryset(self, request: HttpRequest) -> QuerySet[Organization]:
        return super().get_queryset(request).defer("crm_configuration", "preferences")

    @admin.display(description="Description", ordering="description")
    def get_truncated_description(self, obj: Organization) -> str:
        return f"{obj.description[:50]}..." if len(obj.description) > 50 else obj.description

    actions = [
        "update_bot_preferences",
        "import_users_using_csv",
        "update_auto_join_calendar_events",
        "delete_organization_and_associated_data",
        "disable_organization",
        "export_organization",
    ]

    def get_urls(self):  # type: ignore[no-untyped-def]
        urls = super().get_urls()
        custom_urls = [
            path(
                "onboarding/",
                self.admin_site.admin_view(OrganizationOnboardingWizard.as_view()),
                name="organization_onboarding",
            ),
            path(
                "import/",
                self.admin_site.admin_view(OrganizationImportView.as_view()),
                name="organization_import",
            ),
        ]
        return custom_urls + urls

    def changelist_view(self, request, extra_context=None):  # type: ignore[no-untyped-def]
        extra_context = extra_context or {}
        extra_context["has_onboarding_permission"] = True
        extra_context["onboarding_url"] = reverse("admin:organization_onboarding")
        return super().changelist_view(request, extra_context=extra_context)

    def save_related(
        self,
        request: HttpRequest,
        form: forms.ModelForm[Organization],
        formsets: forms.BaseModelFormSet[Organization, Any],
        change: bool,
    ) -> None:
        # Flush the cache for any flags that are affected by changes to the organization.
        def affected_flags() -> set[Flag]:
            flags = set()
            for formset in formsets:
                if not formset.has_changed():
                    continue
                if (
                    formset.queryset.model == Organization.flag_set.through
                    or formset.queryset.model == Organization.disabledflag_set.through
                ):
                    for flag in Flag.objects.filter(pk__in=formset.queryset.values_list("flag_id", flat=True)):
                        flags.add(flag)
            return flags

        # Track affected flags before and after saving related objects. Before the save, the
        # organization-flag relations for flags that are being unset are in the querysets; after,
        # the relations for flags that are being set are in the querysets.
        flags_to_flush = affected_flags()
        super().save_related(request, form, formsets, change)
        flags_to_flush = flags_to_flush.union(affected_flags())
        for flag in flags_to_flush:
            flag.flush()

    @admin.display(description="Users")
    def users_link(self, obj: Organization) -> SafeString:
        users_url = f"/api/admin/users/user/?organization__pk__exact={obj.pk}"
        return format_html(f'<a href="{users_url}" target="_blank">View Users</a>')

    @admin.display(description="Notes")
    def notes_link(self, obj: Organization) -> SafeString:
        notes_url = f"/api/admin/meetingsapp/note/?note_owner__organization__pk__exact={obj.pk}"
        return format_html(f'<a href="{notes_url}" target="_blank">View Notes</a>')

    @admin.action(description="User Onboarding using CSV")
    def import_users_using_csv(self, request: HttpRequest, queryset: QuerySet[Organization]) -> HttpResponse:
        if queryset.count() > 1:
            self.message_user(request, "Please select exactly one organization for this action.", level=messages.ERROR)
            return HttpResponse(status=400)

        organization = queryset.first()

        if not organization:
            self.message_user(request, "No organization selected.", level=messages.ERROR)
            return HttpResponse(status=400)

        if "apply" not in request.POST:
            logger.info("Displaying initial form")
            form = UserImportForm()
        else:
            logger.info("Processing form submission")
            form = UserImportForm(request.POST, request.FILES)
            if not form.is_valid():
                if csv_file_errors := form.errors.get("csv_file"):
                    errors_text = csv_file_errors.as_text()
                    logger.error(f"Form is invalid. Error(s): {errors_text}")
                    messages.error(request, f"Form is invalid. Error(s): {errors_text}")
                else:
                    logger.error("Form is invalid. Error(s): {form.errors}")
                    messages.error(request, "Form is invalid. Error(s): {form.errors}")
            else:
                logger.info("Form is valid, processing data")
                csv_file = form.cleaned_data.get("csv_file")
                if not csv_file:
                    logger.error("No CSV file provided.")
                    messages.error(request, "No CSV file provided.")
                else:
                    try:
                        (
                            users_created,
                            users_skipped,
                            licenses_created,
                            licenses_skipped,
                            errors,
                        ) = process_users_from_csv(organization, csv_file)

                        if errors:
                            error_message = "\n".join(errors)
                            self.message_user(
                                request, f"Errors occurred during processing:\n{error_message}", level=messages.ERROR
                            )
                        self.message_user(
                            request,
                            f"Processed CSV for {organization.name}. "
                            f"Users: {users_created} created, {users_skipped} skipped. "
                            f"Licenses: {licenses_created} created, {licenses_skipped} skipped.",
                            level=messages.SUCCESS,
                        )
                        return redirect(request.get_full_path())
                    except Exception as e:
                        logger.error("Error creating users.", exc_info=e)
                        messages.error(request, f"Error processing data: {str(e)}")

        context = {
            "opts": self.model._meta,
            "form": form,
            "action": "import_users_using_csv",
            "title": f"Create Users for {organization.name}",
            "objects": queryset,
        }
        logger.debug(f"Rendering template with context: {context}")
        return render(request, "admin/import_users_using_csv.html", context)

    delete_organization_and_associated_data = admin.action(description="Delete organization and all associated data")(
        delete_organization_and_associated_data
    )

    disable_organization = admin.action(description="Disable organization and its users")(disable_organization)

    @admin.action(description="Export organization & all its data")
    def export_organization(self, request: HttpRequest, queryset: QuerySet[Organization]) -> HttpResponse:
        if queryset.count() != 1:
            self.message_user(request, "Please select exactly one organization for export.", level=messages.ERROR)
            return HttpResponse(status=400)

        organization = queryset.first()
        notes = Note.objects.filter(note_owner__organization=organization)

        response = HttpResponse(
            notes_data_for_export([str(pk) for pk in notes.values_list("pk", flat=True)]),
            content_type="application/json",
        )
        response["Content-Disposition"] = "attachment; filename=org_data.json"
        return response

    def _image_tag_for_base64_jpeg_data(self, image_data: str) -> SafeString:
        if not image_data:
            return format_html("<span>No image data</span>")
        try:
            if not base64.b64decode(image_data, validate=True):
                return format_html("<span>Invalid image data</span>")
        except ValueError:
            return format_html("<span>Invalid image data</span>")

        return format_html(
            '<img src="data:image/jpeg; base64, {}" style="max-width: 400px; max-height: 400px;" />', image_data
        )

    @admin.display(description="Recording image")
    def recording_image(self, obj: Organization) -> SafeString:
        org_specific_data = obj.get_preferences().bot_preferences.recording_image_b64
        if not org_specific_data:
            return format_html(
                "<p><b>No org-level image set; this is the default</b></p>"
            ) + self._image_tag_for_base64_jpeg_data(
                obj.get_preferences().bot_preferences.recording_image_b64_or_default()
            )
        return self._image_tag_for_base64_jpeg_data(org_specific_data)

    @admin.display(description="Not recording image")
    def not_recording_image(self, obj: Organization) -> SafeString:
        org_specific_data = obj.get_preferences().bot_preferences.not_recording_image_b64
        if not org_specific_data:
            return format_html(
                "<p><b>No org-level image set; this is the default</b></p>"
            ) + self._image_tag_for_base64_jpeg_data(
                obj.get_preferences().bot_preferences.not_recording_image_b64_or_default()
            )
        return self._image_tag_for_base64_jpeg_data(org_specific_data)

    @admin.display(description="Flags enabled for everyone")
    def flags_enabled_for_everyone(self, _: Organization) -> SafeString:
        flags = Flag.objects.filter(everyone=True).values_list("name", flat=True)
        return format_html(f"<ul>{''.join([f'<li>{flag}</li>' for flag in flags])}</ul>")


@admin.register(Attendee)
class AttendeeAdmin(SimpleHistoryAdmin):  # type: ignore[misc]
    list_display = ["get_name", "get_attendee_type", "get_note_title", "speaker_time", "speaker_percentage"]
    list_select_related = ["note"]
    raw_id_fields = ["note", "client", "user"]
    search_fields = ["attendee_name", "client__name", "user__email", "note__metadata__meeting_name"]
    list_per_page = 50

    def get_queryset(self, request: HttpRequest) -> QuerySet[Attendee]:
        return (  # type: ignore[no-any-return]
            super()
            .get_queryset(request)
            .select_related("note")
            .prefetch_related(
                Prefetch("client", queryset=Client.objects.only("id", "name", "first_name", "last_name")),
                Prefetch("user", queryset=User.objects.only("id", "name", "first_name", "last_name", "email")),
                Prefetch(
                    "note", queryset=Note.objects.only("id", "metadata").defer("raw_asr_response", "raw_transcript")
                ),
            )
        )

    @admin.display(description="Name", ordering="attendee_name")
    def get_name(self, obj) -> str:  # type: ignore[no-untyped-def]
        return obj.get_name()  # type: ignore[no-any-return]

    @admin.display(description="Type", ordering="client")
    def get_attendee_type(self, obj) -> str:  # type: ignore[no-untyped-def]
        return obj.get_attendee_type()  # type: ignore[no-any-return]

    @admin.display(description="Note Title", ordering="note__metadata__meeting_name")
    def get_note_title(self, obj):  # type: ignore[no-untyped-def]
        return obj.note.metadata.get("meeting_name", "<No Title>") if obj.note and obj.note.metadata else "<No Title>"


# A form that uses the JSONFormWidget for the data or initial_data fields, pulled from the object's schema.
class JSONSchemaEditorForm(forms.ModelForm):  # type: ignore[type-arg]
    class Media:
        css = {"all": ["css/django_jsonform_style_overrides.css"]}

    def __init__(self, *args, **kwargs):  # type: ignore[no-untyped-def]
        super().__init__(*args, **kwargs)
        if not self.instance or not self.instance.schema:
            return
        for field in ["data"]:
            if field in self.fields:
                self.fields[field].widget = JSONFormWidget(
                    schema=self.instance.schema, validate_on_submit=True, attrs={"max-width": "none"}
                )


@admin.register(StructuredMeetingDataTemplate)
class StructuredMeetingDataTemplateAdmin(admin.ModelAdmin):  # type: ignore[type-arg]
    list_display = ["internal_name_for_list_display", "title", "created"]
    formfield_overrides = {JSONField: {"widget": JSONEditorWidget(mode="view")}}
    search_fields = ["title", "internal_name", "uuid"]
    readonly_fields = ["uuid"]
    fields = [
        "title",
        "internal_name",
        "kind",
        "schema_definition",
        "initial_data",
        "prompt",
        "context",
    ]

    def get_urls(self):  # type: ignore[no-untyped-def]
        urls = super().get_urls()
        custom_urls = [
            path(
                "generate/",
                self.admin_site.admin_view(TemplateGeneratorView.as_view()),
                name="structuredmeetingdatatemplate_generate",
            ),
        ]
        return custom_urls + urls

    def internal_name_for_list_display(self, obj: StructuredMeetingDataTemplate) -> str:
        return obj.internal_name or "<No internal name>"


@admin.register(StructuredMeetingData)
class StructuredMeetingDataAdmin(SimpleHistoryAdmin):  # type: ignore[misc]
    list_filter = ["status"]
    list_display = ["internal_name_for_list_display", "title", "created", "status"]
    search_fields = ["title", "internal_name", "uuid"]
    formfield_overrides = {JSONField: {"widget": JSONEditorWidget(mode="view")}}
    readonly_fields = ["uuid", "schema"]
    raw_id_fields = ["note"]

    def internal_name_for_list_display(self, obj: StructuredMeetingData) -> str:
        return obj.internal_name or "<No internal name>"


@admin.register(StructuredMeetingDataTemplateRule)
class StructuredMeetingDataTemplateRuleAdmin(admin.ModelAdmin):  # type: ignore[type-arg]
    list_display = [
        "internal_name_for_list_display",
        "all_meetings",
        "everyone",
        "user_names",
        "org_names",
        "template_names",
        "created",
    ]
    readonly_fields = ["uuid"]
    filter_horizontal = ["meeting_types", "organizations", "users", "follow_up_templates"]
    fields = [
        "internal_name",
        "all_meetings",
        "meeting_types",
        "meeting_categories",
        "everyone",
        "organizations",
        "users",
        "follow_up_templates",
        "uuid",
    ]
    list_filter = ["organizations", "users", "meeting_types", "everyone"]

    @admin.display(description="Internal Name")
    def internal_name_for_list_display(self, obj: StructuredMeetingDataTemplateRule) -> str:
        return obj.internal_name or "<No internal name>"

    @admin.display(description="Users")
    def user_names(self, obj: StructuredMeetingDataTemplateRule) -> str:
        return ",".join([u.name for u in obj.users.all()]) or "<None>"

    @admin.display(description="Orgs")
    def org_names(self, obj: StructuredMeetingDataTemplateRule) -> str:
        return ",".join([o.name for o in obj.organizations.all()]) or "<None>"

    @admin.display(description="Templates")
    def template_names(self, obj: StructuredMeetingDataTemplateRule) -> str:
        return ",".join([(t.internal_name or "<No internal name>") for t in obj.follow_up_templates.all()]) or "<None>"

    def get_queryset(self, request: HttpRequest) -> QuerySet[StructuredMeetingDataTemplateRule]:
        queryset = super().get_queryset(request)
        return queryset.prefetch_related("organizations", "users", "follow_up_templates", "meeting_types")


@admin.register(StructuredMeetingDataSchema)
class StructuredMeetingDataSchemaAdmin(SimpleHistoryAdmin):  # type: ignore[misc]
    list_display = ["name", "description", "created", "modified"]
    search_fields = ["name", "description", "internal_name"]
    readonly_fields = ["uuid", "created", "modified"]
    formfield_overrides = {JSONField: {"widget": JSONEditorWidget(mode="view")}}


@admin.register(MeetingType)
class MeetingTypeAdmin(admin.ModelAdmin):  # type: ignore[type-arg]
    list_display = ["internal_name_for_list_display", "name", "key", "created", "everyone"]
    search_fields = ["name", "internal_name", "key", "uuid"]
    filter_horizontal = ["organizations", "users"]
    readonly_fields = ["uuid"]

    def internal_name_for_list_display(self, obj: MeetingType) -> str:
        return obj.internal_name or "<No internal name>"


@admin.register(MeetingSummaryEmailTemplate)
class MeetingSummaryEmailTemplateAdmin(admin.ModelAdmin):  # type: ignore[type-arg]
    list_display = ["internal_name_for_list_display", "name", "tone", "wordiness", "everyone"]
    search_fields = ["name", "internal_name", "uuid"]
    filter_horizontal = ["organizations", "users"]
    readonly_fields = ["uuid"]

    def internal_name_for_list_display(self, obj: MeetingSummaryEmailTemplate) -> str:
        return obj.internal_name or "<No internal name>"


@admin.register(Prompt)
class PromptAdmin(SimpleHistoryAdmin):  # type: ignore[misc]
    search_fields = ["uuid", "name", "user_prompt"]
    list_display = ["name", "created", "start_of_text"]
    readonly_fields = ["uuid"]

    @admin.display(description="Start of text", ordering="user_prompt")
    def start_of_text(self, obj: Prompt) -> str:
        return (obj.user_prompt or "<Empty Prompt>")[:250]


@admin.register(SearchQuery)
class SearchQueryAdmin(admin.ModelAdmin):  # type: ignore[type-arg]
    list_display = ["query", "structured_response", "created"]
    exclude = ("notes",)
    search_fields = ["query", "structured_response"]
    readonly_fields = ["created"]


@admin.register(AudioBuffer)
class AudioBufferAdmin(admin.ModelAdmin):  # type: ignore[type-arg]
    actions = ["download_audio_data"]
    list_filter = [("note", admin.RelatedOnlyFieldListFilter), "nonce"]
    list_display = ["note", "sequence", "nonce"]
    raw_id_fields = ["note"]

    def _download_ios_audio_data(self, queryset: QuerySet[AudioBuffer]) -> HttpResponse:
        zip_data = io.BytesIO()
        with zipfile.ZipFile(zip_data, "w") as zip_buffer:
            segments: list[dict[str, Any]] = []
            for o in queryset.order_by("sequence"):
                name = f"{o.id}.ts"
                zip_buffer.writestr(name, o.data)
                segments.append({"name": name, "duration": str(o.duration or "0")})
            if len(segments) > 0:
                zip_buffer.writestr("playlist.m3u8", PlaylistGenerator(segments).generate())

        response = HttpResponse(zip_data.getvalue(), content_type="application/zip")
        response["Content-Disposition"] = "attachment; filename=audio.zip"
        return response

    def _download_web_audio_data(self, queryset: QuerySet[AudioBuffer]) -> HttpResponse:
        file_data = io.BytesIO()
        for o in queryset.order_by("sequence"):
            file_data.write(o.data)

        response = HttpResponse(file_data.getvalue(), content_type="audio/ogg;codecs=opus")
        response["Content-Disposition"] = "attachment; filename=audio.opus"
        return response

    @admin.action(description="Download audio data")
    def download_audio_data(self, _: HttpRequest, queryset: QuerySet[AudioBuffer]) -> HttpResponse | None:
        mime_types = set(queryset.values_list("mime_type", flat=True))
        if len(mime_types) > 1:
            self.message_user(_, "Cannot download audio data with different MIME types", messages.ERROR)
            return None
        if len(mime_types) == 0 or not (mime_type := mime_types.pop()):
            self.message_user(_, "No MIME type found for audio. Assumed audio/webm", messages.WARNING)
            mime_type = "audio/webm"

        if "audio/webm" in mime_type:
            return self._download_web_audio_data(queryset)
        if "audio/mp4" in mime_type or "audio/m4a" in mime_type:
            return self._download_ios_audio_data(queryset)
        self.message_user(_, "Unsupported MIME type", messages.ERROR)
        return None


@admin.register(ClientInteraction)
class ClientInteractionAdmin(SimpleHistoryAdmin):  # type: ignore[misc]
    list_display = ["note_name"]
    raw_id_fields = ["note"]

    @admin.display(description="Note Title")
    def note_name(self, obj: ClientInteraction) -> str:
        return obj.note.metadata.get("meeting_name", "<No Title>") if obj.note and obj.note.metadata else "<No Title>"


@admin.register(UserImpersonation)
class UserImpersonationAdmin(admin.ModelAdmin[UserImpersonation]):
    list_display = ["user_impersonated", "user_impersonating", "access_token", "purpose"]
    readonly_fields = ["access_token"]


@admin.register(PhoneNumber)
class PhoneNumberAdmin(admin.ModelAdmin[PhoneNumber]):
    list_display = ["user", "number", "type", "primary"]


@admin.register(ScheduledEvent)
class ScheduledEventAdmin(admin.ModelAdmin[ScheduledEvent]):
    list_display = ["title", "start_time", "end_time", "platform"]
    search_fields = ["title"]
    readonly_fields = ["uuid", "user_specific_source_id", "shared_source_id"]
    list_filter = [("user", admin.RelatedOnlyFieldListFilter), ("note", admin.RelatedOnlyFieldListFilter)]
    raw_id_fields = ["note", "user"]

    @admin.display(description="Calendar event or related note title")
    def title(self, obj: ScheduledEvent) -> str:
        return (
            str(obj.note)
            if obj.note
            else obj.source_data.get("title", "<No Title>")
            if obj.source_data
            else "<No Title>"
        )

    @admin.display(description="Calendar platform")
    def platform(self, obj: ScheduledEvent) -> str:
        return obj.source_data.get("provider", "<No Platform>") if obj.source_data else "<No Platform>"


# Keep this at the bottom of the file after all other admin.register calls
try:
    admin.site.unregister(PeriodicTask)
except admin.sites.NotRegistered:  # type: ignore[attr-defined]
    pass
admin.site.register(PeriodicTask, CustomPeriodicTaskAdmin)


@admin.register(OAuthClientCredentials)
class OAuthClientCredentialsAdmin(admin.ModelAdmin[OAuthClientCredentials]):
    pass
