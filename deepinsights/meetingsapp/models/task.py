from typing import Any, Iterable, Literal
from uuid import UUID

from django.db import models
from simple_history.models import HistoricalRecords

from deepinsights.core.behaviours import StatusMixin, UUIDMixin
from deepinsights.meetingsapp.models.note import Note
from deepinsights.users.models.user import User


class Task(StatusMixin, UUIDMixin):
    task_title = models.CharField(blank=False)
    task_desc = models.TextField(null=True, blank=True)
    completed = models.BooleanField(default=False, blank=True)
    due_date = models.DateTimeField(null=True)
    task_owner = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    note = models.ForeignKey(Note, on_delete=models.CASCADE, null=True)
    metadata = models.JSONField(null=True, blank=True, default=dict)
    assignee = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name="assigned_tasks")
    history = HistoricalRecords(inherit=True)

    @property
    def possible_assignees(self) -> Iterable[User]:
        return self.task_owner.organization.users.all() if self.task_owner and self.task_owner.organization else []

    def __str__(self) -> str:
        return str(self.task_title)

    def get_completed(self) -> Literal["complete", "incomplete"]:
        return "complete" if self.completed else "incomplete"

    def get_parent_note_uuid(self) -> UUID | None:
        return self.note.uuid if self.note else None

    def save(self, *args: Any, **kwargs: Any) -> None:
        if not self.assignee:
            self.assignee = self.task_owner
        super().save(*args, **kwargs)
