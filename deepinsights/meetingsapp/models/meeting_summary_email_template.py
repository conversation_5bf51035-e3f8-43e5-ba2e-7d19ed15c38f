from django.db import models

import deepinsights.core.ml.prompt_templates.followup_email as email_prompt
from deepinsights.core.behaviours import StatusMixin, UUIDMixin
from deepinsights.users.models.user import User


def get_default_data() -> list[str]:
    return [MeetingSummaryEmailTemplate.DataInclusionChoices.DEFAULT]


class MeetingSummaryEmailTemplate(StatusMixin, UUIDMixin):
    """A template for follow-up emails after meetings, with configurable modifiers for tone and content."""

    class ToneChoices(models.TextChoices):
        DEFAULT = "default", "Default"

        CASUAL = "casual", "Casual - Friendly"
        BUSINESS = "business", "Business"
        FORMAL = "formal", "Very Formal"

    class WordinessChoices(models.TextChoices):
        DEFAULT = "default", "Default"

        TERSE = "terse", "Terse"
        SUMMARY = "summary", "Summary"
        VERBOSE = "verbose", "Verbose"
        VERBATIM = "verbatim", "Verbatim"

    class AmountDiscussionChoices(models.TextChoices):
        DEFAULT = "default", "Default"
        INCLUDE = "include", "Include"
        OMIT = "omit", "Omit"

    class VoiceChoices(models.TextChoices):
        DEFAULT = "default", "Default"
        USER1 = "user1", "User 1 emails (To be added later)"
        USER2 = "user2", "User 2 email (To be added)"

    class DataInclusionChoices(models.TextChoices):
        DEFAULT = "default", "Default"
        TASKS_INTERNAL = "tasks_internal", "Tasks (internal)"
        TASKS_EXTERNAL = "tasks_external", "Tasks (external)"
        TASKS_CLIENT = "tasks_client", "Tasks (client)"
        TASKS_ADVISOR = "tasks_advisor", "Tasks (advisor)"
        KEY_TAKEAWAYS = "key_takeaways", "Key Takeaways"
        SUMMARY = "summary", "Summary"
        DISCLOSURE_DOCS = "disclosure_docs", "Additional Disclosure docs"
        ATTACHMENTS = "attachments", "Relevant Attachments"

    name = models.CharField(max_length=200, help_text="The user-visible name of this email template")

    internal_name = models.TextField(
        blank=True,
        null=True,
        max_length=200,
        help_text=(
            "An internally-visible name used in the admin console to identify this template. "
            "This should never be shown to users; it is only for internal reference."
            "Can include the client name."
        ),
    )

    system_prompt = models.TextField(
        help_text="The system prompt for this email generator.", default=email_prompt.SYSTEM_PROMPT
    )

    generation_prompt = models.TextField(
        help_text="The prompt for email generation. a.k.a HUMAN_PROMPT", default=email_prompt.HUMAN_PROMPT
    )

    email_template_content = models.TextField(
        null=True,
        blank=True,
        help_text=(
            "The actual email template content. Can include placeholders for dynamic content "
            "that will be replaced when generating the email."
            "Can be left empty for a default email."
        ),
    )

    description = models.TextField(
        blank=True, null=True, help_text="Optional description of when and how to use this template"
    )

    use_html = models.BooleanField(
        default=False,
        help_text="Whether the email template should be generated as HTML. If false, the email will be plain text.",
    )

    # Modifiers - Single Select Fields
    tone = models.CharField(
        max_length=20,
        choices=ToneChoices.choices,
        default=ToneChoices.DEFAULT,
        help_text="The overall tone of the email",
    )

    wordiness = models.CharField(
        max_length=20,
        choices=WordinessChoices.choices,
        default=WordinessChoices.DEFAULT,
        help_text="How detailed/verbose the email should be",
    )

    amount_discussion = models.CharField(
        max_length=20,
        choices=AmountDiscussionChoices.choices,
        default=AmountDiscussionChoices.DEFAULT,
        help_text="Whether to include specific amounts discussed in the meeting",
    )

    voice = models.CharField(
        max_length=20,
        choices=VoiceChoices.choices,
        default=VoiceChoices.DEFAULT,
        help_text="Which voice/persona to use in the email",
    )

    everyone = models.BooleanField(
        default=False,
        help_text=(
            "Whether or not this template is available to everyone. "
            "If true, all users have access to this template. If false, only users "
            "allowlisted by the organizations list or users list have access."
        ),
    )

    organizations = models.ManyToManyField(
        to="meetingsapp.Organization",
        blank=True,
        help_text=(
            "The organizations which have access to this template. "
            "If empty, no specific organizations have access. Users may still have "
            "access via the `users` or `everyone` fields."
        ),
    )

    users = models.ManyToManyField(
        to=User,
        blank=True,
        help_text=(
            "The users who have access to this template. "
            "If empty, no specific users have access. Users may still have "
            "access via the `organizations` or `everyone` fields."
        ),
    )

    def is_user_allowed(self, user: User) -> bool:
        """Check if a user is allowed to access this template."""
        if self.everyone:
            return True
        return self.users.contains(user) or (org := user.organization) and self.organizations.contains(org) or False

    def __str__(self) -> str:
        internal_name = self.internal_name or "<No internal name>"
        return f"{internal_name} (external name: {self.name})"
