from django.db import models
from phonenumber_field.modelfields import PhoneNumber<PERSON>ield
from phonenumber_field.phonenumber import PhoneNumber

from deepinsights.core.behaviours import StatusMixin, UUIDMixin
from deepinsights.meetingsapp.models import organization
from deepinsights.users.models.user import User


class Client(UUIDMixin, StatusMixin):
    name = models.CharField()
    first_name = models.CharField(null=True, blank=True)
    last_name = models.CharField(null=True, blank=True)
    job_title = models.CharField(null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    phone_number: models.Field[PhoneNumber | str | None, PhoneNumber | None] = PhoneNumberField(
        blank=True,
        null=True,
        help_text=(
            "A phone number for the client."
            "\n\n"
            "This field is somewhat intelligent: if you pass in an invalid phone number it will return an error. "
            "If you provide a valid US phone number (without country code), it will automatically add the country "
            "code. If you provide in a valid phone number with a country code, it will use that country code."
            "\n\n"
            "Note that the number is stored and rendered in E.164 format (e.g., +12125551212), regardless of how "
            "you enter it."
        ),
    )
    organization = models.ForeignKey(organization.Organization, on_delete=models.CASCADE)
    crm_id = models.CharField(max_length=100, null=True, blank=True)
    client_type = models.CharField(max_length=100, default="individual")
    crm_system = models.CharField(max_length=100, default="")
    authorized_users = models.ManyToManyField(User, related_name="authorized_clients")
    owner = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name="owned_clients")

    def __str__(self) -> str:
        return self.name

    def to_dict(self) -> dict[str, str | None]:
        return {
            "uuid": str(self.uuid),
            "name": self.name,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "email": self.email,
            "job_title": self.job_title,
            "client_type": self.client_type,
            "crm_id": self.crm_id,
        }
