from django.db import models
from django_stubs_ext.db.models import TypedModelMeta

from deepinsights.core.behaviours import StatusMixin, UUIDMixin
from deepinsights.core.integrations.calendar.calendar_models import CalendarEvent
from deepinsights.users.models.user import User


class ScheduledEvent(StatusMixin, UUIDMixin):
    class AutoJoinOverride(models.TextChoices):
        DEFAULT = "default", "Default"
        ENABLED = "enabled", "Enabled"
        DISABLED = "disabled", "Disabled"

    start_time = models.DateTimeField(help_text="The start time of the event.")
    end_time = models.DateTimeField(help_text="The end time of the event.")
    user_specific_source_id = models.TextField(
        null=True,
        blank=True,
        help_text=(
            "The user-specific identifier of the event in the source system which provided it (likely a calendar "
            "provider). This is distinct from the shared source ID because calendar systems can have representations "
            "of events that are specific to each user, and we may want to store information about an event for a "
            "specific user rather than generically across all users who have the same event."
        ),
    )
    shared_source_id = models.TextField(
        null=True,
        blank=True,
        help_text=(
            "The shared/common identifier of the event in the source system which provided it (likely a calendar "
            "provider). This identifier is meant to be the same across all ScheduledEvent instances when the underlying "
            "event is the same (within reason; we don't expect to be able to unify the same real-world event across "
            "different calendar systems or unify two different calendar events in a calendar system that match one "
            "real-world event but don't have any shared identifier in the calendar system). This is distinct from the "
            "user source ID because calendar systems can have representations of events that are specific to each user, "
            "and we may want to store information about an event for a specific user rather than generically across all "
            "users who have the same event."
        ),
    )
    source_data = models.JSONField(
        null=True,
        blank=True,
        help_text="The data from the source system which provided this event (i.e., the calendar provider or CRM).",
    )
    autojoin_behavior = models.CharField(
        choices=AutoJoinOverride.choices,
        default=AutoJoinOverride.DEFAULT,
        max_length=10,
        help_text=(
            "The user-determined autojoin behavior for this event. Note that this value on a single scheduled event "
            "instance by itself does not indicate whether a bot will automatically join the the meeting. It's possible "
            "that another user attending the same meeting has enabled autojoin for this meeting, and so a bot will join "
            "the meeting."
        ),
    )
    user = models.ForeignKey(
        "users.User",
        on_delete=models.CASCADE,
        related_name="scheduled_events",
        help_text="The user who this scheduled event is associated with.",
    )
    note = models.OneToOneField(
        "meetingsapp.Note",
        on_delete=models.CASCADE,
        # Prefix this with an underscore so the Note model can expose a scheduled_event property to its collaborators.
        related_name="_scheduled_event",
        blank=True,
        null=True,
        help_text="The note this scheduled event is associated with.",
    )

    class Meta(TypedModelMeta):
        unique_together = (
            "user",
            "user_specific_source_id",
        )

    # Creates or updates the matching ScheduledEvent instance with the data from a CalendarEvent.
    @classmethod
    def create_or_update_with_calendar_event(
        self, user: User, calendar_event: CalendarEvent
    ) -> tuple["ScheduledEvent", bool]:
        return ScheduledEvent.objects.update_or_create(
            user=user,
            user_specific_source_id=calendar_event.user_specific_id,
            shared_source_id=calendar_event.id,
            defaults={
                "start_time": calendar_event.start_time,
                "end_time": calendar_event.end_time,
                "source_data": calendar_event.model_dump(mode="json"),
            },
        )

    @property
    def is_from_external_system(self) -> bool:
        """Whether or not this event is from an external system (i.e., not created from the Zeplyn app)."""
        return self.user_specific_source_id is not None

    def is_recall_autojoin_available(self, user: User) -> bool:
        """Whether or not Recall autojoin is available for this event for the given user."""
        if not user.recall_calendar_id:
            return False
        if not user.recall_calendar_platform:
            return False
        return bool(
            self.source_data
            and self.source_data.get("meeting_urls")
            and self.source_data.get("provider") == user.recall_calendar_platform
        )

    def is_recall_autojoin_enabled(self, user: User) -> bool:
        """Whether or not Recall autojoin is enabled for this event for the given user."""
        return (
            self.is_recall_autojoin_available(user)
            and self.autojoin_behavior != ScheduledEvent.AutoJoinOverride.DISABLED
        )
