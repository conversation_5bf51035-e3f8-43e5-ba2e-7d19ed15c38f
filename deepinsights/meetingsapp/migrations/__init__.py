
# Data for the Bento life event template
life_event_data = {
    "review_entries": [
        {"id": "birth_or_adoption", "kind": "toggle", "topic": "Birth or Adoption", "discussed": False},
        {"id": "saving_and_investing", "kind": "toggle", "topic": "Saving and Investing", "discussed": False},
        {"id": "off_to_college", "kind": "toggle", "topic": "Off to College", "discussed": False},
        {"id": "age_of_majority", "kind": "toggle", "topic": "Age of Majority", "discussed": False},
        {"id": "first_job", "kind": "toggle", "topic": "First Job", "discussed": False},
        {"id": "paying_off_student_debt", "kind": "toggle", "topic": "Paying Off Student Debt", "discussed": False},
        {"id": "getting_divorced", "kind": "toggle", "topic": "Getting Divorced", "discussed": False},
        {"id": "getting_married", "kind": "toggle", "topic": "Getting Married", "discussed": False},
        {"id": "buying_a_home", "kind": "toggle", "topic": "Buying a Home", "discussed": False},
        {"id": "selling_a_home", "kind": "toggle", "topic": "Selling a Home", "discussed": False},
        {"id": "receiving_a_windfall", "kind": "toggle", "topic": "Receiving a Windfall", "discussed": False},
        {"id": "moving_to_a_new_state", "kind": "toggle", "topic": "Moving to a New State", "discussed": False},
    ]
}

# The prompt for the template generator.
template_prompt = """You are generating a structured data template that must exactly follow this schema:

{schema}

Given these questions:
{questions}

Your task:
1. Generate TWO json objects wrapped in a parent object:
   - "initial_data": Template structure from questions
   - "example": Example with answers based on the transcript

For each question:
- Generate a unique 'id' as lowercase with underscores
- Set 'kind' as either:
  - "header" for section headers
  - "toggle" for yes/no questions
  - "select" for single choice questions
  - "multiselect" for multiple choice questions
- For select/multiselect, include 'options' array
- Set 'discussed' to false in initial_data
- Do not populate in evidence or example

Example transcript for answers:
speaker0    "00:15:20-->00:15:40"
Let's review your current financial position. Your portfolio currently has $500,000 in retirement accounts.
speaker1    "00:15:40-->00:15:55"
Yes, and I recently inherited $200,000 which I'd like to invest.
speaker0    "00:15:55-->00:16:15"
Your risk tolerance questionnaire from last month indicates moderate risk tolerance.

Return ONLY the following JSON structure with no additional text:
{{
    "initial_data": {{
        "review_entries": [
            // Array of review entries following schema exactly with initial values populated
        ]
    }},
    "example": {{
        "review_entries": [
            // Array of filled review entries with updated values based on evidence
        ]
    }}
}}"""

unstructured_meeting_data_schema_name = "unstructured_text"

# The schema used for unstructured meeting data.
unstructured_meeting_data_schema = {
    "$id": "https://zeplyn.ai/unstructured_text_content.schema.json",
    "type": "object",
    "title": "Unstructured meeting data",
    "$schema": "https://json-schema.org/draft/2020-12/schema",
    "required": [
        "content",
        "format",
    ],
    "properties": {
        "content": {"type": "string", "description": "The textual content of this unstructured meeting data."},
        "format": {
            "enum": ["markdown"],
            "type": "string",
            "description": "The format of the content, to help inform how it should be rendered.",
        },
    },
    "description": "",
    "additionalProperties": False,
}

pre_meeting_agenda_generator_prompt_key = "pre_meeting_agenda_generator"
pre_meeting_agenda_generator_prompt_text = """As an experienced financial advisor, please fill in this agenda template with relevant information from the client's history. Use specific details from the provided summaries and household information.

The output should maintain the same format and structure as the input template, but should include relevant details from the client history under each section. If there is no relevant information for a section, keep the section and don't mention anything. If the client history and household info is not available, just output the template as it was input.

Client History:
{client_history}

Household Information:
{household_info}

Agenda Template:
{agenda_template}

Please fill in the agenda template while maintaining its original format. Reference specific details from the client history or household where relevant, such as previous discussions, decisions, and numerical data. Include dates when mentioning past events.

When filling out info for agenda items, be terse. Take inspiration from the example below. Write bullet points as if you are starting a sentence by "Talk about ...".

If no client history or household info is available, don't output them. Just output the template as it was input. 

Here is an example agenda, filled out with information for a specific client:

Agenda:

# Personal and Business Developments [10 mins]
  - Aaron’s potential relocation to Singapore
  - Ariel’s plans to expand her consulting business
# Financial Planning [30 mins]
  a. Last Planning Deliverable (plan, projection, Roth conversion analysis, etc.)
    - Key planning assumption: Liquidating $40,000 in Google stock for a lower refinance interest rate
    - Implementation timeline update
  b. Cash Flow Planning
    - Transition to a $100,000 annual salary in 2 months
    - Tax-efficient savings and withdrawal strategies
    - Liquidity planning: Asset liquidation on January 2nd to defer capital gains taxes
    - Liability management: Construction loan at 4.15% and potential cash-out refinance
  c. Cash Flow Planning
    - YTD capital gains and losses: Plan to liquidate $159,000 in 2025 to avoid current-year taxes
    - YTD investment income update ($31,000 increase since November 1st)
  d. Insurance Planning
  e. Estate Planning
  f. Charitable Goals
  g. Education Goals/Planning
    - New York 529 plan strategy: Maintaining DIY plan for $10,000 state tax benefit
  h. Business Succession Planning
# Investment Portfolio Strategy & Economic Overview [15 min]
  - Administrative/service questions include concerns about the contractor's delay in providing amendments necessary for the loan execution.
# Next Meeting / Meeting Frequency
  - To be scheduled based on ongoing developments and client availability."""

# The key for the Discovery Meeting meeting type.
discovery_meeting_key = "discovery_meeting"

# The kind for the Discovery Meeting agenda template.
discovery_agenda_kind = "discovery_agenda"

# The initial data for a discvoery meeting agenda template.
discovery_agenda_initial_data = {
  "format": "markdown",
  "content": "# Meeting Agenda\n\n## 1. Welcome and Introductions\n- Introduce yourself and your firm\n- Mention mutual connections/referrals\n- Ask about the prospect's background and needs\n\n## 2. Understand Prospect's Goals and Needs\n- Discuss financial goals\n- Identify specific objectives (retirement, tax, etc.)\n- Address challenges in managing wealth\n- Clarify investment timeline\n\n## 3. Present Your Value Proposition\n- Explain your approach\n- Outline investment philosophy\n- Discuss risk management\n- Highlight services offered\n- Share success stories\n\n## 4. Discuss Fees and Service Model\n- Explain fee structure\n- Clarify what fees cover\n- Answer pricing questions\n\n## 5. Agree on Next Steps\n- Address any final questions\n- Schedule follow-up meeting\n\n## 6. Closing and Wrap-up\n- Thank the prospect\n- Confirm availability for follow-up\n- Schedule next call/meeting\n- Optional: Share educational resources"
}

# The key for the Onboarding Meeting meeting type.
onboarding_meeting_key = "onboarding_meeting"

# The kind for the Onboarding Meeting agenda template.
onboarding_agenda_kind = "onboarding_agenda"

# The initial data for an onboarding meeting agenda template.
onboarding_agenda_initial_data =  {
  "format": "markdown",
  "content": "# Onboarding Agenda\n\n## Initial Assessment\n- Review financial goals and timeline\n- Discuss risk tolerance assessment\n- Review current investment allocation\n- Discuss estate planning needs\n\n## Document Collection\n- Investment account statements\n- Tax returns (last 2 years)\n- Insurance policies\n- Estate planning documents\n- Employee benefits information\n\n## Compliance Items\n- Complete client agreement\n- Review privacy policy\n- Sign investment policy statement\n- Complete risk assessment questionnaire\n\n# Next Steps\n- Schedule follow-up meeting\n- Complete risk assessment questionnaire\n- Gather requested documents\n- Meeting Reference ID: [UUID will be inserted here]"
}

# The kind for the Annual Review Meeting agenda template.
annual_review_agenda_kind="client_review_agenda"

# The initial data for an annual review meeting agenda template.
annual_review_agenda_initial_data = {
  "format": "markdown",
  "content": "# Onboarding Meeting Agenda\n## Objectives\nShare and affirm alignment with ongoing planning. Review investment strategy, objectives, and markets. Review and update on planning opportunities. Discuss any relevant life and financial updates.\n## Agenda\n### Special Trading Instructions\n- Document any client-specific trading instructions or exceptions\n- Note limitations on specific securities or industries\n- Discuss cash management requirements (if applicable)\n### KYC Review/Personal Overview\n- **Personal Overview**: Age, marital status, number of dependents, residency status\n- **Investment Goals/Objectives**: Primary investment goals (growth, income, preservation)\n- **Time Horizon**: Expected investment timeframe considering age, fund access plans, long-term goals\n- **Investment Restrictions/Unique Circumstances**: Industry exclusions, ESG preferences, family trusts\n- **Enhanced KYC Explanation**: Additional measures for high-risk factors or complex profiles (if applicable)\n### AML Risk Assessment\n- **Risk Level**: High/Medium/Low determination based on established criteria\n- **Explanation**: Rationale behind assessment and factors contributing to risk level\n### Additional Important Information\n- **Inflows/Outflows**: Expected contributions, withdrawals, or cash requirements\n- **Outside Investments**: External accounts or investments affecting overall allocation\n- **Household Employment Status and Income**: Financial stability and investment capacity assessment\n- **Net Worth**: Approximate value for determining investment suitability\n- **Unique Circumstances**: Important restrictions, health conditions, or other relevant factors\n### Investment Sophistication\n- **Level**: Low/Medium/High assessment of knowledge and experience\n- **Explanation**: Reasoning for sophistication level based on experience with asset classes\n- **Client's Investment Knowledge**: Assessment based on observed criteria\n### PM Mandate Recommendation and Explanation\n- **Risk Profile**: Evaluation of willingness and ability to accept risk\n- **Recommended Mandate**: Classification (Defensive, Defensive Balanced, Balanced, Balanced Growth, Growth)\n- **Rationale**: Explanation of alignment with client objectives and any deviation from Risk Tolerance Questionnaire\n### Next Steps\n- Review implementation timeline\n- Schedule follow-up communications\n- Document action items\n### Next Meeting\n- Proposed date and time\n- Meeting frequency and preference (in-person, virtual)"
}

# A prompt for the "Ask Anything on a Note" feature.
search_prompt = """You are a highly specialized financial AI assistant, tasked with analyzing and interpreting financial content.
You will be provided with a transcript or summary of a meeting conducted between an advisor and their client containing financial
discussions, reports, or analyses. Your role is to extract relevant insights and provide a comprehensive, accurate,
and contextually relevant response to the specific question or query posed as

<query>
{query}.
</query>

Your response must be structured as a JSON object that follows this format:
{{
    "topic": "Single focused topic that directly addresses the query",
    "bullets": [
        "Detailed bullet point capturing key information, numbers, and context",
        "Additional bullet points with supporting details and evidence"
    ]
}}

Guidelines for analyzing the content:

1. Focus exclusively on information present in the provided content
2. Extract and preserve all quantitative data, exact numbers, and financial metrics
3. Maintain chronological flow and relationships between different points
4. Use financial terminology and principles where applicable
5. If calculations are involved, show step-by-step reasoning
6. Support your points with specific evidence from the content
7. When relevant, include timestamps or reference specific sections
8. Ensure all bullet points are detailed and self-contained

<Example>
Example with Transcripts:

Query: "What was discussed about the client's retirement planning?"

Content:
speaker0    "00:15:20-->00:15:40"
Let's review your current financial position. Your portfolio currently has $500,000 in retirement accounts.
speaker1    "00:15:40-->00:15:55"
Yes, and I recently inherited $200,000 which I'd like to invest.
speaker0    "00:15:55-->00:16:15"
Your risk tolerance questionnaire from last month indicates moderate risk tolerance.
speaker0    "00:16:15-->00:16:45"
For your time horizon, you mentioned wanting to retire in 15 years.
speaker1    "00:16:45-->00:17:00"
That's right, I'm aiming to retire at 65.
speaker0    "00:17:00-->00:17:30"
Let's look at your current investment allocation. You're currently at 70% stocks, 30% bonds.
speaker1    "00:17:30-->00:17:50"
I'm comfortable with that mix given the current market.
speaker0    "00:17:50-->00:18:10"
Your portfolio performance has been strong, up 8% year to date.

Response:
{{
    "topic": "Retirement Planning Status and Strategy",
    "bullets": [
    "Current retirement portfolio valued at $500,000, with additional $200,000 inheritance pending investment (timestamp: 00:15:20)",
    "Established 15-year time horizon with target retirement age of 65 (timestamp: 00:16:15)",
    "Investment strategy aligned with moderate risk tolerance: 70% stocks, 30% bonds allocation, showing 8% YTD performance",
    "Client confirms comfort with current allocation strategy given market conditions"
    ]
}}

</Example>

Here is the meeting transcript or summary for your reference:

{input_data}

Here is additonal context including search history for your reference:
{additional_context}
"""

