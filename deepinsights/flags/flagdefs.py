from enum import Enum
from typing import TYPE_CHECKING

from deepinsights.flags.flags import Environment, FeatureFlag

if TYPE_CHECKING:
    from django.contrib.auth.models import AbstractBaseUser


# Flags for the app.
#
# Only flags in this list will exist in the database in the app. Flags not in this list will be
# deleted from the database. Flags in this list will be created or updated to match the environment
# specifications when the app starts.
#
# Example of a feature flag that is available (can be toggled) in demo and active (always on) in
# dev. This flag cannot be togged in other environments.
# EnablePodBayDoors = FeatureFlag(
#     name="EnablePodBayDoors",
#     details="Enables the pod bay doors for the spacecraft",
#     available_environments=[Environment.DEMO],
#     active_environments=[Environment.DEV]
# ),
# Example of a feature flag that is active (always on) in dev and available (can be toggled) in
# all other environments.
# EnableHAL9000 = FeatureFlag(
#     name="EnableHAL9000",
#     details=(
#         "Enables the updated HAL9000 AI implementation. Still has some prod issues to work out, "
#         "but should be stable."
#     ),
#     active_environments=[Environment.DEV],
# ),
class Flags(Enum):
    EnableMeetingPrepExperience = FeatureFlag(
        name="EnableMeetingPrepExperience",
        active_environments=[Environment.DEV],
        details=(
            "Enables the client recap experience (part of "
            "https://docs.google.com/document/d/1BJP4ks6xx7XIcTDc_2cOngTvzJTLsIgfTLOicJNL49A/edit#heading=h.y37thcgh3gr5)"
        ),
    )
    EnableClientView = FeatureFlag(
        name="EnableClientView",
        active_environments=[Environment.DEV, Environment.DEMO],
        details="Client view, a new top-level item in the app sidebar/bottom nav",
    )
    EnableSalesforceOAuthIntegration = FeatureFlag(
        name="EnableSalesforceOAuthIntegration",
        details="Support connecting to Salesforce via the authorization code OAuth flow.",
    )

    EnableAutoJoinBotsToCalendarEvents = FeatureFlag(
        name="EnableAutoJoinBotsToCalendarEvents",
        details="Enables automatic joining of bots to meetings on a user's calendar.",
    )

    EnableSalesforceClientCredentialsOAuthIntegration = FeatureFlag(
        name="EnableSalesforceClientCredentialsOAuthIntegration",
        details=(
            "Support connecting to Salesforce via the Client Credentials Flow OAuth flow "
            "(https://help.salesforce.com/s/articleView?id=sf.remoteaccess_oauth_client_credentials_flow.htm&type=5)."
        ),
    )
    EnableHideNotes = FeatureFlag(
        name="EnableHideNotes",
        active_environments=[Environment.DEV],
        details="Enables a button that allows users to hide the notes in the notes list.",
    )
    DemoClientMockDataEnabled = FeatureFlag(
        name="DemoClientMockDataEnabled",
        details="Whether to show the fake data that we are using for demos.",
    )
    EnableManualEditSaves = FeatureFlag(
        name="EnableManualEditSaves",
        details="Whether or not you have to click out of edit mode to save a note.",
    )

    EnableDetailedActionItems = FeatureFlag(
        name="EnableDetailedActionItems",
        active_environments=[Environment.DEV],
        details="Whether or not to use the updated prompt that should return more action items.",
    )

    EnableProcessRecallBotTranscriptsWithZeplynDeepgramIntegration = FeatureFlag(
        # The FeatureFlag was previously named EnableProcessRecallBotTranscriptsLocally, but that
        # caused confusion. Changing a flag's name is equivalent to deleting a flag and creating a
        # new one, so we renamed the FeatureFlag but not the flag that it creates.
        name="EnableProcessRecallBotTranscriptsLocally",
        details=(
            "Enable processing of Recall bot transcripts using our Deepgram integration instead of Recall's "
            "bot transcription integration."
            "\n\n"
            "This entails:\n"
            "- disabling Recall's live transcription feature\n"
            "- fetching recorded audio from Recall instead of fetching transcripts\n"
            "- processing Recall audio using our Deepgram integration (the same as with mic recordings)\n"
            "\n\n"
            "Note that this is disctinct from the 'fallback to local Deepgram on Recall failure' logic, which "
            "is always active and runs when Recall's transcription fails. If this flag is disabled, when Recall "
            "transcription fails, the bot will fall back to Deepgram. When this flag is enabled, then that logic "
            "is never hit, because we always use local Deepgram."
        ),
    )

    EnableAutoJoinToggleSetting = FeatureFlag(
        name="EnableAutoJoinToggleSetting",
        details="Enables a setting that allows users to turn off and on auto calendar event joining.",
    )

    EnableCalendarLookaheadSetting = FeatureFlag(
        name="EnableCalendarLookaheadSetting",
        details="Enables a setting that allows users to select how far in advance to display calendar events.",
    )

    EnableCalendarEventsFromCRM = FeatureFlag(
        name="EnableCalendarEventsFromCRM",
        details="Whether or not to fetch calendar events based on data in the user's CRM.",
    )

    EnableOrgLevelBotDeduplication = FeatureFlag(
        name="EnableOrgLevelBotDeduplication",
        details=(
            "Whether to deduplicate auto-join bots at the org level, instead of not at all. "
            "This flag doesn't make sense to set at a user level: if only one user has it enabled, "
            "then only that user will have org-level deduplication, which is functionally the same "
            "as if the flag were not enabled for that user at all."
            "\n\n"
            "NOTE: when toggling this flag, you MUST run the 'Update auto-join calendar events "
            "(including bot image)' action for the affected users/organizations."
            "\n\n"
            "NOTE: it's almost certainly the case that 'EnableSetAttendeesAsAuthorizedUsers' should "
            "be enabled for any org for which this flag is enabled."
            "\n\n"
            "If this is enabled, then if multiple users in the same org have the same event on their "
            "calendar and have auto-join enabled, only one bot should be scheduled for that event."
        ),
    )

    EnableSetAttendeesAsAuthorizedUsers = FeatureFlag(
        name="EnableSetAttendeesAsAuthorizedUsers",
        details="Whether or not note attendees should be added as authorized users.",
    )

    EnableShareNotes = FeatureFlag(
        name="EnableShareNotes",
        details="Enables a frontend feature that allows the user to select authorized users on a note.",
    )

    EnableEditAttendees = FeatureFlag(
        name="EnableEditAttendees",
        details="Enables a frontend feature that allows the user to edit meeting attendees from the note edit screen.",
    )

    EnableNativeZoomBots = FeatureFlag(
        name="EnableNativeZoomBots",
        details=(
            "Enables native Zoom bots instead of web bots."
            "\n\n"
            "This flag changes how we create Zoom bots via Recall. See "
            "https://docs.recall.ai/docs/zoom#web-vs-native-zoom-bots for details from Recall about the differences."
            "Most importantly for us, if a customer has E2E encryption enabled for Zoom, recording only works for "
            "native bots; but, Recall recommends using web bots if possible, and native bots cannot output audio to "
            "the meeting (so our 'Notetaking started/stopped' messages will not work)."
            "\n\n"
            "True/on means that we will create native Zoom bots; False/off means that we will create web Zoom bots."
        ),
    )

    EnableMeetingSummaryEmailTemplates = FeatureFlag(
        name="EnableMeetingSummaryEmailTemplates",
        details="Enables email template selector go/project-email-templates",
    )

    EnableShowMeetingsWithoutURLsSetting = FeatureFlag(
        name="EnableShowMeetingsWithoutURLsSetting",
        details=(
            "Enables a toggle in the settings that allows users to show/hide meetings without URLs in the "
            "scheduled meetings section (https://zeplyn.atlassian.net/browse/ENG-715)"
        ),
    )

    EnableSearchOnNote = FeatureFlag(
        name="EnableSearchOnNote",
        details="Enables a feature that allows users to ask a question to the LLM about notes on the note details page.",
    )

    EnableDashboardFeature = FeatureFlag(
        name="EnableDashboardFeature",
        active_environments=[Environment.DEV],
        details="Enables the dashboard route and page on the frontend. https://drive.google.com/file/d/1XiciGOgvjfDZjoLvy6QB54i9VvRx7j5x/view?usp=drive_link",
    )

    EnablePhoneCallRecordings = FeatureFlag(
        name="EnablePhoneCallRecordings",
        details="Enables the recording of phone calls, used as an audio source for notes (https://zeplyn.atlassian.net/browse/MA-22).",
    )

    EnableMeetingLinkSuggestions = FeatureFlag(
        name="EnableMeetingLinkSuggestions",
        details=(
            "Enables a dropdown of suggested meeting links or phone numbers when creating a note: "
            "https://drive.google.com/file/d/1InwHw_euQCkuxdAajHHLj3Jol7EOP4Jh/view?usp=sharing"
        ),
    )

    ShowClientTypesInAttendeesList = FeatureFlag(
        name="ShowClientTypesInAttendeesList",
        details=(
            "Shows client types in the attendees list (when there are multiple client types): "
            "https://drive.google.com/file/d/1wI-ilMt-d7NYMPJVc6tN1ibZKd4qy573/view?usp=drive_link"
        ),
    )

    EnableBentoIntegrationSettingsToggle = FeatureFlag(
        name="EnableBentoIntegrationSettingsToggle",
        details=(
            "Enables a toggle in the settings that allows users to enable/disable the Bento integration for themselves."
            "\n\n"
            "The toggle will only appear when the user is using Wealthbox and has Wealthbox correctly integrated. "
            "Toggling the toggle on will set up a rule that generates a Bento life tags follow-up for each client category "
            "meeting (not internal or debrief). Toggling the toggle off will remove that rule rule."
            "\n\n"
            "If there is an org-level rule that enables Bento for all users in the org, the toggle will be forced on, "
            "the user will not be able to toggle it off, and a note will direct them to contact support. If there is also "
            "a user-level rule, then toggling the toggle off will delete that rule, but the toggle will then be forced on."
        ),
    )

    EnableClientIntelligencePreMeetingWorkflow = FeatureFlag(
        name="EnableClientIntelligencePreMeetingWorkflow",
        details=(
            "Enables the client intelligence pre-meeting workflow. "
            "\n\n"
            "See http://go/client-intelligence-v1 for details."
        ),
    )

    EnableQuickQuestions = FeatureFlag(
        name="EnableQuickQuestions",
        active_environments=[Environment.DEV],
        details=(
            "Enables quick question buttons in the note sumaries. "
            "See https://drive.google.com/file/d/15gVGhHvtL5J3uQ81j_ff7UaK_0jspxAg/view?usp=drive_link for demo"
        ),
    )

    EnableSaveScheduledNotes = FeatureFlag(
        name="EnableSaveScheduledNotes",
        details=(
            "Enables a feature that allows users to save notes on the creation screen, creating scheduled notes."
            "\n\n"
            "This entails:\n"
            "- adding a 'scheduled' time selector to the note creation page\n"
            "- enabling the 'Save' button on the note creation page before a meeting has started\n"
            "- scheduling a meeting for a selected time will create a scheduled meeting at that time\n"
            "- adjusting the 'Save' button to an 'End meeting' button when the meeting is in progress\n"
        ),
    )

    EnableLockdownMode = FeatureFlag(
        name="EnableLockdownMode",
        details="Disables sending emails from Zeplyn.",
    )

    EnableChunkedAudioUploadsOniOS = FeatureFlag(
        name="EnableChunkedAudioUploadsOniOS",
        details="Enables chunked audio uploads on iOS.",
    )

    EnableChunkedAudioUploadsOnWeb = FeatureFlag(
        name="EnableChunkedAudioUploadsOnWeb",
        details="Enables chunked audio uploads on web.",
    )

    EnablePracticeInsightsDashboard = FeatureFlag(
        name="EnablePracticeInsightsDashboard",
        details="Enables the practice insights dashboard (https://zeplyn.atlassian.net/browse/MA-54).",
    )

    EnableUserImpersonation = FeatureFlag(
        name="EnableUserImpersonation", details="Enables user impersonation for superuser"
    )
    EnableCrmSyncPrefixText = FeatureFlag(
        name="EnableCrmSyncPrefixText",
        details="Enables the prefix text addition while CRM note syncing.",
    )

    EnableNewActionItemsUI = FeatureFlag(
        name="EnableNewActionItemsUI",
        details="Enables in new actions layout for notes page.",
    )

    EnableInAppTutorials = FeatureFlag(
        name="EnableInAppTutorials",
        details="Enables in app tutorials for web app.",
    )

    UseScheduledEventsForCalendarAPI = FeatureFlag(
        name="UseScheduledEventsForCalendarAPI",
        details=(
            "Enables the use of data from ScheduledEvent models for the calendar API, instead of "
            "directly pulling from the calendar providers."
        ),
    )

    EnableOrgLevelMicrosoftCalendarIntegration = FeatureFlag(
        name="EnableOrgLevelMicrosoftCalendarIntegration",
        details=(
            "Enables the org-level Microsoft calendar integration. "
            "If this is enabled for a user in an org where there is an existing org-level Microsoft "
            "calendar integration, then that user will get Microsoft calendar events without requring "
            "a user-level integration. If the user has a user-level integration, that will be used "
            "instead (including if they integrate after having previously relied on the org-level "
            "integration)."
        ),
    )

    EnableMidMeetingNudge = FeatureFlag(
        name="EnableMidMeetingAgendaCompletionNudge",
        details=(
            "Enables Mid-Meeting Intelligence Nudge - a chat message just before the meeting ends to check all agenda items were covered."
        ),
    )

    EnableClientsPagination = FeatureFlag(
        name="EnableClientsPagination",
        details=(
            "Enable pagination for clients - i.e., dynamically load more data as user scrolls.\n"
            "This affects /clients & /notes pages as well as Sync to CRM flow.\n"
            "(JIRA: ENG-1381)"
        ),
    )

    EnableAudioFileUpload = FeatureFlag(
        name="EnableAudioFileUpload",
        details="Enables the ability to upload audio files to Zeplyn, which can then be used to create notes.",
    )

    # A convenience to avoid having to reference `Flags.Foo.value` to access the flag value.
    def is_active_for_user(self, user: "AbstractBaseUser", read_only: bool = False) -> bool | None:
        return self.value.is_active_for_user(user, read_only)
