name: Run tests

on:
  pull_request:
    types: [opened, synchronize]
  push:
    branches:
      - main
  workflow_dispatch: # Allows manual triggering

jobs:
  run-config-tests:
    name: Run tests for deployment config
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Run the validation test script
        run: |
          cd docker && ./validate.sh
  run-tests:
    name: Run unit tests
    permissions:
      contents: read
      pull-requests: write
    runs-on: codebuild-ZeplynGithubRunners-${{ github.run_id }}-${{ github.run_attempt }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
          cache: pip
          cache-dependency-path: requirements/local.txt
      - name: Install Dependencies
        run: pip install -r requirements/local.txt
      - name: Install 1Password CLI
        uses: 1password/install-cli-action@v1
      - name: Configure the 1Password CLI credentials
        uses: 1password/load-secrets-action/configure@v2
        with:
          service-account-token: ${{ secrets.ONEPASSWORD_SECURITY_ACCOUNT_TOKEN }}
      - name: Fetch secrets file
        run: |
          op document get 32zcmblturf42ilfs7wqfvc4zi -o .env.secrets
      - name: Set up Docker image cache
        uses: AndreKurait/docker-cache@0.6.0
        with:
          key: docker-${{ runner.os }}-${{ hashFiles('docker/docker-compose.development.yml') }}
      - name: Start the database
        run: docker compose -f docker/docker-compose.development.yml up db redis -d
      - name: Run the tests
        uses: pavelzw/pytest-action@v2
        with:
          custom-arguments: >
            --cov
            --cov-report=xml:coverage.xml
            --cov-report=html:htmlcov
            --cov-report=lcov:coverage.lcov
            -n auto
            --dist load
            -v
            --reruns 2
            --reruns-delay 1
          emoji: false
          verbose: false
      - name: Upload coverage data
        uses: actions/upload-artifact@v4
        with:
          name: coverage_report_xml
          path: coverage.xml
      - name: Upload LCOV coverage data
        uses: actions/upload-artifact@v4
        with:
          name: coverage_report_lcov
          path: coverage.lcov
      - name: Upload rich coverage data
        uses: actions/upload-artifact@v4
        with:
          name: coverage_report_html
          path: htmlcov/
      - name: Stop the database
        if: always()
        run: docker compose -f docker/docker-compose.development.yml down
  coverage:
    name: Compute coverage
    permissions:
      contents: read
      pull-requests: write
    needs: run-tests
    runs-on: ubuntu-latest
    steps:
      - name: Fetch coverage data
        if: github.event_name != 'push'
        uses: actions/download-artifact@v4
        with:
          name: coverage_report_xml
      - name: Fetch LCOV coverage data
        if: github.event_name != 'push'
        uses: actions/download-artifact@v4
        with:
          name: coverage_report_lcov
      - name: Display coverage information
        if: github.event_name != 'push'
        uses: orgoro/coverage@v3.2
        with:
          coverageFile: coverage.xml
          token: ${{ secrets.GITHUB_TOKEN }}
          thresholdNew: 0.75
      - name: Display LCOV coverage information
        if: github.event_name != 'push'
        uses: romeovs/lcov-reporter-action@v0.4.0
        with:
          lcov-file: coverage.lcov
          filter-changed-files: true
          github-token: ${{ secrets.GITHUB_TOKEN }}
