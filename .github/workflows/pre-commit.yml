name: Pre-commit Checks

on:
  pull_request:
    types: [opened, synchronize]
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  pre-commit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
          cache: pip
          cache-dependency-path: requirements/local.txt
      - name: Install pre-commit
        run: pip install pre-commit
      - name: Run pre-commit
        run: pre-commit run --all-files

  check-mypy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
          cache: pip
          cache-dependency-path: requirements/local.txt
      - name: Set up PYTHONPATH
        shell: bash
        run: echo "PYTHONPATH=$GITHUB_WORKSPACE" >> $GITHUB_ENV
      - name: Install Dependencies
        run: pip install -r requirements/local.txt
      - name: Run mypy
        run: |
          output_file="${{ runner.temp }}/mypy_output"
          ./run_mypy.sh -O json "${GITHUB_WORKSPACE}" | tee "${output_file}"
          exit_code="${PIPESTATUS[0]}"
          python "${GITHUB_WORKSPACE}/.github/workflows/parse_mypy_output.py" "${output_file}"
          exit "${exit_code}"
      - name: Generate summary (failure)
        if: failure()
        run: |
          cat <<EOF > $GITHUB_STEP_SUMMARY
          :red_circle:
          mypy checks failed. Please fix the errors and run the checks again:

  check-pip-compile:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
          cache: pip
          cache-dependency-path: requirements/local.txt
      - name: Install pip-tools
        run: pip install pip-tools
      - name: Compile the local requirements (to confirm they are unchanged)
        run: requirements/update.sh
      - name: Generate summary (failure)
        if: failure()
        run: |
          cat <<EOF > $GITHUB_STEP_SUMMARY
          :red_circle:
          requirements.txt files are out-of-sync with the requirements. Run
          `requirements/update.sh` to update the requirements files.
          :construction:
          Diff:
          ```
          $(git diff)
          ```
          EOF

  check-for-jira-ticket:
    runs-on: ubuntu-latest
    name: Check for a Jira ticket in one of the commit messages
    permissions:
      pull-requests: read
    steps:
      - name: Get PR Commits
        id: get-pr-commits
        if: github.event_name == 'pull_request'
        uses: tim-actions/get-pr-commits@v1.3.1
        with:
          token: ${{ github.token}}
      - name: Check for Jira ticket number
        uses: tim-actions/commit-message-checker-with-regex@v0.3.2
        if: github.event_name == 'pull_request'
        with:
          commits: ${{ steps.get-pr-commits.outputs.commits }}
          pattern: '(ENG|CUS)-\d{1,}|NOTICKET[:=].+'
          one_pass_all_pass: true
          error: >-
            Missing Jira ticket number in commit message. Please add a Jira
            ticket number to a commit message in one of the PR's commits, or
            add "NOTICKET: Reason" to the commit message if there is no Jira
            ticket.
      - name: Generate summary (failure)
        if: failure()
        run: |
          cat <<EOF >> $GITHUB_STEP_SUMMARY
          :red_circle:
          Missing Jira ticket number in commit message. Please add a Jira
          ticket number to a commit message in one of the PR's commits, or
          add "NOTICKET: Reason" to the commit message if there is no Jira
          ticket.
          EOF
