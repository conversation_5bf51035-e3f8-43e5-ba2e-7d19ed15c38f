name: Check Django migration status

on:
  pull_request:
    types: [opened, synchronize]
  push:
    branches:
      - main

jobs:
  lint-migrations:
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      # Check out main, so we can get a migration base.
      - uses: actions/checkout@v4
        with:
          ref: "main"
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
          cache: "pip"
          cache-dependency-path: "requirements/local.txt"
      - name: Install Dependencies
        run: pip install -r requirements/local.txt
      - name: Install 1Password CLI
        uses: 1password/install-cli-action@v1
      - name: Configure the 1Password CLI credentials
        uses: 1password/load-secrets-action/configure@v2
        with:
          service-account-token: ${{ secrets.ONEPASSWORD_SECURITY_ACCOUNT_TOKEN }}
      - name: Fetch secrets file
        run: |
          op document get 32zcmblturf42ilfs7wqfvc4zi -o .env.secrets
      - name: Set up Docker image cache
        uses: AndreKurait/docker-cache@0.6.0
        with:
          key: docker-${{ runner.os }}-${{ hashFiles('docker/docker-compose.development.yml') }}
      - name: Start the database
        run: |
          source .env.vars
          source .env.secrets
          docker compose -f docker/docker-compose.development.yml up db -d
      # Do the migrations, so the database is up-to-date with main.
      - name: Do migrations
        run: |
          source .env.vars
          source .env.secrets
          ./manage.py migrate
      # Check out the PR branch.
      - uses: actions/checkout@v4
      # Install dependencies on the branch, in case a new dependency
      # was added.
      - name: Install dependencies (from branch)
        run: pip install -r requirements/local.txt
      - name: Fetch secrets file
        run: |
          op document get 32zcmblturf42ilfs7wqfvc4zi -o .env.secrets
      # Check that the migrations are backwards compatible.
      - name: Lint the migrations
        run: |
          source .env.vars
          source .env.secrets
          ./manage.py lintmigrations
      - name: Stop the database
        if: always()
        run: docker compose -f docker/docker-compose.development.yml down
  check-migrations:
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash
    steps:
      # Check out main, so we can get a migration base.
      - uses: actions/checkout@v4
        with:
          ref: "main"
      - uses: actions/setup-python@v5
        with:
          python-version: "3.11"
          cache: "pip"
          cache-dependency-path: "requirements/local.txt"
      - name: Install Dependencies
        run: pip install -r requirements/local.txt
      - name: Install 1Password CLI
        uses: 1password/install-cli-action@v1
      - name: Configure the 1Password CLI credentials
        uses: 1password/load-secrets-action/configure@v2
        with:
          service-account-token: ${{ secrets.ONEPASSWORD_SECURITY_ACCOUNT_TOKEN }}
      - name: Fetch secrets file
        run: |
          op document get 32zcmblturf42ilfs7wqfvc4zi -o .env.secrets
      - name: Set up Docker image cache
        uses: AndreKurait/docker-cache@0.6.0
        with:
          key: docker-${{ runner.os }}-${{ hashFiles('docker/docker-compose.development.yml') }}
      - name: Start the database
        run: |
          source .env.vars
          source .env.secrets
          docker compose -f docker/docker-compose.development.yml up db -d
      # Do the migrations, so the database is up-to-date with main.
      - name: Do migrations
        run: |
          source .env.vars
          source .env.secrets
          ./manage.py migrate
      # Check out the PR branch.
      - uses: actions/checkout@v4
      # Install dependencies on the branch, in case a new dependency
      # was added.
      - name: Install dependencies (from branch)
        run: pip install -r requirements/local.txt
      - name: Fetch secrets file
        run: |
          op document get 32zcmblturf42ilfs7wqfvc4zi -o .env.secrets
      # First do a dry run, which will print out information; then,
      # run the check to get the failure error code to cause the
      # action to fail if there are any new migrations.
      - name: Make new migrations
        run: |
          source .env.vars
          source .env.secrets
          ./manage.py makemigrations --dry-run
          ./manage.py makemigrations --check
      - name: Stop the database
        if: always()
        run: docker compose -f docker/docker-compose.development.yml down
